<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/HlrMw/" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/HlrMw/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:element name="imsi" type="IMSIType" />
	<xs:element name="msisdn" type="MSISDNType" />
    <xs:element name="PrimaryHLRId" type="primaryhlridType"/>
	<!-- get MessageWaiting -->
	<xs:element name="getResponseMessageWaiting" type="GetMessageWaitingType">
		<xs:annotation>
			<xs:documentation>
				The attributes for get MessageWaiting response.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="msisdnKey_Get">
			<xs:selector xpath="." />
			<xs:field xpath="@msisdn" />
		</xs:key>
		<xs:keyref name="msisdnKeyRef_Get" refer="msisdnKey_Get">
			<xs:selector xpath="." />
			<xs:field xpath="msisdn" />
		</xs:keyref>
		<xs:key name="imsiKey_Get">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:key>
		<xs:keyref name="imsiKeyRef_Get" refer="imsiKey_Get">
			<xs:selector xpath="." />
			<xs:field xpath="imsi" />
		</xs:keyref>
	</xs:element>

	<xs:complexType name="GetMessageWaitingType">
		<xs:sequence>
			<xs:element name="msisdn" type="MSISDNType" >
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementMsisdn"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:element>
			<xs:element name="imsi" type="IMSIType" >
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementImsi"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:element>
			<!-- mobile station memory capacity exceeded -->
			<xs:element name="mce" type="EnumType" />
			<!-- mobile station not reachable through MSC -->
			<xs:element name="mnrf" type="EnumType" minOccurs="0" />
			<!-- mobile station not reachable through SGSN -->
			<xs:element name="mnrg" type="EnumType" minOccurs="0" />
			<xs:element name="scadd" type="ScaddType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
		</xs:attribute>
		<xs:attribute name="imsi" type="IMSIType" use="optional">
		</xs:attribute>
	</xs:complexType>

	<!-- simple type definition -->
	<xs:simpleType name="MSISDNType">
		<xs:annotation>
			<xs:documentation>
				the type definition for MSISDN
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IMSIType">
		<xs:annotation>
			<xs:documentation>
				the type definition for IMSI
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="6" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="EnumType">
		<xs:restriction base="xs:unsignedByte">
			<xs:enumeration value="0" />
			<xs:enumeration value="1" />
		</xs:restriction>
	</xs:simpleType>

	<!--complex type definition -->
	<!-- service center(SC) address -->
	<xs:complexType name="ScaddType">
		<xs:sequence>
			<xs:element name="scaddress" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1" />
						<xs:maxLength value="30" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="primaryhlridType">
			<xs:restriction base="xs:string">
				<xs:pattern value="[1-15]-[1-32]" />
				<xs:maxLength value="5" />
			</xs:restriction>
	</xs:simpleType>	
</xs:schema>
