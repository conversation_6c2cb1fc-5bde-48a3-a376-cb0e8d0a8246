<!-- BCE User 2014-11-17 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/pgm/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/pgm/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/pgm/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:element name="auId" type="AuIdType" />
	<xs:element name="publicId" type="PublicIdType" />
	<xs:element name="documentSelector" type="DocumentSelectorType" />
	<!-- Create PGM Document MOId: auId, publicId, documentSelector MOType: document@http://schemas.ericsson.com/ma/pgm/ -->
	<xs:element name="createDocument">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating PGM Document
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="auId" type="AuIdType" />
				<xs:element name="publicId" type="PublicIdType" />
				<xs:element name="documentSelector" type="DocumentSelectorType" />
				<!-- PGM Document Attributes -->
				<xs:element name="contentType" type="xs:string" />
				<xs:element name="xmlDocument" type="DocumentType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="auId" type="AuIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="auIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="publicId" type="PublicIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="documentSelector" type="DocumentSelectorType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="documentSelectorAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="auIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@auId" />
		</xs:key>
		<xs:keyref name="auIdKeyRef_Create" refer="auIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="auId" />
		</xs:keyref>
		<xs:key name="publicIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@publicId" />
		</xs:key>
		<xs:keyref name="publicIdKeyRef_Create" refer="publicIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="publicId" />
		</xs:keyref>
		<xs:key name="documentSelectorKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@documentSelector" />
		</xs:key>
		<xs:keyref name="documentSelectorKeyRef_Create" refer="documentSelectorKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="documentSelector" />
		</xs:keyref>
	</xs:element>
	<!-- Set PGM Document MOId: auId, publicId, documentSelector MOType: document@http://schemas.ericsson.com/ma/pgm/ -->
	<xs:element name="setDocument">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating PGM Document
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="auId" type="AuIdType" />
				<xs:element name="publicId" type="PublicIdType" />
				<xs:element name="documentSelector" type="DocumentSelectorType" />
				<!-- PGM Document Attributes -->
				<xs:element name="eTag" type="xs:string" minOccurs="0" />
				<xs:element name="contentType" type="xs:string" />
				<xs:element name="xmlDocument" type="DocumentType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="auId" type="AuIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="auIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="publicId" type="PublicIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="documentSelector" type="DocumentSelectorType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="documentSelectorAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- get PGM Document MOId: auId, publicId, documentSelector MOType: document@http://schemas.ericsson.com/ma/pgm/ -->
	<xs:element name="getDocument">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving PGM Document
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="auId" type="AuIdType" />
				<xs:element name="publicId" type="PublicIdType" />
				<xs:element name="documentSelector" type="DocumentSelectorType" />
				<!-- PGM Document Attributes -->
				<xs:element name="eTag" type="xs:string" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="auId" type="AuIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="auIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="publicId" type="PublicIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="documentSelector" type="DocumentSelectorType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="documentSelectorAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- get PGM Document Response , <auId> missing/Empty means retrieving the whole directory document -->
	<xs:element name="getDocumentResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving PGM Document
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="auId" type="AuIdType" />
				<xs:element name="publicId" type="PublicIdType" />
				<xs:element name="documentSelector" type="DocumentSelectorType" />
				<!-- PGM Document Attributes -->
				<xs:element name="eTag" type="xs:string" minOccurs="0" />
				<xs:element name="contentType" type="xs:string" minOccurs="0" />
				<xs:element name="xmlDocument" type="DocumentType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="auId" type="AuIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="auIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="publicId" type="PublicIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="documentSelector" type="DocumentSelectorType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="documentSelectorAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- delete PGM Document MOId: auId, publicId, documentSelector MOType: document@http://schemas.ericsson.com/ma/pgm/ -->
	<!-- types -->
	<xs:simpleType name="PublicIdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(sip:.{1,256})|(tel:.{1,256})" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AuIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="100" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DocumentSelectorType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="100" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DocumentType">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" />
		</xs:sequence>
		<xs:anyAttribute />
	</xs:complexType>
</xs:schema>
