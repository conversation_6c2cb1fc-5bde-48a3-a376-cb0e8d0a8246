<?xml version="1.0" encoding="UTF-8"?>
<!--
   JAXB Binding Configuration for NEO Adapter Client

   This file maps XML Schema namespaces to meaningful Java packages to organize generated code.
   Only includes schemas that are actually imported in the WSDL file.

   Important !!
    - Update schemaLocation paths if XSD/bindings files move
    - Update package names if Java package structure changes
    - Add new bindings when new XSDs are added to the wsd

-->
<jxb:bindings version="3.0"
              xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
              xmlns:xs="http://www.w3.org/2001/XMLSchema"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="https://jakarta.ee/xml/ns/jaxb https://jakarta.ee/xml/ns/jaxb/bindingschema_3_0.xsd">

    <!-- Global configuration applied to all schemas -->
    <jxb:globalBindings>
        <jxb:serializable uid="1"/>
    </jxb:globalBindings>

<!-- 1. OSSJ Order Management API bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/OSSJ-OrderManagement-v1-0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 2. OSSJ Common Types bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/OSSJ-Common-v1-5.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.common"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 3. Amdocs Common Types bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-Common_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.common"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 4. Amdocs Inventory API bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-Inventory_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.inventory"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 5. Amdocs CBE Party Model bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-Common-CBEParty_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeparty"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 6. Amdocs Order Management API bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-OrderManagement_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 7. Amdocs CBE Resource Model bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-Common-CBEResource_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cberesource"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 8. Amdocs CBE Service Model bindings -->
    <jxb:bindings schemaLocation="../OSSJ-schema/xsd/Amdocs-OSSJ-Common-CBEService_3p0.xsd" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeservice"/>
        </jxb:schemaBindings>
    </jxb:bindings>

    <!-- 9. AUAI Request WSDL inline schema bindings -->
    <!-- Updated to reference minimal WSDL for optimized code generation -->
    <jxb:bindings schemaLocation="../OSSJ-schema/wsdl/AUAI-RequestWS-v2-0.wsdl#types1" node="/xs:schema">
        <jxb:schemaBindings>
            <jxb:package name="com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.request"/>
        </jxb:schemaBindings>
    </jxb:bindings>

</jxb:bindings>
