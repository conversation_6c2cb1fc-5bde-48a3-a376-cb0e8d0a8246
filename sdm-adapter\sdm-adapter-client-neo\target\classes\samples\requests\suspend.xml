<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Header>
        <Security xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <UsernameToken>
                <Username>${#Project#OUA_SOAP_USER}</Username>
                <Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${#Project#OUA_SOAP_TOKEN}</Password>
            </UsernameToken>
        </Security>
    </soapenv:Header>
    <soapenv:Body>
               <ns2:createAndStartRequestByValueRequest xmlns="http://ossj.org/xml/Common/v1-5" xmlns:ns1="http://xmlns.aua.oss.amdocs.com/requestcontroller" xmlns:ns10="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:ns11="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:ns12="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:ns13="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:ns14="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:ns15="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:ns16="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:ns17="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:ns18="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:ns19="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:ns2="http://ossj.org/xml/OrderManagement/v1-0" xmlns:ns20="http://ossj.org/xml/Inventory/v1-2" xmlns:ns21="http://amdocs/core/ossj-Inventory/dat/3" xmlns:ns22="http://ossj.org/xml/Common-CBEReport/v1-5" xmlns:ns23="http://ossj.org/xml/Common-CBEUser/v1-5" xmlns:ns3="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:ns4="http://amdocs/core/ossj-Common/dat/3" xmlns:ns5="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:ns6="http://ossj.org/xml/Common-CBEProductOffering/v1-5" xmlns:ns7="http://ossj.org/xml/Common-CBEDatatypes/v1-5" xmlns:ns8="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:ns9="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:rc="http://xmlns.aua.oss.amdocs.com/requestcontroller">
            <ns2:requestValue xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ServiceOrderValue">
                <key xsi:type="ns19:ServiceOrderKey">
                    <type>Service</type>
                    <primaryKey>
                        <ns4:primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">Suspend-Wireless-${=java.util.UUID.randomUUID()}</ns4:primaryKey>
                    </primaryKey>
                </key>
                <ns19:priority_Request>0</ns19:priority_Request>
                <ns2:serviceOrderItems>
                    <ns2:item xsi:type="ns19:ServiceOrderItemValue">
                        <ns5:action>suspend</ns5:action>
                        <ns2:service xsi:type="ns14:ServiceValue">
                            <key xsi:type="ns14:ServiceKey">
                                <type>Service</type>
                                <primaryKey>
                                    <ns4:primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">Suspend-Wireless-${=java.util.UUID.randomUUID()}</ns4:primaryKey>
                                </primaryKey>
                            </key>
                            <ns3:describedBy xsi:type="ns4:ArrayOfCharacteristicValue">
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>imsi</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">9991119880</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>operator</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">BSSe</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>accountType</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">I</ns4:values>
                                </ns3:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>acctStatus</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>billingAccountNumber</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">************</ns4:values>
                                </ns3:item>
                                    <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>suspendReasonCode</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">false</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>sliceServiceType</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>orderCreationDate</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>networkAccessMode</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">NA</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>chfGroupId</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">null</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>subscriptionId</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">4D73861DAA3144069556538F58B3C1F9</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>wbbService</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">NA</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>orderSubmissionDate</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>hssGroupId</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">CE07</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>gprsRoamingRestriction</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>acctStatus</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">590005</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>iccid</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">89021807320026000000</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>msisdn</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">${#Project#MSISDN}</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>priority</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>roamingRestriction</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>subscriptionClass</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>vmCosId</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>chargingCharacteristic</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string" />
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>hssId</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">HC7B</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>market</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">TNK</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>reasonCode</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">vacation</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>resubmitMode</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">failed</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>imei</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">89011807300026000001</ns4:values>
                                </ns3:item>
                                <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>ir94VideoCalls</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns4:values>
                                </ns3:item>
                                  <ns3:item xsi:type="ns4:CharacteristicValue">
                                    <ns3:characteristic>mms</ns3:characteristic>
                                    <ns4:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns4:values>
                                </ns3:item>
                            </ns3:describedBy>
                            <ns14:serviceType>Mobility</ns14:serviceType>
                            <ns14:activationTargets>
                                <ns4:item>
                                    <ns4:id>Activation</ns4:id>
                                </ns4:item>
                            </ns14:activationTargets>
                        </ns2:service>
                        <ns19:subAction />
                    </ns2:item>
                </ns2:serviceOrderItems>
                <ns19:operator>BSSe</ns19:operator>
                <ns19:resubmitMode>failed</ns19:resubmitMode>
                <dox-om:maxResponseWaitTime xmlns:dox-om="http://amdocs/core/ossj-OrderManagement/dat/3">300</dox-om:maxResponseWaitTime>
            </ns2:requestValue>
        </ns2:createAndStartRequestByValueRequest>
        
    </soapenv:Body>
</soapenv:Envelope>