<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified">

	<xs:simpleType name="NUMBERType">
		<xs:annotation>
			<xs:documentation>
				the type definition for fanumber
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msisdnallType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="hlrfeidType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="primaryhlridType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[1-15]-[1-32]" />
			<xs:maxLength value="5" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsisType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsiallType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsiType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:maxLength value="15" />
					<xs:minLength value="6" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="profileType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="8191" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="lmuType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="ridType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="63" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridLogicalType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(([0-9]|[1-5][0-9]|[6][0-3])([&amp;]{1,2}(([0-9]|[1-5][0-9]|[6][0-3])))*)" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="zoneidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>


	<xs:simpleType name="stateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NOT CONNECTED" />
			<xs:enumeration value="CONNECTED" />
			<xs:enumeration value="ADDITIONAL" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imischstateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PEND" />
			<xs:enumeration value="EXEC" />
			<xs:enumeration value="FORC" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="authdType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AVAILABLE" />
			<xs:enumeration value="NO IMSI IN AUC" />
			<xs:enumeration value="NO ACCESS TO AUC" />
		</xs:restriction>
	</xs:simpleType>

	<!-- Only checks the format, not if the command exist or not, or if the parameters are valid. A more thorough check is made in Java code. -->
	<xs:simpleType name="sudType">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]{3,9}-[0-9]{1,5}(-[0-9]{1,4})?)([&amp;]([A-Za-z0-9]{3,9}-[0-9]{1,5}(-[0-9]{1,4})?))*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="fanType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="frontendidType">
		<xs:restriction base="xs:string"></xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="bcType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="65534" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="udiType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="accstType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="rdiType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="audioType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="faxType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="altfaxType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="rcType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="accType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="itnType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="dateType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9][0-9][0-1][0-9][0-3][0-9]" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cspType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="8160" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="mmtdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="skType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="2147483647" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="gsaType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:maxLength value="15" />
					<xs:minLength value="3" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="dstdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="10" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="dialnumType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-4]-[0-9,*,#,a,b,c]*" />
			<xs:maxLength value="17" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="dehType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tsmstdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="osmstdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="gprstdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="octdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="tctdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="iType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="true"/>
			<xs:enumeration value="false"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="0"/>
			<xs:enumeration value="N"/>
			<xs:enumeration value="Y"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="vttdpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="cchType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="4" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="ssloType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gcsoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gc2soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gc3soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gc4soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mcsoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mc2soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mc3soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mc4soType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tifType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gprssoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="osmssoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tsmssoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mmsoType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="etinciType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="etickType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eoinciType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eoickType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tdptypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DSTDP" />
			<xs:enumeration value="GPRSTDP" />
			<xs:enumeration value="MMTDP" />
			<xs:enumeration value="OCTDP" />
			<xs:enumeration value="OSMSTDP" />
			<xs:enumeration value="TCTDP" />
			<xs:enumeration value="TSMSTDP" />
			<xs:enumeration value="VTTDP" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="criteriaTDPType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OCTDP2" />
			<xs:enumeration value="TCTDP12" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tdpType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mtyType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="I" />
			<xs:enumeration value="E" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ftcType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="F" />
					<xs:enumeration value="N" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:boolean">
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="dnumType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-4]-[0-9*#]{1,15}(&amp;([0-4]-[0-9*#]{1,15}))*" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="dlghType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="15" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="bsType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:minLength value="2" />
					<xs:maxLength value="6" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="bsgType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:minLength value="2" />
					<xs:maxLength value="6" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="apnidType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="16383" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="NS" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="WILDCARD" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="apnType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="62" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cicType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="1" />
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ixcType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="7" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="piciType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eqosidHEEQPType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4095" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="eqosidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="4095" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="conType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="strType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="intType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="backType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="thpType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="tdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="100" />
			<xs:maxInclusive value="4000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gbrdType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="256000" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="gbruType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="256000" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="arpType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="doType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="YES" />
			<xs:enumeration value="NO" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="sduType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(NDE|YES|NO)-([1-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9]|6[0-9]|7[0-9]|8[0-9]|9[0-9]|10[0-9]|11[0-9]|12[0-9]|13[0-9]|14[0-9]|15[0-1])-[1-9]-[1-7]" />
			<xs:minLength value="8" />
			<xs:maxLength value="11" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mbrdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="256000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mbruType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="256000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pdpaddType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="IPv4Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="IPv6Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="epdpaddType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="IPv4Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="IPv6Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="epdpindType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="pdptyType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPV4" />
			<xs:enumeration value="IPV6" />
			<xs:enumeration value="PPP" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pdpchType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9\-]*" />
					<xs:minLength value="1" />
					<xs:maxLength value="7" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="vpaaType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="pdpidType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="50" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="pdpcpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="8160" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="namType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imeisvType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9F]*" />
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ssType">
		<xs:restriction base="xs:string">
			<xs:minLength value="2" />
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="statusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE-OP" />
			<xs:enumeration value="ACTIVE-QS" />
			<xs:enumeration value="NOT ACTIVE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="fnumType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9a-zA-Z*#\-]*" />
			<xs:maxLength value="18" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="saddType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="45" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="timeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="5" />
			<xs:maxInclusive value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="passwordBarredType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="mcfActiveType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="vlrAddressType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="17" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msrnType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mscNumberType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="lmsidType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mscAreaRestrictedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="msPurgedInVlrType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="rspType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="sgsnNumberType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="17" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msPurgedInSgsnType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="SubscriberDisconnectedDuringPrintoutType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="activeType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="mchType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LOC" />
			<xs:enumeration value="USSD" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="masterType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="mstypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TCL" />
			<xs:enumeration value="MSP" />
			<xs:enumeration value="MSIM" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="tcallType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Y" />
			<xs:enumeration value="N" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="binaryType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="idType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="10" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="saveLocType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SAVELOC" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="keepType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="ofaType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="512" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="univType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="crelType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="cunrlType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="notfType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eaddType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="3" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="plmnoType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="intidType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[\w&amp;]+" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="moclType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z&amp;]+" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="servtType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[\w&amp;]+" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gresType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="indexType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="32767" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="icType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(([0-9][0-9][0-9][0-9]\-)?)([0-9]{1,4}|[0-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="restrType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ICB" />
			<xs:enumeration value="OCB" />
			<xs:enumeration value="NONE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mcfType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eraseType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="dpType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9&amp;]+" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="actionType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="scaddsType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="accessType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OA" />
			<xs:enumeration value="IA" />
			<xs:enumeration value="OIA" />
			<xs:enumeration value="NONE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pcugType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="32767" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="NONE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="trefType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="iegType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ALL" />
			<xs:enumeration value="CM" />
			<xs:enumeration value="MM" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="bssrtType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="BASIC" />
			<xs:enumeration value="HOVER" />
			<xs:enumeration value="RADIO" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="priType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Y" />
			<xs:enumeration value="N" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="omcidType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-46]{1}-[0134689]{1}-[0-9a-zA-Z*#]{1,38}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="steType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gmlcidType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="255" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]{1,3}((&amp;&amp;[0-9]{1,3})|(&amp;[0-9]{1,3})+)" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="hgmlcidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ppridType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gmlcType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="hgmlcType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="pprType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="gprsType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="charType">
		<xs:restriction base="xs:string"><!-- ebenchr: Needs testing! -->
			<xs:pattern value="[\w]+" />
			<xs:minLength value="3" />
			<xs:maxLength value="8" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="locationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="KNOWN" />
			<xs:enumeration value="UNKNOWN" />
			<xs:enumeration value="RESTRICTED" />
			<xs:enumeration value="BARRED" />
			<xs:enumeration value="PURGED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nsubType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>

	<xs:simpleType name="vlraddrType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[34]{1}-[.]*" />
		</xs:restriction>
	</xs:simpleType>

	<!-- ADAPTION PRBT: START -->
	<xs:simpleType name="vlraddsType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[34]{1}-[0-9]{2,15}" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<!-- ADAPTION PRBT: END -->
	
	<xs:simpleType name="sgsnNumbersType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[34]{1}-[0-9]{2,15}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="sgsnnumType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[34]{1}-[.]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="vlrtsType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="DEACT" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mceType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NO" />
			<xs:enumeration value="YES" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mnrfType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="REACH" />
			<xs:enumeration value="NREACH" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mnrgType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="REACH" />
			<xs:enumeration value="NREACH" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="scaddType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[34]{1}-[0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gmlcaddType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:minLength value="3" />
					<xs:maxLength value="15" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="IPv4Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="(\d{1,3}\.){3}\d{1,3}" />
			<xs:minLength value="7" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IPv6Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-Fa-f:]*" />
			<xs:minLength value="2" />
			<xs:maxLength value="39" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="hgmlcaddType">
		<xs:union memberTypes="IPv4Type IPv6Type">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="gmlcaddSingleType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="3" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="usrfType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LCS" />
			<xs:enumeration value="STE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gmlcidSingleType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ipaddressType">
		<xs:union memberTypes="IPv4Type IPv6Type" />
	</xs:simpleType>

	<xs:simpleType name="ppraddType">
		<xs:union memberTypes="IPv4Type IPv6Type">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="cfrType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>

	<xs:simpleType name="dpstatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="smspamType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="NACTIVE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="allpclType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="ccType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="1" />
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="rdpiType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="32" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="grdpiType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="32" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="31" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nprpType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="prpType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="10" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nlurejType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="plmnidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="10" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="percType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="95" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="zcsetType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="65534" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="rsaiType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="65535" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="zcType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="65535" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9&amp;]+" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="rsipType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="restappType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="indappType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cpdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="([\w]+-[0-1])([&amp;][\w]+-[0-1])*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msisdnsType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<!-- 1-15 - 1-32 -->
	<xs:simpleType name="mrdpidType">
		<xs:restriction base="xs:string">
			<xs:minLength value="3" />
			<xs:maxLength value="5" />
			<xs:pattern value="([1-9]|1[0-5])-(1[0-9]|2[0-9]|3[0-2]|[1-9])" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="subsnumValueType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="256000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nimsisType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]{1,15}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nimsiallType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cspallType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="optType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="extType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="critType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="updatenetType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="subsnumType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pdpcpsType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pendType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="execType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="forcedType">
		<xs:restriction base="xs:boolean">
			<xs:pattern value="true" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gsapType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mapopType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9a-zA-Z]+-[0,1]" />
		</xs:restriction>
	</xs:simpleType>

	<!-- AUC data types -->

	<!-- TODO qdafran. Place AUC data types in seperat file??? -->
	<xs:simpleType name="a3a8indType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="a4indType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="7" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="fsetindType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ekiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="kindType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="511" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="akatypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="amfType">
		<xs:restriction base="xs:string">
			<xs:pattern value="6553[0-5]|655[0-2]\d|65[0-4]\d\d|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}|0|DEFAULT" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="akaalgindType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1" />
			<xs:enumeration value="2" />
			<xs:enumeration value="N/A" />
			<xs:enumeration value="DEFAULT" />
		</xs:restriction>
	</xs:simpleType>


	<xs:simpleType name="rsaType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="raidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="127" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="srrType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="0" />
			<xs:enumeration value="1" />
			<xs:enumeration value="2" />
			<xs:enumeration value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridMultipleType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(([0-9]|[1-5][0-9]|[6][0-3])([&amp;]{1,2}(([0-9]|[1-5][0-9]|[6][0-3])))*)" />
		</xs:restriction>
	</xs:simpleType>

	<!-- TODO qdafran Remove or change "continueType" when decided how to handle CONTINUE. -->
	<xs:simpleType name="continueType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<!-- Enf of AUC data types -->


	<!-- M2M types -->

	<!-- Device mobility information -->
	<xs:simpleType name="dmiType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="STA" />
			<xs:enumeration value="DYN" />
		</xs:restriction>
	</xs:simpleType>


	<!-- Network application server name -->
	<xs:simpleType name="nnameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>


	<!-- Network application server type -->
	<xs:simpleType name="ntypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="127" />
		</xs:restriction>
	</xs:simpleType>


	<!-- Uniform Resource Locator -->
	<xs:simpleType name="urlType">
		<xs:restriction base="xs:string">
			<xs:minLength value="2" />
			<xs:maxLength value="63" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="urlEraseType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:minLength value="2" />
					<xs:maxLength value="63" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<!-- IP Type -->
	<xs:simpleType name="iptypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPV4" />
			<xs:enumeration value="IPV6" />
		</xs:restriction>
	</xs:simpleType>

	<!-- IP Address type -->
	<xs:simpleType name="ipChoiceType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="IPv4Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="IPv6Type" />
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="ipChoiceEraseType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="IPv4Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="IPv6Type" />
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<!-- IP Port -->
	<xs:simpleType name="ipportType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="m2mipportType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>


	<!-- Network application server MSISDN -->
	<xs:simpleType name="nmsisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nmsisdnEraseType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:minLength value="5" />
					<xs:maxLength value="15" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>


	<xs:simpleType name="m2mpidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="m2mspType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="m2mspvType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="socfnrcType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="socfnryType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gprsobpType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="gsmobcinvType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="4" />
		</xs:restriction>
	</xs:simpleType>

	<!-- M2MSP MPID Print Profile type -->
	<xs:simpleType name="m2msppType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="65535" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="mpidpType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="65535" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<!-- End M2MSP MPID Print Profile type -->

	<!-- End of M2M types -->
	
	<xs:element name="PGFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="errorcode" type="xs:integer" />
				<xs:element name="errormessage" type="xs:string" maxOccurs="unbounded" />
				<xs:element name="errordetails" type="xs:string" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UDCFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="errorcode" type="xs:integer" />
				<xs:element name="errormessage" type="xs:string" maxOccurs="unbounded" />
				<xs:element name="errordetails" type="xs:string" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
