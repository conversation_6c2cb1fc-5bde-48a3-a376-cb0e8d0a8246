<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
			 xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
			 xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
			 xmlns:xs="http://www.w3.org/2001/XMLSchema"
			 xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
			 xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
			 xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
			 xmlns:up="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"
			 xmlns:ma="http://schemas.ericsson.com/ema/UserProvisioning/"
			 xmlns:pg="http://schemas.ericsson.com/pg/1.0" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema xmlns="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
			<xs:import namespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/" schemaLocation="MTAS_Service_MMTelContext_wsdl_fixed.xsd"/>
			<xs:import namespace="http://schemas.ericsson.com/ema/UserProvisioning/" schemaLocation="UserProvisioningFault.xsd" />
			<xs:import namespace="http://schemas.ericsson.com/pg/1.0" schemaLocation="PGFault.xsd" />
			<xs:element name="Create">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Service@http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"/>
						<xs:element name="MOId" type="MOIdType" />
						<xs:element name="MOAttributes" >
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="up:createService"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreateResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId" type="MOIdType"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Get">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Service@http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"/>
						<xs:element name="MOId" type="MOIdType" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId" type="MOIdType" minOccurs="0" />
						<xs:element name="MOAttributes" >
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="up:getResponseService"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Set">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Service@http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"/>
						<xs:element name="MOId" type="MOIdType"/>
						<xs:element name="MOAttributes">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="up:setService"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SetResponse">
				<xs:complexType>
					<xs:sequence>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Delete">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Service@http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"/>
						<xs:element name="MOId" type="MOIdType"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeleteResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId" type="MOIdType" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:complexType name="MOIdType">
				<xs:sequence>
					<xs:element ref="up:publicId"/>
					<xs:element ref="up:context-identity"/>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="SessionIdFault" type="SessionIdFaultType"/>
			<xs:element name="SequenceIdFault" type="SequenceIdFaultType"/>
			<xs:element name="TransactionIdFault" type="TransactionIdFaultType"/>
			<xs:element name="SessionId" type="SessionIdType"/>
			<xs:element name="TransactionId" type="xs:string"/>
			<xs:element name="SequenceId" type="xs:unsignedLong"/>
			<xs:simpleType name="SessionIdType">
				<xs:restriction base="xs:string">
					<xs:pattern value="[\d\w]{1,}"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:element name="Cai3gFault">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="faultcode" type="xs:integer"/>
						<xs:element name="faultreason">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="reasonText" type="xs:string" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="faultrole" type="xs:string"/>
						<xs:element name="details" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="ma:UserProvisioningFault"/>
										<xs:element ref="pg:PGFault"/>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>

			<xs:complexType name="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultactor" type="xs:string"/>
					<xs:element name="description" type="xs:string"/>
				</xs:sequence>
			</xs:complexType>
			<xs:complexType name="SessionIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid SessionId"/>
										<xs:enumeration value="Session Timeout"/>
										<xs:enumeration value="SessionId Syntax Error"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
			<xs:complexType name="SequenceIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid SequenceId"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
			<xs:complexType name="TransactionIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid TransactionId"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
		</xs:schema>
	</types>
	<message name="Get">
		<part name="Get" element="cai3g:Get"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="GetResponse">
		<part name="GetResponse" element="cai3g:GetResponse"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="Cai3G12FaultException">
		<part name="fault" element="cai3g:Cai3gFault"/>
	</message>
	<message name="Set">
		<part name="Set" element="cai3g:Set"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="SetResponse">
		<part name="SetResponse" element="cai3g:SetResponse"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="Delete">
		<part name="Delete" element="cai3g:Delete"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="DeleteResponse">
		<part name="DeleteResponse" element="cai3g:DeleteResponse"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="Create">
		<part name="Create" element="cai3g:Create"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="CreateResponse">
		<part name="CreateResponse" element="cai3g:CreateResponse"/>
		<part name="SessionId" element="cai3g:SessionId"/>
		<part name="TransactionId" element="cai3g:TransactionId"/>
		<part name="SequenceId" element="cai3g:SequenceId"/>
	</message>
	<portType name="Context">
		<operation name="Create">
			<input message="cai3g:Create"/>
			<output message="cai3g:CreateResponse"/>
			<fault name="Cai3G12FaultException" message="cai3g:Cai3G12FaultException"/>
		</operation>
		<operation name="Delete">
			<input message="cai3g:Delete"/>
			<output message="cai3g:DeleteResponse"/>
			<fault name="Cai3G12FaultException" message="cai3g:Cai3G12FaultException"/>
		</operation>
		<operation name="Get">
			<input message="cai3g:Get"/>
			<output message="cai3g:GetResponse"/>
			<fault name="Cai3G12FaultException" message="cai3g:Cai3G12FaultException"/>
		</operation>
		<operation name="Set">
			<input message="cai3g:Set"/>
			<output message="cai3g:SetResponse"/>
			<fault name="Cai3G12FaultException" message="cai3g:Cai3G12FaultException"/>
		</operation>
	</portType>
	<binding name="Context" type="cai3g:Context">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<operation name="Create">
			<soap:operation soapAction="CAI3G#Create" style="document"/>
			<input>
				<soap:body parts="Create" use="literal"/>
				<soap:header message="cai3g:Create" part="SessionId" use="literal"/>
				<soap:header message="cai3g:Create" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:Create" part="SequenceId" use="literal"/>
			</input>
			<output>
				<soap:body parts="CreateResponse" use="literal"/>
				<soap:header message="cai3g:CreateResponse" part="SessionId" use="literal"/>
				<soap:header message="cai3g:CreateResponse" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:CreateResponse" part="SequenceId" use="literal"/>
			</output>
			<fault name="Cai3G12FaultException">
				<soap:fault name="Cai3G12FaultException" use="literal"/>
			</fault>
		</operation>
		<operation name="Delete">
			<soap:operation soapAction="CAI3G#Delete" style="document"/>
			<input>
				<soap:body parts="Delete" use="literal"/>
				<soap:header message="cai3g:Delete" part="SessionId" use="literal"/>
				<soap:header message="cai3g:Delete" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:Delete" part="SequenceId" use="literal"/>
			</input>
			<output>
				<soap:body parts="DeleteResponse" use="literal"/>
				<soap:header message="cai3g:DeleteResponse" part="SessionId" use="literal"/>
				<soap:header message="cai3g:DeleteResponse" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:DeleteResponse" part="SequenceId" use="literal"/>
			</output>
			<fault name="Cai3G12FaultException">
				<soap:fault name="Cai3G12FaultException" use="literal"/>
			</fault>
		</operation>
		<operation name="Get">
			<soap:operation soapAction="CAI3G#Get" style="document"/>
			<input>
				<soap:body parts="Get" use="literal"/>
				<soap:header message="cai3g:Get" part="SessionId" use="literal"/>
				<soap:header message="cai3g:Get" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:Get" part="SequenceId" use="literal"/>
			</input>
			<output>
				<soap:body parts="GetResponse" use="literal"/>
				<soap:header message="cai3g:GetResponse" part="SessionId" use="literal"/>
				<soap:header message="cai3g:GetResponse" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:GetResponse" part="SequenceId" use="literal"/>
			</output>
			<fault name="Cai3G12FaultException">
				<soap:fault name="Cai3G12FaultException" use="literal"/>
			</fault>
		</operation>
		<operation name="Set">
			<soap:operation soapAction="CAI3G#Set" style="document"/>
			<input>
				<soap:body parts="Set" use="literal"/>
				<soap:header message="cai3g:Set" part="SessionId" use="literal"/>
				<soap:header message="cai3g:Set" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:Set" part="SequenceId" use="literal"/>
			</input>
			<output>
				<soap:body parts="SetResponse" use="literal"/>
				<soap:header message="cai3g:SetResponse" part="SessionId" use="literal"/>
				<soap:header message="cai3g:SetResponse" part="TransactionId" use="literal"/>
				<soap:header message="cai3g:SetResponse" part="SequenceId" use="literal"/>
			</output>
			<fault name="Cai3G12FaultException">
				<soap:fault name="Cai3G12FaultException" use="literal"/>
			</fault>
		</operation>
	</binding>
	<service name="CAI3G">
		<port name="Context" binding="cai3g:Context">
			<soap:address location="http://anyema.anyprovisioningprovider.com/cai3g"/>
		</port>
	</service>
</definitions>
