<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 sp1 (http://www.altova.com) by <PERSON><PERSON> (<PERSON> (China) Communications Company Ltd) -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/nonSIM/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:nonSIM="http://schemas.ericsson.com/ma/nonSIM/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://schemas.ericsson.com/ma/nonSIM/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="./types/dataTypes.xsd"/>
    <xs:element name="transactionLogId" type="transactionLogIdType"/>
	<xs:element name="vImsi" type="imsiType"/>
	<xs:element name="CreateVoWifiService">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="vImsi" type="imsiType"/>
				<xs:element name="deviceRealm" type="devRealmType"/>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="impi" type="impiType"/>
				<xs:element name="apn" type="apnType"/>
				<xs:element name="password" type="passwdType"/>
				<xs:element name="userStatus" type="userStatusType" minOccurs="0"/>
				<xs:element name="csr" type="csrType"/>
			</xs:sequence>
			<xs:attribute name="vImsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="vImsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_identity">
			<xs:selector xpath="."/>
			<xs:field xpath="@vImsi"/>
		</xs:key>
		<xs:keyref name="keyref_create_identity" refer="key_create_identity">
			<xs:selector xpath="./nonSIM:vImsi"/>
			<xs:field xpath="."/>
		</xs:keyref>
	</xs:element>
	<xs:element name="CreateResponseVoWifiService">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="certificate" type="certType"/>
				<xs:element name="certId" type="certIdType"/>
				<xs:element name="certExpireTime" type="certExpireTimeType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="SetVoWifiService">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="deviceRealm" type="devRealmType"/>
				<xs:element name="imsi" type="imsiType" minOccurs="0"/>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0"/>
				<xs:element name="impi" type="impiType" minOccurs="0"/>
				<xs:element name="apn" type="apnType" minOccurs="0"/>
				<xs:element name="password" type="passwdType" minOccurs="0"/>
				<xs:element name="userStatus" type="userStatusType" minOccurs="0"/>
				<xs:element name="csr" type="csrType" minOccurs="0"/>
				<xs:element name="certId" type="certIdType" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="vImsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="vImsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="SetResponseVoWifiService">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="certificate" type="certType"/>
				<xs:element name="certId" type="certIdType"/>
				<xs:element name="certExpireTime" type="certExpireTimeType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeleteVoWifiService">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="deviceRealm" type="devRealmType"/>
				<xs:element name="imsi" type="imsiType" minOccurs="0"/>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0"/>
				<xs:element name="impi" type="impiType" minOccurs="0"/>
				<xs:element name="certId" type="certIdType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="vImsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="vImsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	
</xs:schema>
