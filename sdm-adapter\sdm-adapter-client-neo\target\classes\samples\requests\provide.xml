<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Header>
        <Security
            xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <UsernameToken>
                <Username>${#Project#OUA_SOAP_USER}</Username>
                <Password
                    Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"
                    >${#Project#OUA_SOAP_TOKEN}</Password>
            </UsernameToken>
        </Security>
    </soapenv:Header>
    <soapenv:Body>
        <ns5:createAndStartRequestByValueRequest xmlns:ns2="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns4="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:ns3="http://ossj.org/xml/Common/v1-5" xmlns:ns6="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:ns31="http://www.tmforum.org/xml/tip/common/notifications" xmlns:ns5="http://ossj.org/xml/OrderManagement/v1-0" xmlns:ns30="http://www.tmforum.org/xml/tip/internal/entity" xmlns:ns8="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:ns7="http://amdocs/core/ossj-Common/dat/3" xmlns:ns13="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:ns9="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:ns12="http://ossj.org/xml/Common-CBEDatatypes/v1-5" xmlns:ns34="http://docs.oasis-open.org/wsrf/bf-2" xmlns:ns11="http://ossj.org/xml/Common-CBEProductOffering/v1-5" xmlns:ns33="http://www.amdocs.com/xml/tip/internal/entity" xmlns:ns10="http://amdocs/core/ossj-Inventory/dat/3" xmlns:ns32="http://www.amdocs.com/xml/tip/common/notifications" xmlns:ns17="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:ns16="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:ns15="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:ns14="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:ns19="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:ns18="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:ns20="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:ns24="http://ossj.org/xml/Inventory/v1-2" xmlns:ns23="http://ossj.org/xml/Common-CBEUser/v1-5" xmlns:ns22="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:ns21="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:ns28="http://docs.oasis-open.org/wsn/t-1" xmlns:ns27="http://docs.oasis-open.org/wsn/b-2" xmlns:ns26="http://www.w3.org/2005/08/addressing" xmlns:ns25="http://ossj.org/xml/Common-CBEReport/v1-5" xmlns:ns29="http://www.tmforum.org/xml/tip/internal/notifications">
            <ns5:requestValue xsi:type="ns9:ServiceOrderValue" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns3:key xsi:type="ns9:ServiceOrderKey">
                    <ns3:type>Service</ns3:type>
                    <ns3:primaryKey>
                        <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">Provide-Wireless-${=java.util.UUID.randomUUID()}</primaryKey>
                    </ns3:primaryKey>
                </ns3:key>
                <ns9:priority_Request>2</ns9:priority_Request>
                <ns5:serviceOrderItems>
                    <ns5:item xsi:type="ns9:ServiceOrderItemValue">
                        <ns4:action>provide</ns4:action>
                        <ns5:service xsi:type="ns21:ServiceValue">
                            <ns3:key xsi:type="ns21:ServiceKey">
                                <ns3:type>Service</ns3:type>
                                <ns3:primaryKey>
                                    <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">b6fa4a0b-dd68-4bfa-aad0-9c596a686807</primaryKey>
                                </ns3:primaryKey>
                            </ns3:key>
                            <ns6:describedBy xsi:type="ns7:ArrayOfCharacteristicValue">
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>imsi</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">311180500001111</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>subscriberExternalId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">*********</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>accountType</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">I</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>isActiveArmorBasic</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>videoStreamingQuality</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">HD</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>billingAccountNumber</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">187071</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>ipme</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>lastNameFirstName</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">Mahesh Yadav</ns7:values>
                                </ns6:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>acctStatus</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>wiFiCalling</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">false</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>downloadSpeed</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>effectiveDate</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">20210301000000, 20230717000000, 20250717000000, 20000717000000</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>orderCreationDate</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">2024-05-16T09:35:22.742Z</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>networkAccessMode</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">0</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>terminatingClassOfService</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">2</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>subscriptionId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">DCAC1CEA74EC40369F5A095A7072EB1B</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>speechTelephonyAuthorization</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>shortMessageOriginationActivation</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>dcap</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">${#Project#dcaps}</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>cycleCode</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">3333</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>shortMessageTerminationActivation</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>realTimeText</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>N11Calls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>iccid</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1234567891</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>priority</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">2</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>carrierId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>customerType</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">R</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>shortMessageOriginationAuthorization</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>cycleCloseDay</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1111</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>adminSMS</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>emlppActive</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">0</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>rateplan</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">${#Project#rateplan}</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>988Calls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>uploadSpeed</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>maxPlanTechnology</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">68798798789</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>shortMessageTerminationAuthorization</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>externalOfferId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">434323086, 89166695, 89166235, 913311709</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>resubmitMode</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">failed</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>paymentType</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">PO</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>mms</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>operator</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">BSSe</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>imeiType</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">P9</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>customerId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">test</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>starCalls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>voiceMail</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>deviceOperatingSystem</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">Android</ns7:values>
                                </ns6:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>deviceSubCategory</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">Smartphone</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>payBills</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>subscriberProfile</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">LTEUSIM</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>sms</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">false</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>911Calls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>roamingCCSOfferId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">7891011</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>eid</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">9876543210</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>611Calls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>visualVoiceMail</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">false</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>ir94VideoCalls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>shortCodeCalls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>411Calls</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>orderSubmissionDate</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">2024-05-16T09:35:22.088Z</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>msisdn</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">${#Project#MSISDN}</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>offerLevel</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">S, A, B, Z</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>speechTelephonyActivation</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">0</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>discoveryServer</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">sq-carrier.apple.com</ns7:values>
                                </ns6:item>
<!--                                <ns6:item xsi:type="ns7:CharacteristicValue">-->
<!--                                    <ns6:characteristic>roamingRestriction</ns6:characteristic>-->
<!--                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">NATL</ns7:values>-->
<!--                                </ns6:item>-->
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>roamingRestriction</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">INTL</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>smsTerminatingClassOfService</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">SADMN</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>smsOriginatingClassOfService</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">SADMN</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>imei</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">350252710000010</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>timeZone</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">EDT</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>callForward</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>callForwardBusy</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>callForwardNoAnwser</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>CallForwardNotReach</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>callerId</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>assignedDNN</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">nrfwbb</ns7:values>
                                </ns6:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>accessPointNames</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">nrfwbb, nrfwbb2, nrfwbb-c, nrfwbb-b</ns7:values>
                                </ns6:item>
                                <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>acwbdpolicy</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">095051</ns7:values>
                                </ns6:item>
                                 <ns6:item xsi:type="ns7:CharacteristicValue">
                                    <ns6:characteristic>isHotspot</ns6:characteristic>
                                    <ns7:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">true</ns7:values>
                                </ns6:item>
                            </ns6:describedBy>
                            <ns21:serviceType>Mobility</ns21:serviceType>
                            <ns21:activationTargets>
                                <ns7:item>
                                    <ns7:id>Activation</ns7:id>
                                </ns7:item>
                            </ns21:activationTargets>
                        </ns5:service>
                        <ns9:subAction></ns9:subAction>
                    </ns5:item>
                </ns5:serviceOrderItems>
<!--                <dox-om:maxResponseWaitTime xmlns:dox-om="http://amdocs/core/ossj-OrderManagement/dat/3">420</dox-om:maxResponseWaitTime>-->
                <ns9:operator>BSSe</ns9:operator>
                <ns9:resubmitMode>failed</ns9:resubmitMode>
            </ns5:requestValue>
        </ns5:createAndStartRequestByValueRequest>
    </soapenv:Body>
</soapenv:Envelope>