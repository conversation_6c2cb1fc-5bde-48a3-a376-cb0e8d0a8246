<!-- <PERSON><PERSON>, MobileStation - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	targetNamespace="http://schemas.ericsson.com/ma/EIR/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<!-- CreateMobileStation MOId: imei, svn (optional) MOType: CreateMobileStation@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="imei" type="imeiType" />
	<xs:element name="svn" type="svnType" />
	<xs:element name="CreateMobileStation">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imei" type="imeiType" />
				<xs:element name="svn" type="svnType" default="0F" minOccurs="0" />
				<xs:element name="lockedImsi" type="imsiType" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imei">
			<xs:selector xpath="./x:imei" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imei" refer="key_imei">
			<xs:selector xpath="." />
			<xs:field xpath="@imei" />
		</xs:keyref>
	</xs:element>
	<!-- DeleteMobileStation MOId: imei, svn (optional) MOType: DeleteMobileStation@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetMobileStation MOId: imei, svn (optional) MOType: MobileStation@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetResponseMobileStation MOId: imei, svn (optional) MOType: MobileStation@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="GetResponseMobileStation">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imei" type="imeiType" />
				<xs:element name="svn" type="svnType" minOccurs="0" />
				<xs:element name="lockedImsi" type="imsiType" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
