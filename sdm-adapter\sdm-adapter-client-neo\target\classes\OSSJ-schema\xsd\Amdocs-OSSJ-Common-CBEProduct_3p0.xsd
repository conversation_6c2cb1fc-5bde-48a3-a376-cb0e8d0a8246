<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:amdocs-product="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:amdocs-customer="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:cbecustomer-v1-5="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5" targetNamespace="http://amdocs/core/ossj-Common-CBEProduct/dat/3" elementFormDefault="qualified">
	<xs:import namespace="http://amdocs/core/ossj-Common-CBECustomer/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBECustomer_3p0.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5" schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5" schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECustomer/v1-5" schemaLocation="OSSJ-Common-CBECustomer-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5" schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common/dat/3" schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>
	<xs:annotation>
		<xs:documentation>This schema was updated: 26 October 2011</xs:documentation>
	</xs:annotation>
	<xs:element name="product_State" type="xs:string" substitutionGroup="cbeproduct-v1-5:baseProductState_Product">
		<xs:annotation>
			<xs:documentation>The state of the product. Defines whether the product can be assigned to customers or not.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:element name="ProductKey" type="amdocs-product:ProductKey"/>
	<xs:complexType name="ProductKey">
		<xs:complexContent>
			<xs:extension base="cbeproduct-v1-5:ProductKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ProductSpecificationKey" type="amdocs-product:ProductSpecificationKey">
		<xs:annotation>
			<xs:documentation>The identifier for the "base product" that this product option should be associated with.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="ProductSpecificationKey">
		<xs:complexContent>
			<xs:extension base="cbeproduct-v1-5:ProductSpecificationKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ProductSpecificationValue" type="amdocs-product:ProductSpecificationValue"/>
	<xs:complexType name="ProductSpecificationValue">
		<xs:complexContent>
			<xs:extension base="cbeproduct-v1-5:ProductSpecificationValue">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ProductRelationship" type="amdocs-product:ProductRelationship"/>
	<xs:complexType name="ProductRelationship">
		<xs:sequence>
			<xs:element name="type" type="xs:string"/>
			<xs:element name="productKey" type="amdocs-product:ProductKey"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfProductRelationship" type="amdocs-product:ArrayOfProductRelationship"/>
	<xs:complexType name="ArrayOfProductRelationship">
		<xs:sequence>
			<xs:element name="item" type="amdocs-product:ProductRelationship" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ProductValue" type="amdocs-product:ProductValue"/>
	<xs:complexType name="ProductValue">
		<xs:complexContent>
			<xs:extension base="cbeproduct-v1-5:ProductValue">
				<xs:sequence>
					<xs:element name="name" type="xs:string" minOccurs="0"/>
					<xs:element name="productCategory" type="xs:string" minOccurs="0"/>
					<xs:element name="productType" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="previousProductConfiguration" type="amdocs-product:ProductValue" minOccurs="0"/>
					<xs:element name="activationTargets" type="amdocs-co:ArrayOfActivationTargetValue" minOccurs="0"/>
					
					<xs:element name="associatedProducts" type="amdocs-product:ArrayOfProductRelationship" minOccurs="0"/>
					<xs:element name="associatedSubscribers" type="amdocs-customer:ArrayOfSubscriberKey" minOccurs="0"/>
					
					<xs:element name="associatedResources" type="cberesource-v1-5:ArrayOfResourceKey" minOccurs="0">
					<xs:annotation>
							<xs:documentation>The resource or resources that the product or products apply to. </xs:documentation>
						</xs:annotation>
					</xs:element>	

					<xs:element name="associatedServices" type="cbeservice-v1-5:ArrayOfServiceKey" minOccurs="0"/>
					<xs:element name="operator" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductKey" type="amdocs-product:ArrayOfProductKey"/>
	<xs:complexType name="ArrayOfProductKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-product:ProductKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfProductSpecificationKey" type="amdocs-product:ArrayOfProductSpecificationKey"/>
	<xs:complexType name="ArrayOfProductSpecificationKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-product:ProductSpecificationKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfProductSpecificationValue" type="amdocs-product:ArrayOfProductSpecificationValue"/>
	<xs:complexType name="ArrayOfProductSpecificationValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-product:ProductSpecificationValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfProductValue" type="amdocs-product:ArrayOfProductValue"/>
	<xs:complexType name="ArrayOfProductValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-product:ProductValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
