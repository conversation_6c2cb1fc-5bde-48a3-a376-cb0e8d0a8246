<!-- Customer Adaptation, RoamingServiceArea-->
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:ns="http://schemas.ericsson.com/pg/hlr/13.5/"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
           targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/"
           elementFormDefault="qualified" attributeFormDefault="unqualified"
           jaxb:version="2.0">
    <xs:include schemaLocation="./types/rp_types.xsd"/>
    <!-- CAI3G MOId type definitions. -->
    <xs:element name="rsa" type="rsaType"/>
    <xs:element name="raid" type="raidType"/>
    <xs:element name="frontendid" type="hlrfeidType"/>
    <!-- SetRoamingServiceArea MOId: rsa,raid MOType: RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/ -->
    <xs:element name="SetRoamingServiceArea">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="rsa" type="rsaType"/>
                <xs:element name="raid" type="raidType"/>
                <xs:element name="rsp" type="rspType" minOccurs="0"/>
                <xs:element name="srr" type="srrType" minOccurs="0"/>
                <xs:element name="rsip" type="rsipType" minOccurs="0"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="rsa" type="rsaType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="rsaAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="raid" type="raidType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="raidAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <xs:element name="HlrRoamingServiceAreaData">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="RsaData" type="rsaDataType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="rsaDataType">
        <xs:sequence>
            <xs:element name ="rsa" type="rsaType"/>
            <xs:element name ="raidData" type="raidDataType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="raidDataType">
        <xs:sequence>
            <xs:element name="raid" type="raidType" minOccurs="0"/>
            <xs:element name="rsp" type="rspType" minOccurs="0"/>
            <xs:element name="srr" type="srrType" minOccurs="0"/>
            <xs:element name="rsip" type="rsipType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
