<!-- HssEsmNetworkDomain Adaptation, HssEsmNetworkDomain example, HssEsmNetworkDomain@http://schemas.ericsson.com/ma/HSS/ -->
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
             targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
    <jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
        <!-- disable wrapper style generation -->
        <jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
    </jaxws:bindings>

    <service name="Provisioning">
        <port name="HssEsmNetworkDomainPort" binding="cai3g:HssEsmNetworkDomainBinding">
            <soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
        </port>
    </service>

    <binding name="HssEsmNetworkDomainBinding" type="cai3g:HssEsmNetworkDomainPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="Get">
            <soap:operation soapAction="CAI3G#Get" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
                </soap:header>
            </output>
            <fault name="Cai3gFault">
                <soap:fault name="Cai3gFault" use="literal"/>
            </fault>
        </operation>

        <operation name="Create">
            <soap:operation soapAction="CAI3G#Create" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
                </soap:header>
            </output>
            <fault name="Cai3gFault">
                <soap:fault name="Cai3gFault" use="literal"/>
            </fault>
        </operation>

        <operation name="Delete">
            <soap:operation soapAction="CAI3G#Delete" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
                </soap:header>
            </output>
            <fault name="Cai3gFault">
                <soap:fault name="Cai3gFault" use="literal"/>
            </fault>
        </operation>

    </binding>

    <portType name="HssEsmNetworkDomainPortType">
        <operation name="Get">
            <input message="cai3g:GetRequest"/>
            <output message="cai3g:GetResponse"/>
            <fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
        </operation>

        <operation name="Create">
            <input message="cai3g:CreateRequest"/>
            <output message="cai3g:CreateResponse"/>
            <fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
        </operation>

        <operation name="Delete">
            <input message="cai3g:DeleteRequest"/>
            <output message="cai3g:DeleteResponse"/>
            <fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
        </operation>

    </portType>

    <message name="HeadInfo">
        <part name="sessionId" element="cai3g:SessionId"/>
        <part name="transactionId" element="cai3g:TransactionId"/>
        <part name="sequenceId" element="cai3g:SequenceId"/>
    </message>

    <message name="GetRequest">
        <part name="parameters" element="cai3g:Get"/>
    </message>
    <message name="GetResponse">
        <part name="parameters" element="cai3g:GetResponse"/>
    </message>
    <message name="CreateRequest">
        <part name="parameters" element="cai3g:Create"/>
    </message>
    <message name="CreateResponse">
        <part name="parameters" element="cai3g:CreateResponse"/>
    </message>
    <message name="DeleteRequest">
        <part name="parameters" element="cai3g:Delete"/>
    </message>
    <message name="DeleteResponse">
        <part name="parameters" element="cai3g:DeleteResponse"/>
    </message>
    <message name="Cai3gFault">
        <part name="parameters" element="cai3g:Cai3gFault"/>
    </message>


    <types>
        <xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   xmlns:esmnd="http://schemas.ericsson.com/ma/HSS/"
                   xmlns:pg="http://schemas.ericsson.com/pg/1.0"
                   xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified"
                   attributeFormDefault="unqualified">
            <xs:include schemaLocation="../../../schemas/LTE_EPC/RoamingProfile/cai3g1.2_provisioning_without_cai3gfault.xsd"/>
            <xs:import namespace="http://schemas.ericsson.com/pg/1.0"
                       schemaLocation="../../../schemas/LTE_EPC/RoamingProfile/PGFault.xsd" />
            <xs:import namespace="http://schemas.ericsson.com/ma/HSS/"
                       schemaLocation="../../../schemas/LTE_EPC/RoamingProfile/HssEsmNetworkDomain.xsd"/>
            <xs:element name="Get">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOType" type="xs:string"
                                    fixed="HssEsmNetworkDomain@http://schemas.ericsson.com/ma/HSS/"/>
                        <xs:element name="MOId">
                            <xs:complexType>
                                <xs:sequence>
                                        <xs:element ref="esmnd:EsmNetworkDomainId"/>
                                        <xs:element ref="esmnd:frontendid" minOccurs="0" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <!-- GetNumberSeriesAnalysis
            -->
            <xs:element name="GetResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOAttributes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="esmnd:HssEsmNetworkDomainData" minOccurs="0"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>

            <xs:element name="Cai3gFault">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="faultcode" type="xs:integer"/>
                        <xs:element name="faultreason">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="reasonText" type="xs:string" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="faultrole" type="xs:string"/>
                        <xs:element name="details" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="pg:PGFault"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>

            <xs:element name="Create">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOType" type="xs:string"
                                    fixed="HssEsmNetworkDomain@http://schemas.ericsson.com/ma/HSS/"/>
                        <xs:element name="MOId">
                            <xs:complexType>
                                <xs:sequence>
                                        <xs:element ref="esmnd:EsmNetworkDomainId"/>
                                        <xs:element ref="esmnd:EsmRaid"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="MOAttributes" minOccurs="1">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="esmnd:CreateHssEsmNetworkDomain"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>

            <xs:element name="Delete">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOType" type="xs:string"
                                    fixed="HssEsmNetworkDomain@http://schemas.ericsson.com/ma/HSS/"/>
                        <xs:element name="MOId">
                            <xs:complexType>
                                <xs:sequence>
                                        <xs:element ref="esmnd:EsmNetworkDomainId"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="MOAttributes" minOccurs="1">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="esmnd:DeleteHssEsmNetworkDomain"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:schema>
    </types>

</definitions>
