<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="getAccumulators">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="promotionNotificationFlag" type="promotionNotificationFlagType"/>
<xs:element minOccurs="0" name="firstIVRCallSetFlag" type="firstIVRCallSetFlagType"/>
<xs:element minOccurs="0" name="accountActivationFlag" type="accountActivationFlagType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accumulatorSelection">
<xs:complexType>
<xs:sequence>
<xs:element name="accumulatorIDFirst" type="accumulatorIDFirstType"/>
<xs:element minOccurs="0" name="accumulatorIDLast" type="accumulatorIDLastType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="getAccumulatorsResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accumulatorInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="accumulatorID" type="accumulatorIDType"/>
<xs:element minOccurs="0" name="accumulatorValue" type="accumulatorValueType"/>
<xs:element minOccurs="0" name="accumulatorStartDate" type="accumulatorStartDateType"/>
<xs:element minOccurs="0" name="accumulatorEndDate" type="accumulatorEndDateType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="temporaryBlockedFlag" type="temporaryBlockedFlagType"/>
<xs:element minOccurs="0" name="accountFlagsAfter">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="accountFlagsBefore">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="setAccumulators">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" name="accumulatorUpdateInformation">
<xs:complexType>
<xs:sequence>
<xs:element name="accumulatorID" type="accumulatorIDType"/>
<xs:choice>
<xs:element minOccurs="0" name="accumulatorValueRelative" type="accumulatorValueRelativeType"/>
<xs:element minOccurs="0" name="accumulatorValueAbsolute" type="accumulatorValueAbsoluteType"/>
</xs:choice>
<xs:element minOccurs="0" name="accumulatorStartDate" type="accumulatorStartDateType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setAccumulatorsResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="deleteAccumulators">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element maxOccurs="unbounded" minOccurs="1" name="accumulatorIdentifier">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="accumulatorID" type="accumulatorIDType"/>
<xs:element minOccurs="0" name="accumulatorEndDate" type="accumulatorEndDateType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="deleteAccumulatorsResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="1" name="accumulatorInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="accumulatorID" type="accumulatorIDType"/>
<xs:element minOccurs="0" name="accumulatorValue" type="accumulatorValueType"/>
<xs:element minOccurs="0" name="accumulatorStartDate" type="accumulatorStartDateType"/>
<xs:element minOccurs="0" name="accumulatorEndDate" type="accumulatorEndDateType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originHostNameType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTimeStampType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="subscriberNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionNotificationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="firstIVRCallSetFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accountActivationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accumulatorIDFirstType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDLastType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="reservationCorrelationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="languageIDCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorValueType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-2147483648"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="accumulatorEndDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="temporaryBlockedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="activationStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="negativeBarringStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorValueRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-2147483648"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorValueAbsoluteType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-2147483648"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="chargingRequestInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="chargingType" type="chargingTypeType"/>
<xs:element minOccurs="0" name="chargingIndicator" type="chargingIndicatorType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="chargingResultInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="currency1" type="xs:string"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
<xs:element minOccurs="0" name="currency2" type="xs:string"/>
<xs:element minOccurs="0" name="chargingResultCode" type="chargingResultCodeType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
<xs:element minOccurs="0" name="chargingResultInformationService" type="chargingResultInformationServiceType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="costType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingResultCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="chargingResultInformationServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
</xs:sequence>
</xs:complexType>
</xs:schema>
