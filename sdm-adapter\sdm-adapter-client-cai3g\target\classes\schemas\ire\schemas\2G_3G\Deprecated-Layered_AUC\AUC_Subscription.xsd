<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/auc/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/auc/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/auc/13.5/">
<xs:include schemaLocation="types/auc_types.xsd"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="CreateSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="eki" type="ekiType"/>
<xs:element name="kind" type="kindType"/>
<xs:choice minOccurs="0">
<xs:element name="a3a8ind" type="a3a8indType"/>
<xs:element name="fsetind" type="fsetindType"/>
</xs:choice>
<xs:element minOccurs="0" name="a4ind" type="a4indType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="SubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="akatype" type="akatypeType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="eki" type="ekiType"/>
<xs:element name="kind" type="kindType"/>
<xs:choice>
<xs:element name="a3a8ind" type="a3a8indType"/>
<xs:element name="fsetind" type="fsetindType"/>
</xs:choice>
<xs:element name="a4ind" type="a4indType"/>
<xs:element minOccurs="0" name="akaalgind" type="akaalgindType"/>
<xs:element minOccurs="0" name="amf" type="amfType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="SetSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="amf" type="amfType"/>
<xs:element name="fsetind" type="fsetindType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="DeleteSubscription">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
