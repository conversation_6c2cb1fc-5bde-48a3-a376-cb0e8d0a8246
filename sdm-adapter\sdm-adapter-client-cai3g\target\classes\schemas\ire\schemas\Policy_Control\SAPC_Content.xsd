<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/"
	xmlns:x="http://schemas.ericsson.com/ma/SAPC/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	targetNamespace="http://schemas.ericsson.com/ma/SAPC/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="types/sapcla_types.xsd" />
	<xs:element name="pcContentName" type="pcContentNameType" />
	<xs:element name="CreateContent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcContentName" type="pcContentNameType" />
				<xs:element name="pcPccRuleName" type="pcPccRuleNameType" minOccurs="0" />
				<xs:element name="pcPccRuleId" type="pcPccRuleIdType"
					minOccurs="0" />
				<xs:element name="pcPccRuleType" type="pcPccRuleTypeType"
					minOccurs="0" />
				<xs:element name="pcDescription" type="pcDescriptionType" minOccurs="0" />
				<xs:element name="pcAdcRuleName" type="pcAdcRuleNameType" minOccurs="0" />
				<xs:element name="pcAdcRuleType" type="pcAdcRuleTypeType" minOccurs="0" />
				<xs:element name="pcTdfAppId" type="pcTdfAppIdType"
					minOccurs="0" />
				<xs:element name="pcPrecedence" type="pcPrecedenceType"
					minOccurs="0" />
				<xs:element name="pcFlows" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcFlowName" type="pcFlowNameType"/>
							<xs:element name="pcSourceIpAddr" type="pcSourceIpAddrType" minOccurs="0" />
							<xs:element name="pcSourcePort" type="pcSourcePortType" minOccurs="0" />
							<xs:element name="pcDestIpAddr" type="pcDestIpAddrType" minOccurs="0" />
							<xs:element name="pcDestPort" type="pcDestPortType" minOccurs="0" />
							<xs:element name="pcProtocol" type="pcProtocolType" />
							<xs:element name="pcDirection" type="pcDirectionType" />
						</xs:sequence>
						<xs:attribute name="pcFlowName" type="pcFlowNameType" use="required" >
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcFlowNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcFlowName">
						<xs:selector xpath="." />
						<xs:field xpath="@pcFlowName" />
					</xs:key>
					<xs:keyref name="keyref_create_pcFlowName" refer="key_create_pcFlowName">
						<xs:selector xpath="./x:pcFlowName" />
						<xs:field xpath="." />
					</xs:keyref>	
			    </xs:element>
				<xs:element name="pcFlowStatus" type="pcFlowStatusType" minOccurs="0" />
				<xs:element name="pcDefQosFlowIndication" type="pcDefQosFlowIndicationType" minOccurs="0" />
				<xs:element name="pcStaticQualification" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentQosProfileId" type="pcContentQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentChargingProfileId" type="pcContentChargingProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentAdcRedirectProfileId" type="pcContentAdcRedirectProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentAdcMuteNotification" type="pcContentAdcMuteNotificationType"
								minOccurs="0" />
							<xs:element name="pcContentMonitoringKey" type="pcContentMonitoringKeyType"
								minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcContentName" type="pcContentNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcContentNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_pcContentName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcContentName" />
		</xs:key>
		<xs:keyref name="keyref_create_pcContentName" refer="key_create_pcContentName">
			<xs:selector xpath="./x:pcContentName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

	<xs:element name="GetResponseContent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcContentName" type="pcContentNameType" />
				<xs:element name="pcPccRuleName" type="pcPccRuleNameType" minOccurs="0" />
				<xs:element name="pcPccRuleId" type="pcPccRuleIdType"
					minOccurs="0" />
				<xs:element name="pcPccRuleType" type="pcPccRuleTypeType"
					minOccurs="0" />
				<xs:element name="pcDescription" type="pcDescriptionType" minOccurs="0" />
				<xs:element name="pcAdcRuleName" type="pcAdcRuleNameType"
							minOccurs="0" />
				<xs:element name="pcAdcRuleType" type="pcAdcRuleTypeType"
							minOccurs="0" />
				<xs:element name="pcTdfAppId" type="pcTdfAppIdType"
					minOccurs="0" />
				<xs:element name="pcPrecedence" type="pcPrecedenceType"
					minOccurs="0" />
				<xs:element name="pcFlows"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcFlowName" type="pcFlowNameType"/>
							<xs:element name="pcSourceIpAddr" type="pcSourceIpAddrType" minOccurs="0"/>
							<xs:element name="pcSourcePort" type="pcSourcePortType" minOccurs="0"/>
							<xs:element name="pcDestIpAddr" type="pcDestIpAddrType" minOccurs="0" />
							<xs:element name="pcDestPort" type="pcDestPortType" minOccurs="0" />
							<xs:element name="pcProtocol" type="pcProtocolType"/>
							<xs:element name="pcDirection" type="pcDirectionType"/>
						</xs:sequence>
						<xs:attribute name="pcFlowName" type="pcFlowNameType" use="required" >
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcFlowNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>	
				</xs:element>
				<xs:element name="pcFlowStatus" type="pcFlowStatusType"
							minOccurs="0" />
				<xs:element name="pcDefQosFlowIndication" type="pcDefQosFlowIndicationType"
							minOccurs="0" />
				<xs:element name="pcStaticQualification" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentQosProfileId" type="pcContentQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentChargingProfileId" type="pcContentChargingProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentAdcRedirectProfileId" type="pcContentAdcRedirectProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentAdcMuteNotification" type="pcContentAdcMuteNotificationType"
								minOccurs="0" />
							<xs:element name="pcContentMonitoringKey" type="pcContentMonitoringKeyType"
								minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcContentName" type="pcContentNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcContentNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<xs:element name="SetContent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcContentName" type="pcContentNameType" />
				<xs:element name="pcPccRuleName" type="pcPccRuleNameType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcPccRuleId" type="pcPccRuleIdType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcPccRuleType" type="pcPccRuleTypeType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcDescription" type="pcDescriptionType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcAdcRuleName" type="pcAdcRuleNameType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcAdcRuleType" type="pcAdcRuleTypeType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcTdfAppId" type="pcTdfAppIdType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcPrecedence" type="pcPrecedenceType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcFlows"
					nillable="true" minOccurs="0" maxOccurs="unbounded" >
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcFlowName" type="pcFlowNameType" nillable="true" />
							<xs:element name="pcSourceIpAddr" type="pcSourceIpAddrType" nillable="true" minOccurs="0" />
							<xs:element name="pcSourcePort" type="pcSourcePortType" nillable="true" minOccurs="0" />
							<xs:element name="pcDestIpAddr" type="pcDestIpAddrType" nillable="true" minOccurs="0" />
							<xs:element name="pcDestPort" type="pcDestPortType" nillable="true" minOccurs="0" />
							<xs:element name="pcProtocol" type="pcProtocolType" nillable="true" />
							<xs:element name="pcDirection" type="pcDirectionType" nillable="true" />
						</xs:sequence>
						<xs:attribute name="pcFlowName" type="pcFlowNameType" use="required" >
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcFlowNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcFlowName">
						<xs:selector xpath="." />
						<xs:field xpath="@pcFlowName" />
					</xs:key>
					<xs:keyref name="keyref_set_pcFlowName" refer="key_set_pcFlowName">
						<xs:selector xpath="./x:pcFlowName" />
						<xs:field xpath="." />
					</xs:keyref>	
				</xs:element>
				<xs:element name="pcFlowStatus" type="pcFlowStatusType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcDefQosFlowIndication" type="pcDefQosFlowIndicationType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcStaticQualification" nillable="true"
					minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentQosProfileId" type="pcContentQosProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcContentChargingProfileId" type="pcContentChargingProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcContentAdcRedirectProfileId" type="pcContentAdcRedirectProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcContentAdcMuteNotification" type="pcContentAdcMuteNotificationType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcContentMonitoringKey" type="pcContentMonitoringKeyType"
								nillable="true" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcContentName" type="pcContentNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcContentNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_pcContentName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcContentName" />
		</xs:key>
		<xs:keyref name="keyref_set_pcContentName" refer="key_set_pcContentName">
			<xs:selector xpath="./x:pcContentName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

</xs:schema>
