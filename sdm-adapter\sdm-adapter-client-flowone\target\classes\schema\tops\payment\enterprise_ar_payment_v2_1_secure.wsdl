<?xml version="1.0" encoding="UTF-8"?>
<!-- 
Build_Label: REL500
QualityCenter_MR#: 00348796 
Build_Date: 2016-12-07-14:56:28
--><wsdl:definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:enterprise_ar_payment="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_0"
                  xmlns:enterprise_ar_payment_xsd="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types"
                  xmlns:enterprise_messageheader_xsd="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types"
                  xmlns:enterprise_ar_payment_messages="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/messages"
                  name="enterprise_ar_payment"
                  targetNamespace="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_0">
	  <wsdl:documentation>
   		Payment Service will provides following functionalities for consumers.
   			- Make immediate payments
   			- Retrieves payment transaction details
   			- Retrieves payment transaction history
   	</wsdl:documentation>
	  <wsdl:types>
		    <xsd:schema elementFormDefault="qualified" attributeFormDefault="unqualified"
                  version="v2_1"
                  targetNamespace="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_0">
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types"
                     schemaLocation="enterprise_ar_payment_types_v2_1.xsd"/>
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types"
                     schemaLocation="enterprise_messageheader_types_v1_0.xsd"/>
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/messages"
                     schemaLocation="enterprise_ar_payment_messages_v2_1.xsd"/>
		    </xsd:schema>
	  </wsdl:types>
	  <wsdl:message name="PaymentExceptionMsg">
		    <wsdl:documentation>Payment exception extension based on USCC fault definition.</wsdl:documentation>
		    <wsdl:part name="PaymentException" element="enterprise_ar_payment_xsd:PaymentException">
			      <wsdl:documentation>Service Exception used for Payment.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_makePayment_RequestMsg">
		    <wsdl:part name="parameters" element="enterprise_ar_payment_messages:makePayment_Request">
			      <wsdl:documentation>Request message for operation makePayment.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_makePayment_ResponseMsg">
		    <wsdl:part name="result" element="enterprise_ar_payment_messages:makePayment_Response">
			      <wsdl:documentation>Response message (makePayment_Response) for operation makePayment.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_getPaymentTransactionDetails_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_ar_payment_messages:getPaymentTransactionDetails_Request">
			      <wsdl:documentation>Request message for operation getPaymentTransactionDetails.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_getPaymentTransactionDetails_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_ar_payment_messages:getPaymentTransactionDetails_Response">
			      <wsdl:documentation>Response message (getPaymentTransactionDetails_Response) for operation getPaymentTransactionDetails.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_getPaymentTransactionHistory_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_ar_payment_messages:getPaymentTransactionHistory_Request">
			      <wsdl:documentation>Request message for operation getPaymentTransactionHistory.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="Payment_v2_0_getPaymentTransactionHistory_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_ar_payment_messages:getPaymentTransactionHistory_Response">
			      <wsdl:documentation>Response message (getPaymentTransactionHistory_Response) for operation getPaymentTransactionHistory.</wsdl:documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:portType name="Payment_v2_0_Port">
		    <wsdl:documentation>Payment service is enterprise level service for payment related operations.</wsdl:documentation>
		    <wsdl:operation name="makePayment">
			      <wsdl:documentation>Operation to make immediate payments.</wsdl:documentation>
			      <wsdl:input name="makePaymentRequest"
                     message="enterprise_ar_payment:Payment_v2_0_makePayment_RequestMsg"/>
			      <wsdl:output name="makePaymentResponse"
                      message="enterprise_ar_payment:Payment_v2_0_makePayment_ResponseMsg"/>
			      <wsdl:fault name="PaymentExceptionMsg" message="enterprise_ar_payment:PaymentExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="getPaymentTransactionDetails">
			      <wsdl:documentation>Retrieves payment transaction details.</wsdl:documentation>
			      <wsdl:input name="getPaymentTransactionDetailsRequest"
                     message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionDetails_RequestMsg"/>
			      <wsdl:output name="getPaymentTransactionDetailsResponse"
                      message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionDetails_ResponseMsg"/>
			      <wsdl:fault name="PaymentExceptionMsg" message="enterprise_ar_payment:PaymentExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="getPaymentTransactionHistory">
			      <wsdl:documentation>Retrieves payment transaction history.</wsdl:documentation>
			      <wsdl:input name="getPaymentTransactionHistoryRequest"
                     message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionHistory_RequestMsg"/>
			      <wsdl:output name="getPaymentTransactionHistoryResponse"
                      message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionHistory_ResponseMsg"/>
			      <wsdl:fault name="PaymentExceptionMsg" message="enterprise_ar_payment:PaymentExceptionMsg"/>
		    </wsdl:operation>
	  </wsdl:portType>
	  <wsdl:binding name="Payment_v2_0_Binding" type="enterprise_ar_payment:Payment_v2_0_Port">
		    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		    <wsdl:operation name="makePayment">
			      <wsdl:documentation>Operation to make immediate payments.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_1#Payment_v2_0_Binding.makePayment"
                         style="document"/>
			      <wsdl:input name="makePaymentRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_makePayment_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="makePaymentResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_makePayment_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="PaymentExceptionMsg">
				        <soap:fault name="PaymentExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="getPaymentTransactionDetails">
			      <wsdl:documentation>Retrieves payment transaction details.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_1#Payment_v2_0_Binding.getPaymentTransactionDetails"
                         style="document"/>
			      <wsdl:input name="getPaymentTransactionDetailsRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionDetails_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="getPaymentTransactionDetailsResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionDetails_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="PaymentExceptionMsg">
				        <soap:fault name="PaymentExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="getPaymentTransactionHistory">
			      <wsdl:documentation>Retrieves payment transaction history.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_1#Payment_v2_0_Binding.getPaymentTransactionHistory"
                         style="document"/>
			      <wsdl:input name="getPaymentTransactionHistoryRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionHistory_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="getPaymentTransactionHistoryResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_ar_payment:Payment_v2_0_getPaymentTransactionHistory_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="PaymentExceptionMsg">
				        <soap:fault name="PaymentExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
	  </wsdl:binding>
	  <wsdl:service name="Payment_v2_0">
		    <wsdl:documentation>Service to make immediate payments, get payment transaction details and get payment transaction history.</wsdl:documentation>
		    <wsdl:port name="Payment_v2_0_Port" binding="enterprise_ar_payment:Payment_v2_0_Binding">
			      <soap:address location="http://localhost:9080/enterprise/ar/payment/v2_1"/>
		       <wsp:PolicyReference xmlns:xs="http://www.w3.org/2001/XMLSchema"
                              xmlns:fn="http://www.w3.org/2005/xpath-functions"
                              xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                              URI="Payment_v2_1_Policy"/>
      </wsdl:port>
	  </wsdl:service>
   <wsp:UsingPolicy xmlns:xs="http://www.w3.org/2001/XMLSchema"
                    xmlns:fn="http://www.w3.org/2005/xpath-functions"
                    xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                    xmlns:wssutil="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                    wssutil:Required="true"/>
   <wsp:Policy xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
               xmlns:xs="http://www.w3.org/2001/XMLSchema"
               xmlns:fn="http://www.w3.org/2005/xpath-functions"
               xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
               xmlns:wssutil="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
               wssutil:Id="Payment_v2_1_Policy">
      <wsp:ExactlyOne>
         <wsp:All>
            <sp:SupportingTokens xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
               <sp:X509Token sp:IncludeToken="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient">
                  <wsp:Policy>
                     <sp:WssX509V3Token11/>
                  </wsp:Policy>
               </sp:X509Token>
            </sp:SupportingTokens>
         </wsp:All>
      </wsp:ExactlyOne>
   </wsp:Policy>
</wsdl:definitions>