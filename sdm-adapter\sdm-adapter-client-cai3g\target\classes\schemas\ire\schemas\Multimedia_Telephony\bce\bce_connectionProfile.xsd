<!-- BCE Connection Profile Unit 2014-12-08 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd"/>
	<xs:element name="connectionProfileId" type="ConnectionProfileId"/>
	<!-- Create Connection Profile MOId: serviceProviderId, connectionProfileId, companyId,   MOType: connectionProfile@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createConnectionProfile">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Connection Profile
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" minOccurs="0"/>
				<xs:element name="connectionProfileId" type="ConnectionProfileId"/>
				<xs:element name="companyId" type="CompanyId" minOccurs="0"/>
				<!-- Connection Profile Attributes -->
				<xs:element name="dbSchema" type="NameType" minOccurs="0"/>
				<xs:element name="directoryType" type="DirectoryType" minOccurs="0"/>
				<xs:element name="searchDomain" type="NameType" minOccurs="0"/>
				<xs:element name="url" type="UrlType" minOccurs="0"/>
				<xs:element name="userName" type="UserNameType" minOccurs="0"/>
				<xs:element name="password" type="PasswordType" minOccurs="0"/>
				<xs:element name="vendor" type="NameType" minOccurs="0"/>
				<xs:element name="schema" type="UpDirectorySchema" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="connectionProfileId" type="ConnectionProfileId" use="required"/>
		</xs:complexType>
		<xs:key name="connectionProfileIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@connectionProfileId"/>
		</xs:key>
		<xs:keyref name="connectionProfileIdKeyRef_Create" refer="connectionProfileIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="connectionProfileId"/>
		</xs:keyref>
	</xs:element>
	<!-- Set Connection Profile MOId: serviceProviderId, connectionProfileId, companyId,   MOType: connectionProfile@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setConnectionProfile">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Connection Profile
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="connectionProfileId" type="ConnectionProfileId"/>
				<!-- Connection Profile Attributes -->
				<xs:element name="dbSchema" type="NameType" minOccurs="0"/>
				<xs:element name="directoryType" type="DirectoryType" minOccurs="0"/>
				<xs:element name="searchDomain" type="NameType" minOccurs="0"/>
				<xs:element name="url" type="UrlType" minOccurs="0"/>
				<xs:element name="userName" type="UserNameType" minOccurs="0"/>
				<xs:element name="password" type="PasswordType" minOccurs="0"/>
				<xs:element name="vendor" type="NameType" minOccurs="0"/>
				<xs:element name="schema" type="UpDirectorySchema" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="connectionProfileId" type="ConnectionProfileId" use="required"/>
		</xs:complexType>
	</xs:element>
	<!-- get Connection Profile MOId: serviceProviderId, connectionProfileId, companyId,   MOType: connectionProfile@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get Connection Profile Response -->
	<xs:element name="getConnectionProfileResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Connection Profile
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="connectionProfileId" type="ConnectionProfileId"/>
				<!-- Connection Profile Attributes -->
				<xs:element name="dbSchema" type="NameType" minOccurs="0"/>
				<xs:element name="directoryType" type="DirectoryType" minOccurs="0"/>
				<xs:element name="searchDomain" type="NameType" minOccurs="0"/>
				<xs:element name="url" type="UrlType" minOccurs="0"/>
				<xs:element name="userName" type="UserNameType" minOccurs="0"/>
				<xs:element name="password" type="PasswordType" minOccurs="0"/>
				<xs:element name="vendor" type="NameType" minOccurs="0"/>
				<xs:element name="schema" type="UpDirectorySchema" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="connectionProfileId" type="ConnectionProfileId" use="required"/>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="deleteConnectionProfile">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting BCE Company (single or
				recursively)
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" minOccurs="0"/>
				<xs:element name="companyId" type="CompanyId" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- delete Connection Profile MOId: serviceProviderId, connectionProfileId, companyId,   MOType: connectionProfile@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
