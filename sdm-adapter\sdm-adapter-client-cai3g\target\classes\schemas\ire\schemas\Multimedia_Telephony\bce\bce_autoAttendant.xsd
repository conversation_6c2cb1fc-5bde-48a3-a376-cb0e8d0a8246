<!-- BCE Call BarringDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd"/>
	<xs:element name="serviceProviderId" type="ServiceProviderId"/>
	<xs:element name="companyId" type="CompanyId"/>
	<xs:element name="autoAttendantId" type="UpAutoAttendantIdType"/>
	<xs:complexType name="BceAutoAttendantType">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="autoAttendantId" type="UpAutoAttendantIdType"/>
			<!-- Auto Attendant Attributes -->
			<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
			<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
			<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
			<xs:element name="voiceMailAddress" type="GenericAddressType" minOccurs="0"/>
			<xs:element name="name" type="NameType" minOccurs="0"/>
			<xs:element name="description" type="DescriptionType" minOccurs="0"/>
			<xs:element name="aaOpen" type="UpOpenEnum" minOccurs="0"/>
			<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
			<xs:element name="incomingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
			<!-- AutoAttendant embedded objects -->
			<xs:element ref="UpAutoAttendantMenuItemType" minOccurs="0" maxOccurs="10"/>
			<xs:element name="timeOutAction" type="UpAutoAttendantMenuActionType" minOccurs="0"/>
			<xs:element name="wrongSelectionAction" type="UpAutoAttendantMenuActionType" minOccurs="0"/>
			<xs:element name="announcements" type="UpAnnouncementType" minOccurs="0" maxOccurs="unbounded"/>		</xs:sequence>
		<xs:attribute name="serviceProviderId" type="ServiceProviderId" use="required"/>
		<xs:attribute name="companyId" type="CompanyId" use="required"/>
		<xs:attribute name="autoAttendantId" type="UpAutoAttendantIdType"/>
	</xs:complexType>
	<!-- Create Auto Attendant MOId: serviceProviderId, companyId, objectId  MOType: AutoAttendant@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createAutoAttendant" type="BceAutoAttendantType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Auto Attendant
			</xs:documentation>
		</xs:annotation>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@serviceProviderId"/>
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="serviceProviderId"/>
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@companyId"/>
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="companyId"/>
		</xs:keyref>
		<xs:key name="autoAttendantIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@autoAttendantId"/>
		</xs:key>
		<xs:keyref name="autoAttendantIdKeyRef_Create" refer="autoAttendantIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="autoAttendantId"/>
		</xs:keyref>
	</xs:element>
	<!-- Set Auto Attendant MOId: serviceProviderId, companyId, objectId  MOType: AutoAttendant@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setAutoAttendant" type="BceAutoAttendantType">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Auto Attendant
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- get Auto Attendant MOId: serviceProviderId, companyId, objectId  MOType: AutoAttendant@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get Auto Attendant response -->
	<xs:element name="getAutoAttendantResponse" type="BceAutoAttendantType">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Auto Attendant
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- delete Auto Attendant MOId: serviceProviderId, companyId, objectId  MOType: AutoAttendant@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
