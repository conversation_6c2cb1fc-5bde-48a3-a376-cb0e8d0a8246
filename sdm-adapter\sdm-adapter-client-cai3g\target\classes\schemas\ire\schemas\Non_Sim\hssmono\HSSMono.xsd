<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 sp1 (http://www.altova.com) by <PERSON><PERSON><PERSON><PERSON> 
	(<PERSON><PERSON> (China) Communications Company Ltd) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns="http://schemas.ericsson.com/ema/UserProvisioning/HSS/ISM/"
	xmlns:ism="http://schemas.ericsson.com/ema/UserProvisioning/HSS/ISM/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
	targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/HSS/ISM/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:element name="subscriberId" type="subscriberIdType" />
	<xs:element name="SetISMSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="privateUser" nillable="true" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateUserId" type="privateUserIdType"
								minOccurs="0" />
							<xs:choice minOccurs="0">
								<xs:element name="userPassword" type="userPasswordType"
									minOccurs="0" />
								<xs:sequence>
									<xs:element name="userPrimaryHA1Password" type="userPrimaryHA1PasswordType"
										minOccurs="0" />
									<xs:element name="userSecondaryHA1Password" type="userSecondaryHA1PasswordType"
										minOccurs="0" />
								</xs:sequence>
							</xs:choice>
							<xs:element name="allowedAuthMechanism" type="allowedAuthMechanismType"
								minOccurs="0" maxOccurs="unbounded" />
							<xs:element name="userBarringInd" type="userBarringIndType"
								minOccurs="0" />
							<xs:element name="roamingAllowed" type="roamingAllowedType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="privateUserId" type="privateUserIdType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateUserIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_privateUserId">
						<xs:selector xpath="." />
						<xs:field xpath="@privateUserId" />
					</xs:key>
					<xs:keyref name="keyref_set_privateUserId" refer="key_set_privateUserId">
						<xs:selector xpath="." />
						<xs:field xpath="ism:privateUserId" />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="subscriberId" type="subscriberIdType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="subscriberIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_subscriberId">
			<xs:selector xpath="." />
			<xs:field xpath="@subscriberId" />
		</xs:key>
		<xs:keyref name="keyref_set_subscriberId" refer="key_set_subscriberId">
			<xs:selector xpath="." />
			<xs:field xpath="@subscriberId" />
		</xs:keyref>
	</xs:element>
	<!-- GetISMSubscription MOId: subscriberId MOType: <EMAIL>/ema/UserProvisioning/HSS/ISM -->
	<!-- getResponseISMSubscription MOId: subscriberId MOType: <EMAIL>/ema/UserProvisioning/HSS/ISM -->
	<xs:element name="getResponseISMSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="subscriberId" type="subscriberIdType" />
				<xs:element name="privateUser" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateUserId" type="privateUserIdType" />
							<xs:element name="userBarringInd" type="userBarringIndType"
								minOccurs="0" />
							<xs:element name="roamingAllowed" type="roamingAllowedType"
								minOccurs="0" />
							<xs:element name="allowedAuthMechanism" type="allowedAuthMechanismType"
								minOccurs="0" maxOccurs="unbounded" />
						</xs:sequence>
						<xs:attribute name="privateUserId" type="privateUserIdType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateUserIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="subscriberId" type="subscriberIdType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="subscriberIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="subscriberIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="privateUserIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="256" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userPasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="4" />
			<xs:maxLength value="256" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userPrimaryHA1PasswordType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userSecondaryHA1PasswordType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="allowedAuthMechanismType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Digest" />
			<xs:enumeration value="SSO" />
			<xs:enumeration value="NBA" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userBarringIndType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRUE" />
			<xs:enumeration value="FALSE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="roamingAllowedType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRUE" />
			<xs:enumeration value="FALSE" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
