<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\wsdl\wsdl.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2006-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<wsdl:definitions name="AUAI-RequestWS-v2-0"
    targetNamespace ="http://ossj.org/wsdl/OrderManagement/v1-0"
    xmlns:omWS-v1-0="http://ossj.org/wsdl/OrderManagement/v1-0"
    xmlns:om-v1-0="http://ossj.org/xml/OrderManagement/v1-0"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
   >

    <!-- Tigerstripe : WSDL Types -->
    <wsdl:types>
        <xsd:schema>
            <xsd:import namespace="http://ossj.org/xml/OrderManagement/v1-0"
                schemaLocation="../xsd/OSSJ-OrderManagement-v1-0.xsd"/>
            <xsd:import namespace="http://ossj.org/xml/Common/v1-5" 
                schemaLocation="../xsd/OSSJ-Common-v1-5.xsd"/>
            	
            <!-- Amdocs Schema import being added to standard WSDL to enable the generation of Amdocs Entities -->
            <xsd:import namespace="http://amdocs/core/ossj-Common/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-Common_3p0.xsd"/>
            <xsd:import namespace="http://amdocs/core/ossj-Inventory/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-Inventory_3p0.xsd"/>
            <xsd:import namespace="http://amdocs/core/ossj-Common-CBEParty/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-Common-CBEParty_3p0.xsd"/>
            <xsd:import namespace="http://amdocs/core/ossj-OrderManagement/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-OrderManagement_3p0.xsd"/>
            <xsd:import namespace="http://amdocs/core/ossj-Common-CBEResource/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-Common-CBEResource_3p0.xsd"/>
            <xsd:import namespace="http://amdocs/core/ossj-Common-CBEService/dat/3"
                schemaLocation="../xsd/Amdocs-OSSJ-Common-CBEService_3p0.xsd"/>
              
        </xsd:schema>
    </wsdl:types>

    <!--  Tigerstripe : Message definitions -->
    <!--  Common API 1.5 : Message definitions -->
    
	<wsdl:message name="getSupportedOptionalOperationsResponse">
		<wsdl:part name="parameters" element="co-v1-5:getSupportedOptionalOperationsResponse"/>
	</wsdl:message>
	<wsdl:message name="getSupportedOptionalOperationsRequest">
		<wsdl:part name="parameters" element="co-v1-5:getSupportedOptionalOperationsRequest"/>
	</wsdl:message>
	<wsdl:message name="getSupportedOptionalOperationsException">
		<wsdl:part name="parameters" element="co-v1-5:getSupportedOptionalOperationsException"/>
	</wsdl:message>
	<wsdl:message name="getManagedEntityTypesResponse">
		<wsdl:part name="parameters" element="co-v1-5:getManagedEntityTypesResponse"/>
	</wsdl:message>
	<wsdl:message name="getManagedEntityTypesRequest">
		<wsdl:part name="parameters" element="co-v1-5:getManagedEntityTypesRequest"/>
	</wsdl:message>
	<wsdl:message name="getManagedEntityTypesException">
		<wsdl:part name="parameters" element="co-v1-5:getManagedEntityTypesException"/>
	</wsdl:message>
	<wsdl:message name="getEventTypesResponse">
		<wsdl:part name="parameters" element="co-v1-5:getEventTypesResponse"/>
	</wsdl:message>
	<wsdl:message name="getEventTypesRequest">
		<wsdl:part name="parameters" element="co-v1-5:getEventTypesRequest"/>
	</wsdl:message>
	<wsdl:message name="getEventTypesException">
		<wsdl:part name="parameters" element="co-v1-5:getEventTypesException"/>
	</wsdl:message>
	<wsdl:message name="getEventDescriptorResponse">
		<wsdl:part name="parameters" element="co-v1-5:getEventDescriptorResponse"/>
	</wsdl:message>
	<wsdl:message name="getEventDescriptorRequest">
		<wsdl:part name="parameters" element="co-v1-5:getEventDescriptorRequest"/>
	</wsdl:message>
	<wsdl:message name="getEventDescriptorException">
		<wsdl:part name="parameters" element="co-v1-5:getEventDescriptorException"/>
	</wsdl:message>
	<wsdl:message name="updateResponse">
		<wsdl:part name="parameters" element="co-v1-5:updateResponse"/>
	</wsdl:message>
	<wsdl:message name="updateRequest">
		<wsdl:part name="parameters" element="co-v1-5:updateRequest"/>
	</wsdl:message>
	<wsdl:message name="updateException">
		<wsdl:part name="parameters" element="co-v1-5:updateException"/>
	</wsdl:message>
	<wsdl:message name="getUpdateProcedureTypesResponse">
		<wsdl:part name="parameters" element="co-v1-5:getUpdateProcedureTypesResponse"/>
	</wsdl:message>
	<wsdl:message name="getUpdateProcedureTypesRequest">
		<wsdl:part name="parameters" element="co-v1-5:getUpdateProcedureTypesRequest"/>
	</wsdl:message>
	<wsdl:message name="getUpdateProcedureTypesException">
		<wsdl:part name="parameters" element="co-v1-5:getUpdateProcedureTypesException"/>
	</wsdl:message>
	<wsdl:message name="queryResponse">
		<wsdl:part name="parameters" element="co-v1-5:queryResponse"/>
	</wsdl:message>
	<wsdl:message name="queryRequest">
		<wsdl:part name="parameters" element="co-v1-5:queryRequest"/>
	</wsdl:message>
	<wsdl:message name="queryException">
		<wsdl:part name="parameters" element="co-v1-5:queryException"/>
	</wsdl:message>
	<wsdl:message name="getNamedQueryTypesResponse">
		<wsdl:part name="parameters" element="co-v1-5:getNamedQueryTypesResponse"/>
	</wsdl:message>
	<wsdl:message name="getNamedQueryTypesRequest">
		<wsdl:part name="parameters" element="co-v1-5:getNamedQueryTypesRequest"/>
	</wsdl:message>
	<wsdl:message name="getNamedQueryTypesException">
		<wsdl:part name="parameters" element="co-v1-5:getNamedQueryTypesException"/>
	</wsdl:message>
    <!-- Tigerstripe : Messages relating to RequestSpecification -->
    <wsdl:message name="getRequestSpecificationByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationByKeyException" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationsByTemplatesRequest">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationsByTemplatesRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationsByTemplatesResponse">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationsByTemplatesResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationsByTemplatesException">
        <wsdl:part name="parameters" element="om-v1-0:getRequestSpecificationsByTemplatesException" />
    </wsdl:message>

    
    
    <!-- Tigerstripe : Messages relating to Request -->
    <wsdl:message name="createRequestByValueRequest">
        <wsdl:part name="parameters" element="om-v1-0:createRequestByValueRequest" />
    </wsdl:message>
    <wsdl:message name="createRequestByValueResponse">
        <wsdl:part name="parameters" element="om-v1-0:createRequestByValueResponse" />
    </wsdl:message>
    <wsdl:message name="createRequestByValueException">
        <wsdl:part name="parameters" element="om-v1-0:createRequestByValueException" />
    </wsdl:message>
    <wsdl:message name="createRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:createRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="createRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:createRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="createRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:createRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="tryCreateRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="tryCreateRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="tryCreateRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="getRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:getRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:getRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:getRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="getRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="getRequestsByTemplatesRequest">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByTemplatesRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestsByTemplatesResponse">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByTemplatesResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestsByTemplatesException">
        <wsdl:part name="parameters" element="om-v1-0:getRequestsByTemplatesException" />
    </wsdl:message>
    <wsdl:message name="setRequestByValueRequest">
        <wsdl:part name="parameters" element="om-v1-0:setRequestByValueRequest" />
    </wsdl:message>
    <wsdl:message name="setRequestByValueResponse">
        <wsdl:part name="parameters" element="om-v1-0:setRequestByValueResponse" />
    </wsdl:message>
    <wsdl:message name="setRequestByValueException">
        <wsdl:part name="parameters" element="om-v1-0:setRequestByValueException" />
    </wsdl:message>
    <wsdl:message name="setRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="setRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="setRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="setRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="setRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="setRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:setRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="trySetRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:trySetRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="removeRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="removeRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="removeRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="removeRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="removeRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="removeRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:removeRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="tryRemoveRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="tryRemoveRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="tryRemoveRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveRequestsByKeysException" />
    </wsdl:message>

    <wsdl:message name="startRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:startRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="startRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:startRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="startRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:startRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="startRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:startRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="startRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:startRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="startRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:startRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="tryStartRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryStartRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="tryStartRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryStartRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="tryStartRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:tryStartRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestByValueRequest">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestByValueRequest" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestByValueResponse">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestByValueResponse" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestByValueException">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestByValueException" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="createAndStartRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:createAndStartRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="tryCreateAndStartRequestsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateAndStartRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="tryCreateAndStartRequestsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateAndStartRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="tryCreateAndStartRequestsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateAndStartRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="abortRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="abortRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="abortRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="abortRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="abortRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="abortRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:abortRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="tryAbortRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryAbortRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="tryAbortRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryAbortRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="tryAbortRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:tryAbortRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="suspendRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="suspendRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="suspendRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="suspendRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="suspendRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="suspendRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:suspendRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="trySuspendRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:trySuspendRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="trySuspendRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:trySuspendRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="trySuspendRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:trySuspendRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="resumeRequestByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="resumeRequestByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="resumeRequestByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestByKeyException" />
    </wsdl:message>
    <wsdl:message name="resumeRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="resumeRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="resumeRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:resumeRequestsByKeysException" />
    </wsdl:message>
    <wsdl:message name="tryResumeRequestsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryResumeRequestsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="tryResumeRequestsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryResumeRequestsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="tryResumeRequestsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:tryResumeRequestsByKeysException" />
    </wsdl:message>
    
    
    <!-- Tigerstripe : Messages relating to BusinessInteractionRelationship -->
    <wsdl:message name="createBusinessInteractionRelationshipByValueRequest">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipByValueRequest" />
    </wsdl:message>
    <wsdl:message name="createBusinessInteractionRelationshipByValueResponse">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipByValueResponse" />
    </wsdl:message>
    <wsdl:message name="createBusinessInteractionRelationshipByValueException">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipByValueException" />
    </wsdl:message>
    <wsdl:message name="createBusinessInteractionRelationshipsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="createBusinessInteractionRelationshipsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="createBusinessInteractionRelationshipsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:createBusinessInteractionRelationshipsByValuesException" />
    </wsdl:message>
    <wsdl:message name="tryCreateBusinessInteractionRelationshipsByValuesRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateBusinessInteractionRelationshipsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="tryCreateBusinessInteractionRelationshipsByValuesResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateBusinessInteractionRelationshipsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="tryCreateBusinessInteractionRelationshipsByValuesException">
        <wsdl:part name="parameters" element="om-v1-0:tryCreateBusinessInteractionRelationshipsByValuesException" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipByKeyException" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByKeysException" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByTemplateRequest">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByTemplateRequest" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByTemplateResponse">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByTemplateResponse" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipsByTemplateException">
        <wsdl:part name="parameters" element="om-v1-0:getBusinessInteractionRelationshipsByTemplateException" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipByKeyRequest">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipByKeyRequest" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipByKeyResponse">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipByKeyResponse" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipByKeyException">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipByKeyException" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="removeBusinessInteractionRelationshipsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:removeBusinessInteractionRelationshipsByKeysException" />
    </wsdl:message>
    <wsdl:message name="tryRemoveBusinessInteractionRelationshipsByKeysRequest">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysRequest" />
    </wsdl:message>
    <wsdl:message name="tryRemoveBusinessInteractionRelationshipsByKeysResponse">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysResponse" />
    </wsdl:message>
    <wsdl:message name="tryRemoveBusinessInteractionRelationshipsByKeysException">
        <wsdl:part name="parameters" element="om-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysException" />
    </wsdl:message>

    
    

    <!-- Tigerstripe : Messages for Interface artifact methods ( JVTOrderManagementSession ) -->
    <wsdl:message name="getRequestSpecificationsResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationsResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationsRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationsRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationsException">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationsException" />
    </wsdl:message>
    <wsdl:message name="getSupportedEntitySpecificationValuesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:getSupportedEntitySpecificationValuesResponse" />
    </wsdl:message>
    <wsdl:message name="getSupportedEntitySpecificationValuesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:getSupportedEntitySpecificationValuesRequest" />
    </wsdl:message>
    <wsdl:message name="getSupportedEntitySpecificationValuesException">
        <wsdl:part name="parameters"
             element="om-v1-0:getSupportedEntitySpecificationValuesException" />
    </wsdl:message>
    <wsdl:message name="createRelatedRequestsByValuesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:createRelatedRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="createRelatedRequestsByValuesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:createRelatedRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="createRelatedRequestsByValuesException">
        <wsdl:part name="parameters"
             element="om-v1-0:createRelatedRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="createAndStartRelatedRequestsByValuesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:createAndStartRelatedRequestsByValuesResponse" />
    </wsdl:message>
    <wsdl:message name="createAndStartRelatedRequestsByValuesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:createAndStartRelatedRequestsByValuesRequest" />
    </wsdl:message>
    <wsdl:message name="createAndStartRelatedRequestsByValuesException">
        <wsdl:part name="parameters"
             element="om-v1-0:createAndStartRelatedRequestsByValuesException" />
    </wsdl:message>
    <wsdl:message name="getRequestTypesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestTypesResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestTypesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestTypesRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestTypesException">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestTypesException" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationTypesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationTypesResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationTypesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationTypesRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestSpecificationTypesException">
        <wsdl:part name="parameters"
             element="om-v1-0:getRequestSpecificationTypesException" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipTypesResponse">
        <wsdl:part name="parameters"
             element="om-v1-0:getBusinessInteractionRelationshipTypesResponse" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipTypesRequest">
        <wsdl:part name="parameters"
             element="om-v1-0:getBusinessInteractionRelationshipTypesRequest" />
    </wsdl:message>
    <wsdl:message name="getBusinessInteractionRelationshipTypesException">
        <wsdl:part name="parameters"
             element="om-v1-0:getBusinessInteractionRelationshipTypesException" />
    </wsdl:message>


    <!--  Tigerstripe : Port type -->
    <wsdl:portType name="JVTOrderManagementSessionWSPort">

          <!-- Common API 1.5 : Operations from JVTSession -->
          <wsdl:operation name="getSupportedOptionalOperations">
           <wsdl:input name="getSupportedOptionalOperationsRequest" message="omWS-v1-0:getSupportedOptionalOperationsRequest"/>
           <wsdl:output name="getSupportedOptionalOperationsResponse" message="omWS-v1-0:getSupportedOptionalOperationsResponse"/>
           <wsdl:fault name="getSupportedOptionalOperationsException" message="omWS-v1-0:getSupportedOptionalOperationsException"/>
          </wsdl:operation>
          <wsdl:operation name="getManagedEntityTypes">
           <wsdl:input name="getManagedEntityTypesRequest" message="omWS-v1-0:getManagedEntityTypesRequest"/>
           <wsdl:output name="getManagedEntityTypesResponse" message="omWS-v1-0:getManagedEntityTypesResponse"/>
           <wsdl:fault name="getManagedEntityTypesException" message="omWS-v1-0:getManagedEntityTypesException"/>
          </wsdl:operation>
          <wsdl:operation name="getEventTypes">
           <wsdl:input name="getEventTypesRequest" message="omWS-v1-0:getEventTypesRequest"/>
           <wsdl:output name="getEventTypesResponse" message="omWS-v1-0:getEventTypesResponse"/>
           <wsdl:fault name="getEventTypesException" message="omWS-v1-0:getEventTypesException"/>
          </wsdl:operation>
          <wsdl:operation name="getEventDescriptor">
           <wsdl:input name="getEventDescriptorRequest" message="omWS-v1-0:getEventDescriptorRequest"/>
           <wsdl:output name="getEventDescriptorResponse" message="omWS-v1-0:getEventDescriptorResponse"/>
           <wsdl:fault name="getEventDescriptorException" message="omWS-v1-0:getEventDescriptorException"/>
          </wsdl:operation>
          <wsdl:operation name="update">
           <wsdl:input name="updateRequest" message="omWS-v1-0:updateRequest"/>
           <wsdl:output name="updateResponse" message="omWS-v1-0:updateResponse"/>
           <wsdl:fault name="updateException" message="omWS-v1-0:updateException"/>
          </wsdl:operation>
          <wsdl:operation name="getUpdateProcedureTypes">
           <wsdl:input name="getUpdateProcedureTypesRequest" message="omWS-v1-0:getUpdateProcedureTypesRequest"/>
           <wsdl:output name="getUpdateProcedureTypesResponse" message="omWS-v1-0:getUpdateProcedureTypesResponse"/>
           <wsdl:fault name="getUpdateProcedureTypesException" message="omWS-v1-0:getUpdateProcedureTypesException"/>
          </wsdl:operation>
          <wsdl:operation name="query">
           <wsdl:input name="queryRequest" message="omWS-v1-0:queryRequest"/>
           <wsdl:output name="queryResponse" message="omWS-v1-0:queryResponse"/>
           <wsdl:fault name="queryException" message="omWS-v1-0:queryException"/>
          </wsdl:operation>
          <wsdl:operation name="getNamedQueryTypes">
           <wsdl:input name="getNamedQueryTypesRequest" message="omWS-v1-0:getNamedQueryTypesRequest"/>
           <wsdl:output name="getNamedQueryTypesResponse" message="omWS-v1-0:getNamedQueryTypesResponse"/>
           <wsdl:fault name="getNamedQueryTypesException" message="omWS-v1-0:getNamedQueryTypesException"/>
          </wsdl:operation>
        <!-- Tigerstripe : Operations relating to RequestSpecification -->
        <wsdl:operation name="getRequestSpecificationByKey">
            <wsdl:input name="getRequestSpecificationByKeyRequest" message="omWS-v1-0:getRequestSpecificationByKeyRequest" />
            <wsdl:output name="getRequestSpecificationByKeyResponse" message="omWS-v1-0:getRequestSpecificationByKeyResponse" />
            <wsdl:fault name="getRequestSpecificationByKeyException" message="omWS-v1-0:getRequestSpecificationByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationsByTemplates">
            <wsdl:input name="getRequestSpecificationsByTemplatesRequest" message="omWS-v1-0:getRequestSpecificationsByTemplatesRequest" />
            <wsdl:output name="getRequestSpecificationsByTemplatesResponse" message="omWS-v1-0:getRequestSpecificationsByTemplatesResponse" />
            <wsdl:fault name="getRequestSpecificationsByTemplatesException" message="omWS-v1-0:getRequestSpecificationsByTemplatesException" />
        </wsdl:operation>



        <!-- Tigerstripe : Operations relating to Request -->
        <wsdl:operation name="createRequestByValue">
            <wsdl:input name="createRequestByValueRequest" message="omWS-v1-0:createRequestByValueRequest" />
            <wsdl:output name="createRequestByValueResponse" message="omWS-v1-0:createRequestByValueResponse" />
            <wsdl:fault name="createRequestByValueException" message="omWS-v1-0:createRequestByValueException" />
        </wsdl:operation>
        <wsdl:operation name="createRequestsByValues">
            <wsdl:input name="createRequestsByValuesRequest" message="omWS-v1-0:createRequestsByValuesRequest" />
            <wsdl:output name="createRequestsByValuesResponse" message="omWS-v1-0:createRequestsByValuesResponse" />
            <wsdl:fault name="createRequestsByValuesException" message="omWS-v1-0:createRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="tryCreateRequestsByValues">
            <wsdl:input name="tryCreateRequestsByValuesRequest" message="omWS-v1-0:tryCreateRequestsByValuesRequest" />
            <wsdl:output name="tryCreateRequestsByValuesResponse" message="omWS-v1-0:tryCreateRequestsByValuesResponse" />
            <wsdl:fault name="tryCreateRequestsByValuesException" message="omWS-v1-0:tryCreateRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestByKey">
            <wsdl:input name="getRequestByKeyRequest" message="omWS-v1-0:getRequestByKeyRequest" />
            <wsdl:output name="getRequestByKeyResponse" message="omWS-v1-0:getRequestByKeyResponse" />
            <wsdl:fault name="getRequestByKeyException" message="omWS-v1-0:getRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestsByKeys">
            <wsdl:input name="getRequestsByKeysRequest" message="omWS-v1-0:getRequestsByKeysRequest" />
            <wsdl:output name="getRequestsByKeysResponse" message="omWS-v1-0:getRequestsByKeysResponse" />
            <wsdl:fault name="getRequestsByKeysException" message="omWS-v1-0:getRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestsByTemplates">
            <wsdl:input name="getRequestsByTemplatesRequest" message="omWS-v1-0:getRequestsByTemplatesRequest" />
            <wsdl:output name="getRequestsByTemplatesResponse" message="omWS-v1-0:getRequestsByTemplatesResponse" />
            <wsdl:fault name="getRequestsByTemplatesException" message="omWS-v1-0:getRequestsByTemplatesException" />
        </wsdl:operation>
        <wsdl:operation name="setRequestByValue">
            <wsdl:input name="setRequestByValueRequest" message="omWS-v1-0:setRequestByValueRequest" />
            <wsdl:output name="setRequestByValueResponse" message="omWS-v1-0:setRequestByValueResponse" />
            <wsdl:fault name="setRequestByValueException" message="omWS-v1-0:setRequestByValueException" />
        </wsdl:operation>
        <wsdl:operation name="setRequestsByValues">
            <wsdl:input name="setRequestsByValuesRequest" message="omWS-v1-0:setRequestsByValuesRequest" />
            <wsdl:output name="setRequestsByValuesResponse" message="omWS-v1-0:setRequestsByValuesResponse" />
            <wsdl:fault name="setRequestsByValuesException" message="omWS-v1-0:setRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="setRequestsByKeys">
            <wsdl:input name="setRequestsByKeysRequest" message="omWS-v1-0:setRequestsByKeysRequest" />
            <wsdl:output name="setRequestsByKeysResponse" message="omWS-v1-0:setRequestsByKeysResponse" />
            <wsdl:fault name="setRequestsByKeysException" message="omWS-v1-0:setRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByValues">
            <wsdl:input name="trySetRequestsByValuesRequest" message="omWS-v1-0:trySetRequestsByValuesRequest" />
            <wsdl:output name="trySetRequestsByValuesResponse" message="omWS-v1-0:trySetRequestsByValuesResponse" />
            <wsdl:fault name="trySetRequestsByValuesException" message="omWS-v1-0:trySetRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByKeys">
            <wsdl:input name="trySetRequestsByKeysRequest" message="omWS-v1-0:trySetRequestsByKeysRequest" />
            <wsdl:output name="trySetRequestsByKeysResponse" message="omWS-v1-0:trySetRequestsByKeysResponse" />
            <wsdl:fault name="trySetRequestsByKeysException" message="omWS-v1-0:trySetRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="removeRequestByKey">
            <wsdl:input name="removeRequestByKeyRequest" message="omWS-v1-0:removeRequestByKeyRequest" />
            <wsdl:output name="removeRequestByKeyResponse" message="omWS-v1-0:removeRequestByKeyResponse" />
            <wsdl:fault name="removeRequestByKeyException" message="omWS-v1-0:removeRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="removeRequestsByKeys">
            <wsdl:input name="removeRequestsByKeysRequest" message="omWS-v1-0:removeRequestsByKeysRequest" />
            <wsdl:output name="removeRequestsByKeysResponse" message="omWS-v1-0:removeRequestsByKeysResponse" />
            <wsdl:fault name="removeRequestsByKeysException" message="omWS-v1-0:removeRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="tryRemoveRequestsByKeys">
            <wsdl:input name="tryRemoveRequestsByKeysRequest" message="omWS-v1-0:tryRemoveRequestsByKeysRequest" />
            <wsdl:output name="tryRemoveRequestsByKeysResponse" message="omWS-v1-0:tryRemoveRequestsByKeysResponse" />
            <wsdl:fault name="tryRemoveRequestsByKeysException" message="omWS-v1-0:tryRemoveRequestsByKeysException" />
        </wsdl:operation>

        <wsdl:operation name="startRequestByKey">
            <wsdl:input name="startRequestByKeyRequest" message="omWS-v1-0:startRequestByKeyRequest" />
            <wsdl:output name="startRequestByKeyResponse" message="omWS-v1-0:startRequestByKeyResponse" />
            <wsdl:fault name="startRequestByKeyException" message="omWS-v1-0:startRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="startRequestsByKeys">
            <wsdl:input name="startRequestsByKeysRequest" message="omWS-v1-0:startRequestsByKeysRequest" />
            <wsdl:output name="startRequestsByKeysResponse" message="omWS-v1-0:startRequestsByKeysResponse" />
            <wsdl:fault name="startRequestsByKeysException" message="omWS-v1-0:startRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="tryStartRequestsByKeys">
            <wsdl:input name="tryStartRequestsByKeysRequest" message="omWS-v1-0:tryStartRequestsByKeysRequest" />
            <wsdl:output name="tryStartRequestsByKeysResponse" message="omWS-v1-0:tryStartRequestsByKeysResponse" />
            <wsdl:fault name="tryStartRequestsByKeysException" message="omWS-v1-0:tryStartRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="createAndStartRequestByValue">
            <wsdl:input name="createAndStartRequestByValueRequest" message="omWS-v1-0:createAndStartRequestByValueRequest" />
            <wsdl:output name="createAndStartRequestByValueResponse" message="omWS-v1-0:createAndStartRequestByValueResponse" />
            <wsdl:fault name="createAndStartRequestByValueException" message="omWS-v1-0:createAndStartRequestByValueException" />
        </wsdl:operation>
        <wsdl:operation name="createAndStartRequestsByValues">
            <wsdl:input name="createAndStartRequestsByValuesRequest" message="omWS-v1-0:createAndStartRequestsByValuesRequest" />
            <wsdl:output name="createAndStartRequestsByValuesResponse" message="omWS-v1-0:createAndStartRequestsByValuesResponse" />
            <wsdl:fault name="createAndStartRequestsByValuesException" message="omWS-v1-0:createAndStartRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="tryCreateAndStartRequestsByValues">
            <wsdl:input name="tryCreateAndStartRequestsByValuesRequest" message="omWS-v1-0:tryCreateAndStartRequestsByValuesRequest" />
            <wsdl:output name="tryCreateAndStartRequestsByValuesResponse" message="omWS-v1-0:tryCreateAndStartRequestsByValuesResponse" />
            <wsdl:fault name="tryCreateAndStartRequestsByValuesException" message="omWS-v1-0:tryCreateAndStartRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="abortRequestByKey">
            <wsdl:input name="abortRequestByKeyRequest" message="omWS-v1-0:abortRequestByKeyRequest" />
            <wsdl:output name="abortRequestByKeyResponse" message="omWS-v1-0:abortRequestByKeyResponse" />
            <wsdl:fault name="abortRequestByKeyException" message="omWS-v1-0:abortRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="abortRequestsByKeys">
            <wsdl:input name="abortRequestsByKeysRequest" message="omWS-v1-0:abortRequestsByKeysRequest" />
            <wsdl:output name="abortRequestsByKeysResponse" message="omWS-v1-0:abortRequestsByKeysResponse" />
            <wsdl:fault name="abortRequestsByKeysException" message="omWS-v1-0:abortRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="tryAbortRequestsByKeys">
            <wsdl:input name="tryAbortRequestsByKeysRequest" message="omWS-v1-0:tryAbortRequestsByKeysRequest" />
            <wsdl:output name="tryAbortRequestsByKeysResponse" message="omWS-v1-0:tryAbortRequestsByKeysResponse" />
            <wsdl:fault name="tryAbortRequestsByKeysException" message="omWS-v1-0:tryAbortRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="suspendRequestByKey">
            <wsdl:input name="suspendRequestByKeyRequest" message="omWS-v1-0:suspendRequestByKeyRequest" />
            <wsdl:output name="suspendRequestByKeyResponse" message="omWS-v1-0:suspendRequestByKeyResponse" />
            <wsdl:fault name="suspendRequestByKeyException" message="omWS-v1-0:suspendRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="suspendRequestsByKeys">
            <wsdl:input name="suspendRequestsByKeysRequest" message="omWS-v1-0:suspendRequestsByKeysRequest" />
            <wsdl:output name="suspendRequestsByKeysResponse" message="omWS-v1-0:suspendRequestsByKeysResponse" />
            <wsdl:fault name="suspendRequestsByKeysException" message="omWS-v1-0:suspendRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="trySuspendRequestsByKeys">
            <wsdl:input name="trySuspendRequestsByKeysRequest" message="omWS-v1-0:trySuspendRequestsByKeysRequest" />
            <wsdl:output name="trySuspendRequestsByKeysResponse" message="omWS-v1-0:trySuspendRequestsByKeysResponse" />
            <wsdl:fault name="trySuspendRequestsByKeysException" message="omWS-v1-0:trySuspendRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="resumeRequestByKey">
            <wsdl:input name="resumeRequestByKeyRequest" message="omWS-v1-0:resumeRequestByKeyRequest" />
            <wsdl:output name="resumeRequestByKeyResponse" message="omWS-v1-0:resumeRequestByKeyResponse" />
            <wsdl:fault name="resumeRequestByKeyException" message="omWS-v1-0:resumeRequestByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="resumeRequestsByKeys">
            <wsdl:input name="resumeRequestsByKeysRequest" message="omWS-v1-0:resumeRequestsByKeysRequest" />
            <wsdl:output name="resumeRequestsByKeysResponse" message="omWS-v1-0:resumeRequestsByKeysResponse" />
            <wsdl:fault name="resumeRequestsByKeysException" message="omWS-v1-0:resumeRequestsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="tryResumeRequestsByKeys">
            <wsdl:input name="tryResumeRequestsByKeysRequest" message="omWS-v1-0:tryResumeRequestsByKeysRequest" />
            <wsdl:output name="tryResumeRequestsByKeysResponse" message="omWS-v1-0:tryResumeRequestsByKeysResponse" />
            <wsdl:fault name="tryResumeRequestsByKeysException" message="omWS-v1-0:tryResumeRequestsByKeysException" />
        </wsdl:operation>


        <!-- Tigerstripe : Operations relating to BusinessInteractionRelationship -->
        <wsdl:operation name="createBusinessInteractionRelationshipByValue">
            <wsdl:input name="createBusinessInteractionRelationshipByValueRequest" message="omWS-v1-0:createBusinessInteractionRelationshipByValueRequest" />
            <wsdl:output name="createBusinessInteractionRelationshipByValueResponse" message="omWS-v1-0:createBusinessInteractionRelationshipByValueResponse" />
            <wsdl:fault name="createBusinessInteractionRelationshipByValueException" message="omWS-v1-0:createBusinessInteractionRelationshipByValueException" />
        </wsdl:operation>
        <wsdl:operation name="createBusinessInteractionRelationshipsByValues">
            <wsdl:input name="createBusinessInteractionRelationshipsByValuesRequest" message="omWS-v1-0:createBusinessInteractionRelationshipsByValuesRequest" />
            <wsdl:output name="createBusinessInteractionRelationshipsByValuesResponse" message="omWS-v1-0:createBusinessInteractionRelationshipsByValuesResponse" />
            <wsdl:fault name="createBusinessInteractionRelationshipsByValuesException" message="omWS-v1-0:createBusinessInteractionRelationshipsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="tryCreateBusinessInteractionRelationshipsByValues">
            <wsdl:input name="tryCreateBusinessInteractionRelationshipsByValuesRequest" message="omWS-v1-0:tryCreateBusinessInteractionRelationshipsByValuesRequest" />
            <wsdl:output name="tryCreateBusinessInteractionRelationshipsByValuesResponse" message="omWS-v1-0:tryCreateBusinessInteractionRelationshipsByValuesResponse" />
            <wsdl:fault name="tryCreateBusinessInteractionRelationshipsByValuesException" message="omWS-v1-0:tryCreateBusinessInteractionRelationshipsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipByKey">
            <wsdl:input name="getBusinessInteractionRelationshipByKeyRequest" message="omWS-v1-0:getBusinessInteractionRelationshipByKeyRequest" />
            <wsdl:output name="getBusinessInteractionRelationshipByKeyResponse" message="omWS-v1-0:getBusinessInteractionRelationshipByKeyResponse" />
            <wsdl:fault name="getBusinessInteractionRelationshipByKeyException" message="omWS-v1-0:getBusinessInteractionRelationshipByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByKeys">
            <wsdl:input name="getBusinessInteractionRelationshipsByKeysRequest" message="omWS-v1-0:getBusinessInteractionRelationshipsByKeysRequest" />
            <wsdl:output name="getBusinessInteractionRelationshipsByKeysResponse" message="omWS-v1-0:getBusinessInteractionRelationshipsByKeysResponse" />
            <wsdl:fault name="getBusinessInteractionRelationshipsByKeysException" message="omWS-v1-0:getBusinessInteractionRelationshipsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByTemplate">
            <wsdl:input name="getBusinessInteractionRelationshipsByTemplateRequest" message="omWS-v1-0:getBusinessInteractionRelationshipsByTemplateRequest" />
            <wsdl:output name="getBusinessInteractionRelationshipsByTemplateResponse" message="omWS-v1-0:getBusinessInteractionRelationshipsByTemplateResponse" />
            <wsdl:fault name="getBusinessInteractionRelationshipsByTemplateException" message="omWS-v1-0:getBusinessInteractionRelationshipsByTemplateException" />
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipByKey">
            <wsdl:input name="removeBusinessInteractionRelationshipByKeyRequest" message="omWS-v1-0:removeBusinessInteractionRelationshipByKeyRequest" />
            <wsdl:output name="removeBusinessInteractionRelationshipByKeyResponse" message="omWS-v1-0:removeBusinessInteractionRelationshipByKeyResponse" />
            <wsdl:fault name="removeBusinessInteractionRelationshipByKeyException" message="omWS-v1-0:removeBusinessInteractionRelationshipByKeyException" />
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipsByKeys">
            <wsdl:input name="removeBusinessInteractionRelationshipsByKeysRequest" message="omWS-v1-0:removeBusinessInteractionRelationshipsByKeysRequest" />
            <wsdl:output name="removeBusinessInteractionRelationshipsByKeysResponse" message="omWS-v1-0:removeBusinessInteractionRelationshipsByKeysResponse" />
            <wsdl:fault name="removeBusinessInteractionRelationshipsByKeysException" message="omWS-v1-0:removeBusinessInteractionRelationshipsByKeysException" />
        </wsdl:operation>
        <wsdl:operation name="tryRemoveBusinessInteractionRelationshipsByKeys">
            <wsdl:input name="tryRemoveBusinessInteractionRelationshipsByKeysRequest" message="omWS-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysRequest" />
            <wsdl:output name="tryRemoveBusinessInteractionRelationshipsByKeysResponse" message="omWS-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysResponse" />
            <wsdl:fault name="tryRemoveBusinessInteractionRelationshipsByKeysException" message="omWS-v1-0:tryRemoveBusinessInteractionRelationshipsByKeysException" />
        </wsdl:operation>




        <!-- Tigerstripe : Operations for Interface artifact methods ( JVTOrderManagementSession ) -->
        <wsdl:operation name="getRequestSpecifications">
            <wsdl:input
                name="getRequestSpecificationsRequest"
                message="omWS-v1-0:getRequestSpecificationsRequest" />
            <wsdl:output
                name="getRequestSpecificationsResponse"
                message="omWS-v1-0:getRequestSpecificationsResponse" />
            <wsdl:fault
                name="getRequestSpecificationsException"
                message="omWS-v1-0:getRequestSpecificationsException" />
        </wsdl:operation>
        <wsdl:operation name="getSupportedEntitySpecificationValues">
            <wsdl:input
                name="getSupportedEntitySpecificationValuesRequest"
                message="omWS-v1-0:getSupportedEntitySpecificationValuesRequest" />
            <wsdl:output
                name="getSupportedEntitySpecificationValuesResponse"
                message="omWS-v1-0:getSupportedEntitySpecificationValuesResponse" />
            <wsdl:fault
                name="getSupportedEntitySpecificationValuesException"
                message="omWS-v1-0:getSupportedEntitySpecificationValuesException" />
        </wsdl:operation>
        <wsdl:operation name="createRelatedRequestsByValues">
            <wsdl:input
                name="createRelatedRequestsByValuesRequest"
                message="omWS-v1-0:createRelatedRequestsByValuesRequest" />
            <wsdl:output
                name="createRelatedRequestsByValuesResponse"
                message="omWS-v1-0:createRelatedRequestsByValuesResponse" />
            <wsdl:fault
                name="createRelatedRequestsByValuesException"
                message="omWS-v1-0:createRelatedRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="createAndStartRelatedRequestsByValues">
            <wsdl:input
                name="createAndStartRelatedRequestsByValuesRequest"
                message="omWS-v1-0:createAndStartRelatedRequestsByValuesRequest" />
            <wsdl:output
                name="createAndStartRelatedRequestsByValuesResponse"
                message="omWS-v1-0:createAndStartRelatedRequestsByValuesResponse" />
            <wsdl:fault
                name="createAndStartRelatedRequestsByValuesException"
                message="omWS-v1-0:createAndStartRelatedRequestsByValuesException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestTypes">
            <wsdl:input
                name="getRequestTypesRequest"
                message="omWS-v1-0:getRequestTypesRequest" />
            <wsdl:output
                name="getRequestTypesResponse"
                message="omWS-v1-0:getRequestTypesResponse" />
            <wsdl:fault
                name="getRequestTypesException"
                message="omWS-v1-0:getRequestTypesException" />
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationTypes">
            <wsdl:input
                name="getRequestSpecificationTypesRequest"
                message="omWS-v1-0:getRequestSpecificationTypesRequest" />
            <wsdl:output
                name="getRequestSpecificationTypesResponse"
                message="omWS-v1-0:getRequestSpecificationTypesResponse" />
            <wsdl:fault
                name="getRequestSpecificationTypesException"
                message="omWS-v1-0:getRequestSpecificationTypesException" />
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipTypes">
            <wsdl:input
                name="getBusinessInteractionRelationshipTypesRequest"
                message="omWS-v1-0:getBusinessInteractionRelationshipTypesRequest" />
            <wsdl:output
                name="getBusinessInteractionRelationshipTypesResponse"
                message="omWS-v1-0:getBusinessInteractionRelationshipTypesResponse" />
            <wsdl:fault
                name="getBusinessInteractionRelationshipTypesException"
                message="omWS-v1-0:getBusinessInteractionRelationshipTypesException" />
        </wsdl:operation>
    </wsdl:portType>


    <wsdl:binding name="JVTOrderManagementSessionSoap11Binding"
        type="omWS-v1-0:JVTOrderManagementSessionWSPort">
        <soap:binding style="document"
            transport="http://schemas.xmlsoap.org/soap/http" />

        <!-- Common API 1.5 : Bindings for JVTSession -->
        <wsdl:operation name="getSupportedOptionalOperations">
           <soap:operation soapAction="getSupportedOptionalOperations" style="document" />
           <wsdl:input name="getSupportedOptionalOperationsRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getSupportedOptionalOperationsResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getSupportedOptionalOperationsException">
               <soap:fault name="getSupportedOptionalOperationsException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getManagedEntityTypes">
           <soap:operation soapAction="getManagedEntityTypes" style="document" />
           <wsdl:input name="getManagedEntityTypesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getManagedEntityTypesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getManagedEntityTypesException">
               <soap:fault name="getManagedEntityTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getEventTypes">
           <soap:operation soapAction="getEventTypes" style="document" />
           <wsdl:input name="getEventTypesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getEventTypesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getEventTypesException">
               <soap:fault name="getEventTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getEventDescriptor">
           <soap:operation soapAction="getEventDescriptor" style="document" />
           <wsdl:input name="getEventDescriptorRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getEventDescriptorResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getEventDescriptorException">
               <soap:fault name="getEventDescriptorException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="update">
           <soap:operation soapAction="update" style="document" />
           <wsdl:input name="updateRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="updateResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="updateException">
               <soap:fault name="updateException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getUpdateProcedureTypes">
           <soap:operation soapAction="getUpdateProcedureTypes" style="document" />
           <wsdl:input name="getUpdateProcedureTypesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getUpdateProcedureTypesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getUpdateProcedureTypesException">
               <soap:fault name="getUpdateProcedureTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="query">
           <soap:operation soapAction="query" style="document" />
           <wsdl:input name="queryRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="queryResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="queryException">
               <soap:fault name="queryException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getNamedQueryTypes">
           <soap:operation soapAction="getNamedQueryTypes" style="document" />
           <wsdl:input name="getNamedQueryTypesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getNamedQueryTypesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getNamedQueryTypesException">
               <soap:fault name="getNamedQueryTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <!-- Tigerstripe : Bindings relating to RequestSpecification -->
        <wsdl:operation name="getRequestSpecificationByKey">
           <soap:operation soapAction="getRequestSpecificationByKey" style="document" />
           <wsdl:input name="getRequestSpecificationByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestSpecificationByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestSpecificationByKeyException">
               <soap:fault name="getRequestSpecificationByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationsByTemplates">
           <soap:operation soapAction="getRequestSpecificationsByTemplates" style="document" />
           <wsdl:input name="getRequestSpecificationsByTemplatesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestSpecificationsByTemplatesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestSpecificationsByTemplatesException">
               <soap:fault name="getRequestSpecificationsByTemplatesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>


        <!-- Tigerstripe : Bindings relating to Request -->
        <wsdl:operation name="createRequestByValue">
           <soap:operation soapAction="createRequestByValue" style="document" />
           <wsdl:input name="createRequestByValueRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createRequestByValueResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createRequestByValueException">
               <soap:fault name="createRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createRequestsByValues">
           <soap:operation soapAction="createRequestsByValues" style="document" />
           <wsdl:input name="createRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createRequestsByValuesException">
               <soap:fault name="createRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateRequestsByValues">
           <soap:operation soapAction="tryCreateRequestsByValues" style="document" />
           <wsdl:input name="tryCreateRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateRequestsByValuesException">
               <soap:fault name="tryCreateRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestByKey">
           <soap:operation soapAction="getRequestByKey" style="document" />
           <wsdl:input name="getRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestByKeyException">
               <soap:fault name="getRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestsByKeys">
           <soap:operation soapAction="getRequestsByKeys" style="document" />
           <wsdl:input name="getRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestsByKeysException">
               <soap:fault name="getRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestsByTemplates">
           <soap:operation soapAction="getRequestsByTemplates" style="document" />
           <wsdl:input name="getRequestsByTemplatesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestsByTemplatesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestsByTemplatesException">
               <soap:fault name="getRequestsByTemplatesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestByValue">
           <soap:operation soapAction="setRequestByValue" style="document" />
           <wsdl:input name="setRequestByValueRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestByValueResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestByValueException">
               <soap:fault name="setRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestsByValues">
           <soap:operation soapAction="setRequestsByValues" style="document" />
           <wsdl:input name="setRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestsByValuesException">
               <soap:fault name="setRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestsByKeys">
           <soap:operation soapAction="setRequestsByKeys" style="document" />
           <wsdl:input name="setRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestsByKeysException">
               <soap:fault name="setRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByValues">
           <soap:operation soapAction="trySetRequestsByValues" style="document" />
           <wsdl:input name="trySetRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySetRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySetRequestsByValuesException">
               <soap:fault name="trySetRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByKeys">
           <soap:operation soapAction="trySetRequestsByKeys" style="document" />
           <wsdl:input name="trySetRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySetRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySetRequestsByKeysException">
               <soap:fault name="trySetRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeRequestByKey">
           <soap:operation soapAction="removeRequestByKey" style="document" />
           <wsdl:input name="removeRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeRequestByKeyException">
               <soap:fault name="removeRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeRequestsByKeys">
           <soap:operation soapAction="removeRequestsByKeys" style="document" />
           <wsdl:input name="removeRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeRequestsByKeysException">
               <soap:fault name="removeRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryRemoveRequestsByKeys">
           <soap:operation soapAction="tryRemoveRequestsByKeys" style="document" />
           <wsdl:input name="tryRemoveRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryRemoveRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryRemoveRequestsByKeysException">
               <soap:fault name="tryRemoveRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="startRequestByKey">
           <soap:operation soapAction="startRequestByKey" style="document" />
           <wsdl:input name="startRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="startRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="startRequestByKeyException">
               <soap:fault name="startRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="startRequestsByKeys">
           <soap:operation soapAction="startRequestsByKeys" style="document" />
           <wsdl:input name="startRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="startRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="startRequestsByKeysException">
               <soap:fault name="startRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryStartRequestsByKeys">
           <soap:operation soapAction="tryStartRequestsByKeys" style="document" />
           <wsdl:input name="tryStartRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryStartRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryStartRequestsByKeysException">
               <soap:fault name="tryStartRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="createAndStartRequestByValue">
           <soap:operation soapAction="createAndStartRequestByValue" style="document" />
           <wsdl:input name="createAndStartRequestByValueRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createAndStartRequestByValueResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createAndStartRequestByValueException">
               <soap:fault name="createAndStartRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createAndStartRequestsByValues">
           <soap:operation soapAction="createAndStartRequestsByValues" style="document" />
           <wsdl:input name="createAndStartRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createAndStartRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createAndStartRequestsByValuesException">
               <soap:fault name="createAndStartRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateAndStartRequestsByValues">
           <soap:operation soapAction="tryCreateAndStartRequestsByValues" style="document" />
           <wsdl:input name="tryCreateAndStartRequestsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateAndStartRequestsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateAndStartRequestsByValuesException">
               <soap:fault name="tryCreateAndStartRequestsByValuesException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="abortRequestByKey">
           <soap:operation soapAction="abortRequestByKey" style="document" />
           <wsdl:input name="abortRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="abortRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="abortRequestByKeyException">
               <soap:fault name="abortRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="abortRequestsByKeys">
           <soap:operation soapAction="abortRequestsByKeys" style="document" />
           <wsdl:input name="abortRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="abortRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="abortRequestsByKeysException">
               <soap:fault name="abortRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryAbortRequestsByKeys">
           <soap:operation soapAction="tryAbortRequestsByKeys" style="document" />
           <wsdl:input name="tryAbortRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryAbortRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryAbortRequestsByKeysException">
               <soap:fault name="tryAbortRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="suspendRequestByKey">
           <soap:operation soapAction="suspendRequestByKey" style="document" />
           <wsdl:input name="suspendRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="suspendRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="suspendRequestByKeyException">
               <soap:fault name="suspendRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="suspendRequestsByKeys">
           <soap:operation soapAction="suspendRequestsByKeys" style="document" />
           <wsdl:input name="suspendRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="suspendRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="suspendRequestsByKeysException">
               <soap:fault name="suspendRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySuspendRequestsByKeys">
           <soap:operation soapAction="trySuspendRequestsByKeys" style="document" />
           <wsdl:input name="trySuspendRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySuspendRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySuspendRequestsByKeysException">
               <soap:fault name="trySuspendRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="resumeRequestByKey">
           <soap:operation soapAction="resumeRequestByKey" style="document" />
           <wsdl:input name="resumeRequestByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="resumeRequestByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="resumeRequestByKeyException">
               <soap:fault name="resumeRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="resumeRequestsByKeys">
           <soap:operation soapAction="resumeRequestsByKeys" style="document" />
           <wsdl:input name="resumeRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="resumeRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="resumeRequestsByKeysException">
               <soap:fault name="resumeRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryResumeRequestsByKeys">
           <soap:operation soapAction="tryResumeRequestsByKeys" style="document" />
           <wsdl:input name="tryResumeRequestsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryResumeRequestsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryResumeRequestsByKeysException">
               <soap:fault name="tryResumeRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>

        <!-- Tigerstripe : Bindings relating to BusinessInteractionRelationship -->
        <wsdl:operation name="createBusinessInteractionRelationshipByValue">
           <soap:operation soapAction="createBusinessInteractionRelationshipByValue" style="document" />
           <wsdl:input name="createBusinessInteractionRelationshipByValueRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createBusinessInteractionRelationshipByValueResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createBusinessInteractionRelationshipByValueException">
               <soap:fault name="createBusinessInteractionRelationshipByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createBusinessInteractionRelationshipsByValues">
           <soap:operation soapAction="createBusinessInteractionRelationshipsByValues" style="document" />
           <wsdl:input name="createBusinessInteractionRelationshipsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createBusinessInteractionRelationshipsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createBusinessInteractionRelationshipsByValuesException">
               <soap:fault name="createBusinessInteractionRelationshipsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateBusinessInteractionRelationshipsByValues">
           <soap:operation soapAction="tryCreateBusinessInteractionRelationshipsByValues" style="document" />
           <wsdl:input name="tryCreateBusinessInteractionRelationshipsByValuesRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateBusinessInteractionRelationshipsByValuesResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateBusinessInteractionRelationshipsByValuesException">
               <soap:fault name="tryCreateBusinessInteractionRelationshipsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipByKey">
           <soap:operation soapAction="getBusinessInteractionRelationshipByKey" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipByKeyException">
               <soap:fault name="getBusinessInteractionRelationshipByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByKeys">
           <soap:operation soapAction="getBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipsByKeysException">
               <soap:fault name="getBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByTemplate">
           <soap:operation soapAction="getBusinessInteractionRelationshipsByTemplate" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipsByTemplateRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipsByTemplateResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipsByTemplateException">
               <soap:fault name="getBusinessInteractionRelationshipsByTemplateException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipByKey">
           <soap:operation soapAction="removeBusinessInteractionRelationshipByKey" style="document" />
           <wsdl:input name="removeBusinessInteractionRelationshipByKeyRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeBusinessInteractionRelationshipByKeyResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeBusinessInteractionRelationshipByKeyException">
               <soap:fault name="removeBusinessInteractionRelationshipByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipsByKeys">
           <soap:operation soapAction="removeBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="removeBusinessInteractionRelationshipsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeBusinessInteractionRelationshipsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeBusinessInteractionRelationshipsByKeysException">
               <soap:fault name="removeBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryRemoveBusinessInteractionRelationshipsByKeys">
           <soap:operation soapAction="tryRemoveBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="tryRemoveBusinessInteractionRelationshipsByKeysRequest">
               <soap:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryRemoveBusinessInteractionRelationshipsByKeysResponse">
               <soap:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryRemoveBusinessInteractionRelationshipsByKeysException">
               <soap:fault name="tryRemoveBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>



        <wsdl:operation name="getRequestSpecifications">
            <soap:operation soapAction="getRequestSpecifications" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestSpecificationsException"> 
                <soap:fault name ="getRequestSpecificationsException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSupportedEntitySpecificationValues">
            <soap:operation soapAction="getSupportedEntitySpecificationValues" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getSupportedEntitySpecificationValuesException"> 
                <soap:fault name ="getSupportedEntitySpecificationValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createRelatedRequestsByValues">
            <soap:operation soapAction="createRelatedRequestsByValues" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="createRelatedRequestsByValuesException"> 
                <soap:fault name ="createRelatedRequestsByValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createAndStartRelatedRequestsByValues">
            <soap:operation soapAction="createAndStartRelatedRequestsByValues" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="createAndStartRelatedRequestsByValuesException"> 
                <soap:fault name ="createAndStartRelatedRequestsByValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestTypes">
            <soap:operation soapAction="getRequestTypes" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestTypesException"> 
                <soap:fault name ="getRequestTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationTypes">
            <soap:operation soapAction="getRequestSpecificationTypes" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestSpecificationTypesException"> 
                <soap:fault name ="getRequestSpecificationTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipTypes">
            <soap:operation soapAction="getBusinessInteractionRelationshipTypes" style="document" /> 
            <wsdl:input>
                <soap:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getBusinessInteractionRelationshipTypesException"> 
                <soap:fault name ="getBusinessInteractionRelationshipTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
   
   <wsdl:binding name="JVTOrderManagementSessionSoap12Binding"
        type="omWS-v1-0:JVTOrderManagementSessionWSPort">
        <soap12:binding style="document"
            transport="http://schemas.xmlsoap.org/soap/http" />

        <!-- Common API 1.5 : Bindings for JVTSession -->
        <wsdl:operation name="getSupportedOptionalOperations">
           <soap12:operation soapAction="getSupportedOptionalOperations" style="document" />
           <wsdl:input name="getSupportedOptionalOperationsRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getSupportedOptionalOperationsResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getSupportedOptionalOperationsException">
               <soap12:fault name="getSupportedOptionalOperationsException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getManagedEntityTypes">
           <soap12:operation soapAction="getManagedEntityTypes" style="document" />
           <wsdl:input name="getManagedEntityTypesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getManagedEntityTypesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getManagedEntityTypesException">
               <soap12:fault name="getManagedEntityTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getEventTypes">
           <soap12:operation soapAction="getEventTypes" style="document" />
           <wsdl:input name="getEventTypesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getEventTypesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getEventTypesException">
               <soap12:fault name="getEventTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getEventDescriptor">
           <soap12:operation soapAction="getEventDescriptor" style="document" />
           <wsdl:input name="getEventDescriptorRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getEventDescriptorResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getEventDescriptorException">
               <soap12:fault name="getEventDescriptorException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="update">
           <soap12:operation soapAction="update" style="document" />
           <wsdl:input name="updateRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="updateResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="updateException">
               <soap12:fault name="updateException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getUpdateProcedureTypes">
           <soap12:operation soapAction="getUpdateProcedureTypes" style="document" />
           <wsdl:input name="getUpdateProcedureTypesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getUpdateProcedureTypesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getUpdateProcedureTypesException">
               <soap12:fault name="getUpdateProcedureTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="query">
           <soap12:operation soapAction="query" style="document" />
           <wsdl:input name="queryRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="queryResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="queryException">
               <soap12:fault name="queryException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getNamedQueryTypes">
           <soap12:operation soapAction="getNamedQueryTypes" style="document" />
           <wsdl:input name="getNamedQueryTypesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getNamedQueryTypesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getNamedQueryTypesException">
               <soap12:fault name="getNamedQueryTypesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <!-- Tigerstripe : Bindings relating to RequestSpecification -->
        <wsdl:operation name="getRequestSpecificationByKey">
           <soap12:operation soapAction="getRequestSpecificationByKey" style="document" />
           <wsdl:input name="getRequestSpecificationByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestSpecificationByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestSpecificationByKeyException">
               <soap12:fault name="getRequestSpecificationByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationsByTemplates">
           <soap12:operation soapAction="getRequestSpecificationsByTemplates" style="document" />
           <wsdl:input name="getRequestSpecificationsByTemplatesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestSpecificationsByTemplatesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestSpecificationsByTemplatesException">
               <soap12:fault name="getRequestSpecificationsByTemplatesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>


        <!-- Tigerstripe : Bindings relating to Request -->
        <wsdl:operation name="createRequestByValue">
           <soap12:operation soapAction="createRequestByValue" style="document" />
           <wsdl:input name="createRequestByValueRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createRequestByValueResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createRequestByValueException">
               <soap12:fault name="createRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createRequestsByValues">
           <soap12:operation soapAction="createRequestsByValues" style="document" />
           <wsdl:input name="createRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createRequestsByValuesException">
               <soap12:fault name="createRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateRequestsByValues">
           <soap12:operation soapAction="tryCreateRequestsByValues" style="document" />
           <wsdl:input name="tryCreateRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateRequestsByValuesException">
               <soap12:fault name="tryCreateRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestByKey">
           <soap12:operation soapAction="getRequestByKey" style="document" />
           <wsdl:input name="getRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestByKeyException">
               <soap12:fault name="getRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestsByKeys">
           <soap12:operation soapAction="getRequestsByKeys" style="document" />
           <wsdl:input name="getRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestsByKeysException">
               <soap12:fault name="getRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestsByTemplates">
           <soap12:operation soapAction="getRequestsByTemplates" style="document" />
           <wsdl:input name="getRequestsByTemplatesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getRequestsByTemplatesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getRequestsByTemplatesException">
               <soap12:fault name="getRequestsByTemplatesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestByValue">
           <soap12:operation soapAction="setRequestByValue" style="document" />
           <wsdl:input name="setRequestByValueRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestByValueResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestByValueException">
               <soap12:fault name="setRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestsByValues">
           <soap12:operation soapAction="setRequestsByValues" style="document" />
           <wsdl:input name="setRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestsByValuesException">
               <soap12:fault name="setRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRequestsByKeys">
           <soap12:operation soapAction="setRequestsByKeys" style="document" />
           <wsdl:input name="setRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="setRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="setRequestsByKeysException">
               <soap12:fault name="setRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByValues">
           <soap12:operation soapAction="trySetRequestsByValues" style="document" />
           <wsdl:input name="trySetRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySetRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySetRequestsByValuesException">
               <soap12:fault name="trySetRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySetRequestsByKeys">
           <soap12:operation soapAction="trySetRequestsByKeys" style="document" />
           <wsdl:input name="trySetRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySetRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySetRequestsByKeysException">
               <soap12:fault name="trySetRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeRequestByKey">
           <soap12:operation soapAction="removeRequestByKey" style="document" />
           <wsdl:input name="removeRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeRequestByKeyException">
               <soap12:fault name="removeRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeRequestsByKeys">
           <soap12:operation soapAction="removeRequestsByKeys" style="document" />
           <wsdl:input name="removeRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeRequestsByKeysException">
               <soap12:fault name="removeRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryRemoveRequestsByKeys">
           <soap12:operation soapAction="tryRemoveRequestsByKeys" style="document" />
           <wsdl:input name="tryRemoveRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryRemoveRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryRemoveRequestsByKeysException">
               <soap12:fault name="tryRemoveRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="startRequestByKey">
           <soap12:operation soapAction="startRequestByKey" style="document" />
           <wsdl:input name="startRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="startRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="startRequestByKeyException">
               <soap12:fault name="startRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="startRequestsByKeys">
           <soap12:operation soapAction="startRequestsByKeys" style="document" />
           <wsdl:input name="startRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="startRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="startRequestsByKeysException">
               <soap12:fault name="startRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryStartRequestsByKeys">
           <soap12:operation soapAction="tryStartRequestsByKeys" style="document" />
           <wsdl:input name="tryStartRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryStartRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryStartRequestsByKeysException">
               <soap12:fault name="tryStartRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="createAndStartRequestByValue">
           <soap12:operation soapAction="createAndStartRequestByValue" style="document" />
           <wsdl:input name="createAndStartRequestByValueRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createAndStartRequestByValueResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createAndStartRequestByValueException">
               <soap12:fault name="createAndStartRequestByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createAndStartRequestsByValues">
           <soap12:operation soapAction="createAndStartRequestsByValues" style="document" />
           <wsdl:input name="createAndStartRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createAndStartRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createAndStartRequestsByValuesException">
               <soap12:fault name="createAndStartRequestsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateAndStartRequestsByValues">
           <soap12:operation soapAction="tryCreateAndStartRequestsByValues" style="document" />
           <wsdl:input name="tryCreateAndStartRequestsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateAndStartRequestsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateAndStartRequestsByValuesException">
               <soap12:fault name="tryCreateAndStartRequestsByValuesException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="abortRequestByKey">
           <soap12:operation soapAction="abortRequestByKey" style="document" />
           <wsdl:input name="abortRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="abortRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="abortRequestByKeyException">
               <soap12:fault name="abortRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="abortRequestsByKeys">
           <soap12:operation soapAction="abortRequestsByKeys" style="document" />
           <wsdl:input name="abortRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="abortRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="abortRequestsByKeysException">
               <soap12:fault name="abortRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryAbortRequestsByKeys">
           <soap12:operation soapAction="tryAbortRequestsByKeys" style="document" />
           <wsdl:input name="tryAbortRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryAbortRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryAbortRequestsByKeysException">
               <soap12:fault name="tryAbortRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="suspendRequestByKey">
           <soap12:operation soapAction="suspendRequestByKey" style="document" />
           <wsdl:input name="suspendRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="suspendRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="suspendRequestByKeyException">
               <soap12:fault name="suspendRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="suspendRequestsByKeys">
           <soap12:operation soapAction="suspendRequestsByKeys" style="document" />
           <wsdl:input name="suspendRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="suspendRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="suspendRequestsByKeysException">
               <soap12:fault name="suspendRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="trySuspendRequestsByKeys">
           <soap12:operation soapAction="trySuspendRequestsByKeys" style="document" />
           <wsdl:input name="trySuspendRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="trySuspendRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="trySuspendRequestsByKeysException">
               <soap12:fault name="trySuspendRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>
        <wsdl:operation name="resumeRequestByKey">
           <soap12:operation soapAction="resumeRequestByKey" style="document" />
           <wsdl:input name="resumeRequestByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="resumeRequestByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="resumeRequestByKeyException">
               <soap12:fault name="resumeRequestByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="resumeRequestsByKeys">
           <soap12:operation soapAction="resumeRequestsByKeys" style="document" />
           <wsdl:input name="resumeRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="resumeRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="resumeRequestsByKeysException">
               <soap12:fault name="resumeRequestsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryResumeRequestsByKeys">
           <soap12:operation soapAction="tryResumeRequestsByKeys" style="document" />
           <wsdl:input name="tryResumeRequestsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryResumeRequestsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryResumeRequestsByKeysException">
               <soap12:fault name="tryResumeRequestsByKeysException" use="literal" />
           </wsdl:fault>
         </wsdl:operation>

        <!-- Tigerstripe : Bindings relating to BusinessInteractionRelationship -->
        <wsdl:operation name="createBusinessInteractionRelationshipByValue">
           <soap12:operation soapAction="createBusinessInteractionRelationshipByValue" style="document" />
           <wsdl:input name="createBusinessInteractionRelationshipByValueRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createBusinessInteractionRelationshipByValueResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createBusinessInteractionRelationshipByValueException">
               <soap12:fault name="createBusinessInteractionRelationshipByValueException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createBusinessInteractionRelationshipsByValues">
           <soap12:operation soapAction="createBusinessInteractionRelationshipsByValues" style="document" />
           <wsdl:input name="createBusinessInteractionRelationshipsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="createBusinessInteractionRelationshipsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="createBusinessInteractionRelationshipsByValuesException">
               <soap12:fault name="createBusinessInteractionRelationshipsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryCreateBusinessInteractionRelationshipsByValues">
           <soap12:operation soapAction="tryCreateBusinessInteractionRelationshipsByValues" style="document" />
           <wsdl:input name="tryCreateBusinessInteractionRelationshipsByValuesRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryCreateBusinessInteractionRelationshipsByValuesResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryCreateBusinessInteractionRelationshipsByValuesException">
               <soap12:fault name="tryCreateBusinessInteractionRelationshipsByValuesException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipByKey">
           <soap12:operation soapAction="getBusinessInteractionRelationshipByKey" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipByKeyException">
               <soap12:fault name="getBusinessInteractionRelationshipByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByKeys">
           <soap12:operation soapAction="getBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipsByKeysException">
               <soap12:fault name="getBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipsByTemplate">
           <soap12:operation soapAction="getBusinessInteractionRelationshipsByTemplate" style="document" />
           <wsdl:input name="getBusinessInteractionRelationshipsByTemplateRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="getBusinessInteractionRelationshipsByTemplateResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="getBusinessInteractionRelationshipsByTemplateException">
               <soap12:fault name="getBusinessInteractionRelationshipsByTemplateException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipByKey">
           <soap12:operation soapAction="removeBusinessInteractionRelationshipByKey" style="document" />
           <wsdl:input name="removeBusinessInteractionRelationshipByKeyRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeBusinessInteractionRelationshipByKeyResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeBusinessInteractionRelationshipByKeyException">
               <soap12:fault name="removeBusinessInteractionRelationshipByKeyException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeBusinessInteractionRelationshipsByKeys">
           <soap12:operation soapAction="removeBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="removeBusinessInteractionRelationshipsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="removeBusinessInteractionRelationshipsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="removeBusinessInteractionRelationshipsByKeysException">
               <soap12:fault name="removeBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tryRemoveBusinessInteractionRelationshipsByKeys">
           <soap12:operation soapAction="tryRemoveBusinessInteractionRelationshipsByKeys" style="document" />
           <wsdl:input name="tryRemoveBusinessInteractionRelationshipsByKeysRequest">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:input>
           <wsdl:output name="tryRemoveBusinessInteractionRelationshipsByKeysResponse">
               <soap12:body parts="parameters" use="literal" />
           </wsdl:output>
           <wsdl:fault name="tryRemoveBusinessInteractionRelationshipsByKeysException">
               <soap12:fault name="tryRemoveBusinessInteractionRelationshipsByKeysException" use="literal" />
           </wsdl:fault>
        </wsdl:operation>



        <wsdl:operation name="getRequestSpecifications">
            <soap12:operation soapAction="getRequestSpecifications" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestSpecificationsException"> 
                <soap12:fault name ="getRequestSpecificationsException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSupportedEntitySpecificationValues">
            <soap12:operation soapAction="getSupportedEntitySpecificationValues" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getSupportedEntitySpecificationValuesException"> 
                <soap12:fault name ="getSupportedEntitySpecificationValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createRelatedRequestsByValues">
            <soap12:operation soapAction="createRelatedRequestsByValues" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="createRelatedRequestsByValuesException"> 
                <soap12:fault name ="createRelatedRequestsByValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createAndStartRelatedRequestsByValues">
            <soap12:operation soapAction="createAndStartRelatedRequestsByValues" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="createAndStartRelatedRequestsByValuesException"> 
                <soap12:fault name ="createAndStartRelatedRequestsByValuesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestTypes">
            <soap12:operation soapAction="getRequestTypes" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestTypesException"> 
                <soap12:fault name ="getRequestTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRequestSpecificationTypes">
            <soap12:operation soapAction="getRequestSpecificationTypes" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getRequestSpecificationTypesException"> 
                <soap12:fault name ="getRequestSpecificationTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getBusinessInteractionRelationshipTypes">
            <soap12:operation soapAction="getBusinessInteractionRelationshipTypes" style="document" /> 
            <wsdl:input>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap12:body parts="parameters" use="literal" />
            </wsdl:output>
            <wsdl:fault name ="getBusinessInteractionRelationshipTypesException"> 
                <soap12:fault name ="getBusinessInteractionRelationshipTypesException" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>


    <wsdl:service name="AUAI-RequestWS-v2-0">
        <wsdl:port name="JVTOrderManagementSessionWSPort11"
            binding="omWS-v1-0:JVTOrderManagementSessionSoap11Binding">
            <soap:address location="http://localhost:7001/soap/services/AUAI-RequestWS-v2-0" />
        </wsdl:port>
        <wsdl:port name="JVTOrderManagementSessionWSPort12"
            binding="omWS-v1-0:JVTOrderManagementSessionSoap12Binding">
            <soap12:address location="http://localhost:7001/soap/services/AUAI-RequestWS-v2-0" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
