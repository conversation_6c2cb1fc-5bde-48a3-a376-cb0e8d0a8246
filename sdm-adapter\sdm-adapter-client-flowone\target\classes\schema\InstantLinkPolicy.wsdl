<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- 
	Policy that forces use of UserNameToken
	
	Can be used with HTTP or HTTPS
 -->
<definitions targetNamespace="http://soa.comptel.com/2011/02/instantlink"
	name="InstantLinkWebServices" xmlns="http://schemas.xmlsoap.org/wsdl/"
	xmlns:tns="http://soa.comptel.com/2011/02/instantlink" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
	xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
	xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsap10="http://www.w3.org/2006/05/addressing/wsdl"
	xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex"
	xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy"
	xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa10="http://www.w3.org/2005/08/addressing">
	<ns1:Policy xmlns:ns1="http://schemas.xmlsoap.org/ws/2004/09/policy"
		Name="http://soa.comptel.com/2011/02/instantlink/InstantLinkWebServices_policy">
		<ns1:ExactlyOne>
			<ns1:All>
				<ns2:SupportingTokens
					xmlns:ns2="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
					<ns1:Policy>
						<ns1:ExactlyOne>
							<ns1:All>
								<ns2:UsernameToken
									ns2:IncludeToken="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy/IncludeToken/AlwaysToRecipient">
									<ns1:Policy>
										<ns1:ExactlyOne>
											<ns1:All>
												<ns2:WssUsernameToken10 />
											</ns1:All>
										</ns1:ExactlyOne>
									</ns1:Policy>
								</ns2:UsernameToken>
							</ns1:All>
						</ns1:ExactlyOne>						
					</ns1:Policy>
				</ns2:SupportingTokens>
				<!--<ns3:UsingAddressing xmlns:ns3="http://www.w3.org/2006/05/addressing/wsdl" />-->
				<sc:ValidatorConfiguration
					xmlns:sc="http://schemas.sun.com/2006/03/wss/server">
					<sc:Validator name="usernameValidator"
						classname="com.comptel.instantlink.ws.validation.ILWSSecurityPolicyPasswordValidator" />
				</sc:ValidatorConfiguration>
			</ns1:All>
		</ns1:ExactlyOne>
	</ns1:Policy>
	<wsp:Policy Name="http://soa.comptel.com/2011/02/instantlink/InstantLinkWebServices_Input_policy">
		<wsp:ExactlyOne>
			<wsp:All>
				<sp:SignedParts
					xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<sp:Body />
					<sp:Header Name="To" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="From" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="FaultTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="ReplyTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="MessageID" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="RelatesTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="Action" Namespace="http://www.w3.org/2005/08/addressing" />
				</sp:SignedParts>
				<sp:EncryptedParts
					xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<sp:Body />
				</sp:EncryptedParts>
			</wsp:All>
		</wsp:ExactlyOne>
	</wsp:Policy>
	<wsp:Policy Name="http://soa.comptel.com/2011/02/instantlink/InstantLinkWebServices_Output_policy">
		<wsp:ExactlyOne>
			<wsp:All>
				<sp:SignedParts
					xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<sp:Body />
					<sp:Header Name="To" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="From" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="FaultTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="ReplyTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="MessageID" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="RelatesTo" Namespace="http://www.w3.org/2005/08/addressing" />
					<sp:Header Name="Action" Namespace="http://www.w3.org/2005/08/addressing" />
				</sp:SignedParts>
				<sp:EncryptedParts
					xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<sp:Body />
				</sp:EncryptedParts>
			</wsp:All>
		</wsp:ExactlyOne>
	</wsp:Policy>
</definitions>