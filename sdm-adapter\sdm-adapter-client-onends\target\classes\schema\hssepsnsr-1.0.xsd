﻿<?xml version="1.0" encoding="UTF-8"?>
<!--*****************************************************************************-->
<!-- HSSd Schema v1.0                                                            -->
<!-- Extends the SPML V2.0 ProvGw schema                                         -->
<!-- Interface specification document:                                           -->
<!-- HSS Non Subscriber Related Data Provisioning SPML Interface Specification   -->
<!-- P30309-A5694-A294-03-7618                                                   -->
<!--*****************************************************************************-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:nsr="urn:siemens:names:prov:gw:HSS_UNIFIED_NSR:1:0" xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0" targetNamespace="urn:siemens:names:prov:gw:HSS_UNIFIED_NSR:1:0" elementFormDefault="unqualified" attributeFormDefault="unqualified" version="1.0">
    <!--  schema imports -->
    <xs:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
   
    <!-- ******************************************************************** -->
    <!--           Root Elements                                              -->
    <!-- ******************************************************************** -->
    
    <!--************************************************************************-->
    <!--  TYPE SECTION: Defining complex types/enumeration for flat attributes  -->
    <!--************************************************************************-->
    
    <!--************************************************************************-->
    <!--  FIRST CLASS OBJECTS                                                   -->
    <!--************************************************************************-->
   
	
    
    <!--************************************************************************-->
    <!--  EPSQos palceholder is needed for HSS view on it                           -->
    <!--************************************************************************-->
 
	<xsd:complexType name="EPSQos">
        <xsd:annotation>
            <xsd:documentation>EPS Quality Of Service Profile</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="classIdentifier" type="nsr:UnsignedInt8bit" minOccurs="0"/>
                    <xsd:element name="arPrio" type="xsd:unsignedInt" minOccurs="0"/>
                    <xsd:element name="preEmptCapability" type="nsr:PreEmption" minOccurs="0"/>
                    <xsd:element name="preEmptVulnerability" type="nsr:PreEmption" minOccurs="0"/> 
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
	
	<xsd:simpleType name="UnsignedInt8bit">
        <xsd:annotation>
            <xsd:documentation>Unsigned 8 bit integer </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
            <xsd:maxInclusive value="255"></xsd:maxInclusive>
        </xsd:restriction>
    </xsd:simpleType>

     <xsd:simpleType name="PreEmption">
        <xsd:annotation>
            <xsd:documentation>Enumerated type  
               values :
                   ENABLED
                   DISABLED
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="enable"/>
            <xsd:enumeration value="disable"/>
        </xsd:restriction>
    </xsd:simpleType>

	 

     <xsd:element name="PsRoamingArea" type="nsr:PsRoamingArea"/>

     <xs:complexType name="PsRoamingArea">
            <xs:annotation>
                <xs:documentation></xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:FirstClassObject">
                    <xs:sequence>
                                     <xs:element name="isAreaPos" type="xsd:boolean"  minOccurs="0"/>
                                     <xs:element name="vplmnId" type="nsr:NumericString"  minOccurs="0"  maxOccurs="unbounded" />
									 <!-- FC123_107267 / RE123_107240 - Provisionable error responses for roaming tiers -->
									 <xs:element name="refVplmnIdAlternateResultCode" type="xsd:string"  minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>

	<!-- Start FC123_107267 / RE123_107240 - Provisionable error responses for roaming tiers -->
	<xsd:element name="VplmnIdAlternateResultCode" type="nsr:VplmnIdAlternateResultCode"/>
	 <xs:complexType name="VplmnIdAlternateResultCode">
            <xs:annotation>
                <xs:documentation></xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:FirstClassObject">
                    <xs:sequence>
                                     <xs:element name="vplmnIdAlternateResultCodeList" type="nsr:VplmnIdAlternateResultCodeList"  minOccurs="0"  maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
	<xs:complexType name="VplmnIdAlternateResultCodeList">
		<xs:annotation>
			<xs:documentation></xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:SecondClassObject">
				<xs:sequence>
					<xs:element name="vplmnId" type="nsr:NumericString"  minOccurs="0"/>
					<xs:element name="alternativeResultCode" type="xsd:string"  minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- End FC123_107267 / RE123_107240 - Provisionable error responses for roaming tiers -->
		
	<xsd:element name="PsRoamAreaMmeAddr" type="nsr:PsRoamAreaMmeAddr"/>
	
	<xs:complexType name="PsRoamAreaMmeAddr">
	    <xs:annotation>
		<xs:documentation></xs:documentation>
	    </xs:annotation>
	    <xs:complexContent>
		<xs:extension base="spml:FirstClassObject">
		    <xs:sequence>
				     <xs:element name="isAreaPos" type="xsd:boolean"  minOccurs="0"/>
				     <xs:element name="mmeAddress" type="nsr:MMEaddress"  minOccurs="0"  maxOccurs="unbounded" />
		    </xs:sequence>
		</xs:extension>
	    </xs:complexContent>
	</xs:complexType>

	<xs:complexType name="MMEaddress">
		<xs:annotation>
			<xs:documentation></xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:SecondClassObject">
				<xs:sequence>
					<xs:element name="mmeRealm" type="xsd:string"  minOccurs="0"/>
					<xs:element name="mmeHostName" type="xsd:string"  minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

<!-- SCHEMA CHANGES FOR FEATURE LTE_ROAMING_PROFILE START -->
	<!--RoamSubscriptionInfoTable_Schema_Start-->	
<xsd:element name="RoamSubscriptionInfo" type="nsr:RoamSubscriptionInfo" />
<xsd:complexType name="RoamSubscriptionInfo">
  <xsd:annotation>
    <xsd:documentation>
      This object class defines the Roam Subscription Info profile attached to the subscriber.
    </xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
    <xsd:extension base="spml:FirstClassObject">
      <xsd:sequence>
        <xsd:element name="eRoamPlanInfo" type="nsr:ERoamPlanInfo"  maxOccurs="unbounded" minOccurs="0" />
	    <!-- Add for 19.0 RE123_108401 start -->		
        <xsd:element name="refRoamPlan" type="xsd:string" minOccurs="0" />		
        <xsd:element name="refImeisvWildCardList" type="xsd:string"  maxOccurs="unbounded" minOccurs="0" />		
        <!-- Add for 19.0 RE123_108401 end -->  
      </xsd:sequence>
    </xsd:extension>
  </xsd:complexContent>
</xsd:complexType>

<xsd:complexType name="ERoamPlanInfo">
  <xsd:annotation>
    <xsd:documentation>
      This object class maps a Roaming Plan to a particular entity number and type of message.
    </xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
    <xsd:extension base="spml:SecondClassObject">
      <xsd:sequence>
        <xsd:element name="roamPlanInfoId" type="xsd:string"  />
        <xsd:element name="mmeIdentity" type="xsd:string"  minOccurs="0"   />
        <xsd:element name="mmeRealm" type="xsd:string"  minOccurs="0"   />
        <xsd:element name="vplmnId" type="nsr:NumericString"  minOccurs="0"   />
        <xsd:element name="refRoamPlan" type="xsd:string"  minOccurs="0"   />
		<!-- Add for 19.0 RE123_108401 start -->
        <xsd:element name="refImeisvWildCardList" type="xsd:string"  maxOccurs="unbounded" minOccurs="0" />
        <!-- Add for 19.0 RE123_108401 end -->
      </xsd:sequence>
    </xsd:extension>
  </xsd:complexContent>
</xsd:complexType>
	<!--RoamSubscriptionInfoTable_Schema_End-->
<xsd:element name="HssImeisvWildCardList" type="nsr:HssImeisvWildCardList" />
<xsd:complexType name="HssImeisvWildCardList">
  <xsd:annotation>
	<xsd:documentation>
	  This object class defines the IMEISVN List attached to RSI and ERPI.
	</xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
	<xsd:extension base="spml:FirstClassObject">
	  <xsd:sequence>
		<xsd:element name="imeisvWildCard" type="xsd:string"  maxOccurs="unbounded" minOccurs="0" />
	  </xsd:sequence>
	</xsd:extension>
  </xsd:complexContent>
</xsd:complexType>

	<!-- RoamPlanTable_Schema_Start -->
<xsd:element name="RoamPlan" type="nsr:RoamPlan" />
<xsd:complexType name="RoamPlan">
  <xsd:annotation>
    <xsd:documentation>
      This object class defines the Roaming Plan
    </xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
    <xsd:extension base="spml:FirstClassObject">
      <xsd:sequence>
		<xsd:element name="accessRestr" type="nsr:EpsAccessRestriction"  minOccurs="0" maxOccurs="9" />
		<xsd:element name="ardTreatment" type="nsr:ArdTreatment"  minOccurs="0" />
		<xsd:element name="defaultPdnContextId" type="xsd:unsignedInt"  minOccurs="0" />
		<xsd:element name="matchDefaultPdnContextId" type="xsd:unsignedInt"  minOccurs="0" />
		<xsd:element name="defPdnContextIdTreatment" type="nsr:EpsDefContextIdTreatmentTreat"  minOccurs="0" />
		<xsd:element name="maxBandwidthDown" type="xsd:unsignedInt"  minOccurs="0" />
		<xsd:element name="maxBandwidthUp" type="xsd:unsignedInt"  minOccurs="0" />
		<xsd:element name="userAmbrTreatment" type="nsr:EpsUserAmbrTreatmentType"  minOccurs="0" />
		<xsd:element name="apnOIReplacement" type="nsr:PrintableString64"  minOccurs="0" />
		<xsd:element name="matchApnOIReplacement" type="nsr:PrintableString64"  minOccurs="0" />
		<xsd:element name="apnOIReplTreatment" type="nsr:EpsApnOIReplTreatmentType"  minOccurs="0" />
		<xsd:element name="sessionTransferNumber" type="xsd:unsignedLong"  minOccurs="0" />
		<xsd:element name="matchSessionTransferNumber" type="nsr:PrintableString64"  minOccurs="0" />
		<xsd:element name="strTreatment" type="nsr:EpsStrTreatment"  minOccurs="0" />
		<xsd:element name="sessionTimeout" type="xsd:unsignedInt"  minOccurs="0" />
		<xsd:element name="sessionTimeoutTreatment" type="nsr:EpsSessionTimeoutTreatmentType"  minOccurs="0" />
		<xsd:element name="accessAPNEnabled" type="xsd:boolean"  minOccurs="0" />
		<xsd:element name="accessApnTreatment" type="nsr:EpsAccessApnTreatmentType"  minOccurs="0" />
		<xsd:element name="notAllowedRATTypes" type="nsr:EpsNotAllowedRATType"  minOccurs="0" maxOccurs="12" />
		<xsd:element name="notAllowedRATTreatment" type="nsr:EpsNotAllowedRATTreatmentType"  minOccurs="0" />
		<xsd:element name="mipFeatureVector" type="nsr:EpsMipFeatureVector"  minOccurs="0" maxOccurs="4" />
		<xsd:element name="mipFeatureVectorTreatment" type="nsr:EpsMip6VectorTreatmentType"  minOccurs="0" />
		<xsd:element name="rfspIndex" type="nsr:EpsRfspIndexType"  minOccurs="0" />
		<xsd:element name="rfspIndexTreatment" type="nsr:EpsRfspIndexTreatmentType"  minOccurs="0" />
		<xsd:element name="tracingTreatment" type="nsr:EpsTracingTreatmentType"  minOccurs="0" />
		<xsd:element name="refpdnPlanName" type="nsr:PrintableString64"  minOccurs="0"  maxOccurs="unbounded" />
		<xsd:element name="refodbPlanName" type="nsr:PrintableString64"  minOccurs="0"  maxOccurs="unbounded" />
		<xsd:element name="pdpTreatment" type="nsr:PdpTreatmentType" minOccurs="0"  />
		<xsd:element name="refqOfServName" type="xsd:string"  minOccurs="0" />
		<xsd:element name="matchQofServName" type="xsd:string" minOccurs="0"  />
                   <!-- RE123_106100 Enhanced Multimedia priority services (S6a) changes-->
		<xsd:element name="isMPSEnabled" type="xsd:boolean"  minOccurs="0" />
		<xsd:element name="isMPSEnabledTreatment" type="nsr:EpsMpsPriorityTreatmentType"  minOccurs="0" />
		<xsd:element name="refDeviceProfileName" type="nsr:PrintableString64"  minOccurs="0" />
		<xsd:element name="matchDeviceProfileName" type="nsr:PrintableString64"  minOccurs="0" />
		<xsd:element name="deviceProfileTreatment" type="nsr:EpsDeviceProfileTreatmentType"  minOccurs="0" />
        <xsd:element name="icsIndicator" type="xsd:int"  minOccurs="0" />
        <xsd:element name="icsIndicatorTreatment" type="nsr:EpsIcsIndicatorTreatmentType"  minOccurs="0" />
		<!-- BEGIN changes for HSS 18.5: RE123_108362-->
		<xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
		<xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
		<!-- END changes for HSS 18.5: RE123_108362-->
		<xsd:element name="refssPlanName" type="xsd:string" minOccurs="0" maxOccurs="47"/>
		<xsd:element name="coreNetworkRestr" type="nsr:EpsCoreNetworkRestriction" minOccurs="0" maxOccurs="unbounded"/>
        <xsd:element name="cnrTreatment" type="nsr:EpsCnrTreatment"  minOccurs="0" />
      </xsd:sequence>
    </xsd:extension>
  </xsd:complexContent>
</xsd:complexType>

<xsd:simpleType name="EpsAccessRestriction">
        <xsd:annotation>
            <xsd:documentation>EPS access restriction</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="UTRAN"/>
            <xsd:enumeration value="GERAN"/>
            <xsd:enumeration value="GAN"/>
            <xsd:enumeration value="E-UTRAN"/>
            <xsd:enumeration value="I-HSPA-Evolution"/>
            <xsd:enumeration value="HO-To-Non3GPP"/>
			<xsd:enumeration value="NB-IoT"/>
			<xsd:enumeration value="NR-As-Secondary-RAT"/>
			<xsd:enumeration value="NR-In-5GS"/>
        </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="ArdTreatment">
            <xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on Access Restriction.
                    It can take the following values :   ns, induce, suppress.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="induce" />
                    <xsd:enumeration value="suppress" />
            </xsd:restriction>
  </xsd:simpleType>
  
  <xsd:simpleType name="EpsCoreNetworkRestriction">
        <xsd:annotation>
            <xsd:documentation>
                 Enumeration of the Core Network that UE is not allowed to use.
				 1 - EPC Not Allowed
                 2 - 5GC Not Allowed
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
		        <xsd:enumeration value="EPC"/>
                <xsd:enumeration value="5GC"/>
        </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsCnrTreatment">
            <xsd:annotation>
                <xsd:documentation>
                    This attribute indicates the treatment to be applied on Core Network Restriction.
                    It can take the following values :   ns, induce, suppress.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="induce" />
                    <xsd:enumeration value="suppress" />
            </xsd:restriction>
  </xsd:simpleType>
<xsd:simpleType name="PrintableString">
        <xsd:annotation>
            <xsd:documentation>Printable String  with '@' character</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[a-zA-Z\d '()+,-./:=?@_;]+"/>
        </xsd:restriction>
</xsd:simpleType>

<xsd:simpleType name="PrintableString64">
        <xsd:annotation>
            <xsd:documentation>String of length 1 .. 64</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableString">
        <!--xsd:restriction base="xsd:string"-->
            <xsd:maxLength value="64"/>
        </xsd:restriction>
</xsd:simpleType>

<xsd:simpleType name="EpsStrTreatment">
            <xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on session transfer number.
                    It can take the following values :   ns, replace, conditional replace
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="conditionalReplace" />
            </xsd:restriction>
  </xsd:simpleType>

 <xsd:simpleType name="EpsRfspIndexType">
        <xsd:annotation>
            <xsd:documentation>Integer and the range is  1 to 256 </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:int">
            <xsd:minInclusive value="1"/>
            <xsd:maxInclusive value="256"/>
        </xsd:restriction>
 </xsd:simpleType>

  <xsd:simpleType name="EpsRfspIndexTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on rfsp Index.
                    It can take the following values :   ns, replace, induce.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="induce" />
            </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="EpsApnOIReplTreatmentType">
	<xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on APN OI replacement.
                    It can take the following values :   ns, replace, conditional replace
                    </xsd:documentation>
            </xsd:annotation>
			<xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="conditionalReplace" />
            </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="EpsDefContextIdTreatmentTreat">
	<xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on Default PDN Context ID .
                    It can take the following values :   replace, conditional replace
                    </xsd:documentation>
            </xsd:annotation>
	<xsd:restriction base="xsd:string">
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="conditionalReplace" />
            </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="EpsUserAmbrTreatmentType">
        <xsd:annotation>
                <xsd:documentation>
		Treatment to be applied on User AMBR . It can take the possible values : ns, replace, induce
	</xsd:documentation>
         </xsd:annotation>
        <xsd:restriction base="xsd:string">
	<xsd:enumeration value="ns"/>
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
         </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="EpsTracingTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on User AMBR . It can take the possible value : ns.
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
            </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="EpsSessionTimeoutTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on Session Time Out. It can take the possible values : ns, replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
			 <xsd:enumeration value="replace"/>
			 <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="EpsAccessApnTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on APN Acces . It can take the possible values : ns, replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
			 <xsd:enumeration value="replace"/>
			 <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="EpsNotAllowedRATType">
        <xsd:annotation>
            <xsd:documentation>RAT type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="WLAN"/>
            <xsd:enumeration value="UTRAN"/>
            <xsd:enumeration value="GERAN"/>
            <xsd:enumeration value="GAN"/>
            <xsd:enumeration value="HSPA_EVOLUTION"/>
            <xsd:enumeration value="CDMA2000_1X"/>
            <xsd:enumeration value="HRPD"/>
            <xsd:enumeration value="UMB"/>
            <xsd:enumeration value="VIRTUAL"/>
            <xsd:enumeration value="EUTRAN"/>
            <xsd:enumeration value="EHRPD"/>
            <xsd:enumeration value="BBF-WLAN"/>
        </xsd:restriction>
</xsd:simpleType>

<xsd:simpleType name="EpsNotAllowedRATTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on Restriceted RAT types . It can take the possible values : ns, replace, suppress
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
			 <xsd:enumeration value="induce"/>
			 <xsd:enumeration value="suppress"/>
            </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="EpsMipFeatureVector">
        <xsd:annotation>
            <xsd:documentation>EPS Mip Feature Vector</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="mip6_integrated"/>
            <xsd:enumeration value="pmip6_supported"/>
            <xsd:enumeration value="ip4_hoa_supported"/>
            <xsd:enumeration value="assign_local_ip"/>
        </xsd:restriction>
</xsd:simpleType>

<xsd:simpleType name="EpsMip6VectorTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on MIP Feature Vector. It can take the possible values : ns, induce, suppress, replace
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
			 <xsd:enumeration value="induce"/>
			 <xsd:enumeration value="suppress"/>
			 <xsd:enumeration value="replace"/>
            </xsd:restriction>
    </xsd:simpleType>	

  <xsd:simpleType name="EpsDeviceProfileTreatmentType">
	<xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on APN OI replacement.
                    It can take the following values :   ns, replace, conditional replace
                    </xsd:documentation>
            </xsd:annotation>
			<xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="conditionalReplace" />
            </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsIcsIndicatorTreatmentType">
    <xsd:annotation>
                    <xsd:documentation>
                    Treatment to be applied on icsIndicator. It can take the possible values : ns, replace, induce
                    </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns"/>
                    <xsd:enumeration value="replace"/>
                    <xsd:enumeration value="induce"/>
    </xsd:restriction>
  </xsd:simpleType>
	
	<!-- RoamPlanTable_Schema_End -->

  	<!-- ODBPlanTable_Schema_Start -->
<xsd:element name="ODBPlan" type="nsr:ODBPlan" />
<xsd:complexType name="ODBPlan">
  <xsd:annotation>
     <xsd:documentation>
       This object class defines the Operator Determined Barring related treatments.
     </xsd:documentation>
   </xsd:annotation>
   <xsd:complexContent>
     <xsd:extension base="spml:FirstClassObject">
       <xsd:sequence>
         <xsd:element name="odbId" type="nsr:EpsOdbIdType" minOccurs="0"/>
         <xsd:element name="odbTreatment" type="nsr:EpsOdbTreatmentType"  minOccurs="0"/>
         <xsd:element name="odbValue" type="xsd:int" minOccurs="0"/>		  
       </xsd:sequence>
     </xsd:extension>
   </xsd:complexContent>
 </xsd:complexType>

 <xsd:simpleType name="EpsOdbIdType">
            <xsd:annotation>
                    <xsd:documentation>
                            Treatment to be applied on ODB.  It can take one of the possible values: BAROAM, POACCESSEPS, ODBROAMGPRS 
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="BAROAM"/>
                    <xsd:enumeration value="POACCESSEPS"/>
                    <xsd:enumeration value="ODBROAMGPRS"/>
					<xsd:enumeration value="ODBEUTRAN"/>
					<xsd:enumeration value="ODBWLAN"/>
            </xsd:restriction>
    </xsd:simpleType>
 
 <xsd:simpleType name="EpsOdbTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>					
                            Treatment to be applied on ODB.  It can take one of the possible values: induce                           
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="induce"/>                    
            </xsd:restriction>
    </xsd:simpleType>

	<!-- ODBPlanTable_Schema_End -->
	
	<!-- EPSPDNPlanTable_Schema_Start -->   
	<xsd:element name="epsPDNPlan" type="nsr:EPSPDNPlan" />

<xsd:complexType name="EPSPDNPlan">
  <xsd:annotation>
     <xsd:documentation>
       This object class defines the PDN Context plan and related treatments.
     </xsd:documentation>
   </xsd:annotation>
   <xsd:complexContent>
     <xsd:extension base="spml:FirstClassObject">
       <xsd:sequence>
         <xsd:element name="apn" type="nsr:PrintableStringAPN255" minOccurs="0"/>
         <xsd:element name="type" type="nsr:EpsPdnType" minOccurs="0"/>
         <xsd:element name="typeTreatment" type="nsr:EpsPdnTypeTreatmentType" minOccurs="0"/>
	 <xsd:element name="ipv4Address" type="nsr:IPv4" minOccurs="0"/>
         <xsd:element name="ipv6Address" type="nsr:IPv6" minOccurs="0"/>
         <xsd:element name="pdnAddressTreatment" type="nsr:EpsPdnAddressTreatmentType" minOccurs="0"/>
         <xsd:element name="pdnGwDynamicAllocation" type="xsd:boolean" minOccurs="0"/>
         <xsd:element name="pdnGwDynamicAllocTreatment" type="nsr:EpsPdnGwAllocTypeTreatmentType" minOccurs="0"/>
         <xsd:element name="vplmnAddressAllowed" type="xsd:boolean" minOccurs="0"/>
         <xsd:element name="vplmnAddrAllowedTreatment" type="nsr:EpsVplmnAddrAllowedTreatmentType" minOccurs="0"/>
         <xsd:element name="maxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
         <xsd:element name="maxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
         <xsd:element name="apnAmbrTreatment" type="nsr:EpsApnAmbrTreatmentType" minOccurs="0"/>
         <xsd:element name="qos" type="nsr:PrintableString255" minOccurs="0"/>
         <xsd:element name="matchQos" type="nsr:PrintableString255" minOccurs="0"/>
         <xsd:element name="qosTreatment" type="nsr:EpsQosTreatmentType" minOccurs="0"/>
         <xsd:element name="pdnGwHostName" type="nsr:PrintableString255" minOccurs="0"/>
         <xsd:element name="pdnGwRealm" type="nsr:PrintableString255" minOccurs="0"/>
         <xsd:element name="pdnGwIPV4" type="nsr:IPv4" minOccurs="0"/>
         <xsd:element name="pdnGwIPV6" type="nsr:IPv6" minOccurs="0"/>
         <xsd:element name="pdnGwIdentityTreatment" type="nsr:EpsPdnGwIdentityTreatmentType" minOccurs="0"/>
         <xsd:element name="pdnContextBlocking" type="nsr:EpsPdnContextBlocking" minOccurs="0"/>
         <xsd:element name="pdnContextBlockingTreatment" type="nsr:EpsPdnContextBlockingTreatmentType" minOccurs="0"/>
	 <xsd:element name="lipaPermission" type="nsr:EpsLipaPermissionType" minOccurs="0"/>
	 <xsd:element name="lipaPermissionTreatment" type="nsr:EpsLipaPermissionTreatmentType" minOccurs="0"/>
         <xsd:element name="restorationPriority" type="xsd:unsignedInt" minOccurs="0"/>
  	 <xsd:element name="restorationPriorityTreatment" type="nsr:EpsRestorationPriorityTreatmentType" minOccurs="0"/>
         <xsd:element name="apnOIReplacement" type="nsr:PrintableString64"  minOccurs="0" />
         <xsd:element name="matchApnOIReplacement" type="nsr:PrintableString64"  minOccurs="0" />
         <xsd:element name="apnOIReplTreatment" type="nsr:EpsApnOIReplTreatmentType"  minOccurs="0" />
		 <!-- BEGIN changes for HSS 18.5: RE123_108362-->
		 <xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
		 <xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
		 <!-- END changes for HSS 18.5: RE123_108362-->
	</xsd:sequence>
     </xsd:extension>
   </xsd:complexContent>
 </xsd:complexType>

 <xsd:simpleType name="PrintableStringAPN255">
        <xsd:annotation>
            <xsd:documentation>Printable String APN of length 1 .. 255</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableStringAPN">
            <xsd:minLength value="1"/>
            <xsd:maxLength value="255"/>
        </xsd:restriction>
    </xsd:simpleType>


  <xsd:simpleType name="PrintableStringAPN">
        <xsd:annotation>
            <xsd:documentation>Printable String  with '@' and '*' character</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <!--xsd:pattern value="\*|[a-zA-Z\d '()+,-./:=?@_]+"/-->
            <xsd:pattern value="\*|\\\*|[a-zA-Z\d '()+,-./:=?@_\*]+"/>
        </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="PrintableString255">
        <xsd:annotation>
            <xsd:documentation>Printable String of length 1 .. 255</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableString">
            <xsd:minLength value="1"/>
            <xsd:maxLength value="255"/>
        </xsd:restriction>
    </xsd:simpleType>
	
 <xsd:simpleType name="PrintableStringGeneric255">
        <xsd:annotation>
            <xsd:documentation>Printable String of max length 255</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableStringGeneric">
            <xsd:maxLength value="255"/>
        </xsd:restriction>
    </xsd:simpleType>

     <xsd:simpleType name="PrintableStringGeneric">
        <xsd:annotation>
            <xsd:documentation>Printable String with special char *</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
	    <xsd:pattern value="\*|\\\*|[a-zA-Z\d '()+,-./:=?@\*]*"/>
        </xsd:restriction>
    </xsd:simpleType>	

 <xsd:simpleType name="EpsPdnAddressTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
						Treatment to be applied on PDN Address.  It can take one of the possible value : ns						
					</xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns"/>                    
            </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="EpsQosTreatmentType">
		<xsd:annotation>
                    <xsd:documentation>
						Treatment to be applied on QOS NSR value.  It can take one of the possible values: ns, replace, conditional replace						
					</xsd:documentation>
            </xsd:annotation>
			<xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns"/>
                    <xsd:enumeration value="replace"/>
                    <xsd:enumeration value="conditionalReplace"/>
            </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="EpsVplmnAddrAllowedTreatmentType">
	<xsd:annotation>
                    <xsd:documentation>
					Treatment to be applied on vplmnAllowed. It can take the possible values : ns, replace, induce
					</xsd:documentation>
            </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="EpsApnAmbrTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on APN AMBR. It can take the possible values : ns, replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="ns"/>
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

<xsd:complexType name="EpsPdnContextBlocking">
        <xsd:annotation>
            <xsd:documentation>
                This attribute indicates whether the PDN context subscription is blocked or not for a
                specific access type (eUTRAN/S6a or eHPPD/SWx). It consists of 8 bits. Currently only
                the first two bits are used. Bit 1 denotes whether the APN is blocked via the S6a
                interface, bit 2 denotes whether the APN is blocked via the SWx interface. The
                remaining bits are for future use.
                Possible values:
                Bit 1 value 0: APN is not blocked over S6a
                Bit 1 value 1: APN is blocked over S6a
                Bit 2 value 0: APN is not blocked over SWx
                Bit 2 value 1: APN is blocked over SWx
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="flagAPNBlockedOverS6a" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagAPNBlockedOverSWx" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="flagReserved2" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagReserved3" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagReserved4" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagReserved5" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagReserved6" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagReserved7" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
</xsd:complexType>
	
	
	<xsd:simpleType name="EpsPdnContextBlockingTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on PDN CONTEXT BLOCKING. It can take the possible values :  replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">			 
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="EpsPdnType">
        <xsd:annotation>
            <xsd:documentation>EPS Pdn type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ipv4"/>
            <xsd:enumeration value="ipv6"/>
            <xsd:enumeration value="both"/>
            <xsd:enumeration value="ipv4oripv6"/>
</xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="EpsPdnTypeTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				treatment to be applied on PDN Type . It can take the possible values :  replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="EpsPdnGwAllocTypeTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				treatment to be applied on PDN Type . It can take the possible values : replace, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
             <xsd:enumeration value="replace"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="IPv4">
        <xsd:annotation>
            <xsd:documentation>IPv4 address
                Example:  ***********
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="IPv6">
        <xsd:annotation>
            <xsd:documentation>IPv6 address
                Examples:
                4FDE:0000:0000:0002:0022:F376:FF3B:AB3F
                10FB:0:0:0:C:ABC:1F0C:44DA
                0:0:0:0:0:0:0:1
                10FB::C:ABC:1F0C:44DA
                FD01::1F
                ::1
                ::
                ::ffff:************
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <!-- normal full format -->
            <xsd:pattern value="([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}"/>
            <!-- full format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
            <xsd:pattern value="(([0-9A-Fa-f]{1,4}:){6})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>

            <!-- compressed format -->
            <xsd:pattern value="::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,6})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,5})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){1})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,4})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,3})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,2})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,1})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){5})::(([0-9A-Fa-f]{1,4})?)"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){6})::"/>
	    <!-- compressed format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
            <xsd:pattern value="::(([0-9A-Fa-f]{1,4}:){0,5})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}:){0,4})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}))::(([0-9A-Fa-f]{1,4}:){0,3})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}:){0,2})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}:){0,1})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
        </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="IPv4orIPv6">
   <xsd:annotation>
       <xsd:documentation>IPv4 or IPv6 address </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!-- normal full format -->
      <xsd:pattern value="([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}"/>
      <!-- full format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
       <xsd:pattern value="(([0-9A-Fa-f]{1,4}:){6})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <!-- compressed format -->
       <xsd:pattern value="::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,6})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,5})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){1})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,4})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,3})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,2})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,1})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){5})::(([0-9A-Fa-f]{1,4})?)"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){6})::"/>
       <!-- compressed format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
       <xsd:pattern value="::(([0-9A-Fa-f]{1,4}:){0,5})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}:){0,4})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}))::(([0-9A-Fa-f]{1,4}:){0,3})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}:){0,2})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}:){0,1})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
       <xsd:pattern value="([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])"/>
    </xsd:restriction>
 </xsd:simpleType>

	<xsd:simpleType name="EpsPdnGwIdentityTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on  . It can take the possible values : ns, induce
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
             <xsd:enumeration value="ns"/>
             <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>
	<!-- EPSPDNPlanTable_Schema_End -->

<!-- SCHEMA CHANGES FOR FEATURE LTE_ROAMING_PROFILE END -->
<!-- SCHEMA CHANGES FOR FEATURE LOCAL IP ACCESS START -->
<!-- HplmnList_Schema_Start -->

<xsd:complexType name="HplmnList">
  <xsd:annotation>
    <xsd:documentation>
     List of HPLMN.
    </xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
    <xsd:extension base="spml:FirstClassObject">
      <xsd:sequence>
        <xsd:element name="hplmnId" type="nsr:NumericString" minOccurs="0"  maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:extension>
  </xsd:complexContent>
</xsd:complexType>
<xsd:simpleType name="NumericString">
        <xsd:annotation>
            <xsd:documentation>Only digits allowed</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="\d*"/>
        </xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="NumericString3">
        <xsd:annotation>
            <xsd:documentation>Numeric String of length 1..3</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:NumericString">
            <xsd:maxLength value="3"/>
        </xsd:restriction>
</xsd:simpleType>  
<xsd:simpleType name="NumericString5">
        <xsd:annotation>
            <xsd:documentation>Numeric String of length 1..5</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:NumericString">
            <xsd:maxLength value="5"/>
        </xsd:restriction>
</xsd:simpleType>
  <!-- FC123_107815_Self_Activation :: Start -->
  <xs:simpleType name="NumericString1_15">
    <xs:annotation>
      <xs:documentation>Numeric String of length 1..15</xs:documentation>
    </xs:annotation>
    <xs:restriction base="nsr:NumericString">
      <xs:minLength value="1"/>
      <xs:maxLength value="15"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC123_107815_Self_Activation :: End -->
<!-- HplmnList_Schema_End -->
<!-- LipaAllowedVplmnList_Schema_Start -->

<xsd:complexType name="LipaAllowedVplmnList">
  <xsd:annotation>
    <xsd:documentation>
     List of VPLMN.
    </xsd:documentation>
  </xsd:annotation>
  <xsd:complexContent>
    <xsd:extension base="spml:FirstClassObject">
      <xsd:sequence>
        <xsd:element name="vplmnId" type="nsr:NumericString" minOccurs="0"  maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:extension>
  </xsd:complexContent>
</xsd:complexType>
<!-- LipaAllowedVplmnList_Schema_End -->
   <xsd:simpleType name="EpsLipaPermissionTreatmentType">
	<xsd:annotation>
            <xsd:documentation>
				Treatment to be applied on  . It can take the possible values :ns,replace,induce
			</xsd:documentation>
    </xsd:annotation>
	     <xsd:restriction base="xsd:string">
             <xsd:enumeration value="ns"/>
             <xsd:enumeration value="replace"/>
	      <xsd:enumeration value="induce"/>
            </xsd:restriction>
    </xsd:simpleType>

   <xsd:simpleType name="EpsLipaPermissionType">
	<xsd:annotation>
            <xsd:documentation>
				values for lipa Permission  . It can take the possible values :LIPA-PROHIBITED,LIPA-ONLY,LIPA-CONDITIONAL
	     </xsd:documentation>
    </xsd:annotation>
	     <xsd:restriction base="xsd:string">
              <xsd:enumeration value="LIPA-PROHIBITED"/>
             <xsd:enumeration value="LIPA-ONLY"/>
	      <xsd:enumeration value="LIPA-CONDITIONAL"/>
            </xsd:restriction>
    </xsd:simpleType>

<!-- SCHEMA CHANGES FOR FEATURE LOCAL IP ACCESS END -->

   <!-- Definition of Reset Trigger Info start -->
   <xs:simpleType name="ResetTriggerInterface">
        <xs:annotation>
            <xs:documentation>Definition of enumeration for S6 trigger peer</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
             <xs:enumeration value="MME"/>
             <xs:enumeration value="SGSN"/>
			 <xs:enumeration value="AAA"/>
        </xs:restriction>
   </xs:simpleType>

   <xs:complexType name="ResetTriggerInfo">
        <xs:annotation>
            <xs:documentation>Definition of ResetTriggerInfo info class</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
               <xs:sequence>
                  <!-- IMSI or IMSI prefix serves as an identifier -->
                  <xs:element name="interfaceId" type="nsr:ResetTriggerInterface" minOccurs="0"/>
                  <xs:element name="serverIdentity" type="xsd:string" minOccurs="0"/>
                  <xs:element name="serverRealm" type="xsd:string" minOccurs="0"/>
               </xs:sequence>
            </xs:extension>
        </xs:complexContent>
   </xs:complexType>
   <!-- Definition of Reset Trigger Info end -->

   <!-- Definition of Device Profile start -->
   <xs:complexType name="DeviceProfile">
        <xs:annotation>
            <xs:documentation>Definition of DeviceProfile class</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
               <xs:sequence>
                  <!-- deviceProfileName as an identifier -->
                  <xs:element name="rauTauTimer" type="xsd:unsignedInt" minOccurs="0"/>
				  <xs:element name="lowMaxDetectTime" type="xsd:unsignedInt" minOccurs="0"/>
				  <xs:element name="highMaxDetectTime" type="xsd:unsignedInt" minOccurs="0"/>
				  <xs:element name="lowMaxLatency" type="xsd:unsignedInt" minOccurs="0"/>
				  <xs:element name="highMaxLatency" type="xsd:unsignedInt" minOccurs="0"/>
               </xs:sequence>
            </xs:extension>
        </xs:complexContent>
   </xs:complexType>
   <!-- Definition of Device Profile end -->

   	<!-- START OF S6D FEATURE SCHEMA CHANGES-->
	
    <!-- *************************************************************** -->
    <!--                      Quality Of Service Profile                 -->
    <!-- *************************************************************** -->
    <!-- Original attribute: QOFSERV -->
    <xsd:complexType name="QualityOfServiceProfile">
        <xsd:annotation>
            <xsd:documentation>
            Quality Of Service Profile contains attributes
            which determine the degree of satisfaction of a user of the service.
             </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <!-- Profile name is modeled as FCO identifier -->
                <xsd:sequence>
                    <xsd:element name="allocationRetentionPriority" type="nsr:AllocationRetentionPriority" minOccurs="0"/>
                    <xsd:element name="delayClass" type="nsr:DelayClass" minOccurs="0"/>
                    <xsd:element name="reliabilityClass" type="nsr:ReliabilityClass" minOccurs="0"/>
                    <xsd:element name="peakThroughput" type="nsr:PeakThroughput" minOccurs="0"/>
                    <xsd:element name="precedenceClass" type="nsr:PrecedenceClass" minOccurs="0"/>
                    <xsd:element name="meanThroughput" type="nsr:MeanThroughput" minOccurs="0"/>
                    <xsd:element name="trafficClass" type="nsr:TrafficClass" minOccurs="0"/>

                    <!-- This attribute specifies whether the Delivery Order should be maintained or not. -->
                    <!-- (Original attribute: deliverOrder: false = 0 / true = 1)  -->
                    <xsd:element name="deliveryOrder" type="xsd:boolean" minOccurs="0"/>

                    <xsd:element name="deliveryOfErrorneousDataUnit" type="nsr:DeliveryOfErrorneousDataUnit" minOccurs="0"/>
                    <xsd:element name="maximumDataUnitSize" type="nsr:MaximumDataUnitSize" minOccurs="0"/>
                    <xsd:element name="maximumBitRateForUpLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="maximumBitRateForDownLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="extendedMaximumBitRateForDownLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <!-- HLR 4.5 extendedMaximumBitRateForUpLink -->
                    <xsd:element name="extendedMaximumBitRateForUpLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="residualBitErrorRate" type="nsr:ResidualBitErrorRate" minOccurs="0"/>
                    <xsd:element name="dataUnitErrorRatio" type="nsr:DataUnitErrorRatio" minOccurs="0"/>
                    <xsd:element name="transferDelay" type="nsr:TransferDelay" minOccurs="0"/>
                    <xsd:element name="trafficHandlingPriority" type="nsr:TrafficHandlingPriority" minOccurs="0"/>
                    <xsd:element name="guaranteedBitRateForUpLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="guaranteedBitRateForDownLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="extendedGuaranteedBitRateForDownLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="extendedGuaranteedBitRateForUpLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    
                    <!-- This attribute specifies whether the service is optimised for signalling traffic or not
                                            Signalling Indication, octet 14 (see 3GPP TS 23.107)  for communication between MS and network.
                                             If set to '1' the QoS of the PDP context is optimised for signalling 
                                            This value is ignored if the Traffic Class is Conversational Streaming or Background class. -->
                    <!-- (Original attribute: signalingIndication: false = 0 / true = 1)  -->
                    <xsd:element name="optimisedForSignallingTraffic" type="xsd:boolean" default="false" minOccurs="0"/>

                    <xsd:element name="sourceStatisticsDescriptor" type="nsr:SourceStatisticsDescriptor" default="unknown" minOccurs="0"/>
					<!--RBL-1479 Evolved ARP in QOS Profile begin-->
                    <xsd:element name="eARPLevel" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="eARPPreEmptCapability" type="nsr:PreEmption" minOccurs="0"/>
                    <xsd:element name="eARPPreEmptVulnerability" type="nsr:PreEmption" minOccurs="0"/>
                    <!--RBL-1479 Evolved ARP in QOS Profile end-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

	    <!-- Original attribute: allocRetPrio-->
    <xsd:simpleType name="AllocationRetentionPriority">
        <xsd:annotation>
        <xsd:documentation>
        Allocation Retention Priority
        Value Range: 0..255
        </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: delayClass
         Original Values: 
                  LOW   = 1  Low delay
                  NORMAL = 2      Normal delay
                  HIGH   = 3  High delay
                  BESTEFRT = 4  Best effort -->
    <xsd:simpleType name="DelayClass">
        <xsd:annotation>
            <xsd:documentation>Delay Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="low"/>
            <xsd:enumeration value="normal"/>
            <xsd:enumeration value="high"/>
            <xsd:enumeration value="bestEffort"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: reliabClass
         Original Values: 
            RCL1 = 1   Non real-time traffic, error sensitive application that cannot cope with data loss
            RCL2 = 2   Non real-time traffic, error sensitive application that can cope with infrequent data loss
            RCL3 = 3   Non real-time traffic, error sensitive application that can cope with data loss, GMM/SM
            RCL4 = 4   Real-time traffic, error sensitive application that can cope with data loss
            RCL5 = 5   Real-time traffic, error non-sensitive application that can cope with data loss -->
    <xsd:simpleType name="ReliabilityClass">
        <xsd:annotation>
            <xsd:documentation>Reliability Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="nonRealTimeCanNotCopeWithDataLoss"/>
            <xsd:enumeration value="nonRealTimeCanCopeWithInfrequentDataLoss"/>
            <xsd:enumeration value="nonRealTimeCanCopeWithDataLoss"/>
            <xsd:enumeration value="realTimeErrorSensitive"/>
            <xsd:enumeration value="realTimeErrorNonSensitive"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: peakThrput
         Original Values: 
                  P8K = 1             8  KBIT/S           P16K = 2          16 KBIT/S
                  P32K = 3           32 KBIT/S          P64K = 4           64 KBIT/S
                  P128K = 5        128 KBIT/S         P256K = 6         256 KBIT/S
                  P512K = 7         512 KBIT/S        P1024K = 8       1024 KBIT/S
                  P2048K = 9       2048 KBIT/S -->
    <xsd:simpleType name="PeakThroughput">
        <xsd:annotation>
            <xsd:documentation>Peak Throughput [kbit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="8"/>
            <xsd:enumeration value="16"/>
            <xsd:enumeration value="32"/>
            <xsd:enumeration value="64"/>
            <xsd:enumeration value="128"/>
            <xsd:enumeration value="256"/>
            <xsd:enumeration value="512"/>
            <xsd:enumeration value="1024"/>
            <xsd:enumeration value="2048"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: precedClass
         Original Values: 
              HIGH = 1         HIGH PRECEDENCE
                                     Precedence before services with precedence NORMAL and LOW
              NORMAL = 2   NORMAL PRECEDENCE
                                     Precedence before services with precedence LOW
              LOW = 3         LOW PRECEDENCE -->
    <xsd:simpleType name="PrecedenceClass">
        <xsd:annotation>
            <xsd:documentation>Precedence Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="high"/>
            <xsd:enumeration value="normal"/>
            <xsd:enumeration value="low"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: meanThrput
         Original Values: 
            BESTEFRT = 31    Best effort
            M02 = 1      0.22 bit/s        M04 = 2       0.44 bit/s
            M1 = 3       1.11 bit/s        M2 = 4            2.2 bit/s
            M4 = 5       4.4 bit/s          M11 = 6      11 bit/s
            M22 = 7      22 bit/s           M44 = 8      44 bit/s
            M111 = 9     111 bit/s         M222 = 10     222 bit/s
            M444 = 11    444 bit/s         M1K = 12      1 kbit/s
            M2K = 13     2 kbit/s           M4K = 14         4 kbit/s
            M11K = 15    11 kbit/s         M22K = 16     22 kbit/s
            M44K = 17    44 kbit/s         M111K = 18   111 kbit/s -->
    <xsd:simpleType name="MeanThroughput">
        <xsd:annotation>
            <xsd:documentation>Mean Throughput Class [bit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="0.22"/>
            <xsd:enumeration value="0.44"/>
            <xsd:enumeration value="1.11"/>
            <xsd:enumeration value="2.2"/>
            <xsd:enumeration value="4.4"/>
            <xsd:enumeration value="11"/>
            <xsd:enumeration value="22"/>
            <xsd:enumeration value="44"/>
            <xsd:enumeration value="111"/>
            <xsd:enumeration value="222"/>
            <xsd:enumeration value="444"/>
            <xsd:enumeration value="1k"/>
            <xsd:enumeration value="2k"/>
            <xsd:enumeration value="4k"/>
            <xsd:enumeration value="11k"/>
            <xsd:enumeration value="22k"/>
            <xsd:enumeration value="44k"/>
            <xsd:enumeration value="111k"/>
            <xsd:enumeration value="bestEffort"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: trafficClass
         Original Values: 
         CONVCL =1  -> CONVERSATIONAL CLASS
         STREAMCL = 2   -> STREAMING CLASS
         INTACTCL = 3   -> INTERACTIVE CLASS
         BACKGRCL = 4   -> BACKGROUND CLASS -->
    <xsd:simpleType name="TrafficClass">
        <xsd:annotation>
            <xsd:documentation>
            Traffic Class
            Values:
              conversational
                     Real time applications defined for UMTS (e.g. telephony speech). 
                     With Internet and multimedia a number of new application will use this scheme:
                     e.g. voice over IP, video conferencing tools
               streaming
                    Services like real time video (audio) which are one way transports
                    and very sensible to time variation between information entities (packages).
               interactive
                    Non-real time applications expecting messages (responses) within a certain time.
               background
                    Applications like receiving files, e-mails or SMS in the background,
                    in which the destination is not expecting the data within a certain time.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="conversational"/>
            <xsd:enumeration value="streaming"/>
            <xsd:enumeration value="interactive"/>
            <xsd:enumeration value="background"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: deliverESDU
         Original Values: 
              NODETECT = 1   NOT DETECTED
              DLV = 2              DELIVERED
              NODLV = 3          NOT DELIVERED -->
    <xsd:simpleType name="DeliveryOfErrorneousDataUnit">
        <xsd:annotation>
            <xsd:documentation>
            Delivery Of Errorneous service Data Unit
            Values:
               notDetected    Erroneous SDUs are not detected
               delivered         Erroneous SDUs are delivered
               notDelivered   Erroneous SDUs are not delivered
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notDetected"/>
            <xsd:enumeration value="delivered"/>
            <xsd:enumeration value="notDelivered"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: maxSduSize-->
    <xsd:simpleType name="MaximumDataUnitSize">
        <xsd:annotation>
        <xsd:documentation>
            Maximum service Data Unit Size
            Values: Only steps of 10 are allowed. Above 1500 the values 1502, 1510 and 1520 are allowed only.
        </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attributes: maxBrUplnk, maxBrDwnlnk, guarBrDwnlnk, guarBrUplnk -->
    <xsd:simpleType name="BitRate">
        <xsd:annotation>
            <xsd:documentation>
            Bit Rate
            Values: Range of decimal number 1..8640 is allowed.
                        1   .. 63   : steps of 1
                       64  .. 568  : steps of 8
                      576 .. 8640 : steps of 64
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
        </xsd:restriction>
    </xsd:simpleType>

    <!-- Original attributes: extMaxBrDwnlnk, extGuarBrDwnlnk -->
    <xsd:simpleType name="ExtendedBitRate">
        <xsd:annotation>
        <xsd:documentation>
            Extended Bit Rate
            Range of decimal number allowed: 
            8700 kbps – 16 Mbps,in steps of 100kbps increments
            The follwing ranges are allowed only if feature HSDPAPlusSupportInNTHLR is released (See feature: HSDPAPlusSupportInNTHLRin CONFIGURATION DATA):
            17 Mbps – 128 Mbps, in steps of 1 Mbps increments
            130 Mbps – 256Mbps, in steps of 2 Mbps increments
        </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: residualBer
         Original Values: 
             R5EXP2N = 1    5*10 Exponential -2             R1EXP2N = 2    1*10 Exponential -2
             R5EXP3N = 3    5*10 Exponential -3             R4EXP3N = 4    4*10 Exponential -3
             R1EXP3N = 5    1*10 Exponential -3             R1EXP4N = 6    1*10 Exponential -4
             R1EXP5N = 7    1*10 Exponential -5             R1EXP6N = 8    1*10 Exponential -6
             R6EXP8N= 9     6*10 Exponential -8 -->
    <xsd:simpleType name="ResidualBitErrorRate">
        <xsd:annotation>
            <xsd:documentation>
            Residual Bit Error Rate of service
            Values:
                5Exp-2     5*10 Exponential -2                 5Exp-3    5*10 Exponential -3
                4Exp-3    4*10 Exponential -3                  6Exp-8     6*10 Exponential -8
                1Exp-2     1*10 Exponential -2                 1Exp-3    1*10 Exponential -3
                1Exp-4    1*10 Exponential -4                  1Exp-5    1*10 Exponential -5
                1Exp-6    1*10 Exponential -6 
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="5Exp-2"/>
            <xsd:enumeration value="5Exp-3"/>
            <xsd:enumeration value="4Exp-3"/>
            <xsd:enumeration value="6Exp-8"/>
            <xsd:enumeration value="1Exp-2"/>
            <xsd:enumeration value="1Exp-3"/>
            <xsd:enumeration value="1Exp-4"/>
            <xsd:enumeration value="1Exp-5"/>
            <xsd:enumeration value="1Exp-6"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: sduErrRatio
         Original Values: 
            R1EXP2N = 1   1*10 Exponential -2            R7EXP3N = 2   7*10 Exponential -3
            R1EXP3N = 3   1*10 Exponential -3            R1EXP4N = 4   1*10 Exponential -4
            R1EXP5N = 5   1*10 Exponential -5            R1EXP6N = 6   1*10 Exponential -6
            R1EXP1N = 7   1*10 Exponential -1 -->
    <xsd:simpleType name="DataUnitErrorRatio">
        <xsd:annotation>
            <xsd:documentation>
             service Data Unit  Error Ratio
            Values:
                 7Exp-3   7*10 Exponential -3
                 1Exp-1   1*10 Exponential -1                 1Exp-2   1*10 Exponential -2
                 1Exp-3   1*10 Exponential -3                 1Exp-4   1*10 Exponential -4
                 1Exp-5   1*10 Exponential -5                 1Exp-6   1*10 Exponential -6
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="7Exp-3"/>
            <xsd:enumeration value="1Exp-1"/>
            <xsd:enumeration value="1Exp-2"/>
            <xsd:enumeration value="1Exp-3"/>
            <xsd:enumeration value="1Exp-4"/>
            <xsd:enumeration value="1Exp-5"/>
            <xsd:enumeration value="1Exp-6"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: transfDelay -->
    <xsd:simpleType name="TransferDelay">
        <xsd:annotation>
            <xsd:documentation>
            Transfer Delay
            Values: Range of decimal number 10..4000 is allowed.
                        10   .. 150  : steps of  10
                      200  .. 950  : steps of  50
                    1000 .. 4000 : steps of  100
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: trfHndlgPrio
         Original Values: 
            PRIO1 = 1      Traffic priority 1
            PRIO2 = 2    Traffic priority 2
            PRIO3 = 3    Traffic priority 3 -->
    <xsd:simpleType name="TrafficHandlingPriority">
        <xsd:annotation>
            <xsd:documentation>Traffic Handling Priority</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="priority1"/>
            <xsd:enumeration value="priority2"/>
            <xsd:enumeration value="priority3"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <!-- Original attribute: sourceStatDescriptor
         Original Values: 
              UNKNOWN == 0
              SPEECH == 1 -->
    <xsd:simpleType name="SourceStatisticsDescriptor">
        <xsd:annotation>
            <xsd:documentation>
            Source Statistics Descriptor (see 3GPP TS 23.107)
             The Source Statistics Descriptor value is ignored if the Traffic Class is Interactive or Background. 
             </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="unknown"/>
            <xsd:enumeration value="speech"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="PdpTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
             This attribute defines the treatment for the GPRS PDP data.
                The possible values are :
                35- ns
                37- replace
                39- Conditional Replace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="replace"/>
            <xsd:enumeration value="conditionalReplace"/>
        </xsd:restriction>
    </xsd:simpleType>

	
	<!-- END OF S6D FEATURE SCHEMA CHANGES-->
	<!-- RE123_106100 Enhanced Multimedia priority services (S6a) changes-->

 <xsd:simpleType name="EpsMpsPriorityTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                    This attribute indicates the treatment to be applied on mpsPriority.
                    It can take the following values :   ns, induce.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="ns" />
                    <xsd:enumeration value="induce" />
            </xsd:restriction>
  </xsd:simpleType>


	<xsd:simpleType name="EpsRestorationPriorityTreatmentType">
	   <xsd:annotation>
	          <xsd:documentation>
		  Treatment to be applied on  . It can take the possible values :ns,induce
		  </xsd:documentation>
	  </xsd:annotation>
	  <xsd:restriction base="xsd:string">
	        <xsd:enumeration value="ns"/>
		<xsd:enumeration value="induce"/>
	  </xsd:restriction>
	</xsd:simpleType>
	
	<!-- RE123_106765_MTC_interface_enhancements_for_HSS -->
	<xsd:element name="ScsGroup" type="nsr:ScsGroup"/>

    <xs:complexType name="ScsGroup">
		<xs:annotation>
			<xs:documentation></xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="isWhitelist" type="xsd:boolean"  minOccurs="0"/>
					<xs:element name="scsIdentity" type="nsr:EPSHexadecimalString"  minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>
	
	<xsd:simpleType name="EPSHexadecimalString">
        <xsd:annotation>
            <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Fa-f0-9]+"/>
        </xsd:restriction>
    </xsd:simpleType>
	<!-- End -->
	
	<xsd:simpleType name="EpsPdnOnDemandType">
				<xsd:annotation>
						<xsd:documentation>
						0: On-Demand PDN is not allowed
						1: On-Demand PDN type 1
						2: On-Demand PDN type 2
						3: On-Demand PDN type 3
						4: On-Demand PDN type 4
						</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:string">
						<xsd:enumeration value="NOT_ALLOWED" />
						<xsd:enumeration value="TYPE_ONE" />
						<xsd:enumeration value="TYPE_TWO" />
						<xsd:enumeration value="TYPE_THREE" />
						<xsd:enumeration value="TYPE_FOUR" />
				</xsd:restriction>
	  </xsd:simpleType>	
<!-- RE123_106764_Cost_reduction_by_optimized_HSS_for_MTC_devices -->
	<xsd:element name="HssSharedProfile" type="nsr:HssSharedProfile"/>

    <xs:complexType name="HssSharedProfile">
		<xs:annotation>
			<xs:documentation>contains the shared PS, EPS data </xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="hssLastModTimestamp" type="xsd:dateTime" minOccurs="0"/>
					<xs:element name="imsiActive" type="xsd:boolean"  minOccurs="0"/>
					<xs:element name="expiryDate" type="xs:date" minOccurs="0"/>
					<xs:element name="nwa" type="nsr:NetworkAccessMode"  minOccurs="0"/>
					<xsd:element name="odbBaroam" type="nsr:OdbBarringRoam" minOccurs="0"/>
					<xs:element name="obGprs" type="nsr:OdbPOAccess" minOccurs="0"/>
					<xs:element name="generalChargingCharacteristics" type="nsr:GeneralChargingCharacteristics" minOccurs="0"/>
					<xsd:element name="accessRestr" type="nsr:EpsAccessRestriction" minOccurs="0" maxOccurs="9"/>
					<xsd:element name="hssRealm" type="xsd:string" minOccurs="0"/>
					<xsd:element name="refPsRoamAreaName" type="xsd:string" minOccurs="0"/>
					<xsd:element name="refPsRoamAreaMmeName" type="xsd:string" minOccurs="0"/>
					<xsd:element name="refHplmnListName" type="nsr:PrintableString255" minOccurs="0"/>
					<xsd:element name="defaultPdnContextId" type="xsd:unsignedInt" minOccurs="0"/>
					<xsd:element name="refHSSPdnContextList" type="nsr:PrintableString255" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="maxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
                    			<xsd:element name="maxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
					<xsd:element name="rfspIndex" type="nsr:EpsRfspIndexType"  minOccurs="0" /> 
					<xsd:element name="isMPSEnabled" type="xsd:boolean" minOccurs="0"/>					
					<xsd:element name="refDeviceProfileName" type="nsr:PrintableString64" minOccurs="0"/>
					<xsd:element name="pdnOnDemand" type="nsr:EpsPdnOnDemandType" minOccurs="0"/>
                    <xsd:element name="refRoamSubscriptionInfoName" type="nsr:PrintableString64" minOccurs="0"/>
                    <xsd:element name="refLipaAllowedVplmnListName" type="nsr:PrintableString64" minOccurs="0"/>
					<xsd:element name="hssAdditionalServices" type="nsr:HssAdditionalServices" minOccurs="0"/>
					<xsd:element name="epsAAAData" type="nsr:EpsAAAData" minOccurs="0"/>
					<xsd:element name="epsPsRszi" type="nsr:EPSPsRszi" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="userAmbrTreatment" type="nsr:EpsSUPUserAmbrTreatmentType" minOccurs="0"/>
			        <xsd:element name="epsCsg" type="nsr:EPSCsg" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="defaultNonIpPdnContextId" type="xsd:unsignedInt" minOccurs="0"/>
					<xsd:element name="hssSharedIoTServiceData" type="nsr:HssSharedIoTServiceData" minOccurs="0"/>
					<xsd:element name="initialMsisdnDigits" type="nsr:NumericString1_15" minOccurs="0"/> <!-- FC123_107815_Self_Activation -->
					<!-- Start : RE123_107782  ODB EPS -->
					<xsd:element name="odbEutran" type="nsr:EpsBarringType" minOccurs="0"/>
					<xsd:element name="odbWlan" type="nsr:EpsBarringType" minOccurs="0"/>
					<!-- End : RE123_107782  ODB EPS -->
					<!-- Start : FRS123_108213_SMS_Delivery_Feature -->
					<xsd:element name="subscribedForSMSInMME" type="xsd:boolean" minOccurs="0"/>
					<!-- End : FRS123_108213_SMS_Delivery_Feature -->
					<!-- BEGIN changes for HSS 18.5: RE123_108362-->
					<xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
					<xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
					<!-- END changes for HSS 18.5: RE123_108362-->
					<!-- BEGIN RE123_109046_FC123_109664 Subscription based Aerial UE identification support EPS -->
					<xsd:element name="aerialUeSubscriptionInfo" type="xsd:unsignedInt" minOccurs="0"/>
					<!-- End RE123_109046_FC123_109664 Subscription based Aerial UE identification support EPS -->
                    <xsd:element name="coreNetworkRestr" type="nsr:EpsCoreNetworkRestriction" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>
	
	<xsd:simpleType name="EpsBarringType">
      	 <xsd:annotation>
            <xsd:documentation>
				Enumeration of valid values for type of allowed barring
				0 NONE
				1 VPLMN
				2 ALLPLMN
			</xsd:documentation>
       	 </xsd:annotation>
       	 <xsd:restriction base="xsd:string">
           	<xsd:enumeration value="NONE"/>
			<xsd:enumeration value="VPLMN"/>
           	<xsd:enumeration value="ALLPLMN"/>
       	 </xsd:restriction>
	</xsd:simpleType>
	
	<xs:complexType name="HssSharedIoTServiceData">
        <xs:annotation>
            <xs:documentation>
                  Contains shared HSS HLR advanced service Data.
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xsd:element name="subscriptionDataFlags" type="nsr:BitString32" minOccurs="0"/>
					<xsd:element name="ueUsageType" type="xsd:int" minOccurs="0" />
					<xs:element name="sharedImsiGroupId" type="nsr:SharedImsiGroupId" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="extDownLinkDataBuffering" type="xsd:int" minOccurs="0" />
					<xsd:element name="hssServiceGapTimer" type="xsd:unsignedInt" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
	</xs:complexType>
	
	<xsd:simpleType name="EnhancedBitString32">
        <xsd:annotation>
            <xsd:documentation>Keep the former 30 bits are always 0</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:BitString32">
            <xsd:pattern value="[0]{30}[(01)(11)(10)][(01)(11)(10)]"/>
        </xsd:restriction>
    </xsd:simpleType>	
	
	<xsd:simpleType name="BitString32">
        <xsd:annotation>
            <xsd:documentation>Bit string with length 32 characters exactly</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:BitString">
            <xsd:minLength value="32"/>
            <xsd:maxLength value="32"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="BitString">
        <xsd:annotation>
            <xsd:documentation>Only 0|1 is allowed</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="(0|1)+"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xs:complexType name="SharedImsiGroupId">
        <xs:annotation>
            <xs:documentation>
                  Contains IMSI-GROUP-ID related information .
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="imsiGroupIdName" type="xsd:string" minOccurs="0" />
					<xsd:element name="groupServiceId" type="xsd:int" minOccurs="0"/>
                    <xs:element name="groupPLMNId" type="nsr:NumericString" minOccurs="0"/> 
					<xs:element name="localGroupId" type="nsr:HexadecimalString" minOccurs="0"/>
					<xs:element name="localGroupIdBits" type="nsr:HssLocalGroupIdBits" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
	<xs:complexType name="HssLocalGroupIdBits">
        <xs:annotation>
            <xs:documentation>
                  Contains LOCAL-GROUP-ID related information .
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="sgsRegRequired" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="disableSgsReg" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="attachWithoutPDN" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="sgsLiteInterface" type="xsd:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
	<xsd:simpleType name="HexadecimalString">
        <xsd:annotation>
            <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Fa-f0-9]*"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="EpsSUPUserAmbrTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                  This attribute indicates the treatment to be applied on Maximum Aggregated MBR. It is only applicable if userMaxReqBwUL and userMaxReqBwDL are available in this object.
                    It can take the following values :   overwrite, replace, maximum.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="overwrite"/>
                    <xsd:enumeration value="replace"/>
                    <xsd:enumeration value="maximum"/>
            </xsd:restriction>
  </xsd:simpleType>
	
	<!-- RE123_106765_MTC_interface_enhancements_for_HSS - start - Step 2 -->
    <xs:complexType name="RefScsGroupName">
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="refScsGroupName" type="nsr:PrintableString128" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- RE123_106765_MTC_interface_enhancements_for_HSS - end - Step 2 -->
	 <xs:complexType name="HssAdditionalServices">
        <xs:annotation>
            <xs:documentation>
                  Contains shared data which are not relevant for S6a, S6d or SWx but for additional services as for example S6m and SLh .
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <!-- RE123_106765_MTC_interface_enhancements_for_HSS - start - Step 2 -->
                    <xsd:element name="refScsGroupName" type="nsr:RefScsGroupName" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- RE123_106765_MTC_interface_enhancements_for_HSS - end - Step 2 -->
		    <xsd:element name="deviceTriggerAllowed" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="refHssPprId" type="xsd:int" minOccurs="0"/>
		    <xs:element name="monteTypeUeAllowed" type="nsr:MonteTypeUeAllowed" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xsd:simpleType name="MonteTypeUeAllowed">
        <xsd:annotation>
            <xsd:documentation>Types of Allowed Monitoring Events for a subscriber</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="LOSS_OF_CONNECTIVITY"/>
            <xsd:enumeration value="UE_REACHABILITY"/>
            <xsd:enumeration value="LOCATION_REPORTING"/>
            <xsd:enumeration value="CHANGE_OF_IMSI_IMEI(SV)_ASSOCIATION"/>
            <xsd:enumeration value="ROAMING_STATUS"/>
            <xsd:enumeration value="COMMUNICATION_FAILURE"/>
            <xsd:enumeration value="AVAILABILITY_AFTER_DDN_FAILURE"/>
            <xsd:enumeration value="UE_REACHABILITY_AND_IDLE_STATUS_INDICATION"/>
            <xsd:enumeration value="AVAILABILITY_AFTER_DDN_FAILURE_AND_IDLE_STATUS_INDICATION"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xs:complexType name="EpsAAAData">
		<xs:annotation>
			<xs:documentation>This class contains the shared data which are specific for SWx interface towards 3GPP AAA</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:SecondClassObject">
				<xs:sequence>
					<xs:element name="accessAPNEnabled" type="xsd:boolean"  minOccurs="0"/> 
					<xs:element name="rejectReq" type="nsr:RejectRequestType" minOccurs="0"  maxOccurs="4"/> 
					<xs:element name="notAllowedRATTypes" type="nsr:EpsNotAllowedRATType" minOccurs="0" maxOccurs="12"/>
					<xs:element name="mip6Vector" type="nsr:EpsMipFeatureVector"  minOccurs="0" maxOccurs="4" />
					<xs:element name="chargingGatewayFunctionHost" type="xsd:string"  minOccurs="0" />
					<xs:element name="chargingGroupId" type="xsd:string"  minOccurs="0" />
					<xs:element name="proxyCSCFAddress" type="xsd:string"  minOccurs="0" />
					<xs:element name="idleTimeout" type="xsd:unsignedInt"  minOccurs="0" />
					<xs:element name="authLifetime" type="xsd:unsignedInt"  minOccurs="0" />
					<xs:element name="authGracePeriod" type="xsd:unsignedInt"  minOccurs="0" />
					<xs:element name="sessionTimeout" type="xsd:unsignedInt"  minOccurs="0" />
					<xs:element name="nemoSupported" type="xsd:boolean"  minOccurs="0" />
					<xs:element name="framedPool" type="xsd:string"  minOccurs="0" />
					<xs:element name="framedIPv6Pool" type="xsd:string"  minOccurs="0" />
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

    <xsd:complexType name="EPSPsRszi">
        <xsd:annotation>
            <xsd:documentation>EPS PS RSZI Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                            <xs:element name="mcc" type="nsr:NumericString3" minOccurs="0"/>
                            <xs:element name="mnc" type="nsr:NumericString3" minOccurs="0"/>
                            <xs:element name="zoneCode" type="nsr:NumericString5" minOccurs="0" maxOccurs="10" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="EPSCsg">
        <xsd:annotation>
            <xsd:documentation>EPS CSG Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="mcc" type="nsr:NumericString3" minOccurs="0"/>
					<xsd:element name="mnc" type="nsr:NumericString3" minOccurs="0"/>
                    <xsd:element name="csgId" type="nsr:EPSCsgId" minOccurs="0" maxOccurs="5"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

	<xs:complexType name="EPSCsgId">
        <xs:annotation>
             <xsd:documentation>CSG Identifier is string. Note: It is modelled as SCO to allow  modification of  individual value(s)</xsd:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="csgId" type="xsd:string" minOccurs="0"/>
					<xs:element name="pdnAccPointNames" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xsd:element name="HssSharedPDNContext" type="nsr:HssSharedPDNContext"/>
	
	 <xs:complexType name="HssSharedPDNContext">
        <xs:annotation>
            <xs:documentation>Shared PDN Context</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                        <xs:element name="hssLastModTimestamp" type="xsd:dateTime" minOccurs="0"/>
			<xs:element name="serviceSelection" type="xsd:string" minOccurs="0"/>
			<xs:element name="contextId" type="xsd:unsignedInt" minOccurs="0"/>
			<xs:element name="type" type="nsr:EpsPdnType" minOccurs="0"/>
			<xs:element name="pdnGw" type="nsr:PrintableString255" minOccurs="0"/>
			<xs:element name="pdnGwRealm" type="nsr:PrintableString255" minOccurs="0"/>
			<xs:element name="pdnGwIPv4" type="nsr:IPv4" minOccurs="0"/>
			<xs:element name="pdnGwIPv6" type="nsr:IPv6" minOccurs="0"/>
			<xs:element name="vplmnAddressAllowed" type="xsd:boolean" minOccurs="0"/>
			<xs:element name="maxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
			<xs:element name="maxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
			<xs:element name="qos" type="nsr:PrintableString255" minOccurs="0"/>
			<xs:element name="pdnChargingCharacteristics" type="nsr:PdnChargingCharacteristics" minOccurs="0"/>
			<xs:element name="pdnContextBlocking" type="nsr:EpsPdnContextBlocking" minOccurs="0"/>
			<xs:element name="lipaPermission" type="nsr:EpsLipaPermissionType" minOccurs="0"/>
			<xs:element name="apnOIReplacement" type="nsr:PrintableString64" minOccurs="0"/>
			<xs:element name="restorationPriority" type="xsd:unsignedInt" minOccurs="0"/>
			<xs:element name="virtualApnName" type="nsr:PrintableString255" minOccurs="0"/>
			<xs:element name="idleTimeout" type="xsd:unsignedInt"  minOccurs="0" />
			<xs:element name="authLifetime" type="xsd:unsignedInt"  minOccurs="0" />
			<xs:element name="authGracePeriod" type="xsd:unsignedInt"  minOccurs="0" />
			<xs:element name="sessionTimeout" type="xsd:unsignedInt"  minOccurs="0" />
			<xs:element name="framedPool" type="xsd:string"  minOccurs="0" />
			<xs:element name="framedIPv6Pool" type="xsd:string"  minOccurs="0" />
			<xs:element name="pdnContextTreatment" type="nsr:EpsPdnContextTreatmentType" minOccurs="0"/>
			<xs:element name="serviceSelectionTreatment" type="nsr:EpsServiceSelectionTreatmentType" minOccurs="0"/>
			<xs:element name="pdnContextIdTreatment" type="nsr:EpsPdnContextIdTreatmentType" minOccurs="0"/>
			<xs:element name="apnAmbrTreatment" type="nsr:EpsSPCApnAmbrTreatmentType" minOccurs="0"/>
			<xsd:element name="refHssEnterpriseName" type="xsd:string" minOccurs="0"/>
			<xs:element name="nonIpPdnTypeIndicator" type="xsd:boolean" minOccurs="0"/>
            <xs:element name="nonIpDataDeliveryMech" type="nsr:NonIpDataDeliveryMechType"  minOccurs="0"/>
            <xs:element name="scefIdentity" type="nsr:PrintableString255"  minOccurs="0"/>
            <xsd:element name="scefRealm" type="nsr:PrintableString255" minOccurs="0"/>
			<!-- RE123_107456_Local_Mobility_Anchor - start -->
          	<xsd:element name="ipMobilitySupport" type="nsr:HexadecimalString" minOccurs="0"/>
          	<!-- RE123_107456_Local_Mobility_Anchor - End -->
			<!-- BEGIN changes for HSS 18.5: RE123_108362-->
			<xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
			<xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
			<!-- END changes for HSS 18.5: RE123_108362-->
            <!-- BEGIN changes for HSS 19.0: RE123_108233-->
            <xsd:element name="preferredDataMode" type="nsr:EnhancedBitString32" minOccurs="0"/>
            <!-- END changes for HSS 19.0: RE123_108233-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
      </xs:complexType>
	  
	  <xsd:simpleType name="EpsPdnContextTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                  This attribute indicates the treatment to be applied on this shared PDN Context if the PDN is not available in shared profile.
                    It can take the following values :   add, overwrite.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
					<xsd:enumeration value="add" />
                    <xsd:enumeration value="overwrite" />                    
            </xsd:restriction>
  </xsd:simpleType>
  
  <xsd:simpleType name="EpsServiceSelectionTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                  Defines how to determine PDN Context Id during PDN context aggregation
                    It can take the following values :   overwrite, replace.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="overwrite" />
                    <xsd:enumeration value="replace" />
            </xsd:restriction>
  </xsd:simpleType>
  
  <xsd:simpleType name="EpsPdnContextIdTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                  Defines how to determine PDN Context Id during PDN context aggregation. 
                    It can take the following values :   overwrite, replace.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="overwrite" />
                    <xsd:enumeration value="replace" />
            </xsd:restriction>
  </xsd:simpleType>
  
  <xsd:simpleType name="EpsSPCApnAmbrTreatmentType">
            <xsd:annotation>
                    <xsd:documentation>
                 This attribute indicates the treatment to be applied on Maximum Aggregated MBR. It is only applicable if userMaxReqBwUL and userMaxReqBwDL are available in this object.
                    It can take the following values :   overwrite, replace, maximum.
                    </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="overwrite" />
                    <xsd:enumeration value="replace" />
                    <xsd:enumeration value="maximum" />
            </xsd:restriction>
  </xsd:simpleType>
    
	<xs:complexType name="PdnChargingCharacteristics">
       <xs:annotation>
           <xs:documentation>
                   PDN Charging characteristics. 
                   Mapping requires distinguising of these two types.
               </xs:documentation>
       </xs:annotation>
       <xs:complexContent>
           <xs:extension base="nsr:GeneralChargingCharacteristics"/>
       </xs:complexContent>
   </xs:complexType>   

   <xs:complexType name="GeneralChargingCharacteristics">
        <xs:annotation>
            <xs:documentation>
                  General Charging characteristics.
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="chargingCharacteristics" type="nsr:ChargingCharacteristics" minOccurs="0" maxOccurs="4"/>
                    <xs:element name="chargingCharacteristicsProfile" type="xsd:int" minOccurs="0"/>
                    <xs:element name="chargingCharacteristicsBehavior" type="xsd:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType> 

	<xsd:simpleType name="RejectRequestType">
	<xsd:annotation>
            <xsd:documentation>
				This parameter contains a list of SWx requests which shall not be accepted.
			</xsd:documentation>
    </xsd:annotation>
			<xsd:restriction base="xsd:string">
			 <xsd:enumeration value="MAR"/>
			 <xsd:enumeration value="SAR_REG"/>
			 <xsd:enumeration value="SAR_AAA_UDR"/>
			 <xsd:enumeration value="SAR_PGW_UPDATE"/>
            </xsd:restriction>
    </xsd:simpleType>
	
		<xsd:simpleType name="OdbBarringRoam">
        <xsd:annotation>
            <xsd:documentation>
                Operator Determined Barring for Restriction of roaming .
                0 NONE    No barring of roaming
                1 ROAMOH  roaming outside the home PLMN is restricted.
                2 ROAMOHC roaming outside the home PLMN country is restricted.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NONE"/>
            <xsd:enumeration value="ROAMOH"/>
            <xsd:enumeration value="ROAMOHC"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="NetworkAccessMode">
        <xsd:annotation>
            <xsd:documentation>
                Operator Determined Barring for Restriction of roaming .
                0 NONE    No barring of roaming
                1 ROAMOH  roaming outside the home PLMN is restricted.
                2 ROAMOHC roaming outside the home PLMN country is restricted.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NONE"/>
            <xsd:enumeration value="GSM"/>
            <xsd:enumeration value="GPRS"/>
			<xsd:enumeration value="GSMGPRS"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="OdbPOAccess">
        <xsd:annotation>
            <xsd:documentation>
                Operator Determined Barring for Restriction of roaming .
                0 NONE    No barring of roaming
                1 ROAMOH  roaming outside the home PLMN is restricted.
                2 ROAMOHC roaming outside the home PLMN country is restricted.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NONE"/>
            <xsd:enumeration value="ALLPOS"/>
            <xsd:enumeration value="HPLMNAP"/>
			<xsd:enumeration value="VPLMNAP"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xs:simpleType name="ChargingCharacteristics">
		<xs:annotation>
			<xs:documentation>
                This attribute specifies allowed charging characteristics. This attribute only accepts a single value
                entry.
            </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="none"/>
			<xs:enumeration value="normal"/>
			<xs:enumeration value="prepaid"/>
			<xs:enumeration value="flatRate"/>
			<xs:enumeration value="hotBilling"/>
		</xs:restriction>
	</xs:simpleType>
	
	<xsd:simpleType name="PrintableString128">
        <xsd:annotation>
            <xsd:documentation>Printable String of length 1 .. 128</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableString">
            <xsd:maxLength value="128"/>
        </xsd:restriction>
    </xsd:simpleType>

	
	<!-- End -->
 <!--************************************************************************-->
    <!--  HSS PrivacyProfileRegister palceholder is needed for HSS view on it   -->
    <!--************************************************************************-->

        <!-- RE123_106498 Support of 3GPP SLh Interface for Location Services -->

    <xsd:element name="PrivacyProfileRegister" type="nsr:PrivacyProfileRegister"/>
        <xsd:complexType name="PrivacyProfileRegister">
 	   <xsd:annotation>
              <xsd:documentation>HSS PrivacyProfileRegister ADDRESS</xsd:documentation>
	    </xsd:annotation>
            <xsd:complexContent>
	        <xsd:extension base="spml:FirstClassObject">
        	     <xsd:sequence>
                	 <xsd:element name="ipAddress" type="nsr:IPv4orIPv6" minOccurs="0"/>
	             </xsd:sequence>
        	</xsd:extension>
	    </xsd:complexContent>
        </xsd:complexType>
	
	<xs:complexType name="SendResetProfile">
	        <xs:annotation>
	           <xs:documentation>
	                Profile of MME/S6d SGSN for which reset is required.
	           </xs:documentation>
	        </xs:annotation>
	        <xs:complexContent>
	                <xs:extension base="spml:FirstClassObject">
	                  <xs:sequence>
	                    <xs:element name="resetId" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
	                    <xs:element name="refClientInfoList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
	                    <xs:element name="hssRealm" type="xsd:string" minOccurs="0" />
	                    <xs:element name="hssHostIds" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
	                    <xs:element name="sendReset" type="xsd:boolean" minOccurs="0"/>
	                  </xs:sequence>
	                </xs:extension>
	         </xs:complexContent>
	</xs:complexType>
		
	<xs:complexType name="S6aS6dClientInfo">
	        <xs:annotation>
	           <xs:documentation>
	                Client info of S6a MME and S6d SGSN.
	           </xs:documentation>
	        </xs:annotation>
	        <xs:complexContent>
	                <xs:extension base="spml:FirstClassObject">
	                  <xs:sequence>
	                    <xs:element name="hostId" type="xsd:string" minOccurs="0" />
	                    <xs:element name="realm" type="xsd:string" minOccurs="0"/>
	                    <xs:element name="resetSupport" type="xsd:boolean" minOccurs="0" />
	                  </xs:sequence>
	                </xs:extension>
	         </xs:complexContent>
	</xs:complexType>

	<xsd:element name="EpsPDNToSPCMapping" type="nsr:EpsPDNToSPCMapping"/>
	 <xs:complexType name="EpsPDNToSPCMapping">
		<xs:annotation>
			<xs:documentation>holds per “PDN Access Point Name” from individual PDN context the name of the shared PDN context for the Default SUP. </xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="refHssPdnContextName" type="xsd:string"  minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>
	
	<xsd:element name="EpsSgsnMccMnc" type="nsr:EpsSgsnMccMnc"/>
	<xs:complexType name="EpsSgsnMccMnc">
		<xs:annotation>
			<xs:documentation>holds Mcc Mnc data for corresponding sgsnNumbers</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="sgsnNumberEnd" type="nsr:NumericString" minOccurs="0"/>
					<xs:element name="mccMnc" type="xsd:int" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>
	
	 <xsd:element name="EpsEnterpriseData" type="nsr:EpsEnterpriseData"/>
	 <xs:complexType name="EpsEnterpriseData">
		<xs:annotation>
		<xs:documentation>Holds the enterprise data</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="enterpriseAaaApn" type="nsr:EnterpriseAaaApn" minOccurs="0"/>
					<xs:element name="enterpriseAaaMissingParams" type="nsr:EnterpriseAaaMissingParams" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="EnterpriseAaaApn">
	            <xs:annotation>
	                <xs:documentation/>
	            </xs:annotation>
	            <xs:restriction base="xsd:string">
	                <xs:enumeration value="apnNotAssosiatedWithEAaa"/>
	                <xs:enumeration value="apnAssosiatedWithEAaa"/>
					<xs:enumeration value="apnProxyToS6bMpnProxy"/>
	            </xs:restriction>
    </xs:simpleType>

	<xs:simpleType name="EnterpriseAaaMissingParams">
	            <xs:annotation>
	                <xs:documentation/>
	            </xs:annotation>
	            <xs:restriction base="xsd:string">
	                <xs:enumeration value="eAaaParametersNotRequired"/>
	                <xs:enumeration value="eAaaParametersRequired"/>
	            </xs:restriction>
    </xs:simpleType>
	<xsd:simpleType name="NonIpDataDeliveryMechType">
	    <xsd:annotation>
		    <xsd:documentation>
			    0: SGi-BASED-DATA-DELIVERY
				1: SCEF-BASED-DATA-DELIVERY
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SGi-BASED-DATA-DELIVERY" />
            <xsd:enumeration value="SCEF-BASED-DATA-DELIVERY" />
        </xsd:restriction>
    </xsd:simpleType>

	<!-- RE123_107772 SCEF Support-->
	<xsd:element name="EpsIoTIdentityList" type="nsr:EpsIoTIdentityList"/>
	<xs:complexType name="EpsIoTIdentityList">
		<xs:annotation>
			<xs:documentation>Generic NSR, currently holds a list of allowed/forbidden group of Service Capability Exposure Functions(SCEFs).In future it can be reused for new NSR's</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="groupType" type="nsr:GroupType" minOccurs="0"/>
					<xs:element name="isWhitelist" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="identity" type="nsr:PrintableString255" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
    </xs:complexType>
	<xs:simpleType name="GroupType">
	            <xs:annotation>
	                <xs:documentation>GroupType here used is SCEF-MONTE-Group. SCEF for Monitoring Event Configurations, later it can be extended.</xs:documentation>
	            </xs:annotation>
	            <xs:restriction base="xsd:string">
	                <xs:enumeration value="SCEF-MONTE-Group"/>
	            </xs:restriction>
    </xs:simpleType>
	<!-- END RE123_107772 SCEF Support-->
	
	<!-- RE123_107980-IMEI-Based_APN_Enable-Disable :: Started by Ted -->
        <xsd:element name="HssAPNProperties" type="nsr:HssAPNProperties"  />
        <xsd:complexType name="HssAPNProperties">
                <xsd:annotation>
                        <xsd:documentation>
                        APN objects hold global properties like IMEI-based filtering
                        </xsd:documentation>
                </xsd:annotation>
                <xsd:complexContent>
                <xsd:extension base="spml:FirstClassObject">
                        <xsd:sequence>
                                <xsd:element name="blockAPN" type="nsr:HssBlockAPN"  maxOccurs="unbounded" minOccurs="0"/>
                                <xsd:element name="refAPNProperties" type="xsd:string"  minOccurs="0"/>
                        </xsd:sequence>
                </xsd:extension>
                </xsd:complexContent>
        </xsd:complexType>

        <xsd:complexType name="HssBlockAPN">
                <xsd:annotation>
                        <xsd:documentation>
                        IMEI-based filter criteria for APN blocking
                        </xsd:documentation>
                </xsd:annotation>
                <xsd:complexContent>
                        <xsd:extension base="spml:SecondClassObject">
                                <xsd:sequence>
                                        <xsd:element name="blockDevices" type="xsd:string"/>
                                        <xsd:element name="imeiFrom" type="nsr:NumericString"  minOccurs="0"   />
                                        <xsd:element name="imeiTo" type="nsr:NumericString"  minOccurs="0"   />
                                        <xsd:element name="meidFrom" type="nsr:EPSHexadecimalString" minOccurs="0"   />
                                        <xsd:element name="meidTo" type="nsr:EPSHexadecimalString"  minOccurs="0"   />
                                        <xsd:element name="blockProtocol" type= "nsr:EpsAPNBlockProtocol" minOccurs="0"/>
                                </xsd:sequence>
                        </xsd:extension>
                </xsd:complexContent>
        </xsd:complexType>

		<xsd:complexType name="EpsAPNBlockProtocol">
        <xsd:annotation>
            <xsd:documentation>
                This attribute indicates whether the PDN context subscription is blocked or not for a
                specific access type (eUTRAN/S6a or eHPPD/SWx). It consists of 8 bits. Currently only
                the first two bits are used. Bit 1 denotes whether the APN is blocked via the S6a
                interface, bit 2 denotes whether the APN is blocked via the SWx interface. The
                remaining bits are for future use.
                Possible values:
                Bit 1 value 0: APN is not blocked over S6a
                Bit 1 value 1: APN is blocked over S6a
                Bit 2 value 0: APN is not blocked over SWx
                Bit 2 value 1: APN is blocked over SWx
                Bit 3 value 0: APN is not blocked over S6d
                Bit 3 value 1: APN is blocked over S6d
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="flagAPNBlockedOverS6a" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagAPNBlockedOverSWx" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="flagAPNBlockedOverS6d" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
	</xsd:complexType>

        <!-- RE123_107980-IMEI-Based_APN_Enable-Disable :: End -->

  </xs:schema>
