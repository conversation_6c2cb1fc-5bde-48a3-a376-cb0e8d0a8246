<!-- Home Location Register, Subscriber Profile -->
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" jaxb:extensionBindingPrefixes="xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/hlrla_types.xsd" />
	<xs:element name="profile" type="profileType" />
	<xs:element name="frontendid" type="frontendidType" />
	<xs:element name="PrimaryHLRId" type="primaryhlridType" />

	<xs:element name="GetSubscriberProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SubscriberProfileData" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="profile" type="profileType" />
							<xs:element name="sud" type="sudType" maxOccurs="unbounded" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- SetSubscriberProfile MOId: profile MOType: SubscriberProfile@http://schemas.ericsson.se/pg/hlr/13.5/ -->
	<xs:element name="SetSubscriberProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="sud" type="sudType" />
				<xs:element name="frontendid" type="frontendidType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="profile" type="profileType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="profileAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>