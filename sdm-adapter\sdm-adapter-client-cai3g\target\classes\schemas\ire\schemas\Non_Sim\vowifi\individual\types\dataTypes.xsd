<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/nonSIM/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/nonSIM/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{6,16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="devRealmType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="128"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="impiType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="256"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="apnType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="passwdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="15"/>
			<xs:maxLength value="256"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="enable"/>
			<xs:enumeration value="disable"/>
			<xs:enumeration value="reset"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="csrType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="certType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="certIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="certExpireTimeType">
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>
	<xs:simpleType name="transactionLogIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="apnsTokenType">
		<xs:restriction base="xs:string">
            <xs:maxLength value="256"/>
        </xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="serviceNameType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
    <xs:simpleType name="statusType">
		<xs:restriction base="xs:string">
            <xs:enumeration value="enable"/>
			<xs:enumeration value="disable"/>
        </xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="sipUsernameType">
		<xs:restriction base="xs:string">
            <xs:maxLength value="256"/>
        </xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="identityType">
		<xs:restriction base="xs:string">
            <xs:maxLength value="16"/>
        </xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="locationType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="1024"/>
        </xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="ackTimestampType">
        <xs:restriction base="xs:long"/>
	</xs:simpleType>
</xs:schema>
