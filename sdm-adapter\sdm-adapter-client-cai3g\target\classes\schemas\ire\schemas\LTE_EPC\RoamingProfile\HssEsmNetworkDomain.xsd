<!-- Customer Adaptation, NumberSeriesAnalysis -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:ns="http://schemas.ericsson.com/ma/HSS/"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
           targetNamespace="http://schemas.ericsson.com/ma/HSS/"
           elementFormDefault="qualified" attributeFormDefault="unqualified"
           jaxb:version="2.0">
    <xs:include schemaLocation="./types/rp_types.xsd"/>

    <!-- CAI3G MOId type definitions. -->
    <xs:element name="EsmNetworkDomainId" type="EsmNetworkDomainIdType"/>
    <xs:element name="EsmRaid" type="EsmRaidType"/>
    <xs:element name="frontendid" type="hlrfeidType"/>

    <xs:element name="CreateHssEsmNetworkDomain">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmNetworkDomainId" type="EsmNetworkDomainIdType"/>
                <xs:element name="EsmRaid" type="EsmRaidType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="EsmNetworkDomainId" type="EsmNetworkDomainIdType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmNetworkDomainId attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="EsmRaid" type="EsmRaidType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmRaid attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetHssEsmNetworkDomain">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmNetworkDomainId" type="EsmNetworkDomainIdType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="HssEsmNetworkDomainData">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ENDData" type="ENDDataType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ENDDataType">
        <xs:sequence>
            <xs:element name="EsmNetworkDomain" type="EsmNetworkDomainIdType" minOccurs="0"/>
            <xs:element name="EsmRaid" type="EsmRaidType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>


    <xs:element name="DeleteHssEsmNetworkDomain">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmNetworkDomainId" type="EsmNetworkDomainIdType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="EsmNetworkDomainId" type="EsmNetworkDomainIdType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmNetworkDomainId attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

</xs:schema>
