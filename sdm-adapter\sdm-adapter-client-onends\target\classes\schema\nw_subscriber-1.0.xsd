<?xml version="1.0" encoding="UTF-8"?>
<!--**************************************************************************-->
<!--                                                                          -->
<!-- Unified XSD schema for Nokia Wings  (Prepared By:Vignesh G)              -->
<!--                                                                          -->
<!--                                                                          -->
<!--  16/08/2022 - Version1.0 New UPC - SDL v22.0			                  -->
<!--  Added ExPs:                                                             -->
<!-- 			SDL-3GPPHSS-22.2.0-12-WX-ExP 							      -->
<!-- 			SDL-HLR-22.2.0-12-WX-ExP   									  -->
<!-- 			SDL-HSSIMS-22.2.0-12-WX-ExP 								  -->
<!-- 			SDL-HSSEPS-22.2.0-12-WX-ExP  								  -->
<!-- 			SDL-CommonHLRHSS-22.2.0-12-WX-ExP 							  -->
<!-- 			SDL-UDM5G-22.2.0-12-WX-ExP 									  -->
<!-- 			SDL-PCF-21.8.0-3-WX-ExP  									  -->
<!-- 			SDL-EIR-22.2.0-3-WX-ExP   									  -->
<!--  12/09/2022 - Version 2.0 - Changes                                      -->
<!--  Updated UDM5G Exp:                                                      --> 
<!--  SDL-UDM5G-22.5.0-15-WX-ExP                                              -->
<!--**************************************************************************-->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0" xmlns:subscriber="urn:siemens:names:prov:gw:NW_SUBSCRIBER:1:0" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:siemens:names:prov:gw:NW_SUBSCRIBER:1:0" elementFormDefault="unqualified" attributeFormDefault="unqualified" version="2.1">
  <!--  schema imports -->
  <xs:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
  <!-- common.v100 START -->
  <!--********************************************************************************************************-->
  <!--                                     Common / Subscriber Objects                                        -->
  <!--********************************************************************************************************-->
  <!--***********************************************************-->
  <!--  FIRST CLASS OBJECTS                                                        -->
  <!--***********************************************************-->
  <!-- The one and only first class object in SUBSCRIBER: Subscriber -->
  <xs:complexType name="Subscriber">
    <xs:annotation>
      <xs:documentation>Definition of class subscriber </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:FirstClassObject">
        <xs:sequence>
          <xs:element name="auc" type="subscriber:AUC" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="profile" type="subscriber:Profile" minOccurs="0"/>
          <xs:element name="hlr" type="subscriber:HLR" minOccurs="0" maxOccurs="unbounded"/>
          <!-- <xs:element name="hlr" type="subscriber:HLR" minOccurs="0" maxOccurs="unbounded" /> -->
          <xs:element name="hss" type="subscriber:HSS" minOccurs="0"/>
		  <xs:element name="eir" type="subscriber:EIR" minOccurs="0"/>
		  <xs:element name="pcf" type="subscriber:PCF" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="isVoid" type="xs:boolean" minOccurs="0"/>
          <xs:element name="masteredBy" type="xsd:int" minOccurs="0"/>
          <xs:element name="externalIdDataGroup" type="subscriber:hssExternalIdDataGroup" minOccurs="0"/>
          <!-- FC123_108057 Support external ID -->
          <xs:element name="udm5gData" type="subscriber:Udm5gData" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TemplateSubscriber">
    <xs:annotation>
      <xs:documentation>Definition of class TemplateSubscriber.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <!-- hlr element should not be unbounded for a TemplateSubscriber -->
      <xs:extension base="subscriber:Subscriber"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- Note: Having multiple Contracts, IMSIs, AUCs, HSSs under one subscriber -->
  <!-- imsiRef string has to be added in  AUCs, HSSs to bind them with single IMSI -->
  <!-- xs:element name="imsiRef" type="xsd:string" minOccurs="0"/ -->
  <!--************************************************************************-->
  <!--  Common SECOND CLASS OBJECTS      -->
  <!--************************************************************************-->
  <xs:complexType name="AUC">
    <xs:annotation>
      <xs:documentation>Abstract base class for a service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="encKey" type="xsd:string" minOccurs="0"/>
          <xs:element name="iccId" type="xsd:string" minOccurs="0"/>
          <xs:element name="algoId" type="xsd:int" minOccurs="0"/>
          <xs:element name="cv" type="xsd:string" minOccurs="0"/>
          <xs:element name="hmac" type="xsd:string" minOccurs="0"/>
          <xs:element name="kdbId" type="xsd:string" minOccurs="0"/>
          <xs:element name="acsub" type="xsd:int" minOccurs="0"/>
          <xs:element name="amf" type="xsd:string" minOccurs="0"/>
          <xs:element name="sqn" type="xsd:string" minOccurs="0"/>
          <!--  FC122_004658 – OPc based MILENAGE Start  -->
          <xs:element name="opcEncKey" type="xsd:string" minOccurs="0"/>
          <!--  FC122_004658 - OPc based MILENAGE End  -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!--           Profile SCO                                                  -->
  <!--************************************************************************-->
  <xsd:complexType name="Profile">
    <xsd:annotation>
      <xsd:documentation>SubscriberProfile</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="givenName" type="subscriber:PrintableString128" minOccurs="0"/>
          <xsd:element name="surname" type="subscriber:PrintableString128" minOccurs="0"/>
          <xsd:element name="username" type="subscriber:PrintableString128" minOccurs="0"/>
          <xsd:element name="password" type="subscriber:String128" minOccurs="0"/>
          <xsd:element name="loginPassword" type="subscriber:String128" minOccurs="0"/>
          <xsd:element name="prepaid" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="roamingAllowed" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
    <!--************************************************************************-->
    <!-- ******* *           Application Service base class             ********-->
    <!--************************************************************************-->
  <xs:complexType name="AS">
    <xs:annotation>
      <xs:documentation>Abstract base class for an application service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject"/>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!--                                Simple Types                            -->
  <!--************************************************************************-->
  <xsd:simpleType name="PrintableString">
    <xsd:annotation>
      <xsd:documentation>Printable String  with '@' character</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[a-zA-Z\d '()+,-./:=?@_;]+"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PrintableString128">
    <xsd:annotation>
      <xsd:documentation>Printable String of length 1 .. 128</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:maxLength value="128"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PrintableString254">
    <xsd:annotation>
      <xsd:documentation>Printable String of length 1 .. 254</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:maxLength value="254"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumericString">
    <xsd:annotation>
      <xsd:documentation>Only digits allowed</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="\d+"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumericString3">
    <xsd:annotation>
      <xsd:documentation>Numeric String of length 1..3</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:NumericString">
      <xsd:maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumericString5">
    <xsd:annotation>
      <xsd:documentation>Numeric String of length 1..5</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:NumericString">
      <xsd:maxLength value="5"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumericString15">
    <xsd:annotation>
      <xsd:documentation>NumericString of length 1..15</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:NumericString">
      <xsd:maxLength value="15"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumericString1_15">
    <xsd:annotation>
      <xsd:documentation>Numeric String of length 1..15</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:NumericString">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="15"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="NumericString10_15">
    <xsd:annotation>
      <xsd:documentation>Numeric String of length 10..15</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:NumericString">
      <xsd:minLength value="10"/>
      <xsd:maxLength value="15"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="String128">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 128</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="128"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="PrintableString10">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 10</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="String10">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 10</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PrintableString64">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 64</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:maxLength value="64"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--************************************************************************-->
  <!--             Common Subscriber  aliases                                 -->
  <!--************************************************************************-->
  <xs:simpleType name="SubscriberIdentifierAliasType">
    <xs:restriction base="xsd:string">
      <xs:enumeration value="identifier"/>
      <xs:enumeration value="imsi"/>
      <xs:enumeration value="username"/>
      <xs:enumeration value="msisdn"/>
      <xs:enumeration value="impu"/>
      <xs:enumeration value="impi"/>
      <xs:enumeration value="iccid"/>
      <xs:enumeration value="externalId"/>
      <!-- FC123_108057 Support external ID -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SubscriberIdentifier">
    <xs:simpleContent>
      <xs:restriction base="spml:Identifier">
        <xs:attribute name="alias" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="SubscriberIdentifierFileNameType">
    <xs:simpleContent>
      <xs:restriction base="spml:IdentifierFileNameType">
        <xs:attribute name="alias" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
        <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="SubscriberAliasType">
    <xs:complexContent>
      <xs:restriction base="spml:AliasType">
        <xs:attribute name="name" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
        <xs:attribute name="value" type="xsd:string" use="required"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  
  <!-- common.v100 END -->
  <!-- hlr45.v100 START -->
  <!--***********************************************************************************************-->
  <!--                        HLR Application Service                                                -->
  <!--***********************************************************************************************-->
  <!--************************************************************************-->
  <!--                         HLR aliases                                    -->
  <!--************************************************************************-->
  <xs:simpleType name="HLRIdentifierAliasType">
      <xs:restriction base="xs:string">
          <!-- Note: also using Subscriber's common aliases: identifier, imsi -->
          <xs:enumeration value="msisdn"/>
      </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="HLRIdentifier">
      <xs:simpleContent>
          <xs:restriction base="spml:Identifier">
              <xs:attribute name="alias" type="subscriber:HLRIdentifierAliasType" use="required"/>
          </xs:restriction>
      </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="HLRIdentifierFileNameType">
      <xs:simpleContent>
          <xs:restriction base="spml:IdentifierFileNameType">
              <xs:attribute name="alias" type="subscriber:HLRIdentifierAliasType" use="required"/>
              <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
          </xs:restriction>
      </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="HLRAliasType">
      <xs:complexContent>
          <xs:restriction base="spml:AliasType">
              <xs:attribute name="name" type="subscriber:HLRIdentifierAliasType" use="required"/>
              <xs:attribute name="value" type="xs:string" use="required"/>
          </xs:restriction>
      </xs:complexContent>
  </xs:complexType>
  
  <!-- Definitions of application specific extensions of SPML  schema END-->
  <!--************************************************************************-->
  <!--  HLR Application Service SECOND CLASS OBJECTS                          -->
  <!--  Having HLR, insert EPS_Group in the full HLR schema                   -->
  <!--************************************************************************-->
  <xs:complexType name="HLR">
    <xs:annotation>
      <xs:documentation>HLR Application Service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:AS">
        <xs:sequence>
          <!-- imsi is reserved for future multi-valued version: -->
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="contractId" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ntype" type="subscriber:NType" minOccurs="0"/>
          <xs:element name="imsiActive" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="mobileSubscriberType" type="subscriber:MobileSubscriberType" minOccurs="0"/>
          <xs:element name="umtsSubscriber" type="subscriber:UmtsSubscriber" minOccurs="0"/>
          <xs:element name="wllSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="tifSubscriber" type="subscriber:TifSubscriber" minOccurs="0"/>
          <xs:element name="mscat" type="xs:int" minOccurs="0"/>
          <xs:element name="odboc" type="xs:int" minOccurs="0"/>
          <xs:element name="odbic" type="xs:int" minOccurs="0"/>
          <xs:element name="odbr" type="xs:int" minOccurs="0"/>
          <xs:element name="odboprc" type="xs:int" minOccurs="0"/>
          <xs:element name="odbssm" type="xs:int" minOccurs="0"/>
          <xs:element name="osb1" type="xs:boolean" minOccurs="0"/>
          <xs:element name="osb2" type="xs:boolean" minOccurs="0"/>
          <xs:element name="osb3" type="xs:boolean" minOccurs="0"/>
          <xs:element name="osb4" type="xs:boolean" minOccurs="0"/>
          <xs:element name="clip" type="xs:boolean" minOccurs="0"/>
          <xs:element name="clipOverride" type="xs:boolean" minOccurs="0"/>
          <xs:element name="clir" type="xs:int" minOccurs="0"/>
          <xs:element name="colp" type="xs:boolean" minOccurs="0"/>
          <xs:element name="colpOverride" type="xs:boolean" minOccurs="0"/>
          <xs:element name="colr" type="xs:boolean" minOccurs="0"/>
          <xs:element name="hold" type="xs:boolean" minOccurs="0"/>
          <xs:element name="mpty" type="xs:boolean" minOccurs="0"/>
          <xs:element name="aoci" type="xs:boolean" minOccurs="0"/>
          <xs:element name="aocc" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natccbs" type="xs:boolean" minOccurs="0"/>
          <!-- Release2: natCT - call transfer -->
          <xs:element name="natct" type="xs:boolean" minOccurs="0"/>
          <!-- : natHotbill - Hotbill -->
          <xs:element name="natHotbill" type="xs:boolean" minOccurs="0"/>
          <!-- : natFR - Forced Routing -->
          <xs:element name="natFR" type="xs:boolean" minOccurs="0"/>
          <!-- : natusersig1 - User to User Signalling Service -->
          <xs:element name="natusersig1" type="xs:boolean" minOccurs="0"/>
          <!-- Release2: ccbsa and ccbsb added -->
          <xs:element name="ccbsa" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ccbsb" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss01" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss02" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss03" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss04" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss05" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss06" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss07" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss08" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss09" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss10" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss11" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss12" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss13" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss14" type="xs:boolean" minOccurs="0"/>
          <xs:element name="natss15" type="xs:boolean" minOccurs="0"/>
          <!-- Begin of HI40:Activation, Deactivation & interrogation of the new NATSS for 3G services -->
          <xs:element name="statusNatss01" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss02" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss03" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss04" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss05" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss06" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss07" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss08" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss09" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss10" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss11" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss12" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss13" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss14" type="xs:int" minOccurs="0"/>
          <xs:element name="statusNatss15" type="xs:int" minOccurs="0"/>
          <!-- End of HI40:Activation, Deactivation & interrogation of the new NATSS for 3G services -->
          <!-- Release2: modeled as SCO array to be able to remove exact value -->
          <xs:element name="subrelro" type="subscriber:SUBRELRO" minOccurs="0" maxOccurs="unbounded"/>
          <!-- already existing shared EPS/HLR data -->
          <xs:element name="nwa" type="xsd:int" minOccurs="0"/>
          <!-- xs:element name="gprstosm" type="subscriber:GPRSTOSM" minOccurs="0"  maxOccurs="1"/ -->
          <xs:element name="odbgprs" type="xsd:int" minOccurs="0"/>
          <xs:element name="rr" type="xs:string" minOccurs="0"/>
          <xs:element name="sr" type="xsd:int" minOccurs="0"/>
          <!-- Release2: modeled as SCO array to be able to remove exact value -->
          <xs:element name="vgrpid" type="subscriber:VGRPID" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="msp" type="xs:string" minOccurs="0"/>
          <!-- Release2: introduction of ODBSCI attribute -->
          <xs:element name="odbsci" type="xs:int" minOccurs="0"/>
          <!-- BasicServices assigned to subscriber -->
          <xs:element name="ts11" type="subscriber:TS11" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts21" type="subscriber:TS21" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts22" type="subscriber:TS22" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts61" type="subscriber:TS61" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts62" type="subscriber:TS62" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="vgcs" type="subscriber:VGCS" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="vbs" type="subscriber:VBS" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs20genr" type="subscriber:BS20GENR" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs21" type="subscriber:BS21" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs22" type="subscriber:BS22" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs23" type="subscriber:BS23" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs24" type="subscriber:BS24" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs25" type="subscriber:BS25" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs26" type="subscriber:BS26" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs30genr" type="subscriber:BS30GENR" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs31" type="subscriber:BS31" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs32" type="subscriber:BS32" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs33" type="subscriber:BS33" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs34" type="subscriber:BS34" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs40genr" type="subscriber:BS40GENR" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs41" type="subscriber:BS41" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs42" type="subscriber:BS42" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs44" type="subscriber:BS44" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs45" type="subscriber:BS45" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs46" type="subscriber:BS46" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs61a" type="subscriber:BS61A" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bs81a" type="subscriber:BS81A" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="gprs" type="subscriber:GPRS" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts21Gprs" type="subscriber:TS21GPRS" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ts22Gprs" type="subscriber:TS22GPRS" minOccurs="0" maxOccurs="unbounded"/>
          <!-- Call Forwarding Services -->
          <!-- CFU, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="cfu" type="subscriber:CFU" minOccurs="0" maxOccurs="unbounded"/>
          <!-- CFB, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="cfb" type="subscriber:CFB" minOccurs="0" maxOccurs="unbounded"/>
          <!-- CFNrc, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="cfnrc" type="subscriber:CFNrc" minOccurs="0" maxOccurs="unbounded"/>
          <!-- CFNry, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="cfnry" type="subscriber:CFNry" minOccurs="0" maxOccurs="unbounded"/>
          <!-- AllCCF, single-valued -->
          <xs:element name="allccf" type="subscriber:AllCCF" minOccurs="0"/>
          <!-- AllCF, single-valued -->
          <xs:element name="allcf" type="subscriber:AllCF" minOccurs="0"/>
          <!-- CFD, multi-valued -->
          <!-- Release2: changed to multivalued -->
          <xs:element name="cfd" type="subscriber:CFD" minOccurs="0" maxOccurs="unbounded"/>
          <!-- CAW, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="caw" type="subscriber:CAW" minOccurs="0" maxOccurs="unbounded"/>
          <!-- CUG, single-valued, not used in Release 1 -->
          <xs:element name="cug" type="subscriber:CUG" minOccurs="0" maxOccurs="10"/>
          <!-- CUGBSG, single-valued, not used in Release 1 -->
          <xs:element name="cugbsg" type="subscriber:CUGBSG" minOccurs="0" maxOccurs="5"/>
          <!-- Call Barring Services -->
          <!-- COMCB, single-valued -->
          <xs:element name="comcb" type="subscriber:COMCB" minOccurs="0"/>
          <!-- BAOC etc, multi-valued, max number = number of values of attribute Basic Service Group -->
          <xs:element name="baoc" type="subscriber:BAOC" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="boic" type="subscriber:BOIC" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="boicexhc" type="subscriber:BOICexHC" minOccurs="0" maxOccurs="unbounded"/>
          <!-- BORO -->
          <xs:element name="boro" type="subscriber:BORO" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="baic" type="subscriber:BAIC" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="bicroam" type="subscriber:BICRoam" minOccurs="0" maxOccurs="unbounded"/>
          <!-- Misc. Services -->
          <xs:element name="emlpp" type="subscriber:EMLPP" minOccurs="0"/>
          <!-- IMSI Link -->
          <xs:element name="imsilink" type="xs:string" minOccurs="0"/>
          <!-- isActiveIMSI: indicates whether this IMSI is the active IMSI in case of a linked subscriber -->
          <xs:element name="isActiveIMSI" type="xs:boolean" minOccurs="0"/>
          <!-- Release2: Multi-device support - JANUS -->
          <xs:element name="multiDevice" type="subscriber:MultiDeviceType" minOccurs="0"/>
          <!-- RS multi-valued -->
          <xs:element name="rs" type="subscriber:RS" minOccurs="0" maxOccurs="unbounded"/>
          <!-- SRF multi-valued -->
          <xs:element name="srf" type="subscriber:SRF" minOccurs="0" maxOccurs="16"/>
          <xs:element name="followMe" type="subscriber:FollowMe" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="followMeAccessMatrix" type="subscriber:FollowMeClassOfRegistration" minOccurs="0" maxOccurs="5"/>
          <xs:element name="twincardImsi" type="xs:string" minOccurs="0"/>
          <!-- actIMSIGprs: indicates the activation status for a Twin card IMSI subscriber -->
          <xs:element name="actIMSIGprs" type="xs:boolean" minOccurs="0"/>
          <!-- LCS Basic procedures -->
          <xs:element name="cuss" type="subscriber:CUSS" minOccurs="0"/>
          <xs:element name="crelss" type="subscriber:CRELSS" minOccurs="0"/>
          <xs:element name="curelss" type="subscriber:CURELSS" minOccurs="0"/>
          <xs:element name="plmnoss" type="subscriber:PLMNOSS" minOccurs="0"/>
          <xs:element name="basicSelfLocation" type="subscriber:BasicSelfLocation" minOccurs="0"/>
          <xs:element name="autoSelfLocation" type="subscriber:AutoSelfLocation" minOccurs="0"/>
          <xs:element name="transferToThirdParty" type="subscriber:TransferToThirdParty" minOccurs="0"/>
          <xs:element name="callback" type="subscriber:CALLBACK" minOccurs="0"/>
          <xs:element name="obGprs" type="xs:int" minOccurs="0"/>
          <xs:element name="optimalRouting" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ndcLac" type="xs:string" minOccurs="0"/>
          <xs:element name="imsiTraceReference" type="xs:int" minOccurs="0"/>
          <xs:element name="imsiTraceType" type="xs:int" minOccurs="0"/>
          <!-- Expiry Date: The format value of this attribute is YYYY-MM-DD. -->
          <xs:element name="expiryDate" type="xs:string" minOccurs="0"/>
          <xs:element name="preferedInterexchangeCarrier" type="xs:string" minOccurs="0"/>
          <xs:element name="vbsData" type="subscriber:VbsData" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="generalChargingCharacteristics" type="subscriber:GeneralChargingCharacteristics" minOccurs="0"/>
          <!-- VLRID single-valued -->
          <!-- Release2: changed to boolean -->
          <xs:element name="vlrid" type="xs:boolean" minOccurs="0"/>
          <!-- PDPContext multi-valued, max number = 50 -->
          <xs:element name="pdpContext" type="subscriber:PDPContext" minOccurs="0" maxOccurs="50"/>
          <!-- PDPRef single-valued -->
          <!-- Release2: removed because it is not supported by HLR and data model -->
          <!-- xs:element name="pdpRef" type="xs:string" minOccurs="0"/ -->
          <!-- Camel subscription -->
          <xs:element name="smscsi" type="subscriber:SMSCSI" minOccurs="0"/>
          <xs:element name="ocsi" type="subscriber:OCSI" minOccurs="0"/>
          <xs:element name="tcsi" type="subscriber:TCSI" minOccurs="0"/>
          <xs:element name="ucsisub" type="subscriber:UCSISUB" minOccurs="0" maxOccurs="unbounded"/>
          <!-- Release2: changed to flat attribute -->
          <xs:element name="ucsiserv" type="xs:string" minOccurs="0"/>
          <xs:element name="gprscsi" type="subscriber:GPRSCSI" minOccurs="0"/>
          <xs:element name="sscsi" type="subscriber:SSCSI" minOccurs="0"/>
          <xs:element name="mcsi" type="subscriber:MCSI" minOccurs="0"/>
          <xs:element name="dcsi" type="subscriber:DCSI" minOccurs="0"/>
          <xs:element name="vtcsi" type="subscriber:VTCSI" minOccurs="0"/>
          <xs:element name="mtsmscsi" type="subscriber:MTSMSCSI" minOccurs="0"/>
          <xs:element name="mgcsi" type="subscriber:MGCSI" minOccurs="0"/>
          <xs:element name="notifyToCSE" type="subscriber:NotifyToCse" minOccurs="0"/>
          <!-- VlrMobData: cannot be changed by Provisioning Gateway -->
          <xs:element name="vlrMobData" type="subscriber:VlrMobData" minOccurs="0"/>
          <!-- SgsnMobData: cannot be changed by Provisioning Gateway -->
          <xs:element name="sgsnMobData" type="subscriber:SgsnMobData" minOccurs="0"/>
          <!-- MsgWaitData: cannot be changed by Provisioning Gateway -->
          <xs:element name="msgWaitData" type="subscriber:MsgWaitData" minOccurs="0" maxOccurs="unbounded"/>
          <!-- changeOverIMSI: read only attribute. -->
          <!-- It displays the new IMSI in case of pending seamless changeover for the subscriber -->
          <xs:element name="changeOverIMSI" type="xs:string" minOccurs="0"/>
          <!-- ************    HLR4.0   *****************  -->
          <!-- HI40:Routing Categories -->
          <xs:element name="routingCategory" type="xs:int" minOccurs="0"/>
          <xs:element name="routingCategoryExtension" type="xs:int" minOccurs="0"/>
          <!-- HI40:Automatic Device Detection -->
          <xs:element name="imeisv" type="xsd:string" minOccurs="0"/>
          <!-- ************    HLR4.5   *****************  -->
          <!-- HI45: Calling Name Presentation -->
          <xs:element name="cnap" type="xs:boolean" minOccurs="0"/>
          <!-- HI45: Calling Name Presentation Override -->
          <xs:element name="cnapOverride" type="xs:boolean" minOccurs="0"/>
          <!-- new service  usable only for msisdn based searches-->
          <xs:element name="allbs" type="subscriber:ALLBS" minOccurs="0"/>
          <!-- ************    HLR4.5   *****************  -->
          <!-- HLR 4.5: Explicit Call Transfer -->
          <xs:element name="ect" type="xs:boolean" minOccurs="0"/>
          <!-- HLR 4.5: Enhanced SRI for SMS handling -->
          <xs:element name="enhsrisms" type="xs:boolean" minOccurs="0"/>
          <!-- HLR 4.5:  Originating/Terminating IN Category Key (O/T ICK)     -->
          <xs:element name="oick" type="subscriber:OICK" minOccurs="0"/>
          <xs:element name="tick" type="subscriber:TICK" minOccurs="0" maxOccurs="2"/>
          <!-- HLR 4.5:  The Extended CAMEL proprietary IN data:
                IN Category Key (EOICK, ETICK) or IN Capability Indicator (EOINCI, ETINCI).
                Note: Implemented in second phase (planned 03/09) -->
          <xs:element name="eoick" type="subscriber:EOICK" minOccurs="0"/>
          <xs:element name="etick" type="subscriber:ETICK" minOccurs="0" maxOccurs="2"/>
          <xs:element name="eoinci" type="subscriber:EOINCI" minOccurs="0"/>
          <xs:element name="etinci" type="subscriber:ETINCI" minOccurs="0" maxOccurs="2"/>
          <!-- HLR 4.5 Call forwarding controls -->
          <!-- odbftno  and prohibitedFtnoCategory has been made obselete -->
          <xs:element name="odbftno" type="xs:int" minOccurs="0"/>
          <xs:element name="prohibitedFtnoCategory" type="xs:int" minOccurs="0"/>
          <!-- HLR 4.5: Anonymous Caller Reject -->
          <!-- Note: Implementation planned 06/09 -->
          <!-- xs:element name="acr" type="xs:boolean" minOccurs="0"/ -->
          <!-- ************    HLR4.5.1   *****************  -->
          <xs:element name="odbect" type="subscriber:OdbEctType" minOccurs="0" maxOccurs="6"/>
          <!-- ************    HLR4.5.2   *****************  -->
          <xs:element name="clgPtyNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="clgPtyData" type="subscriber:ClgPtyData" minOccurs="0"/>
          <xs:element name="lastCallTimeStamp" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="lcnService" type="subscriber:LcnService" minOccurs="0"/>
          <xs:element name="ownMsisdnService" type="subscriber:OwnMsisdnService" minOccurs="0"/>
          <xs:element name="vlrIdService" type="subscriber:VlrIdService" minOccurs="0"/>
          <xs:element name="actualTimeService" type="subscriber:ActualTimeService" minOccurs="0"/>
          <xs:element name="lastCallSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
          <xs:element name="multiSim" type="subscriber:MultiSimType" minOccurs="0"/>
          <xs:element name="ussdClirService" type="subscriber:UssdClirService" minOccurs="0"/>
          <xs:element name="ucsisubext" type="subscriber:UCSISUBEXT" minOccurs="0" maxOccurs="unbounded"/>
          <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes Begin -->
          <xs:element name="roamSubscription" type="subscriber:ROAMSUBSCRIPTION" minOccurs="0"/>
          <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes End  -->
          <!-- HLR 4.5.5: FC122_00063: Mss MultiSim1 -->
          <xs:element name="profileType" type="subscriber:HlrProfileType" minOccurs="0"/>
          <xs:element name="commonMSISDN" type="xs:string" minOccurs="0"/>
          <xs:element name="overrideCommonCLI" type="xs:boolean" minOccurs="0"/>
          <xs:element name="mssMultiSim" type="subscriber:HlrMssMultiSimType" minOccurs="0"/>
          <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
          <xs:element name="spatialTrigger" type="subscriber:SpatialTrigger" minOccurs="0"/>
          <!-- HLR 5.0 LF: FC122_003888 - Proprietary Nokia IN Short Message Service -->
          <xs:element name="insms" type="subscriber:INSMS" minOccurs="0"/>
          <!-- HLR 5.0 LF: FC122_001926 - Automatic Redirection of Calls -->
          <xs:element name="arc" type="subscriber:ARC" minOccurs="0"/>
          <!-- HLR 5.0 SP1:: Changes for FC122_003779::Account Code Handling -->
          <xs:element name="acc" type="subscriber:HlrAcc" minOccurs="0"/>
          <!-- HLR 5.0 SP1:FC122_003784: PRN retry  -->
          <xs:element name="csFallback" type="subscriber:HlrCSFallback" minOccurs="0"/>
          <!--HLR 5.0 SP1: FC122_002113_SMSImprovements-->
          <xs:element name="smsSubData" type="subscriber:HlrSmsSubData" minOccurs="0"/>
          <!-- HLR 5.0 SP1: FC122_003912: FTNO -->
          <xs:element name="ftnTransIndex" type="xs:int" minOccurs="0"/>
          <!-- HLR 5.0 SP1: FC122_003520: SRVCC -->
          <xs:element name="icsIndicator" type="xs:int" minOccurs="0"/>
          <xs:element name="sset" type="subscriber:HLRSSET" minOccurs="0"/>
          <xs:element name="emoick" type="subscriber:HLREMOICK" minOccurs="0"/>
          <!-- HLR 5.0 SP1: FC122_003452 UE-Reachability -->
          <xs:element name="uEReachabilityReqInfo" type="subscriber:UEReachabilityReqInfo" minOccurs="0"/>
          <!-- HLR 7.0 base: FC122_004632 CSARP START -->
          <xs:element name="csarp" type="subscriber:HLRCSARP" minOccurs="0"/>
          <!-- HLR 7.0 base: FC122_004632 CSARP END -->
          <!-- FC122_004142: Subscriber fraud detection & limitation Start -->
          <xs:element name="refFraudProfileName" type="xs:string" minOccurs="0"/>
          <!-- FC122_004142: Subscriber fraud detection & limitation End  -->
          <!-- FC122_004634 Multimedia Ring Back Tone (MRBT) Start -->
          <xs:element name="mrbt" type="xs:boolean" minOccurs="0"/>
          <!-- FC122_004634 Multimedia Ring Back Tone (MRBT) Start -->
          <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
          <xs:element name="gsmTrace" type="subscriber:HLRGSMTrace" minOccurs="0"/>
          <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR end -->
          <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: Start -->
          <xs:element name="subFraudObsInfo" type="subscriber:HLRFraudObservationInfo" minOccurs="0" maxOccurs="2"/>
          <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: End -->
          <!-- FC122_005796_Subscriber_fraud_observation_phase3_LUP :: Start -->
          <xs:element name="simObsActive" type="xs:boolean" minOccurs="0"/>
          <!-- FC122_005796_Subscriber_fraud_observation_phase3_LUP :: End -->
          <!-- FC122_005801_  Charging_based_on_Home_Area :: Start -->
          <xs:element name="chbha" type="subscriber:HLRChargingBasedOnHomeArea" minOccurs="0"/>
          <!-- FC122_005801_  Charging_based_on_Home_Area :: End -->
          <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
          <xs:element name="primaryBasicService" type="subscriber:HLRPBS" minOccurs="0"/>
          <!-- FC123_106922_CF_Notification_Override - Start -->
          <xs:element name="cf" type="subscriber:HLRCallForward" minOccurs="0"/>
          <!-- FC123_106922_CF_Notification_Override - End -->
          <!--FC123_107003_Separate_Roaming_Restriction_List Start -->
          <xs:element name="rrPs" type="xs:string" minOccurs="0"/>
          <xs:element name="srPs" type="xs:int" minOccurs="0"/>
          <!-- FC123_107003_Separate_Roaming_Restriction_List End -->
          <!-- FC123_107002_Multiple_HPLMN_support - Start -->
          <xs:element name="refHplmnAreaName" type="xs:string" minOccurs="0"/>
          <!-- FC123_107002_Multiple_HPLMN_support - End -->
          <!-- FC123_108726_Combine 4G and 3G roaming indication over Sh interface: Start -->
          <xs:element name="shVplmnId" type="subscriber:PrintableString64" minOccurs="0"/>
          <!-- FC123_108726_Combine 4G and 3G roaming indication over Sh interface: End -->
          <!--FST123_107470 - Enhanced control of CF types to non-Macau international number-->
          <xs:element name="hlrOdbFtnoStandard" type="subscriber:HLROdbFtnoStandard" minOccurs="0"/>
          <xs:element name="hlrOdbFtnoProprietary" type="subscriber:HLROdbFtnoProprietary" minOccurs="0"/>
		      <!-- FC123_107981-VDF_Turkey_SPRING_2-Local_Requirements-SS7_Fraud_and_Twin_SIM_Card : Start -->
          <xs:element name="disableRoamingPlausibility" type="xs:boolean" minOccurs="0"/>
          <!-- FC123_107981-VDF_Turkey_SPRING_2-Local_Requirements-SS7_Fraud_and_Twin_SIM_Card : End -->
		      <!-- FC123_107926 HLR supports MSISDN-less MTC devices - Start -->
          <xs:element name="hssMsisdnLessIndicator" type="xs:boolean" minOccurs="0"/>
          <!-- FC123_107926 HLR supports MSISDN-less MTC devices end-->
          <!-- FC123_107948_Selective_Cancel_Location :: Start -->
          <xs:element name="cancelLocInVLR" type="xs:boolean" minOccurs="0"/>
          <xs:element name="cancelLocInSGSN" type="xs:boolean" minOccurs="0"/>
          <!-- FC123_107948_Selective_Cancel_Location :: End -->
					<xs:group ref="subscriber:HLR_NonAdminDataGroup"/>
					<xs:element name="extendedRoamSubscription" type="subscriber:HssExtendedRoamSubscription" minOccurs="0" maxOccurs="unbounded"/>
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
					<xs:element name="hlrSgsnSupportedFeatures" type="subscriber:HLRSgsnSupportedFeatures" minOccurs="0"/>
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
          <!-- RE123_109041 IWF Support for 5G Migration: Start -->
          <xs:element name="iwf5GUEMode" type="xs:boolean" minOccurs="0"/>
          <!-- RE123_109041 IWF Support for 5G Migration: End -->
          <xs:element name="listBasedUcsi" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
   
         <!-- New shared EPS/HLR data -->
					<xsd:element name="meId" type="subscriber:PrintableString64" minOccurs="0"/>
					<xsd:element name="accessRestr" type="subscriber:EpsAccessRestriction" minOccurs="0" maxOccurs="11"/>
					<xsd:element name="odbBaroam" type="subscriber:EPSOdbBarringRoam" minOccurs="0"/>
				<!-- <xsd:element name="pdpContext" type="subscriber:PDPContext" minOccurs="0" maxOccurs="50"/>
				 <xsd:element name="sgsnMobData" type="subscriber:SgsnMobData" minOccurs="0"/> -->
					<xsd:element name="sgsnIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="sgsnRealm" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="sgsnSupportedFeatures" type="subscriber:SgsnSupportedFeatures" minOccurs="0" maxOccurs="unbounded" />
					<xsd:element name="refHplmnListName" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="s6dSgsnNumber" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="s6dCombinedFlag" type="xsd:boolean" minOccurs="0"/>
					<xsd:element name="notification" type="subscriber:AppSrvNotificationData" minOccurs="0" maxOccurs="unbounded"/>
					<!-- Start : FRS123_108213_SMS_Delivery_Feature -->
					<!-- <xs:element name="smsSubData" type="subscriber:HlrSmsSubData" minOccurs="0"/> -->
					<!-- End : FRS123_108213_SMS_Delivery_Feature -->
					<!-- <xs:element name="icsIndicator" type="xsd:int" minOccurs="0"/> 
					<xsd:element name="uEReachabilityReqInfo" type="subscriber:UEReachabilityReqInfo" minOccurs="0" /> -->
					<xsd:element name="s6dLocUpdateTimestamp" type="xsd:dateTime" minOccurs="0"/>
					<!-- End of: New shared EPS/HLR data -->

					<!-- EPS specific attributes -->
					<group ref="subscriber:EPS_Group"/>
					<xsd:element name="hssIoTServiceData" type="subscriber:HssIoTServiceData" minOccurs="0"/>
					<!-- BEGIN changes for HSS 18.0: RE123_108171 Outroamer Count -->
					<xs:element name="s6dPlmnStatus" type="subscriber:EPSPlmnStatus" minOccurs="0"/>
					<!-- END changes for HSS 18.0: RE123_108171 Outroamer Count -->
					<!-- Start : FRS123_108213_SMS_Delivery_Feature -->
					<xs:element name="mwdSCList" type="subscriber:EpsMWDSCList" minOccurs="0" maxOccurs="unbounded"/>
					<!-- End : FRS123_108213_SMS_Delivery_Feature -->
					<!-- START changes for HSS 18.0: RE123_107772 SCEF Support Step-2 -->
					<xs:element name="scefInfo" type="subscriber:EpsSCEFInfo" minOccurs="0" maxOccurs="unbounded"/>
					<!-- END changes for HSS 18.0: RE123_107772 SCEF Support Step-2 -->
					<!-- START changes for HSS 18.0SP3: RE123_108169_Emergency_Handover_Procedures -->
					<xs:element name="emergencyInfo" type="subscriber:HssEmergencyInfo" minOccurs="0"/>
					<!-- END changes for HSS 18.0SP3: RE123_108169_Emergency_Handover_Procedures -->
					<!-- START changes for RE123_108252_5G RAT type support -->
					<xsd:element name="supportNewRadioSecondaryRAT" type="xsd:boolean" minOccurs="0"/>
					<!-- END changes for RE123_108252_5G RAT type support -->
					<!-- START changes for FC123_108341 Multiple level of inheritance of RSI per subscriber-->
					<!-- <xs:element name="extendedRoamSubscription" type="subscriber:HssExtendedRoamSubscription" minOccurs="0" maxOccurs="unbounded"/> -->
					<!-- END changes for FC123_108341 Multiple level of inheritance of RSI per subscriber-->
					<!-- START changes for RE123_108401_RDSP enhancements to interface with EIR/EEIR/NAP - HSS Impacts-->
					<xs:element name="rdspMobilityRoamPlanInfo" type="subscriber:HssRdspMobilityRoamPlanInfo" minOccurs="0" maxOccurs="unbounded"/>
					<!-- END changes for RE123_108401_RDSP enhancements to interface with EIR/EEIR/NAP - HSS Impacts-->
          <xs:element name="interworkingWithUdm" type="subscriber:HssInterworkingWithUdm" minOccurs="0"/>
          <xs:element name="supportNRin5GS" type="xs:boolean" minOccurs="0"/>
          <xs:element name="is5GSubscriptionActive" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="HssExtendedRoamSubscription">
         <xs:annotation>
             <xs:documentation> This attributes indicates a symbolic name identifying the Roaming Subscription Info entry. </xs:documentation>
         </xs:annotation>
         <xs:complexContent>
             <xs:extension base="spml:SecondClassObject">
                 <xs:sequence>
                     <xs:element name="priorityOrder" type="xs:int"/>
                     <xs:element name="roamSubscriptionInfo" type="xs:string"/>
                 </xs:sequence>
             </xs:extension>
         </xs:complexContent>
	</xs:complexType>
		  
  <xs:group name="HLR_NonAdminDataGroup">
    <xs:annotation>
      <xs:documentation>Definition of EPS data </xs:documentation>
    </xs:annotation>
    <xs:sequence>
        <xs:element name="ccbsTerminatingEntry" type="subscriber:HlrCcbsTerminatingEntry" minOccurs="0" maxOccurs="unbounded"/>        				
		<xs:element name="ccbsOriginatingEntry" type="subscriber:HlrCcbsOriginatingEntry" minOccurs="0" maxOccurs="unbounded"/>
		<xs:element name="ccbsOQueue" type="subscriber:HlrCcbsOQueue" minOccurs="0" maxOccurs="unbounded"/>				
		<xs:element name="ccbsTQueue" type="subscriber:HlrCcbsTQueue" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:group>
  <xs:complexType name="HlrCcbsTerminatingEntry">
    <xs:annotation>
      <xs:documentation>
		CCBSTERMINATINGENTRY
      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="ccbsTerminatingIndex" type="xsd:int" minOccurs="0"/>
          <xsd:element name="msisdn" type="subscriber:NumericString15" minOccurs="0"/>
          <xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>		
          <xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
          <xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="ccbsTimerStart" type="xsd:dateTime" minOccurs="0"/>
          <xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
          <xsd:element name="retainSupported" type="xsd:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HlrCcbsOriginatingEntry">
    <xs:annotation>
      <xs:documentation>
		 CCBSORGINATINGENTRY
      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="ccbsOriginatingIndex" type="xsd:int" minOccurs="0"/>
          <xsd:element name="msisdn" type="subscriber:NumericString15" minOccurs="0"/>
          <xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>		
          <xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
          <xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="ccbsTimerStart" type="xsd:dateTime" minOccurs="0"/>
          <xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
          <xsd:element name="retainSupported" type="xsd:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HlrCcbsOQueue">
    <xs:annotation>
      <xs:documentation>
		CCBSOQUEUE
      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="ccbsOMSISDN" type="subscriber:NumericString15" minOccurs="0"/>
          <xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>	
          <xsd:element name="ccbsIndex" type="xsd:int" minOccurs="0"/>	
          <xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
          <xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="ccbsPrio" type="xsd:int" minOccurs="0"/>	
          <xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HlrCcbsTQueue">
    <xs:annotation>
      <xs:documentation>
		CCBSTQUEUE
      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="ccbsTMSISDN" type="subscriber:NumericString15" minOccurs="0"/>
          <xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>	
          <xsd:element name="ccbsIndex" type="xsd:int" minOccurs="0"/>	
          <xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
          <xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="ccbsPrio" type="xsd:int" minOccurs="0"/>	
          <xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!--  TYPE SECTION: Defining complex types/enumeration for flat attributes  -->
  <!--************************************************************************-->
  <!-- definition of type NType, flat attribute of subscriber -->
  <xs:simpleType name="NType">
    <xs:annotation>
      <xs:documentation>Numbering Type</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="single"/>
      <xs:enumeration value="multi"/>
    </xs:restriction>
  </xs:simpleType>
    <!--************************************************************************-->
    <!--  SECOND CLASS OBJECTS                                                  -->
    <!--************************************************************************-->
   <xs:complexType name="Service">
        <xs:annotation>
            <xs:documentation>Abstract base class for a service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject"/>
        </xs:complexContent>
    </xs:complexType>
  <xs:complexType name="BasicService">
    <xs:annotation>
      <xs:documentation>Abstract base class for a basic service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
          <xs:element name="bcieID" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Teleservices -->
  <xs:complexType name="TS11">
    <xs:annotation>
      <xs:documentation>
                TS11 is a particular subclass of a service class with dedicated parameter
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TS21">
    <xs:annotation>
      <xs:documentation>
                TS21 is a Short Message service mobile terminating
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TS22">
    <xs:annotation>
      <xs:documentation>
                TS22 is a Short Message service mobile originating
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TS61">
    <xs:annotation>
      <xs:documentation>
                TS61 is an alternate speech and facsimile group 3 service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TS62">
    <xs:annotation>
      <xs:documentation>
                TS62 is an automatic facsimile group 3 service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VGCS">
    <xs:annotation>
      <xs:documentation>
                VGCS Voice Group Call Service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VBS">
    <xs:annotation>
      <xs:documentation>
                VBS Voice Broadcast Service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS20GENR">
    <xs:annotation>
      <xs:documentation>
                BS20 GENR is a general circuit switched data asynchronous service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS21">
    <xs:annotation>
      <xs:documentation>
                BS21 datal circuit duplex asynchronous service (300 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS22">
    <xs:annotation>
      <xs:documentation>
                BS22 datal circuit duplex asynchronous service (1200 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS23">
    <xs:annotation>
      <xs:documentation>
                BS23 datal circuit duplex asynchronous service (1200 b/s and 75 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS24">
    <xs:annotation>
      <xs:documentation>
                BS24 datal circuit duplex asynchronous service (2400 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS25">
    <xs:annotation>
      <xs:documentation>
                BS25 datal circuit duplex asynchronous service (4800 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS26">
    <xs:annotation>
      <xs:documentation>
                BS26 datal circuit duplex asynchronous service (9600 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS30GENR">
    <xs:annotation>
      <xs:documentation>
                BS30 GENR is a general circuit switched data synchronous service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS31">
    <xs:annotation>
      <xs:documentation>
                BS31 datal circuit duplex synchronous service (1200 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS32">
    <xs:annotation>
      <xs:documentation>
                BS32 datal circuit duplex ssynchronous service (2400 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS33">
    <xs:annotation>
      <xs:documentation>
                BS33 datal circuit duplex synchronous service (4800 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS34">
    <xs:annotation>
      <xs:documentation>
                BS34 datal circuit duplex synchronous service (9600 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS41">
    <xs:annotation>
      <xs:documentation>
                BS41 PAD access circuit asynchronous service (300 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS42">
    <xs:annotation>
      <xs:documentation>
                BS42 PAD access circuit asynchronous service (12000 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS44">
    <xs:annotation>
      <xs:documentation>
                BS44 PAD access circuit asynchronous service (2400 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS45">
    <xs:annotation>
      <xs:documentation>
                BS45 PAD access circuit asynchronous service (4800 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS46">
    <xs:annotation>
      <xs:documentation>
                BS46 PAD access circuit asynchronous service (9600 b/s)
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS40GENR">
    <xs:annotation>
      <xs:documentation>
                BS40GENR general PAD access circuit asynchronous service
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS61A">
    <xs:annotation>
      <xs:documentation>
                BS61A Alternate Speech / Unrestricted Data
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService">
        <xs:sequence>
          <xs:element name="bcieIDData" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BS81A">
    <xs:annotation>
      <xs:documentation>
                BS81 Speech followed by Data
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService">
        <xs:sequence>
          <xs:element name="bcieIDData" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GPRS">
    <xs:annotation>
      <xs:documentation>
                GPRS This is a Nokia special service for GPRS
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="TransferOptionType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for transferOption element in TS21GPRS</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="transferViaMSC"/>
      <xs:enumeration value="transferViaSGSN"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="TS21GPRS">
    <xs:annotation>
      <xs:documentation>
                TS21GPRS TSMS MT over GPRS
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService">
        <xs:sequence>
          <xs:element name="transferOption" type="subscriber:TransferOptionType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TS22GPRS">
    <xs:annotation>
      <xs:documentation>
                TS22GPRS SMS MO over GPRS
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:BasicService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="CfBasicServiceGroupType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for BasicServiceGroup element in CFU</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="TS10-telephony"/>
      <xs:enumeration value="TS60-fax"/>
      <xs:enumeration value="BS20-dataAsync"/>
      <xs:enumeration value="BS30-dataSync"/>
      <xs:enumeration value="BS40-padAccess"/>
    </xs:restriction>
  </xs:simpleType>
  <!--FC122_003787:SMS Forwarding in HLR Start -->
  <xs:simpleType name="HLRCfBasicServiceGroupTypeExt">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for BasicServiceGroup element in CFU</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="TS10-telephony"/>
      <xs:enumeration value="TS60-fax"/>
      <xs:enumeration value="BS20-dataAsync"/>
      <xs:enumeration value="BS30-dataSync"/>
      <xs:enumeration value="BS40-padAccess"/>
      <xs:enumeration value="TS20-shortMessage"/>
    </xs:restriction>
  </xs:simpleType>
  <!--FC122_003787:SMS Forwarding in HLR End -->
  <xs:complexType name="CFU">
    <xs:annotation>
      <xs:documentation> CFU Call forwarding unconditional </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:HLRCfBasicServiceGroupTypeExt" minOccurs="0"/>
          <!-- Release2: making isdnNumber and status optional (applies through whole schema for CF and Call Barring elements -->
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CFB">
    <xs:annotation>
      <xs:documentation> CFB Call forwarding on mobile subscriber busy </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CFNrc">
    <xs:annotation>
      <xs:documentation> CFNrc Call forwarding on mobile subscriber not reachable </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CFNry">
    <xs:annotation>
      <xs:documentation> CFNry Call forwarding on no reply </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AllCCF">
    <xs:annotation>
      <xs:documentation> AllCCF All conditional call forwarding services </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AllCF">
    <xs:annotation>
      <xs:documentation> AllCF All call forwarding services </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
          <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CFD">
    <xs:annotation>
      <xs:documentation> CFD Call Forwarding by Default </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <!-- xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/ -->
          <xs:element name="replaceCFConditional" type="xs:int" minOccurs="0"/>
          <xs:element name="serviceAvailable" type="xs:int" minOccurs="0"/>
          <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CAW">
    <xs:annotation>
      <xs:documentation> CAW Call Waiting </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CUG">
    <xs:annotation>
      <xs:documentation> CUG Closed User Group </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="cugIndex" type="xs:int" minOccurs="0"/>
          <xs:element name="cugInterlockCode" type="xs:string" minOccurs="0"/>
          <xs:element name="dataNetworkIdentityCode" type="subscriber:NumericString" minOccurs="0"/>
          <xs:element name="intraCUGRestriction" type="xs:int" minOccurs="0"/>
          <xs:sequence>
            <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0" maxOccurs="5"/>
          </xs:sequence>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CUGBSG">
    <xs:annotation>
      <xs:documentation> CUG Closed User Group - Basic Service Group related Data </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType"/>
          <xs:element name="preferentialCUGIndex" type="xs:int" minOccurs="0"/>
          <xs:element name="interCUGAccessType" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SUBRELRO">
    <xs:annotation>
      <xs:documentation> Subscriber Related Routing</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="subscriberRouting" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VGRPID">
    <xs:annotation>
      <xs:documentation> Voice Call Group ID</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="voiceGroupId" type="xs:int"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="ComCbControlType">
    <xs:annotation>
      <xs:documentation> Enumeration of valid values for COMCB_Password used in COMCB </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="byOperator"/>
      <xs:enumeration value="bySubscriber"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="COMCB">
    <xs:annotation>
      <xs:documentation> COMCB Common call barring attributes </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="control" type="subscriber:ComCbControlType" minOccurs="0"/>
          <xs:element name="password" type="xs:string" minOccurs="0"/>
          <xs:element name="pwBlockedBN" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="BarringBasicServiceGroupType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for BasicServiceGroup element in call forwarding</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="TS10-telephony"/>
      <xs:enumeration value="TS20-shortMessage"/>
      <xs:enumeration value="TS60-fax"/>
      <xs:enumeration value="BS20-dataAsync"/>
      <xs:enumeration value="BS30-dataSync"/>
      <xs:enumeration value="BS40-padAccess"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="BAOC">
    <xs:annotation>
      <xs:documentation> BAOC Barring of all outgoing calls </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BOIC">
    <xs:annotation>
      <xs:documentation> BOIC Barring of all outgoing international calls </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BOICexHC">
    <xs:annotation>
      <xs:documentation> BOICexHC Barring of all outgoing international calls except those directed to the home plmn country </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BORO">
    <xs:annotation>
      <xs:documentation> BORO    Barring of outgoing calls when roaming outside the home plmn country </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="barrGSM" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BAIC">
    <xs:annotation>
      <xs:documentation> BAIC Barring of all incoming calls </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BICRoam">
    <xs:annotation>
      <xs:documentation> BICRoam Barring of all incoming calls when roaming outside the home plmn country </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="EMLPPPriorityType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for EMLPPPriorityTypeb used in EMLPP</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <!-- WPS extension -->
      <xs:enumeration value="priorityA"/>
      <xs:enumeration value="priorityB"/>
      <!-- WPS extension -->
      <xs:enumeration value="priority0"/>
      <xs:enumeration value="priority1"/>
      <xs:enumeration value="priority2"/>
      <xs:enumeration value="priority3"/>
      <xs:enumeration value="priority4"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="EMLPP">
    <xs:annotation>
      <xs:documentation> EMLPP eMLPP </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="maxPriority" type="subscriber:EMLPPPriorityType" minOccurs="0"/>
          <xs:element name="defaultPriority" type="subscriber:EMLPPPriorityType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Release2: definition of type Multi Device, JANUS feature -->
  <xs:simpleType name="MultiDeviceIndicatorType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid multi-device indicators</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="master"/>
      <xs:enumeration value="slave1"/>
      <xs:enumeration value="slave2"/>
      <xs:enumeration value="slave3"/>
      <xs:enumeration value="slave4"/>
      <xs:enumeration value="slave5"/>
      <xs:enumeration value="slave6"/>
      <xs:enumeration value="slave7"/>
      <xs:enumeration value="slave8"/>
      <xs:enumeration value="slave9"/>
      <xs:enumeration value="slave10"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ActivePageDeviceType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid multi-devices</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="master"/>
      <xs:enumeration value="slave1"/>
      <xs:enumeration value="slave2"/>
      <xs:enumeration value="slave3"/>
      <xs:enumeration value="slave4"/>
      <xs:enumeration value="slave5"/>
      <xs:enumeration value="slave6"/>
      <xs:enumeration value="slave7"/>
      <xs:enumeration value="slave8"/>
      <xs:enumeration value="slave9"/>
      <xs:enumeration value="slave10"/> 
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="MultiDeviceImsiType">
    <xs:annotation>
      <xs:documentation>SCO for multi device IMSI description</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="mdIndicator" type="subscriber:MultiDeviceIndicatorType"/>
          <xs:element name="mdImsi" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MultiDeviceMsisdnType">
    <xs:annotation>
      <xs:documentation>SCO for multi device MSISDN description</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="mdIndicator" type="subscriber:MultiDeviceIndicatorType"/>
          <xs:element name="mdMsisdn" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MultiDeviceType">
    <xs:annotation>
      <xs:documentation>Support of multi device functionality</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="multiDeviceIndicator" type="subscriber:MultiDeviceIndicatorType" minOccurs="0"/>
          <xs:element name="activePageDevice" type="subscriber:ActivePageDeviceType" minOccurs="0"/>
          <xs:element name="multiDeviceImsi" type="subscriber:MultiDeviceImsiType" minOccurs="0" maxOccurs="10"/>
          <xs:element name="multiDeviceMsisdn" type="subscriber:MultiDeviceMsisdnType" minOccurs="0" maxOccurs="10"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RS">
    <xs:annotation>
      <xs:documentation> RS Regional Subscription </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="countryCode" type="xs:string" minOccurs="0"/>
          <xs:element name="networkDestinationCode" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode01" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode02" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode03" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode04" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode05" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode06" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode07" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode08" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode09" type="xs:string" minOccurs="0"/>
          <xs:element name="zoneCode10" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SRF">
    <xs:annotation>
      <xs:documentation> SRF Subscriber Related Features </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="featureId" type="xs:int" minOccurs="0"/>
          <xs:element name="featureNumber" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  
  <xs:simpleType name="PDPAccessPointNameAreaType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for PDPAccessPointNameArea</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="HPLMN"/>
      <xs:enumeration value="ALLPLMN"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="IPAddress">
    <xs:annotation>
      <xs:documentation>Either ipv4 or ipv6 address is allowed</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <!-- IPV4 patter address-->
      <xs:pattern value="([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])"/>
      <!-- All possible IPV6 patterns -->
      <xs:pattern value="([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}"/>
      <!-- full format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
      <xs:pattern value="(([0-9A-Fa-f]{1,4}:){6})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <!-- compressed format -->
      <xs:pattern value="::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,6})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,5})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){1})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,4})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,3})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,2})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,1})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){5})::(([0-9A-Fa-f]{1,4})?)"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){6})::"/>
      <!-- compressed format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
      <xs:pattern value="::(([0-9A-Fa-f]{1,4}:){0,5})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}:){0,4})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}))::(([0-9A-Fa-f]{1,4}:){0,3})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}:){0,2})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}:){0,1})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="PDPContext">
    <xs:annotation>
      <xs:documentation> PDPContext </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="id" type="xs:int" minOccurs="0"/>
          <xs:element name="type" type="xs:int" minOccurs="0"/>
          <xs:element name="address" type="xs:string" minOccurs="0"/>
          <xs:element name="qosProfile" type="xs:string" minOccurs="0"/>
          <xs:element name="apn" type="xs:string" minOccurs="0"/>
          <xs:element name="apnArea" type="subscriber:PDPAccessPointNameAreaType" minOccurs="0"/>
          <xs:element name="chargingCharacteristics" type="subscriber:ChargingCharacteristics" minOccurs="0" maxOccurs="4"/>
          <xs:element name="chargingCharacteristicsProfile" type="xs:int" minOccurs="0"/>
          <xs:element name="chargingCharacteristicsBehavior" type="xs:int" minOccurs="0"/>
          <xs:element name="lipaPermission" type="subscriber:EPSLipaPermission" minOccurs="0"/>
          <!-- FC122_003531 Support of IPv4v6 Dual Stack in Subscriber Profile -->
          <xs:element name="extType" type="xs:int" minOccurs="0"/>
          <xs:element name="extAddress" type="subscriber:IPAddress" minOccurs="0"/>
          <!-- NTHLR6SP1: RDSP enhancements -Start-->
          <xs:element name="refPdpContextName" type="xs:string" minOccurs="0"/>
          <!-- NTHLR6SP1: RDSP enhancements -End-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CSI">
    <xs:annotation>
      <xs:documentation> Camel Subscription Information  base class</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
          <xs:element name="csiState" type="xs:int" minOccurs="0"/>
          <xs:element name="csiNotify" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="DCSI">
    <xs:annotation>
      <xs:documentation> DCSI Dialed CAMEL subscription information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MCSI">
    <xs:annotation>
      <xs:documentation> MCSI Mobility management CAMEL subscription information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MGCSI">
    <xs:annotation>
      <xs:documentation> MGCSI CAMEL subscription information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MTSMSCSI">
    <xs:annotation>
      <xs:documentation> MTSMSCSI Mobile terminating SMS CAMEL subscription information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="OCSI">
    <xs:annotation>
      <xs:documentation> OCSI Originating Camel Subscription Information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TCSI">
    <xs:annotation>
      <xs:documentation> TCSI Terminating Camel Subscription Information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GPRSCSI">
    <xs:annotation>
      <xs:documentation> GPRSCSI GPRSCSIServices </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SMSCSI">
    <xs:annotation>
      <xs:documentation> SMSCSI SMS Camel Subscription Information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SSCSI">
    <xs:annotation>
      <xs:documentation> SSCSI SSCSIServices </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VTCSI">
    <xs:annotation>
      <xs:documentation> VTCSI VMSC terminating CAMEL subscription information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:CSI"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="UCSISUB">
    <xs:annotation>
      <xs:documentation> UCSISUB This data item is used in customer specific projects for Prepay subscribers. It controls access to the account Query etc. This object can be applied multiple times</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="ucsiServCode" type="xs:int" minOccurs="0"/>
          <xs:element name="gsmscfAddress" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NotifyToCse">
    <xs:annotation>
      <xs:documentation>Notification flag, which indicates the necessity to notify the Camel Service Environment, gsmSCF(GSM Service control function), of the change of subscriber data.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="callForwardingServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="callBarringServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="operatorDeterminedBarringServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="clipServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="clirServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="callWaitServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="callHoldServices" type="xs:boolean" minOccurs="0"/>
          <xs:element name="explicitCallTransferServices" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- msubType -->
  <xs:simpleType name="MobileSubscriberType">
    <xs:annotation>
      <xs:documentation>This attribute specifies the type of the mobile subscriber.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="genericSubscriber"/>
      <xs:enumeration value="lmuSubscriber"/>
      <!-- Location Measurement Unit Subscriber -->
      <!-- FC123_107249_SWAP_phase_1 :: Start -->
      <xs:enumeration value="m2mSubscriber"/>
      <!-- FC123_107249_SWAP_phase_1 :: Stop -->
    </xs:restriction>
  </xs:simpleType>
  <!-- TIF subscriber -->
  <xs:complexType name="TifSubscriber">
    <xs:annotation>
      <xs:documentation>
                Translation Information Flag subscriber.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="tifCsiActive" type="xs:boolean" minOccurs="0"/>
          <xs:element name="tifCsiNotificationToCse" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FollowMe, FollowMeSupervisor -->
  <xs:simpleType name="FollowMeBasicServiceGroupType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for BasicServiceGroup element in FollowMe</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="TS10-telephony"/>
      <xs:enumeration value="TS60-fax"/>
      <xs:enumeration value="BS20-dataAsync"/>
      <xs:enumeration value="BS30-dataSync"/>
      <xs:enumeration value="BS40-padAccess"/>
      <!-- Really also this? -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FollowMe">
    <xs:annotation>
      <xs:documentation>
                Follow Me - Enables a mobile subscriber A to have control over the Follow Me data
                of a remote party B such that subsequent calls directed to remote party B are forwarded
                to subscriber A.
                Follow Me supervisor - Allows to modify the Follow Me data of the remote party who has
                been registered to another initiating subscriber.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="basicServiceGroup" type="subscriber:FollowMeBasicServiceGroupType" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="followMeSupervisor" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Access Matrix for registration -->
  <xs:simpleType name="FollowMeClassOfRegistration">
    <xs:annotation>
      <xs:documentation>
                This attribute holds the classes of registration for the "follow me" supplementary service.
                The following values are allowed:
                    A  Engine/ Train cab- radio basic functions
                    B  Maintenance service user
                    C  Operation support user
                    D  Customer support user
                    E  Train Controller
                This attribute allows entires of a single value or of multiple logical values. The logical
                values are combined to one physical value in database by OR combination.
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="classA"/>
      <xs:enumeration value="classB"/>
      <xs:enumeration value="classC"/>
      <xs:enumeration value="classD"/>
      <xs:enumeration value="classE"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- LCS Basic Procedure -->
  <xs:complexType name="CUSS">
    <xs:annotation>
      <xs:documentation>
                LCS universal status. This attribute specifies the status of Call universal supplementary
                service. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CRELSS">
    <xs:annotation>
      <xs:documentation>
                LCS call related status. This attribute specifies the status of call related supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CURELSS">
    <xs:annotation>
      <xs:documentation>
                LCS call unrelated status. This attribute specifies the status of call unrelated supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PLMNOSS">
    <xs:annotation>
      <xs:documentation>
                LCS PLMN operator status. This attribute specifies the status of PLMN operator supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BasicSelfLocation">
    <xs:annotation>
      <xs:documentation>
                Basic Self Location. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AutoSelfLocation">
    <xs:annotation>
      <xs:documentation>
                Auto Self Location. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TransferToThirdParty">
    <xs:annotation>
      <xs:documentation>
                Mobile subscriber requests transfer of own location to another LCS client. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Charging characteristics -->
  <xs:complexType name="GeneralChargingCharacteristics">
    <xs:annotation>
      <xs:documentation>
                General Charging characteristics.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="chargingCharacteristics" type="subscriber:ChargingCharacteristics" minOccurs="0" maxOccurs="4"/>
          <xs:element name="chargingCharacteristicsProfile" type="xs:int" minOccurs="0"/>
          <xs:element name="chargingCharacteristicsBehavior" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="ChargingCharacteristics">
    <xs:annotation>
      <xs:documentation>
                This attribute specifies allowed charging characteristics. This attribute only accepts a single value
                entry.
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="none"/>
      <xs:enumeration value="normal"/>
      <xs:enumeration value="prepaid"/>
      <xs:enumeration value="flatRate"/>
      <xs:enumeration value="hotBilling"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- CallBack -->
  <xs:complexType name="CALLBACK">
    <xs:annotation>
      <xs:documentation>
                Call Back feature is introduced in the GSM mobile system as a national PLMN specific
                supplementary service. Enables a call that cannot be completed to the called subscriber
                to be automatically forwarded to a special number. Normally to a voice mail system (VMS).
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="status" type="xs:int" minOccurs="0"/>
          <xs:element name="vmsShortCode" type="xs:string" minOccurs="0"/>
          <xs:element name="cbTimer" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Operator Barring GPRS -->
  <xs:simpleType name="OperatorBarringGPRS">
    <xs:annotation>
      <xs:documentation>
                Operator Determined Barring for packet oriented access (GPRS). This attribute indicates
                which one of the following categories of operator determined barring of GPRS applies to
                the mobile subscriber.
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="allPOServices"/>
      <xs:enumeration value="hplmnAccessPoints"/>
      <xs:enumeration value="vplmnAccessPoints"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- UMTS SUbscriber -->
  <xs:complexType name="UmtsSubscriber">
    <xs:annotation>
      <xs:documentation>
                This attribute identifies the type of UMTS Subscription.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="accTypeGSM" type="xs:boolean" minOccurs="0"/>
          <xs:element name="accTypeGERAN" type="xs:boolean" minOccurs="0"/>
          <xs:element name="accTypeUTRAN" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- VBS Data-->
  <xs:complexType name="VbsData">
    <xs:annotation>
      <xs:documentation>
                Voice Broadcast Service data.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="broadCastId" type="xs:string" minOccurs="0"/>
          <xs:element name="initPerm" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FTNO Type -->
  <xs:simpleType name="FtnoType">
    <xs:annotation>
      <xs:documentation>
                Type of FTNO format.
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="unknown"/>
      <xs:enumeration value="internat"/>
      <!--xs:enumeration value="longUnknown"/-->
      <!--xs:enumeration value="longInternat"/-->
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="FeaturesNotSupportedByVLRType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for featuresNotSupportedByVLR in VlrMobData</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="regionalRoaming"/>
      <xs:enumeration value="operatorDetermindBarring"/>
      <xs:enumeration value="closedUserGroup"/>
      <xs:enumeration value="oCSI"/>
      <xs:enumeration value="tCSI"/>
      <xs:enumeration value="sSCSI"/>
      <xs:enumeration value="camelDCSI"/>
      <xs:enumeration value="camelVTCSI"/>
      <xs:enumeration value="camelSMSCSI"/>
      <xs:enumeration value="camelMTSMSCSI"/>
      <xs:enumeration value="camelMCSI"/>
      <xs:enumeration value="sMSMT"/>
      <xs:enumeration value="lateFeature"/>
      <xs:enumeration value="aOCC"/>
      <xs:enumeration value="generalBearerService20"/>
      <xs:enumeration value="generalBearerService30"/>
      <xs:enumeration value="generalBearerService40"/>
      <xs:enumeration value="cCBSA"/>
      <xs:enumeration value="cCBSB"/>
      <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
      <xs:enumeration value="oICK"/>
      <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
      <xs:enumeration value="extCamel"/>
      <xs:enumeration value="superCharger"/>
      <!-- HLR 5.0 LF: FC122_001926: Automatic Redirection of Call -->
      <xs:enumeration value="autoCallRedir"/>
      <xs:enumeration value="sset"/>
      <!--HLR 5.0 SP1 Camel Core INAP -->
      <xs:enumeration value="emoick"/>
      <!--HLR 5.0 SP1 Camel Core INAP -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="VlrMobData">
    <xs:annotation>
      <xs:documentation> VlrMobData - Mobility data for VLR (GSM) </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="vlrIdValid" type="xs:boolean" minOccurs="0"/>
          <xs:element name="isdnNumberOfVLR" type="xs:string" minOccurs="0"/>
          <xs:element name="mobileTerminatingCallPossible" type="xs:boolean" minOccurs="0"/>
          <xs:element name="plmnAllowed" type="xs:boolean" minOccurs="0"/>
          <xs:element name="roamingAreaAllowed" type="xs:boolean" minOccurs="0"/>
          <xs:element name="mscAreaRestrictedReceived" type="xs:boolean" minOccurs="0"/>
          <xs:element name="msPurged" type="xs:boolean" minOccurs="0"/>
          <xs:element name="supportedCAMELPhaseByVLR" type="xs:int" minOccurs="0"/>
          <xs:element name="supportedMAPVersionForLUP" type="xs:int" minOccurs="0"/>
          <xs:element name="featuresNotSupportedByVLR" type="subscriber:FeaturesNotSupportedByVLRType" minOccurs="0" maxOccurs="25"/>
          <xs:element name="prohFtnoUpdInVlrFail" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ts10BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="ts20BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="ts60BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="bs20BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="bs30BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="bs40BarrByCb" type="xs:int" minOccurs="0"/>
          <xs:element name="vlrSupportsLongFtno" type="xs:boolean" minOccurs="0"/>
          <xs:element name="supportedLCSCapabilitySetsForVLR" type="xs:int" minOccurs="0"/>
          <xs:element name="offeredCAMEL4CSIsForVLR" type="xs:int" minOccurs="0"/>
          <xs:element name="mscNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="previousIsdnNumberOfVLR" type="xs:string" minOccurs="0" maxOccurs="5"/>
          <xs:element name="emoickSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="ssetSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="locUpdateCSTimestamp" type="xs:dateTime" minOccurs="0"/>
          <!-- HLR 7.0 base: FC003991_MTRF: Start -->
          <xs:element name="allowedMTRF" type="xs:boolean" minOccurs="0"/>
          <!-- HLR 7.0 base: FC003991_MTRF: End -->
          <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
          <xs:element name="csTraceStatus" type="subscriber:HLRTraceStates" minOccurs="0"/>
          <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="FeaturesNotSupportedBySGSNType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for featuresNotSupportedBySGSN in SgsnMobData</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="sMS"/>
      <xs:enumeration value="gprsCsi"/>
      <xs:enumeration value="smsCsi"/>
      <xs:enumeration value="mtSmsCsi"/>
      <xs:enumeration value="odb"/>
      <xs:enumeration value="mgCsi"/>
      <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
      <xs:enumeration value="extCamel"/>
      <xs:enumeration value="superCharger"/>
      <!--HLR 5.0 Base: FC122_002631 Support of Roaming Zone codes from HLR to SGSN -->
      <xs:enumeration value="regionalSubscriptionNotSupSGSN"/>
      <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
      <xs:enumeration value="tadsDataRetrieval"/>
      <!-- HLR 7.0 base: FC122_005378 HLR/HSS interworking MM -->
      <xs:enumeration value="initialAttach"/>
    </xs:restriction>
  </xs:simpleType>
  <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
  <xs:simpleType name="HLRIMSVoiceOverPSSupport">
    <xs:annotation>
      <xs:documentation>ims voice over PS support enum</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="imsVoiceOverPSNotSupported"/>
      <xs:enumeration value="imsVoiceOverPSSupported"/>
      <xs:enumeration value="imsVoiceOverPSSupportUnknown"/>
    </xs:restriction>
  </xs:simpleType>
  <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
  <xs:simpleType name="HLRTraceStates">
    <xs:annotation>
      <xs:documentation>Gsm Tracing states enum </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="ACTIVE"/>
      <xs:enumeration value="ACTIVEPENDING"/>
      <xs:enumeration value="NOTACTIVE"/>
    </xs:restriction>
  </xs:simpleType>
  <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
  <xs:complexType name="SgsnMobData">
    <xs:annotation>
      <xs:documentation> SgsnMobData - Mobility data for SGSN (GPRS) </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="sgsnIdValid" type="xs:boolean" minOccurs="0"/>
          <xs:element name="isdnNumberOfSGSN" type="xs:string" minOccurs="0"/>
          <xs:element name="plmnAllowed" type="xs:boolean" minOccurs="0"/>
          <xs:element name="roamingAreaAllowed" type="xs:boolean" minOccurs="0"/>
          <xs:element name="gprsAllowed" type="xs:boolean" minOccurs="0"/>
          <xs:element name="supportedCAMELPhaseBySGSN" type="xs:int" minOccurs="0"/>
          <xs:element name="supportedMAPVersionForLUP" type="xs:int" minOccurs="0"/>
          <xs:element name="featuresNotSupportedBySGSN" type="subscriber:FeaturesNotSupportedBySGSNType" minOccurs="0" maxOccurs="11"/>
          <xs:element name="sgsnCamelNot" type="xs:boolean" minOccurs="0"/>
          <xs:element name="sgsnExtQos" type="xs:boolean" minOccurs="0"/>
          <xs:element name="gprsCsiSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="gprsSmsCsiSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="gprsMtSmsCsiSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="mgCsiSubst" type="xs:int" minOccurs="0"/>
          <xs:element name="supportedLCSCapabilitySetsForSGSN" type="xs:int" minOccurs="0"/>
          <xs:element name="offeredCAMEL4CSIsForSGSN" type="xs:int" minOccurs="0"/>
          <xs:element name="sgsnAddress" type="xs:string" minOccurs="0"/>
          <xs:element name="previousIsdnNumberOfSGSN" type="xs:string" minOccurs="0" maxOccurs="5"/>
          <xs:element name="msPurged" type="xs:boolean" minOccurs="0"/>
          <!--HLR 5.0 Base: FC122_002631 Support of Roaming Zone codes from HLR to SGSN -->
          <xs:element name="sgsnAreaRestRcvd" type="xs:boolean" minOccurs="0"/>
          <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
          <xs:element name="imsVoiceOverPS" type="subscriber:HLRIMSVoiceOverPSSupport" minOccurs="0"/>
          <xs:element name="ueSrvccCapability" type="xs:int" minOccurs="0"/>
          <!-- FCC122_003520 -->
          <xs:element name="locUpdatePSTimestamp" type="xs:dateTime" minOccurs="0"/>
          <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
          <xs:element name="psTraceStatus" type="subscriber:HLRTraceStates" minOccurs="0"/>
          <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
          <!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
					<xs:element name="hlrSgsnSupportedFeatures" type="subscriber:BitString40" minOccurs="0"/>
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Sytax definition Bit String 40 -->
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
	<xs:simpleType name="BitString">
		<xs:annotation>
		  <xs:documentation>Only 0|1 is allowed</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xsd:string">
		  <xs:pattern value="(0|1)+"/>
		</xs:restriction>
	  </xs:simpleType>
	<xs:simpleType name="BitString40">
		<xs:annotation>
			<xs:documentation>Bit string with length 40 characters exactly</xs:documentation>
		</xs:annotation>
		<xs:restriction base="subscriber:BitString">
			<xs:minLength value="40"/>
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
  <xs:simpleType name="MobileStationNotReachableReasonType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for mobileStationNotReachableReason... in Data</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="msNoPagingRespViaMsc"/>
      <xs:enumeration value="msImsiDetached"/>
      <xs:enumeration value="msRoamingRestriction"/>
      <xs:enumeration value="msDeregInHLRForNonGPRS"/>
      <xs:enumeration value="msPurgedForNonGPRS"/>
      <xs:enumeration value="msNoPagingRespViaSGSN"/>
      <xs:enumeration value="msGPRSDetached"/>
      <xs:enumeration value="msDeregInHLRForGPRS"/>
      <xs:enumeration value="msPurgedForGPRS"/>
      <xs:enumeration value="unidentifiedSubViaMSC"/>
      <xs:enumeration value="unidentifiedSubViaSGSN"/>
      <xs:enumeration value="deregInHlrHssForIMS"/>
      <xs:enumeration value="noResponseViaIP_SM_GW"/>
      <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
      <xs:enumeration value="ueDeregistered"/>
      <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
      <!-- FC123_107926 HLR supports MSISDN-less MTC devices - Start -->
      <xs:enumeration value="msTemporarilyUnavailable"/>
      <!-- FC123_107926 HLR supports MSISDN-less MTC devices end-->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="MsgWaitData">
    <xs:annotation>
      <xs:documentation> MsgWaitData - SMS message waiting data </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="msisdnUsedForAlertingOfSMSServiceCentre" type="xs:string" minOccurs="0"/>
          <xs:element name="mobileCapacityExceededFlag" type="xs:boolean" minOccurs="0"/>
          <xs:element name="mobileStationNotReachableFlag" type="xs:boolean" minOccurs="0"/>
          <xs:element name="addressOfSMSServicesCentres" type="xs:string" minOccurs="0" maxOccurs="32"/>
          <xs:element name="mobileStationNotReachableFlagForGPRS" type="xs:boolean" minOccurs="0"/>
          <xs:element name="mobileStationNotReachableReasonForGPRS" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
          <xs:element name="mobileStationNotReachableReasonForGSM" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
          <xs:element name="alertMsisdn" type="xs:string" minOccurs="0" maxOccurs="32"/>
          <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
          <xs:element name="ueNotReachableForIP" type="xs:boolean" minOccurs="0"/>
          <xs:element name="ueNotReachableReason" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="INService">
    <xs:annotation>
      <xs:documentation> IN Service</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="OICK">
    <xs:annotation>
      <xs:documentation> Originating IN Category Key</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:INService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EOICK">
    <xs:annotation>
      <xs:documentation> Extended CAMEL Originating IN Category Key</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:INService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EOINCI">
    <xs:annotation>
      <xs:documentation> Extended CAMEL Originating IN Capability Indicator</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:INService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TICK">
    <xs:annotation>
      <xs:documentation> Terminating IN Category Key</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:TerminatingINService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ETICK">
    <xs:annotation>
      <xs:documentation> Extended CAMEL Terminating IN Category Key</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:TerminatingINService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ETINCI">
    <xs:annotation>
      <xs:documentation> Extended CAMEL Terminating IN Capability Indicator</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:TerminatingINService"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- define XML schema for flexible msisdn change -->
  <xs:complexType name="FlexibleMsisdnChangeOperation">
    <xs:complexContent>
      <xs:extension base="spml:AbstractOperation">
        <xs:sequence>
          <xs:element name="regularExpression" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- new service  usable only for msisdn based searches-->
  <xs:complexType name="ALLBS">
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Property">
    <attribute name="name" type="xs:string"/>
    <attribute name="value" type="xs:string"/>
  </xs:complexType>
  <xs:complexType name="Properties">
    <xs:sequence>
      <xs:element name="property" type="subscriber:Property" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SubMigrationOperation">
    <xs:complexContent>
      <xs:extension base="spml:AbstractMigration">
        <xs:sequence>
          <xs:element name="sourceDataModel" type="xs:string"/>
          <xs:element name="targetDataModel" type="xs:string"/>
          <element name="properties" type="subscriber:Properties"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TerminatingINService">
    <xs:annotation>
      <xs:documentation>Terminating IN Service</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
          <xs:element name="roamingType" type="subscriber:RoamingType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="RoamingType">
    <xs:annotation>
      <xs:documentation>This attribute specifies whether the subscriber is roaming in HPLMN or outside HPLMN</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="HPLMN"/>
      <xs:enumeration value="OutsideHPLMN"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="OdbEctType">
    <xs:annotation>
      <xs:documentation>This attribute indicates which of the categories of operator determined barring of explicit call transfer calls applies to the mobile subscriber</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="all"/>
      <xs:enumeration value="chargeable"/>
      <xs:enumeration value="international"/>
      <xs:enumeration value="interzonal"/>
      <xs:enumeration value="doublyChargeable"/>
      <xs:enumeration value="multiple"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ClgPtyData">
    <xs:annotation>
      <xs:documentation>
            This attribute contains information concerning the last calling number
            e.g. it indicates whether the presenation of the calling number is allowed or restricted and if the number
            was already sent in an USSD_Response to the subscriber. Multiple logical values are stored as one value
            combined by OR.
            Values:
            PresentationAllowed = 0
            PresentationRestricted = 1
            NumberUnavailable = 4
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="presentationAllowed"/>
      <xs:enumeration value="presentationRestricted"/>
      <xs:enumeration value="numberUnavailable"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="UnsignedInt9">
    <xs:annotation>
      <xs:documentation>Integer in the range 0..9</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:nonNegativeInteger">
      <xs:maxInclusive value="9"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="NumericString6_15">
    <xs:annotation>
      <xs:documentation>Numeric String of length 15</xs:documentation>
    </xs:annotation>
    <xs:restriction base="subscriber:NumericString">
      <xs:minLength value="6"/>
      <xs:maxLength value="15"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="MultiSimMemberType">
    <xs:annotation>
      <xs:documentation>Support of multi SIM member</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="multiSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
          <xs:element name="multiSimImsi" type="subscriber:NumericString6_15" minOccurs="0"/>
          <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0" maxOccurs="6"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MultiSimType">
    <xs:annotation>
      <xs:documentation>Support of multi SIM functionality</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="subSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
          <xs:element name="multiSimMember" type="subscriber:MultiSimMemberType" minOccurs="0" maxOccurs="10"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="USSDService">
    <xs:annotation>
      <xs:documentation> USSD Service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="active" type="xs:boolean" minOccurs="0"/>
          <xs:element name="refAdvtName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="LcnService">
    <xs:annotation>
      <xs:documentation> LCN </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:USSDService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="OwnMsisdnService">
    <xs:annotation>
      <xs:documentation> OWN MSISDN </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:USSDService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VlrIdService">
    <xs:annotation>
      <xs:documentation> VLR ID </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:USSDService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ActualTimeService">
    <xs:annotation>
      <xs:documentation> ACTUAL TIME </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:USSDService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="UssdClirService">
    <xs:annotation>
      <xs:documentation> USSD CLIR Service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:USSDService"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="UCSISUBEXT">
    <xs:annotation>
      <xs:documentation> UCSISUBEXT It controls access to the account Query etc. This object can be applied multiple times</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="ucsiServCodeExt" type="xs:string" minOccurs="0"/>
          <xs:element name="gsmscfAddress" type="xs:string" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for UGCSI Feature: Start-->
          <xs:element name="origRefIndicator" type="subscriber:HLRRefIndicatorType" minOccurs="0"/>
          <xs:element name="destRefIndicator" type="subscriber:HLRRefIndicatorType" minOccurs="0"/>
          <xs:element name="ussdSendingOptions" type="subscriber:HLRUssdSendingOptions" minOccurs="0" maxOccurs="unbounded"/>
          <!-- NTHLR6 - Introduced for UGCSI Feature: End-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="HlrProfileType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for Subscriber Profile Type</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="mssMultiSim"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="HlrMssMultiSimType">
    <xs:annotation>
      <xs:documentation>Support of MSC-S based MultiSim for subscriber functionality</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="denyDirectSMS" type="xs:boolean" minOccurs="0"/>
          <xs:element name="denyDirectCall" type="xs:boolean" minOccurs="0"/>
          <xs:element name="groupMSISDN" type="xs:string" minOccurs="0"/>
          <xs:element name="primarySMSTerminal" type="xs:string" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
          <xs:element name="allowSyncToGroup" type="xs:boolean" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
          <!-- FC122_006583 - USSD for MSS based MultiSIM hunting group - START -->
          <xs:element name="refAdvtName" type="xs:string" minOccurs="0"/>
          <!-- FC122_006583 - USSD for MSS based MultiSIM hunting group - END -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
  <xs:complexType name="SpatialTrigger">
    <xs:annotation>
      <xs:documentation>Spatial Trigger functionality</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="spatialTriggerEvent" type="xs:int" minOccurs="0"/>
          <xs:element name="gmlcId" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Definitions of application specific extensions of SPML  schema END-->
  <!-- hlr45.v100 END -->
    <!-- IDENTITY SWAP Related Identifiers                                      -->
    <!--************************************************************************-->
    <xs:simpleType name="HLRSwapAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="imsi"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HLRSwapIdentifier">
        <xs:simpleContent>
            <xs:extension base="spml:ID">
                <xs:attribute name="alias" type="subscriber:HLRSwapAliasType" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <!-- canmsub.v100 START -->
  <!--************************************************************************-->
  <!--          CanMsub Operation                                                                         -->
  <!--************************************************************************-->
  <xs:complexType name="canMsubOperation">
    <xs:annotation>
      <xs:documentation> Defines the canMsub extended operation. The vlr.sgsn and mme types of
                                cancelation is supported.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:AbstractOperation">
        <xs:sequence>
          <xs:element name="imsi" type="subscriber:NumericString15" minOccurs="0"/>
          <xs:element name="vlr" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <xs:element name="sgsn" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <xs:element name="mme" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <!-- DU3744 : Changed as per Changes of Common AEP 14.01-->
          <xs:element name="mmeRealm" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <!-- attributes of S6D interface BEGIN-->
          <xs:element name="sgsnIdentity" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <xs:element name="sgsnRealm" type="subscriber:CanMsubTarget" minOccurs="0"/>
          <xs:element name="msubPurgedGprs" type="subscriber:CanMsubTargetBoolean" minOccurs="0"/>
          <xs:element name="gprsDataSentIndication" type="subscriber:CanMsubTargetBoolean" minOccurs="0"/>
          <!-- attributes of S6D interface END-->
          <xs:element name="force" type="xs:boolean" minOccurs="0" default="false"/>
          <xs:element name="reattachRequiredS6A" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="reattachRequiredS6D" type="xsd:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CanMsubTarget">
    <xs:annotation>
      <xs:documentation> Defines optional address attribute for canMsub targets</xs:documentation>
    </xs:annotation>
    <attribute name="address" type="xsd:string"/>
  </xs:complexType>
  <xs:complexType name="CanMsubTargetBoolean">
    <xs:annotation>
      <xs:documentation> Defines optional val attribute (of type boolean) for canMsub targets</xs:documentation>
    </xs:annotation>
    <attribute name="val" type="xsd:boolean"/>
  </xs:complexType>
  <!-- DU3744 : Changed as per Changes of Common AEP 14.01-->
  <xs:complexType name="CanMsubSubscriber">
    <xs:annotation>
      <xs:documentation> Defines the canMsub subscriber firstclass object                                                      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:FirstClassObject">
        <xs:sequence>
          <!-- imsi value is stored in the identifier attribute of the FCO -->
          <xs:element name="vlr" type="xs:string" minOccurs="0"/>
          <xs:element name="sgsn" type="xs:string" minOccurs="0"/>
          <xs:element name="mme" type="xs:string" minOccurs="0"/>
          <xs:element name="mmeRealm" type="xs:string" minOccurs="0"/>
          <xs:element name="mmeHostId" type="xsd:string" minOccurs="0"/>
          <xs:element name="sgsnIdentity" type="xsd:string" minOccurs="0"/>
          <xs:element name="sgsnRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="msubPurgedGprs" type="xsd:string" minOccurs="0"/>
          <xs:element name="gprsDataSentIndication" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- canmsub.v100 END -->
  <!-- mssearch.v100 START -->
  <!--************************************************************************-->
  <!-- MSSearch Operation                                                     -->
  <!--************************************************************************-->
  <xs:simpleType name="IMSI">
    <xs:annotation>
      <xs:documentation>IMSI, International Mobile Subscriber Identity </xs:documentation>
    </xs:annotation>
    <xs:restriction base="subscriber:NumericString">
      <xs:minLength value="6"/>
      <xs:maxLength value="15"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="GeographicalInformation">
    <xs:sequence>
      <xs:element name="geographicalShape" type="subscriber:GeographicalShape" minOccurs="0"/>
      <xs:element name="geographicalLatitude" type="int" minOccurs="0"/>
      <xs:element name="geographicalLongitude" type="int" minOccurs="0"/>
      <xs:element name="geographicalUncertaintyCode" type="int" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="GeographicalShape">
    <xs:restriction base="string">
      <xs:enumeration value="Ellips.Point+Uncert.Circle"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="GeodeticInformation">
    <xs:sequence>
      <xs:element name="geodeticPresentation" type="subscriber:GeodeticPresentation" minOccurs="0"/>
      <xs:element name="geodeticScreeningIndicator" type="subscriber:GeodeticScreeningIndicator" minOccurs="0"/>
      <xs:element name="geodeticShape" type="subscriber:GeodeticShape" minOccurs="0"/>
      <xs:element name="geodeticLatitude" type="int" minOccurs="0"/>
      <xs:element name="geodeticLongitude" type="int" minOccurs="0"/>
      <xs:element name="geodeticUncertaintyCode" type="int" minOccurs="0"/>
      <xs:element name="geodeticConfidence" type="int" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="GeodeticPresentation">
    <xs:restriction base="string">
      <xs:enumeration value="presentation allowed"/>
      <xs:enumeration value="presentation restricted"/>
      <xs:enumeration value="location not available"/>
      <xs:enumeration value="spare"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="GeodeticScreeningIndicator">
    <xs:restriction base="string">
      <xs:enumeration value="user provided, not verified"/>
      <xs:enumeration value="user provided, verified and passed"/>
      <xs:enumeration value="user provided, verified and failed"/>
      <xs:enumeration value="network provided"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="GeodeticShape">
    <xs:restriction base="string">
      <xs:enumeration value="Ellipsoid Point"/>
      <xs:enumeration value="Ellips.Point+Uncert.Circle"/>
      <xs:enumeration value="Point with Altit.+Uncert."/>
      <xs:enumeration value="ellipse on the ellipsoid"/>
      <xs:enumeration value="ellipsoid circle sector"/>
      <xs:enumeration value="polygon"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NetDetNotReachable">
    <xs:restriction base="string">
      <xs:enumeration value="msPurged"/>
      <xs:enumeration value="imsiDetached"/>
      <xs:enumeration value="restrictedArea"/>
      <xs:enumeration value="notRegistered"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SubscriberState">
    <xs:choice>
      <xs:element name="assumedIdle" type="boolean"/>
      <xs:element name="camelBusy" type="boolean"/>
      <xs:element name="netDetNotReachable" type="subscriber:NetDetNotReachable"/>
      <xs:element name="notProvidedFromVLR" type="boolean"/>
    </xs:choice>
  </xs:complexType>
  <xs:complexType name="CellIdOrLAI">
    <xs:sequence>
      <xs:element name="cellGlobalIdOrServiceAreaIdFixedLength" type="subscriber:CellGlobalIdOrServiceAreaIdFixedLength" minOccurs="0"/>
      <xs:element name="laiFixedLength" type="subscriber:LaiFixedLength" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="CellGlobalIdOrServiceAreaIdFixedLength">
    <xs:sequence>
      <xs:element name="mccMnc" type="string" minOccurs="0"/>
      <xs:element name="locationAreaCode" type="int" minOccurs="0"/>
      <xs:element name="cellIdentity" type="int" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="LaiFixedLength">
    <xs:sequence>
      <xs:element name="mccMnc" type="string" minOccurs="0"/>
      <xs:element name="locationAreaCode" type="int" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="LocationInformation">
    <xs:sequence>
      <xs:element name="ageOfLocationInformation" type="int" minOccurs="0"/>
      <xs:element name="geographicalInformation" type="subscriber:GeographicalInformation" minOccurs="0"/>
      <xs:element name="vlr-number" type="string" minOccurs="0"/>
      <xs:element name="locationNumber" type="string" minOccurs="0"/>
      <xs:element name="cellIdOrLAI" type="subscriber:CellIdOrLAI" minOccurs="0"/>
      <xs:element name="selectedLSA-Id" type="string" minOccurs="0"/>
      <xs:element name="msc-Number" type="string" minOccurs="0"/>
      <xs:element name="geodeticInformation" type="subscriber:GeodeticInformation" minOccurs="0"/>
      <xs:element name="currentLocationRetrieved" type="boolean" minOccurs="0"/>
      <xs:element name="sai-Present" type="boolean" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="msSearchOperation">
    <xs:complexContent>
      <xs:extension base="spml:AbstractOperation">
        <xs:sequence>
          <xs:element name="imsi" type="subscriber:IMSI"/>
          <xs:element name="infoText" type="xs:string" minOccurs="0"/>
          <xs:element name="locationInformation" type="subscriber:LocationInformation" minOccurs="0"/>
          <xs:element name="subscriberState" type="subscriber:SubscriberState" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- mssearch.v100 END -->
  <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes Begin -->
  <xs:complexType name="ROAMSUBSCRIPTION">
    <xs:annotation>
      <xs:documentation> This attributes indicates a symbolic name identifying the Roaming Subscription Info entry. </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="roamSubscriptionInfo" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes End  -->
    <!--************************************************************************-->
    <!--                               HLR Group aliases                        -->
    <!--************************************************************************-->
    <xs:simpleType name="HLRGroupIdentifierAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="identifier"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HLRGroupIdentifier">
        <xs:simpleContent>
            <xs:restriction base="spml:Identifier">
                <xs:attribute name="alias" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRGroupIdentifierFileNameType">
        <xs:simpleContent>
            <xs:restriction base="spml:IdentifierFileNameType">
                <xs:attribute name="alias" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
                <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRGroupAliasType">
        <xs:complexContent>
            <xs:restriction base="spml:AliasType">
                <xs:attribute name="name" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
                <xs:attribute name="value" type="xs:string" use="required"/>
            </xs:restriction>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--         GROUP First Class Objects    STARTS                            -->
    <!--************************************************************************-->
    <xs:complexType name="HlrGroup">
        <xs:annotation>
            <xs:documentation>Definition of class group </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                    <xs:element name="alertingType" type="subscriber:HlrGroupAlertingType" minOccurs="0"/>
                    <xs:element name="alertingTimeParallel" type="xs:int" minOccurs="0"/>
                    <xs:element name="alertingTimeSequence" type="xs:int" minOccurs="0"/>
                    <xs:element name="groupType" type="subscriber:HlrGroupType" minOccurs="0"/>
                    <xs:element name="toneType" type="subscriber:HlrGroupToneType" minOccurs="0"/>
                    <xs:element name="memberCfSupp" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="primaryLBSTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="primarySMSTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="alertDataCalls" type="subscriber:HlrGroupAlertDataCallsType" minOccurs="0"/>
                    <xs:element name="odbic" type="xs:int" minOccurs="0"/>
                    <xs:element name="groupBasicService" type="subscriber:HlrGroupBasicService" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfu" type="subscriber:CFU" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfb" type="subscriber:CFB" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfnrc" type="subscriber:CFNrc" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfnry" type="subscriber:CFNry" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfd" type="subscriber:CFD" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="msgWaitData" type="subscriber:MsgWaitData" minOccurs="0"/>
                    <xs:element name="member" type="subscriber:HlrGroupMemberType" minOccurs="0" maxOccurs="5"/>
                    <xs:element name="sset" type="subscriber:HLRSSET" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: Start-->
                    <xs:element name="primaryVoiceTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="ocsi" type="subscriber:OCSI" minOccurs="0"/>
                    <xs:element name="tcsi" type="subscriber:TCSI" minOccurs="0"/>
                    <xs:element name="odboc" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <xs:element name="refRestrictedAreaName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
                    <xs:element name="syncEnabled" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="caw" type="subscriber:CAW" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="comcb" type="subscriber:COMCB" minOccurs="0"/>
                    <xs:element name="baoc" type="subscriber:BAOC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boic" type="subscriber:BOIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boicexhc" type="subscriber:BOICexHC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="baic" type="subscriber:BAIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bicroam" type="subscriber:BICRoam" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
                    <xs:element name="huntingTypeForSms" type="subscriber:HlrGroupSmsHuntingType" minOccurs="0"/>
                    <!-- MSS Multisim enhancements - change - BEGIN -->
                    <!--<xs:element name="refPriorityListName" type="xs:string" minOccurs="0"/>-->
                    <xs:element name="smsSubData" type="subscriber:HlrSmsSubData" minOccurs="0"/>
                    <!-- MSS Multisim enhancements - change - END -->
                    <xs:element name="refAddressListName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
                    <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
                    <xs:element name="primaryBasicService" type="subscriber:HLRGRPPBS" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - Start -->
                    <xs:element name="cf" type="subscriber:HLRCallForward" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - End -->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HlrGroupMemberType">
        <xs:annotation>
            <xs:documentation>Support of MSC-S based MultiSim for group functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
                    <xs:element name="imsi" type="xs:string" minOccurs="0"/>
                    <xs:element name="huntingOrder" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <xs:element name="restrictedCsPlmnId" type="xs:int" minOccurs="0"/>
                    <xs:element name="restrictedPsPlmnId" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
                    <xs:element name="allowSyncFromGroup" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HlrGroupBasicService">
        <xs:annotation>
            <xs:documentation>List basic services that can be assigned to a Group </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="serviceName" type="subscriber:HlrGroupServiceNameType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="HlrGroupServiceNameType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Profile Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ts11"/>
            <xs:enumeration value="ts21"/>
            <xs:enumeration value="ts22"/>
            <xs:enumeration value="ts61"/>
            <xs:enumeration value="ts62"/>
            <xs:enumeration value="bs20genr"/>
            <xs:enumeration value="bs21"/>
            <xs:enumeration value="bs22"/>
            <xs:enumeration value="bs23"/>
            <xs:enumeration value="bs24"/>
            <xs:enumeration value="bs25"/>
            <xs:enumeration value="bs26"/>
            <xs:enumeration value="bs30genr"/>
            <xs:enumeration value="bs31"/>
            <xs:enumeration value="bs32"/>
            <xs:enumeration value="bs33"/>
            <xs:enumeration value="bs34"/>
            <xs:enumeration value="bs40genr"/>
            <xs:enumeration value="bs41"/>
            <xs:enumeration value="bs42"/>
            <xs:enumeration value="bs44"/>
            <xs:enumeration value="bs45"/>
            <xs:enumeration value="bs46"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Group Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="singleUser"/>
            <xs:enumeration value="multipleUser"/>
            <xs:enumeration value="singleUserSpecial"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupAlertingType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Alerting Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="bothWithParallelPrefered"/>
            <xs:enumeration value="parallel"/>
            <xs:enumeration value="sequential"/>
            <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: Start-->
            <xs:enumeration value="intelligent"/>
            <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: End-->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupAlertDataCallsType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Alert Data calls Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="notAlerted"/>
            <xs:enumeration value="sequential"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupToneType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Tone Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="simpleTone"/>
            <xs:enumeration value="ringbackTone"/>
        </xs:restriction>
    </xs:simpleType>
  <!--************************************************************************-->
  <!--           GROUP First Class Objects    ENDS                            -->
  <!--************************************************************************-->
  <!-- HLR 5.0 LF: FC122_003888 - Proprietary Nokia IN Short Message Service -->
  <xs:complexType name="INSMS">
    <xs:annotation>
      <xs:documentation> IN Short Message Service</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
          <xs:element name="state" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- HLR 5.0 LF: FC122_001926 - Automatic Redirection of Calls -->
  <xs:complexType name="ARC">
    <xs:annotation>
      <xs:documentation> Automatic Redirection of Calls</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="active" type="xs:boolean" minOccurs="0"/>
          <xs:element name="redirInd" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Changes for FC122_003779::Account Code Handling -->
  <xs:complexType name="HlrAcc">
    <xs:annotation>
      <xs:documentation> Account Code </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="active" type="xs:boolean" minOccurs="0"/>
          <xs:element name="accountCode" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- HLR 5.0 SP1:FC122_003784: PRN retry  -->
  <xs:complexType name="HlrCSFallback">
    <xs:annotation>
      <xs:documentation>HLR CS Fallback subscription</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="prnRetry" type="xs:boolean" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for CSFallback - MTRR Feature: Start-->
          <xs:element name="mtrr" type="xs:boolean" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for CSFallback - MTRR Feature: End-->
          <!-- NTHLR7 - Introduced for CSFallback - MTRF Feature: Start-->
          <xs:element name="mtrf" type="xs:boolean" minOccurs="0"/>
          <!-- NTHLR7 - Introduced for CSFallback - MTRF Feature: End-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--HLR 5.0 SP1: FC122_002113_SMSImprovements-->
  <xs:complexType name="HlrSmsSubData">
    <xs:annotation>
      <xs:documentation>HLR SMSSubData</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="refPriorityListName" type="xs:string" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
          <xs:element name="ipSmGwAddress" type="xs:string" minOccurs="0"/>
          <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
          <!--NTHLR7 - Introduced for FC122_005383: HLR-HSS interworkingFeature:Start-->
          <xs:element name="refSCAddress" type="xs:string" minOccurs="0"/>
          <!--NTHLR7- Introduced for FC122_005383: HLR-HSS interworking Feature: End-->
          <!-- Attributes used via HSS EXP :: Start -->
					<xs:element name="msCapExceed" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="msNotRchMMEReason" type="xsd:string" minOccurs="0"/>
          <xs:element name="msNotRchMMEFlag" type="xsd:boolean" minOccurs="0"/>
					<!-- Attributes used via HSS EXP :: End -->  
          <!-- REG-22385 : Start -->
					<xs:element name="mobileStationNotReachableFlag" type="xs:boolean" minOccurs="0"/>
					<xs:element name="mobileCapacityExceededFlag" type="xs:boolean" minOccurs="0"/>
					<xs:element name="mobileStationNotReachableFlagForGPRS" type="xs:boolean" minOccurs="0"/>
					<xs:element name="mobileStationNotReachableReasonForGPRS" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
					<xs:element name="mobileStationNotReachableReasonForGSM" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
					<xs:element name="addressOfSMSServicesCentres" type="xs:string" minOccurs="0"/>
					<xs:element name="ueNotReachableForIP" type="xs:boolean" minOccurs="0"/>
					<xs:element name="ueNotReachableReason" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
					<!-- REG-22385 : End -->
          <!-- Start HSS-930 -->
          <xs:element name="ueNotReachableFor3gppSmsf" type="xs:string" minOccurs="0"/>
          <xs:element name="ueNotReachableForNon3gppSmsf" type="xs:string" minOccurs="0"/>
          <!-- End HSS-930 -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
  <xs:complexType name="HLRGSMTrace">
    <xs:annotation>
      <xs:documentation>HLR gsmtracing</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="traceReference" type="xs:int" minOccurs="0"/>
          <xs:element name="omcId" type="xs:string" minOccurs="0"/>
          <xs:element name="csTraceActive" type="xs:boolean" minOccurs="0"/>
          <xs:element name="csTraceType" type="xs:int" minOccurs="0"/>
          <xs:element name="psTraceActive" type="xs:boolean" minOccurs="0"/>
          <xs:element name="psTraceType" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR end -->
  <xs:complexType name="HLRServiceType">
    <xs:annotation>
      <xs:documentation> Service Type</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
          <xs:element name="state" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HLRSSET">
    <xs:annotation>
      <xs:documentation> SSET CAMEL CORE INAP information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:HLRServiceType"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HLREMOICK">
    <xs:annotation>
      <xs:documentation> EMOICK CAMEL CORE INAP information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:HLRServiceType"/>
    </xs:complexContent>
  </xs:complexType>
    <xs:complexType name="UEReachabilityReqInfo">
        <xs:annotation>
            <xs:documentation>UE-Reachability request information</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">	
                <xs:sequence>
                    <xs:element name="urppMME" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="urppSGSN" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="asAddressSh" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for UEReachability MAPJ Support: Start-->
                    <xs:element name="asAddressMAPJ" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for UEReachability MAPJ Support: Start-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
  <!-- NTHLR6 - Introduced for UGCSI Feature: Start-->
  <xs:simpleType name="HLRRefIndicatorType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for Reference Indicator</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="IMSI"/>
      <xs:enumeration value="MSISDN"/>
      <xs:enumeration value="HLR-NUM"/>
      <xs:enumeration value="VLR-NUM"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC122_003407 -->
  <xs:simpleType name="HLRUssdSendingOptions">
    <xs:annotation>
      <xs:documentation>Set of options that control the sending of parameters in USSD messages</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Send MSISDN in OEN in BSA"/>
      <xs:enumeration value="Send HLR NUM in OEN in BSA"/>
      <xs:enumeration value="Send VLR NUM in OEN in BSA"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC122_003407 -->
  <!-- NTHLR6 - Introduced for UGCSI Feature: End-->
  <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
  <xs:simpleType name="HlrGroupSmsHuntingType">
    <xs:annotation>
      <xs:documentation>Enumeration of valid values for HuntingType</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="primaryTerminal"/>
      <xs:enumeration value="intelligentHunting"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
  <!-- HLR 7.0 base: FC122_004632 CSARP START -->
  <xs:complexType name="HLRCSARP">
    <xs:annotation>
      <xs:documentation> CS Allocation / Retention Priority</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="active" type="xs:boolean" minOccurs="0"/>
          <xs:element name="priority" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- HLR 7.0 base: FC122_004632 CSARP END -->
  <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: Start -->
  <xs:complexType name="HLRFraudObservationInfo">
    <xs:annotation>
      <xs:documentation>Subscriber fraud observation information
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="observationName" type="xs:string" minOccurs="0"/>
          <xs:element name="observationNumber" type="xs:int" minOccurs="0"/>
          <xs:element name="samplingSlot" type="xs:int" minOccurs="0"/>
          <xs:element name="currentEventCount" type="xs:int" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: End -->
  <!-- FC122_005801_  Charging_based_on_Home_Area :: Start -->
  <xs:complexType name="HLRChargingBasedOnHomeArea">
    <xs:annotation>
      <xs:documentation>Subscriber charging based on home area information </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:Service">
        <xs:sequence>
          <xs:element name="chargingClass" type="subscriber:HLRChargingClassTypes" minOccurs="0"/>
          <xs:element name="chargingArea" type="xs:int" minOccurs="0" maxOccurs="3"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="HLRChargingClassTypes">
    <xs:annotation>
      <xs:documentation>Charging Class types enum </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="regional"/>
      <xs:enumeration value="national"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC122_005801_  Charging_based_on_Home_Area :: End -->
  <!-- FC123_106922_CF_Notification_Override - Start -->
  <xs:complexType name="HLRCallForward">
    <xs:annotation>
      <xs:documentation>Subscriber call forwarding services information
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="refHlrFtnoProfile" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FC123_106922_CF_Notification_Override - End -->
  <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
  <xs:simpleType name="HLRPBS">
    <xs:annotation>
      <xs:documentation>This attribute specifies the values for PBS.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="notDefined"/>
      <xs:enumeration value="ts11"/>
      <xs:enumeration value="ts21"/>
      <xs:enumeration value="ts22"/>
      <xs:enumeration value="ts61"/>
      <xs:enumeration value="ts62"/>
      <xs:enumeration value="vgcs"/>
      <xs:enumeration value="vbs"/>
      <xs:enumeration value="bs20genr"/>
      <xs:enumeration value="bs21"/>
      <xs:enumeration value="bs22"/>
      <xs:enumeration value="bs23"/>
      <xs:enumeration value="bs24"/>
      <xs:enumeration value="bs25"/>
      <xs:enumeration value="bs26"/>
      <xs:enumeration value="bs30genr"/>
      <xs:enumeration value="bs31"/>
      <xs:enumeration value="bs32"/>
      <xs:enumeration value="bs33"/>
      <xs:enumeration value="bs34"/>
      <xs:enumeration value="bs40genr"/>
      <xs:enumeration value="bs41"/>
      <xs:enumeration value="bs42"/>
      <xs:enumeration value="bs44"/>
      <xs:enumeration value="bs45"/>
      <xs:enumeration value="bs46"/>
      <xs:enumeration value="bs61a"/>
      <xs:enumeration value="bs81a"/>
      <xs:enumeration value="gprs"/>
      <xs:enumeration value="ts21Gprs"/>
      <xs:enumeration value="ts22Gprs"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="HLRGRPPBS">
    <xs:annotation>
      <xs:documentation>This attribute specifies the values for PBS For HLR Group.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="notDefined"/>
      <xs:enumeration value="ts11"/>
      <xs:enumeration value="ts21"/>
      <xs:enumeration value="ts22"/>
      <xs:enumeration value="ts61"/>
      <xs:enumeration value="ts62"/>
      <xs:enumeration value="bs20genr"/>
      <xs:enumeration value="bs21"/>
      <xs:enumeration value="bs22"/>
      <xs:enumeration value="bs23"/>
      <xs:enumeration value="bs24"/>
      <xs:enumeration value="bs25"/>
      <xs:enumeration value="bs26"/>
      <xs:enumeration value="bs30genr"/>
      <xs:enumeration value="bs31"/>
      <xs:enumeration value="bs32"/>
      <xs:enumeration value="bs33"/>
      <xs:enumeration value="bs34"/>
      <xs:enumeration value="bs40genr"/>
      <xs:enumeration value="bs41"/>
      <xs:enumeration value="bs42"/>
      <xs:enumeration value="bs44"/>
      <xs:enumeration value="bs45"/>
      <xs:enumeration value="bs46"/>
    </xs:restriction>
  </xs:simpleType>
  <!--FST123_107470 - Enhanced control of CF types to non-Macau international number-->
  <xs:simpleType name="HLROdbFtnoStandard">
    <xs:annotation>
      <xs:documentation>This attribute specifies the values for OdbFtnoStandard.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="barIntFtnoExHplmnC"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="HLROdbFtnoProprietary">
    <xs:annotation>
      <xs:documentation>This attribute specifies the values for OdbFtnoProprietary.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="barIntCondCfFtnoExHplmnC"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
	 <xs:complexType name="HLRSgsnSupportedFeatures">
        <xs:annotation>
            <xs:documentation>Subscriber Sgsn Supported Features information
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="feature" type="subscriber:HLRFeatureList" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="HLRFeatureList">
        <xs:annotation>
        <xs:documentation>This is an enumeration of all the supported features 
			of the origin hosts in S6a/S6d interface as defined by 3GPP.
		</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
            <xs:enumeration value="nrAsSecondaryRAT"/>
        </xs:restriction>
    </xs:simpleType>
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - End -->
  <!-- end add for HLR -->
  
  <!--***********************************************************************************************-->
  <!--                                        HSS Application Service                                -->
  <!--***********************************************************************************************-->
  <!--************************************************************************-->
  <!--                                     HSS aliases                        -->
  <!--************************************************************************-->
  <xs:simpleType name="HSSIdentifierAliasType">
    <xs:restriction base="xsd:string">
      <!-- Note: also using Subscriber's common aliases: identifier, imsi -->
      <xs:enumeration value="msisdn"/>
      <xs:enumeration value="impu"/>
      <xs:enumeration value="impi"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="HSSIdentifier">
    <xs:simpleContent>
      <xs:restriction base="spml:Identifier">
        <xs:attribute name="alias" type="subscriber:HSSIdentifierAliasType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="HSSIdentifierFileNameType">
    <xs:simpleContent>
      <xs:restriction base="spml:IdentifierFileNameType">
        <xs:attribute name="alias" type="subscriber:HSSIdentifierAliasType" use="required"/>
        <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="HSSAliasType">
    <xs:complexContent>
      <xs:restriction base="spml:AliasType">
        <xs:attribute name="name" type="subscriber:HSSIdentifierAliasType" use="required"/>
        <xs:attribute name="value" type="xsd:string" use="required"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!--  HSS Application Service SECOND CLASS OBJECTS                          -->
  <!--************************************************************************-->
  <xs:complexType name="HSS">
    <xs:annotation>
      <xs:documentation>HSS Application Service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:AS">
        <xs:sequence>
          <xs:element name="subscriptionId" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfDestinationRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="profileType" type="subscriber:ProfileType" minOccurs="0"/>
          <xs:element name="adminBlocked" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="defaultScscfRequired" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="maximumPrivateIds" type="xsd:int" minOccurs="0"/>
          <xs:element name="maximumPublicIds" type="xsd:int" minOccurs="0"/>
          <xs:element name="subscriptionExpiryDate" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfSipAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfHost" type="xsd:string" minOccurs="0"/>
          <xs:element name="sharedIFCSupport" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="chargingGroupId" type="xsd:string" minOccurs="0"/>
          <!-- Charging Collection Function -->
          <xs:element name="ccfPrimary" type="xsd:string" minOccurs="0"/>
          <xs:element name="ccfSecondary" type="xsd:string" minOccurs="0"/>
          <!-- Event Charging Function -->
          <xs:element name="ecfPrimary" type="xsd:string" minOccurs="0"/>
          <xs:element name="ecfSecondary" type="xsd:string" minOccurs="0"/>
          <!-- IMS10.0 RE123_105930 Changes -->
          <xs:element name="directRoutingSipAddress" type="xsd:string" minOccurs="0"/>
          <!-- IMS10.0 RE123_105930 End Changes -->
          <xs:element name="commonMsisdn" type="xsd:string" minOccurs="0"/>
          <xs:element name="suppressAliasForImsiMsisdn" type="xsd:boolean" minOccurs="0"/>
          <!-- SCO's  -->
          <xs:element name="privateUserId" type="subscriber:PrivateUserId" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="implicitRegisteredSet" type="subscriber:ImplicitRegisteredDataSet" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="publicUserId" type="subscriber:PublicUserId" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="serviceProfile" type="subscriber:ServiceProfile" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="repositoryData" type="subscriber:RepositoryData" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="notification" type="subscriber:Notification" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="pprTriggerInfo" type="subscriber:PPRTriggerInfo" minOccurs="0" maxOccurs="unbounded"/>
          <!-- IMS70-BEGIN -->
          <xs:element name="gussData" type="subscriber:GussData" minOccurs="0" maxOccurs="unbounded"/>
          <!-- IMS70-END -->
          <!-- IMS80-BEGIN -->
          <xs:element name="aliasGroup" type="subscriber:AliasGroup" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="aliasRepositoryData" type="subscriber:AliasRepositoryData" minOccurs="0" maxOccurs="unbounded"/>
          <!-- IMS80-END -->
          <xs:element name="refSharedUserProfile" type="xsd:string" minOccurs="0"/>
          <xs:element name="refCxRestrictionVPLMNId" type="xsd:string" minOccurs="0"/>
          <xs:element name="secondaryScscfHost" type="xsd:string" minOccurs="0"/>
          <xs:element name="secondaryScscfSipAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="secondaryScscfDestRealm" type="xsd:string" minOccurs="0"/>
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: Start -->
          <xs:element name="imsResGwData" type="subscriber:ImsResGwData" minOccurs="0"/>
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: End -->
          <!-- FC123_107893_Ref_loc_info :: Start -->
          <xs:element name="refLocationInfo" type="subscriber:ImsRefLocationInfo" minOccurs="0" maxOccurs="unbounded"/>
          <!-- FC123_107893_Ref_loc_info :: End -->
          <xs:element name="imsServicesDisabled" type="xsd:boolean" minOccurs="0"/>

          <xs:element name="restorationData" type="subscriber:RestorationData" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="creationTimeStamp" type="xsd:dateTime" minOccurs="0"/>
          <!-- FRS123_106725 RDSP enhancement in HSS - Start -->
          <xs:element name="imsOrderOfSelection" type="subscriber:ImsOrderOfSelection" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="domainSelectionStrategy" type="subscriber:DomainSelectionStrategy" minOccurs="0" maxOccurs="unbounded"/>
          <!-- FRS123_106725 RDSP enhancement in HSS - End -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!--  SIMPLE TYPE SECTION: definition of simple types of attributes         -->
  <!--************************************************************************-->
  <xs:simpleType name="PreferredDomain">
    <xs:annotation>
      <xs:documentation>Preferred domain. Nokia proprietary. A Parameter that
                identifies the preferred domain for the vcc (voice call continuity) user where the terminating
                calls shall be routed to in case that the vcc-user is available in both domains.
                Vcc user is a subscriber, which has has the feature CS/IMS roaming and handover.
        </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="ims"/>
      <xs:enumeration value="cs"/>
      <xs:enumeration value="none"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="DomainSelectionFlag">
    <xs:annotation>
      <xs:documentation>A parameter that identifies whether the UE is registered in VoLTE IMS or CS-IP IMS
    	     If the domain flag value is 0, the UE is currently registered in a Domain controlled by NEC HLR.
    	     If the domain flag value is 1, then the UE is (supposed to be) currently registered in VoLTE IMS (controlled by NOKIA HSS).
    	     </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="CS-IP"/>
      <xs:enumeration value="VoLTE"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ProfileType">
    <xs:annotation>
      <xs:documentation>Nokia proprietary. Defines type of subscriber profile. 
                Profile can be used as e.g. template for other subscriber profiles.
                Values:
                NORMAL (0) : The profile was added through SA and defines a common IMS subscriber
                TEMPLATE (2): The profile is added through SA and assigned as template
                CUG (3): Closed User group. Special used and assigned from SVS data. SVS Data is XML encoded and stored as repository data which contains reference number to find CUG Profile.
                PSI (4) : The profile was added through PGW and defines a PSI subscriber
                RESGWDATA (5): Represents a Residential Gateway profile
              SELFACTIVATION (6): Represents a Self Activation profile
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="normal"/>
      <xs:enumeration value="template"/>
      <xs:enumeration value="cug"/>
      <!-- IMS70-BEGIN -->
      <xs:enumeration value="guss"/>
      <!-- IMS70-END -->
      <!-- IMS10.0 RE123_105930 Changes -->
      <xs:enumeration value="psi"/>
      <!-- IMS10.0 RE123_105930 Changes -->
      <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: Start -->
      <xs:enumeration value="resGwData"/>
      <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: End -->
      <xs:enumeration value="selfActivation"/>
      <!-- FC123_107815_Self_Activation -->
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="RegistrationStatus">
    <xs:annotation>
      <xs:documentation>The Registration Status, specified in 3GPP TS 29.228 [43], contains the status 
                of registration of a multimedia user (e.g. registered, not registered, temporarily registered waiting for authentication).
                Values:
                NOT_REGISTERED: The user is not registered
                REGISTERED: Indicates that the user is registered
                DEFAULT: Default S-CSCF assigned (Registered for services for unregistered user)
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="notRegistered"/>
      <xs:enumeration value="registered"/>
      <xs:enumeration value="default"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ProfilePartIndication">
    <xs:annotation>
      <xs:documentation>Enumerated type, with possible values "REGISTERED and UNREGISTERED, indicating if the iFC is a part of the registered or unregistered user profile. 
                If ProfilePartIndicator is missing from the iFC, the iFC is  considered to be relevant to both the registered and unregistered parts  of the user profile, 
                i.e. belongs to the common part of the user profile 
                values:
                UN_REGISTERED
                REGISTERED
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="unRegistered"/>
      <xs:enumeration value="registered"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="Handling">
    <xs:annotation>
      <xs:documentation>Default handling The default handling procedure indicates whether to abandon the matching of lower priority triggers and to release the dialogue, 
                or to continue the dialogue and trigger matching.
                values:
                - session continue.
                - terminating.
            </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="continue"/>
      <xs:enumeration value="terminate"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="SessionReleasePolicy">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="deregisterForcedSessionRelease"/>
      <xs:enumeration value="deregisterNoForcedSessionRelease"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ForkingPolicy">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="sequentialForking"/>
      <xs:enumeration value="parallelForking"/>
      <xs:enumeration value="mixedForking"/>
      <xs:enumeration value="noForking"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="AuthenticationScheme">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="aka"/>
      <xs:enumeration value="earlyIms"/>
      <xs:enumeration value="gprs"/>
      <xs:enumeration value="httpDigest"/>
      <xs:enumeration value="nba"/>
      <!-- IMS70LF-BEGIN -->
      <xs:enumeration value="tlsDigestAkaV1"/>
      <xs:enumeration value="tlsDigestAkaV2"/>
      <!-- IMS70LF-END-->
      <!-- IMS80 - BEGIN -->
      <xs:enumeration value="sipDigest"/>
      <xs:enumeration value="caveAka"/>
      <xs:enumeration value="cache"/>
      <xs:enumeration value="imrn"/>
      <!-- IMS80 - END -->
    </xs:restriction>
  </xs:simpleType>
  <!--************************************************************************-->
  <!--  TYPE SECTION: Defining complex types/enumeration for flat attributes  -->
  <!--************************************************************************-->
  <xs:complexType name="PrivateUserId">
    <xs:annotation>
      <xs:documentation>Private User Identity Type - IMPI</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="privateUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="provisionedImsi" type="subscriber:ProvisionedImsi" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="msisdn" type="xsd:string" minOccurs="0"/>
          <xs:element name="ipAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="framedIPV6Prefix" type="xsd:string" minOccurs="0"/>
          <xs:element name="framedInterfaceId" type="xsd:string" minOccurs="0"/>
          <xs:element name="httpDigestKey" type="xsd:string" minOccurs="0"/>
          <xs:element name="httpDigestKeyVersion" type="xsd:int" minOccurs="0"/>
          <xs:element name="preferredAuthenticationScheme" type="subscriber:AuthenticationScheme" minOccurs="0"/>
          <xs:element name="usedAuthenticationScheme" type="subscriber:AuthenticationScheme" minOccurs="0"/>
          <xs:element name="lineIdentifier" type="subscriber:LineIdentifier" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="sgsnId" type="xsd:string" minOccurs="0"/>
          <xs:element name="ggsnId" type="xsd:string" minOccurs="0"/>
          <xs:element name="actAsVLR" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="chargeId" type="xsd:string" minOccurs="0"/>
          <xs:element name="sessionId" type="xsd:string" minOccurs="0"/>
          <xs:element name="preferredDomain" type="subscriber:PreferredDomain" minOccurs="0"/>
          <xs:element name="refGussDataId" type="subscriber:String10" minOccurs="0"/>
          <xs:element name="looseRoutingIndicationRequired" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="digestRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="digestHa1" type="subscriber:PrintableString32" minOccurs="0"/>
          <xs:element name="uimId" type="subscriber:HexadecimalString" minOccurs="0"/>
          <xs:element name="domainSelectionFlag" type="subscriber:DomainSelectionFlag" minOccurs="0"/>
          <xs:element name="digestKeyId" type="xsd:int" minOccurs="0"/>
          <!-- RE123_107530_ISIM_and_USIM_with_no_shared_Authentication_keys - start -->
          <xs:element name="isimAuc" type="subscriber:ISIMAUC" minOccurs="0" maxOccurs="1"/>
          <!-- RE123_107530_ISIM_and_USIM_with_no_shared_Authentication_keys - end -->
          <xs:element name="visitedNetworkId" type="xsd:string" minOccurs="0"/>
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: Start -->
          <xs:element name="refResGwId" type="xsd:string" minOccurs="0"/>
          <xs:element name="resGwAgentFlag" type="xsd:boolean" minOccurs="0"/>
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: End -->
          <!-- BEGIN changes for HSS 18.0: RE123_108171 Outroamer Count -->
          <xs:element name="plmnStatus" type="subscriber:ImsPlmnStatus" minOccurs="0"/>
          <!-- END changes for HSS 18.0: RE123_108171 Outroamer Count -->
          <xs:element name="allowedIPAddressList" type="xsd:string" minOccurs="0" maxOccurs="2"/>
          <!--RE123_108727 START-->
          <xs:element name="VoLTELock" type="xsd:boolean" minOccurs="0"/>
          <!--RE123_108727 END-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PublicUserId">
    <xs:annotation>
      <xs:documentation>Public User ID - IMPU</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="publicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="originalPublicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="barringIndication" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="defaultIndication" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="serviceProfileName" type="xsd:string" minOccurs="0"/>
          <xs:element name="irsId" type="xsd:string" minOccurs="0"/>
          <!-- IMS70-BEGIN -->
          <xs:element name="displayName" type="xsd:string" minOccurs="0"/>
          <xs:element name="displayNamePrivacy" type="xsd:boolean" minOccurs="0"/>
          <!-- IMS70-END -->
          <!-- IMS80-BEGIN -->
          <xs:element name="aliasId" type="xsd:string" minOccurs="0"/>
          <!-- IMS80-END -->
          <xs:element name="isChildIMPU" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="servicePriorityLevel" type="xsd:int" minOccurs="0"/>
          <xs:element name="sarPublicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="publicIdType" type="subscriber:PublicIdentityType" minOccurs="0"/>
          <!-- 16.5 - RE123_107241_enhancemet_of_CMS8200_in_Sh_interface START -->
          <xs:element name="snrPublicUserId" type="xsd:string" minOccurs="0"/>
          <!-- 16.5 - RE123_107241_enhancemet_of_CMS8200_in_Sh_interface END -->
          <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - start -->
          <xs:element name="extendedPriority" type="subscriber:ExtendedPriority" minOccurs="0" maxOccurs="unbounded"/>
          <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - end -->
          <!-- RE123_108417 - Roaming service profile provision - start -->
          <xs:element name="roamingServiceProfileName" type="xsd:string" minOccurs="0"/>
          <!-- RE123_108417 - Roaming service profile provision - end -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="PublicIdentityType">
    <xs:annotation>
      <xs:documentation>
                  The PublicIdentityType indicates the type of public Identity
                  0    distinctIMPU
                  1    wildcardedIMPU
              </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="distinctIMPU"/>
      <xs:enumeration value="wildcardedIMPU"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - start -->
  <xs:complexType name="ExtendedPriority">
    <xs:annotation>
      <xs:documentation>Extended Priority</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="priorityNamespace" type="subscriber:PrintableString255" minOccurs="0"/>
          <xs:element name="priorityLevel" type="subscriber:PrintableString255" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - end -->
  <!-- FC123_107893_Ref_loc_info :: Start -->
  <xs:complexType name="ImsRefLocationInfo">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="refLocationInfoId" type="xsd:int" minOccurs="0"/>
          <xs:element name="accessType" type="xsd:string" minOccurs="0"/>
          <xs:element name="accessInfo" type="xsd:string" minOccurs="0"/>
          <xs:element name="accessValue" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FC123_107893_Ref_loc_info :: End -->
  <xs:complexType name="ImplicitRegisteredDataSet">
    <xs:annotation>
      <xs:documentation>Implicit Registered Data Set - IRS</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="irsId" type="xsd:string" minOccurs="0"/>
          <xs:element name="registrationStatus" type="subscriber:RegistrationStatus" minOccurs="0"/>
          <xs:element name="authenticationPending" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="provisionedPrivateId" type="xsd:string" minOccurs="0"/>
          <xs:element name="privateIds" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="visitedNetworkListId" type="xsd:int" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="emergencyRegistration" type="xsd:boolean" minOccurs="0"/>
          <!--Added changes for REG 16.5 RE123_106492 Registration state timestamp to count IMS active subscribers  -->
          <xs:element name="registrationTimeStamp" type="xsd:string" minOccurs="0"/>
          <xs:element name="defaultRegTimeStamp" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ISIMAUC">
    <xs:annotation>
      <xs:documentation>Abstract base class for a service </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <sequence>
          <xs:element name="acsub" type="xsd:int" minOccurs="0"/>
          <xs:element name="encKey" type="xsd:string" minOccurs="0"/>
          <xs:element name="sqn" type="xsd:string" minOccurs="0"/>
          <xs:element name="amf" type="xsd:string" minOccurs="0"/>
          <xs:element name="kdbId" type="xsd:string" minOccurs="0"/>
          <xs:element name="algoId" type="xsd:int" minOccurs="0"/>
          <xs:element name="iccId" type="xsd:string" minOccurs="0"/>
          <xs:element name="opcEncKey" type="xsd:string" minOccurs="0"/>
        </sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- IMS 8.0 BEGIN -->
  <xs:complexType name="AliasGroup">
    <xs:annotation>
      <xs:documentation>
                Name: Alias Group
                Contents:
                1. aliasGroupId = Identifer for the alias group
                2. serviceProfileName =  Name of the service profile for this alias group.
                3. irsId = IRS identifier for aliasGroup
                4. roamingServiceProfileName = Name of the roaming service profile for this alias group.
				
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="aliasId" type="xsd:string" minOccurs="0"/>
          <xs:element name="serviceProfileName" type="xsd:string" minOccurs="0"/>
          <xs:element name="irsId" type="xsd:string" minOccurs="0"/>
          <!-- RE123_108417 - Roaming service profile provision - start -->
          <xs:element name="roamingServiceProfileName" type="xsd:string" minOccurs="0"/>
          <!-- RE123_108417 - Roaming service profile provision - end -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: Start -->
  <xs:complexType name="ImsResGwData">
    <xs:annotation>
      <xs:documentation>
                Name: ImsResGwData (Residential Gateway related data)
                Contents:
                1. resGwId = Identifier for the Residential Gateway
                2. resGwIpAddress = IP address of Residential Gateway, optional attribute which is stored after the first registration of an RG agent
                3. resGwAgentImpi = List of all IMPIs that are RG agents for this Residential Gateway
                4. resGwNonAgentImpi = List of all IMPIs that are RG non-agents for this Residential Gateway
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="resGwId" type="xsd:string" minOccurs="0"/>
          <xs:element name="resGwIpAddress" type="subscriber:IPAddress" minOccurs="0"/>
          <xs:element name="resGwAgentImpi" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="resGwNonAgentImpi" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- RE123_107725 - HSS Enhancements Step2 - Epic03 :: End -->
  <xs:complexType name="Notification">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="asHostName" type="xsd:string" minOccurs="0"/>
          <xs:element name="publicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="asDestinationRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="asSipAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="quiescenceStatus" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="notifyScscfName" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="notifyFilter" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="notifyChargingInfo" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="notifyPublicId" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="notifyRegistration" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="sessionId" type="xsd:string" minOccurs="0"/>
          <xs:element name="serviceIndNames" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <!-- IMS 8.0 BEGIN -->
          <xs:element name="identitySet" type="subscriber:HSSIdentitSetType" minOccurs="0" maxOccurs="4"/>
          <xs:element name="shExpiryTime" type="xsd:string" minOccurs="0"/>
          <xs:element name="messageWaitIndication" type="xsd:boolean" minOccurs="0"/>
          <!-- IMS 8.0 END -->
          <xs:element name="notifyServicePriorityLevel" type="xsd:boolean" minOccurs="0"/>
          <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - start-->
          <xs:element name="notifyExtendedPriority" type="xsd:boolean" minOccurs="0"/>
          <!-- RE123_107063 - MPS-CS-Priority bit Supports in the MPS-Priority - end-->
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic01 - start -->
          <xs:element name="notifEffSupported" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="updateEffSupported" type="xsd:boolean" minOccurs="0"/>
          <!-- RE123_107725 - HSS Enhancements Step2 - Epic01 - end -->
          <xs:element name="notifyPrivateUserId" type="xsd:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RepositoryData">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="serviceIndId" type="xsd:string" minOccurs="0"/>
          <xs:element name="publicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="version" type="xsd:int" minOccurs="0"/>
          <xs:element name="asData" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="RestorationData">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="privateId" type="xsd:string" minOccurs="0"/>
          <xs:element name="restorationData" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="sipAuthScheme" type="xsd:string" minOccurs="0"/>
          <xs:element name="irsId" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Alias repository Data -->
  <xs:complexType name="AliasRepositoryData">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="serviceIndId" type="xsd:string" minOccurs="0"/>
          <xs:element name="version" type="xsd:int" minOccurs="0"/>
          <xs:element name="asData" type="xsd:string" minOccurs="0"/>
          <xs:element name="aliasId" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ServiceProfile">
    <xs:annotation>
      <xs:documentation>Service Profile</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="profileName" type="xsd:string" minOccurs="0"/>
          <xs:element name="mandatoryCapability" type="subscriber:MandatoryCapability" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="optionalCapability" type="subscriber:OptionalCapability" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="globalFilterId" type="subscriber:GlobalFilterId" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="subscribedMediaProfileID" type="subscriber:SubscribedMediaProfileID" minOccurs="0"/>
          <xs:element name="userFilterCriteria" type="subscriber:UserFilterCriteria" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="subscribedMediaProfileId" type="xsd:int" minOccurs="0"/>
          <xs:element name="globaliFCReplacement" type="subscriber:GlobaliFCReplacement" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="refSharedServiceProfile" type="xsd:string" minOccurs="0"/>
          <xs:element name="refServiceIdList" type="xs:string" minOccurs="0"/>
          <!--FC123_107525_Core_Network_Service_Auth_Enhancement-->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="UserFilterCriteria">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="filterName" type="xsd:string" minOccurs="0"/>
          <xs:element name="asSipAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="priority" type="xsd:int" minOccurs="0"/>
          <xs:element name="triggerPoints" type="xsd:string" minOccurs="0"/>
          <xs:element name="serviceInformation" type="xsd:string" minOccurs="0"/>
          <xs:element name="handling" type="subscriber:Handling" minOccurs="0"/>
          <xs:element name="profilePartIndication" type="subscriber:ProfilePartIndication" minOccurs="0"/>
          <xs:element name="includeRegisterRequest" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="includeRegisterResponse" type="xsd:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GlobaliFCReplacement">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="refGlobalFilterID" type="xsd:string" minOccurs="0"/>
          <xs:element name="refAsSipAddressPrimary" type="xsd:string" minOccurs="0"/>
          <xs:element name="refAsSipAddressSecondary" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MandatoryCapability">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="mandatoryCapability" type="xsd:long" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="OptionalCapability">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="optionalCapability" type="xsd:long" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GlobalFilterId">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="globalFilterId" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SubscribedMediaProfileID">
    <xs:annotation>
      <xs:documentation>The SubscribedMediaProfileID is an optional element of the service profile defined in 29.228. It will be reused to store one or more dedicated policies. 
                A mechanism was defined so that this generic ID can be easily extended to be used to identify other types of policies. Each policy has its own bitmask. 
                The exact policy could be calculated via an AND operation with the generic ID and the policy bit mask.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="sessionReleasePolicy" type="subscriber:SessionReleasePolicy" minOccurs="0"/>
          <xs:element name="forkingPolicy" type="subscriber:ForkingPolicy" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ProvisionedImsi">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="provisionedImsi" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="LineIdentifier">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="lineIdentifier" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PPRTriggerInfo">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="privateUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="publicUserIds" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RTRTriggerInfo">
    <xs:annotation>
      <xs:documentation>Definition of RTRTrigger info class</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:FirstClassObject">
        <xs:sequence>
          <xs:element name="publicUserIds" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="scscfAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfRealm" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EpsMWDSCList">
        <xs:annotation>
            <xs:documentation>
                  List of addresses (SC Addr) of SCs which have made previous unsuccessful delivery attempts of a message.
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xsd:element name="hostId" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="realm" type="subscriber:PrintableString255" minOccurs="0" />
					<xs:element name="address" type="xsd:string" minOccurs="0" />
					<xs:element name="alertMsisdn" type="subscriber:NumericString1_15" minOccurs="0" />
				</xs:sequence>
            </xs:extension>
        </xs:complexContent>
		</xs:complexType>
		<!-- End : FRS123_108213_SMS_Delivery_Feature -->
  <xs:complexType name="HssIoTServiceData">
    <xs:annotation>
      <xs:documentation>
                  Contains shared HSS HLR advanced service Data.
              </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="subscriptionDataFlags" type="subscriber:BitString32" minOccurs="0"/>
          <xsd:element name="ueUsageType" type="xsd:int" minOccurs="0"/>
          <xs:element name="imsiGroupId" type="subscriber:ImsiGroupId" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="extDownLinkDataBuffering" type="xsd:int" minOccurs="0" />
          <xsd:element name="hssServiceGapTimer" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="rauTauTimer" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="activeTime" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="eDRXCycleLength" type="subscriber:EDRXCycleLength" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="pagingTimeWindow" type="subscriber:PagingTimeWindow" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ImsiGroupId">
    <xs:annotation>
      <xs:documentation>
                  Contains IMSI-GROUP-ID related information .
              </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="imsiGroupIdName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="groupServiceId" type="xsd:int" minOccurs="0"/>
          <xs:element name="groupPLMNId" type="subscriber:NumericString" minOccurs="0"/>
          <xs:element name="localGroupId" type="subscriber:HexadecimalString" minOccurs="0"/>
          <xs:element name="localGroupIdBits" type="subscriber:HssLocalGroupIdBits" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="HssLocalGroupIdBits">
        <xs:annotation>
            <xs:documentation>
                  Contains LOCAL-GROUP-ID related information .
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="sgsRegRequired" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="disableSgsReg" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="attachWithoutPDN" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="sgsLiteInterface" type="xsd:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
  <!--************************************************************************-->
  <!--                                     EPS aliases                        -->
  <!--************************************************************************-->
  <xs:simpleType name="EPSIdentifierAliasType">
    <xs:restriction base="xsd:string">
      <!-- Note: also using Subscriber's common aliases: identifier, imsi -->
      <xs:enumeration value="msisdn"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="EPSIdentifier">
    <xs:simpleContent>
      <xs:restriction base="spml:Identifier">
        <xs:attribute name="alias" type="subscriber:EPSIdentifierAliasType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="EPSIdentifierFileNameType">
    <xs:simpleContent>
      <xs:restriction base="spml:IdentifierFileNameType">
        <xs:attribute name="alias" type="subscriber:EPSIdentifierAliasType" use="required"/>
        <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
      </xs:restriction>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="EPSAliasType">
    <xs:complexContent>
      <xs:restriction base="spml:AliasType">
        <xs:attribute name="name" type="subscriber:EPSIdentifierAliasType" use="required"/>
        <xs:attribute name="value" type="xsd:string" use="required"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->
  <!-- EPS Extension of HLR Application Service                               -->
  <!-- Contribution to HLR / Subscriber schema                                -->
  <!--************************************************************************-->
  <xs:group name="EPS_Group">
    <xs:annotation>
      <xs:documentation>Definition of EPS data </xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="eps" type="subscriber:EPS" minOccurs="0"/>
      <xs:element name="epsPdnContext" type="subscriber:EPSPdnContext" minOccurs="0" maxOccurs="100"/>
      <xsd:element name="epsRoamAreaName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="epsPsRoamAreaMmeName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="epsRoamSubscriptionInfoName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="epsCsg" type="subscriber:EPSCsg" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="epsTracingProfile" type="subscriber:EPSTracingProfile" minOccurs="0"/>
      <xsd:element name="epsPsRszi" type="subscriber:EPSPsRszi" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="epsASNotificationInfo" type="subscriber:EPSASNotificationInfo" minOccurs="0" maxOccurs="unbounded"/>
      <!-- RE123_106765_MTC_interface_enhancements_for_HSS -->
      <xs:element name="epsAdditionalHssServiceData" type="subscriber:EPSAdditionalHssServiceData" minOccurs="0"/>
      <!-- End -->
      <!-- RE123_106764_Cost reduction by optimized HSS for MTC devices -->
      <xsd:element name="epsSharedUserProfile" type="subscriber:PrintableString255" minOccurs="0"/>
      <xsd:element name="epsS6dULAHostId" type="subscriber:PrintableString255" minOccurs="0"/>
      <!-- End -->
      <xs:element name="selfActivation" type="subscriber:EPSSelfActivation" minOccurs="0"/>
      <!-- FC123_107815_Self_Activation -->
      <xs:element name="hss5GAutoProvision" type="xs:boolean" minOccurs="0"/> <!-- RE123_109042 5G auto provision -->
    </xs:sequence>
  </xs:group>
  <!--************************************************************************-->
  <!--           EPS SCOs                                                     -->
  <!--************************************************************************-->
  <xsd:complexType name="EPS">
    <xsd:annotation>
      <xsd:documentation>EPS Data</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="defaultPdnContextId" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="maxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="maxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="mmeIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="msPurgedEps" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="apnOIReplacement" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="sessionTransferNumber" type="xsd:unsignedLong" minOccurs="0"/>
          <xsd:element name="vplmnId" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="featuresSupportedByMME" type="subscriber:UnsignedInt8bit" minOccurs="0"/>
          <xsd:element name="ueRegistered" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="sessionTimeout" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="aaaServerAddress" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="aaaServerRealm" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="currentRATType" type="subscriber:RATType" minOccurs="0"/>
          <xsd:element name="accessAPNEnabled" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="notAllowedRATTypes" type="subscriber:RATType" minOccurs="0" maxOccurs="12"/>
          <xsd:element name="mipFeatureVector" type="subscriber:EpsMipFeatureVector" minOccurs="0" maxOccurs="4"/>
          <xsd:element name="msisdn" type="subscriber:NumericString1_15" minOccurs="0"/>
          <!-- IMS 7.2 -->
          <xsd:element name="mmeSuppFeatures" type="subscriber:MmeSuppFeatures" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="odbPOAccessEps" type="subscriber:OdbPOAccessEps" minOccurs="0"/>
          <!-- IMS 7.2 -->
          <xsd:element name="rfspIndex" type="subscriber:EPSRfspType" minOccurs="0"/>
          <xsd:element name="mmeRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="mmeUpdateSucceeded" type="xs:boolean" minOccurs="0"/>
          <xs:element name="plmnStatus" type="subscriber:EPSPlmnStatus" minOccurs="0"/>
          <xs:element name="mmeHostId" type="subscriber:PrintableString255" minOccurs="0"/>
          <xs:element name="imsVoiceOverPS" type="subscriber:EPSIMSVoiceOverPSSupport" minOccurs="0"/>
          <xsd:element name="locUpdateTimestamp" type="xsd:dateTime" minOccurs="0"/>
          <xsd:element name="refLipaAllowedVplmnListName" type="subscriber:PrintableString255" minOccurs="0"/>
          <!-- RE123_106100 Enhanced Multimedia priority services (S6a) changes-->
          <xsd:element name="isMPSEnabled" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="refDeviceProfileName" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="vplmnIdS6a" type="subscriber:PrintableString64" minOccurs="0"/>
          <!-- RE123_106318 s6d support for verizon -->
          <xsd:element name="s6aSgsnNumber" type="xsd:string" minOccurs="0"/>
          <xsd:element name="s6aCombinedFlag" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="virtualApnName" type="subscriber:PrintableString32" minOccurs="0"/>
          <xsd:element name="epsAAARegistration" type="subscriber:EPSAAARegistration" minOccurs="0" maxOccurs="unbounded"/>
          <!-- RE123_106343 -->
          <xsd:element name="emergencyBarringEnabled" type="xsd:boolean" minOccurs="0"/>
          <!-- RE123_106342 NPLI Feature -->
          <!--type has been set as xsd:string limit is 7-->
          <xsd:element name="eUTRANCellGlobalId" type="xsd:string" minOccurs="0"/>
          <!--type has been set as xsd:string limit is 5-->
          <xsd:element name="trackingAreaId" type="xsd:string" minOccurs="0"/>
          <!--RE123_106279 HSS UE Fraud Prevention-->
          <xsd:element name="imeiRef" type="subscriber:NumericString" minOccurs="0"/>
          <xsd:element name="meidRef" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="privateUserIdRef" type="xsd:string" minOccurs="0"/>
          <!-- RE123_106764_Cost reduction by optimized HSS for MTC devices -->
          <xsd:element name="s6aULAHostId" type="subscriber:PrintableString255" minOccurs="0"/>
          <!-- End -->
          <!-- RE123_106765_MTC_interface_enhancements_for_HSS - start - Step 2 -->
          <xsd:element name="sGsMmeIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="pdnOnDemand" type="subscriber:EpsPdnOnDemandType" minOccurs="0"/>
          <!-- RE123_106765_MTC_interface_enhancements_for_HSS - end Step 2 -->
          <!-- RE123_106764_Cost_reduction_by_optimized_HSS_for_MTC_devices Step3 -->
          <xs:element name="chargingGatewayFunctionHost" type="xsd:string" minOccurs="0"/>
          <xs:element name="chargingGroupId" type="xsd:string" minOccurs="0"/>
          <xs:element name="proxyCSCFAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="idleTimeout" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="authLifetime" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="authGracePeriod" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="nemoSupported" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="framedPool" type="xsd:string" minOccurs="0"/>
          <xs:element name="framedIPv6Pool" type="xsd:string" minOccurs="0"/>
          <xsd:element name="defaultNonIpPdnContextId" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="aaaSupportedFeatures" type="subscriber:AaaSupportedFeatures" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="mmeSupportedMONTE" type="subscriber:MmeSupportedMONTE" minOccurs="0" maxOccurs="unbounded"/>
          <!-- FC123_107815_Self_Activation :: Start -->
          <xsd:element name="firstEpsAttachTimeStamp" type="xsd:string" minOccurs="0"/>
          <xsd:element name="firstNon3gppAttachTimeStamp" type="xsd:string" minOccurs="0"/>
          <xsd:element name="non3gppSubDeactivated" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="epsAttachmentCounter" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="non3gppAttachmentCounter" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="eHrpdAttachmentCounter" type="xsd:unsignedInt" minOccurs="0"/>
          <!-- FC123_107815_Self_Activation :: End -->
          <!-- Start : RE123_107782  ODB EPS -->
					<xsd:element name="odbEutran" type="subscriber:EpsBarringType" minOccurs="0"/>
					<xsd:element name="odbWlan" type="subscriber:EpsBarringType" minOccurs="0"/>
					<!-- End : RE123_107782  ODB EPS -->
                  <!-- Start : RE123_108217 MDT -->
                    <xs:element name="userConsentOnMDT" type="xsd:boolean"  minOccurs="0" />
                  <!-- End: RE123_108217 MDT -->
				  <!-- Start : FRS123_108213_SMS_Delivery_Feature -->
					<xsd:element name="mmeNumberForMTSMS" type="xsd:string" minOccurs="0"/>
					<xsd:element name="mmeRegisteredForSMSFlag" type="xsd:boolean" minOccurs="0"/>
					<xsd:element name="subscribedForSMSInMME" type="xsd:boolean" minOccurs="0"/>
				  <!-- End : FRS123_108213_SMS_Delivery_Feature -->
                  <!-- BEGIN changes for HSS 18.0: RE123_108171 Outroamer Count -->
                    <xs:element name="swxPlmnStatus" type="subscriber:EPSPlmnStatus" minOccurs="0"/>
                  <!-- END changes for HSS 18.0: RE123_108171 Outroamer Count -->
                  <!-- BEGIN changes for HSS 18.0: RE123_108065-->
                    <xsd:element name="explicitPPRTriggerTime" type="xsd:dateTime" minOccurs="0"/>
                  <!-- END changes for HSS 18.0: RE123_108065-->
                  <!-- BEGIN changes for HSS 18.5: RE123_108362-->
                    <xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
                    <xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
                  <!-- END changes for HSS 18.5: RE123_108362-->
				    <xsd:element name="aaaHostId" type="subscriber:PrintableString255" minOccurs="0"/>
                  <!-- BEGIN changes for HSS 17.5 SP5: RE123_108381_EnhancedDSFHandling -->
                    <xs:element name="s6aULRFlag" type="xsd:unsignedInt" minOccurs="0"/>
                  <!-- END changes for HSS 17.5 SP5: RE123_108381_EnhancedDSFHandling -->
		  <!-- BEGIN changes for HSS-204: support of SMS via 5G core with SMSF -->
		  <xs:element name="eps5GSmsDeliveryMode" type="xsd:boolean" minOccurs="0"/>
		  <!-- END changes for HSS-204: support of SMS via 5G core with SMSF -->
		  <!-- BEGIN RE123_109046_FC123_109664 Subscription based Aerial UE identification support EPS -->
		  <xs:element name="aerialUeSubscriptionInfo" type="xsd:unsignedInt" minOccurs="0"/>
		  <!-- END RE123_109046_FC123_109664 Subscription based Aerial UE identification support EPS -->
          <xs:element name="coreNetworkRestr" type="subscriber:EpsCoreNetworkRestriction" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="ageOfLocInfo" type="xsd:string" minOccurs="0"/>
		  <xsd:element name="epsFlexibleS6bServiceData" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:simpleType name="EpsBarringType">
      	 <xsd:annotation>
            <xsd:documentation>
				Enumeration of valid values for type of allowed barring
				0 NONE
				1 VPLMN
				2 ALLPLMN
			</xsd:documentation>
       	 </xsd:annotation>
       	 <xsd:restriction base="xsd:string">
           	<xsd:enumeration value="NONE"/>
			<xsd:enumeration value="VPLMN"/>
           	<xsd:enumeration value="ALLPLMN"/>
       	 </xsd:restriction>
	</xsd:simpleType>
  <xsd:complexType name="EPSPdnContext">
    <xsd:annotation>
      <xsd:documentation>EPS PDN Context</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="apn" type="subscriber:PrintableStringAPN255" minOccurs="0"/>
          <xsd:element name="contextId" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="type" type="subscriber:EpsPdnType" minOccurs="0"/>
          <xsd:element name="ipv4Address" type="subscriber:IPv4" minOccurs="0"/>
          <xsd:element name="ipv6Address" type="subscriber:IPv6" minOccurs="0"/>
          <xsd:element name="pdnGw" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="pdnGwRealm" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="pdnGwIPv4" type="subscriber:IPv4" minOccurs="0"/>
          <xsd:element name="pdnGwIPv6" type="subscriber:IPv6" minOccurs="0"/>
          <xsd:element name="pdnGwDynamicAllocation" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="vplmnAddressAllowed" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="maxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="maxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="qos" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="pdnChargingCharacteristics" type="subscriber:PdnChargingCharacteristics" minOccurs="0"/>
          <xsd:element name="dynPdnInfo" type="subscriber:DynPdnInfo" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="pdnContextBlocking" type="subscriber:PdnContextBlocking" minOccurs="0"/>
          <xsd:element name="lipaPermission" type="subscriber:EPSLipaPermission" minOccurs="0"/>
          <xs:element name="apnOIReplacement" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="restorationPriority" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="virtualApnName" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="refHssPdnContextName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="aaaServerAddress" type="xsd:string" minOccurs="0"/>
          <xsd:element name="aaaServerRealm" type="xsd:string" minOccurs="0"/>
          <!-- RE123_106764_Cost_reduction_by_optimized_HSS_for_MTC_devices Step3 -->
          <xs:element name="idleTimeout" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="authLifetime" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="authGracePeriod" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="sessionTimeout" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="framedPool" type="xsd:string" minOccurs="0"/>
          <xs:element name="framedIPv6Pool" type="xsd:string" minOccurs="0"/>
          <xsd:element name="refHssEnterpriseName" type="xsd:string" minOccurs="0"/>
          <xs:element name="nonIpPdnTypeIndicator" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="nonIpDataDeliveryMech" type="subscriber:NonIpDataDeliveryMechType" minOccurs="0"/>
          <xs:element name="scefIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="scefRealm" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="refSharedPdnContextName" type="xsd:string" minOccurs="0"/>
          <!-- RE123_107539 Support Visited-Network-Identifier AVP -->
          <xs:element name="visitedNetworkId" type="subscriber:PrintableString255" minOccurs="0"/>  
          <!-- RE123_107456_Local_Mobility_Anchor - start -->
          <xsd:element name="ipMobilitySupport" type="subscriber:HexadecimalString" minOccurs="0"/>
          <!-- RE123_107456_Local_Mobility_Anchor - End -->
          <!-- BEGIN changes for HSS 18.5: RE123_108362-->
          <xsd:element name="extMaxBandwidthUp" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="extMaxBandwidthDown" type="xsd:unsignedInt" minOccurs="0"/>
          <!-- END changes for HSS 18.5: RE123_108362-->
          <!-- BEGIN changes for HSS 19.0: RE123_108233-->
          <xsd:element name="preferredDataMode" type="subscriber:EnhancedBitString32" minOccurs="0"/>
          <!-- END changes for HSS 19.0: RE123_108233-->
          <!-- BEGIN changes for HSS 19.0: RE123_108500-->
          <xsd:element name="eps5gInterworkIndicator" type="subscriber:UnsignedInt8bit" minOccurs="0"/>
          <!-- END changes for HSS 19.0: RE123_108500-->
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="EPSAAARegistration">
    <xsd:annotation>
      <xsd:documentation>EPS AAA Registration</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="aaaServerAddress" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="aaaServerRealm" type="subscriber:PrintableString64" minOccurs="0"/>
          <xsd:element name="RATType" type="subscriber:RATType" minOccurs="0"/>
          <xsd:element name="regStatus" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="aaaSupportedFeatures" type="subscriber:AaaSupportedFeatures" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="aaaHostId" type="subscriber:PrintableString255" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <!-- IMS70-BEGIN -->
  <xs:complexType name="GussData">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="gussDataId" type="subscriber:String10" minOccurs="0"/>
          <xs:element name="uiccSecurityType" type="subscriber:UiccSecurityType" minOccurs="0"/>
          <xs:element name="keyLifetime" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="gussTimeStamp" type="xsd:dateTime" minOccurs="0"/>
          <xs:element name="ussData" type="subscriber:UssData" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="UiccSecurityType">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="gba"/>
      <xs:enumeration value="gbau"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="UssData">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="ussDataId" type="subscriber:String10" minOccurs="0"/>
          <xs:element name="gsId" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="activeIndication" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="ussType" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="refNafGroupId" type="subscriber:PrintableString64" minOccurs="0"/>
          <xs:element name="permissionListItem" type="subscriber:PermissionListItem" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="keySelectionId" type="subscriber:KeySelectionId" minOccurs="0"/>
          <xs:element name="upiListItem" type="subscriber:UpiListItem" minOccurs="0" maxOccurs="45"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PermissionListItem">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="permissionFlag" type="xsd:unsignedInt" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="KeySelectionId">
    <xs:annotation>
      <xs:documentation/>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="ME-based"/>
      <xs:enumeration value="UICC-based"/>
      <xs:enumeration value="ME-UICC-based"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="UpiListItem">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="upi" type="subscriber:PrintableString128" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- IMS70-END -->
  <!--************************************************************************-->
  <!--                             Types                                      -->
  <!--************************************************************************-->
  <xs:complexType name="PdnChargingCharacteristics">
    <xs:annotation>
      <xs:documentation>
                    PDN Charging characteristics. 
                    Mapping requires distinguising of these two types.
                </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:GeneralChargingCharacteristics"/>
    </xs:complexContent>
  </xs:complexType>
  <xsd:simpleType name="String254">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 254</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="254"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsPdnOnDemandType">
    <xsd:annotation>
      <xsd:documentation>
						0: On-Demand PDN is not allowed
						1: On-Demand PDN type 1
						2: On-Demand PDN type 2
						3: On-Demand PDN type 3
						4: On-Demand PDN type 4
						</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="NOT_ALLOWED"/>
      <xsd:enumeration value="TYPE_ONE"/>
      <xsd:enumeration value="TYPE_TWO"/>
      <xsd:enumeration value="TYPE_THREE"/>
      <xsd:enumeration value="TYPE_FOUR"/>
    </xsd:restriction>
  </xsd:simpleType>
 
  <xsd:simpleType name="PrintableString255">
    <xsd:annotation>
      <xsd:documentation>Printable String of length 1 .. 255</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="255"/>
    </xsd:restriction>
  </xsd:simpleType> 

  <xsd:simpleType name="PrintableString0_255">
    <xsd:annotation>
      <xsd:documentation>Printable String of length 0 .. 255</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableString">
      <xsd:maxLength value="255"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="IPv4">
    <xsd:annotation>
      <xsd:documentation>IPv4 address 
                Example:  ***********
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="IPv6">
    <xsd:annotation>
      <xsd:documentation>IPv6 address 
                Examples:
                4FDE:0000:0000:0002:0022:F376:FF3B:AB3F
                10FB:0:0:0:C:ABC:1F0C:44DA
                0:0:0:0:0:0:0:1
                10FB::C:ABC:1F0C:44DA
                FD01::1F
                ::1
                ::
                ::ffff:************
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!-- normal full format -->
      <xsd:pattern value="([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}"/>
      <!-- full format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
      <xsd:pattern value="(([0-9A-Fa-f]{1,4}:){6})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <!-- compressed format -->
      <xsd:pattern value="::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,6})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,5})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){1})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,4})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,3})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,2})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,1})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){5})::(([0-9A-Fa-f]{1,4})?)"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){6})::"/>
      <!-- compressed format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
      <xsd:pattern value="::(([0-9A-Fa-f]{1,4}:){0,5})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}:){0,4})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}))::(([0-9A-Fa-f]{1,4}:){0,3})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}:){0,2})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}:){0,1})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
      <xsd:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsMipFeatureVector">
    <xsd:annotation>
      <xsd:documentation>EPS Mip Feature Vector</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="mip6_integrated"/>
      <xsd:enumeration value="pmip6_supported"/>
      <xsd:enumeration value="ip4_hoa_supported"/>
      <xsd:enumeration value="assign_local_ip"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsPdnType">
    <xsd:annotation>
      <xsd:documentation>EPS Pdn type</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="ipv4"/>
      <xsd:enumeration value="ipv6"/>
      <xsd:enumeration value="both"/>
      <xsd:enumeration value="ipv4oripv6"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsAccessRestriction">
    <xsd:annotation>
      <xsd:documentation>EPS access restriction</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="UTRAN"/>
      <xsd:enumeration value="GERAN"/>
      <xsd:enumeration value="GAN"/>
      <xsd:enumeration value="E-UTRAN"/>
      <xsd:enumeration value="I-HSPA-Evolution"/>
      <xsd:enumeration value="HO-To-Non3GPP"/>
      <xsd:enumeration value="NB-IoT"/>
      <xsd:enumeration value="NR-As-Secondary-RAT"/>
      <xsd:enumeration value="NR-In-5GS"/>
      <xsd:enumeration value="LTE-M"/>
      <xsd:enumeration value="WB-EUTRAN-Except-LTEM"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EpsCoreNetworkRestriction">
         <xsd:annotation>
            <xsd:documentation>
                 Enumeration of the Core Network that UE is not allowed to use.
                 1 - EPC Not Allowed
                 2 - 5GC Not Allowed
            </xsd:documentation>
         </xsd:annotation>
         <xsd:restriction base="xsd:string">
                <xsd:enumeration value="EPC"/>
                <xsd:enumeration value="5GC"/>
         </xsd:restriction>
    </xsd:simpleType>
    
  <xsd:simpleType name="RATType">
    <xsd:annotation>
      <xsd:documentation>RAT type</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="WLAN"/>
      <xsd:enumeration value="UTRAN"/>
      <xsd:enumeration value="GERAN"/>
      <xsd:enumeration value="GAN"/>
      <xsd:enumeration value="HSPA_EVOLUTION"/>
      <xsd:enumeration value="CDMA2000_1X"/>
      <xsd:enumeration value="HRPD"/>
      <xsd:enumeration value="UMB"/>
      <xsd:enumeration value="VIRTUAL"/>
      <xsd:enumeration value="EUTRAN"/>
      <xsd:enumeration value="EHRPD"/>
      <xsd:enumeration value="BBF-WLAN"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="UnsignedInt8bit">
    <xsd:annotation>
      <xsd:documentation>Unsigned 8 bit integer </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:unsignedInt">
      <xsd:maxInclusive value="255"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!-- Simple type  HSSIdentitSetType IMS 8.0 -->
  <xs:simpleType name="HSSIdentitSetType">
    <xs:restriction base="xsd:string">
      <xs:enumeration value="ALL_IDENTITIES"/>
      <xs:enumeration value="REGISTERED_IDENTITIES"/>
      <xs:enumeration value="IMPLICIT_IDENTITIES"/>
      <xs:enumeration value="ALIAS_IDENTITIES"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- Simple type  HSSIdentitSetType IMS 8.0 -->
  <xsd:simpleType name="PrintableString32">
    <xsd:annotation>
      <xsd:documentation>String of length 1 .. 32</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="32"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PrintableStringAPN255">
    <xsd:annotation>
      <xsd:documentation>Printable String APN of length 1 .. 255</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:PrintableStringAPN">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="255"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PrintableStringAPN">
    <xsd:annotation>
      <xsd:documentation>Printable String  with '@' and '*' character</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="\*|[a-zA-Z\d '()+,-./:=?@_]+"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="DynPdnInfo">
    <xsd:annotation>
      <xsd:documentation>
                This read only attribute stores the dynamic APN configuration (access point name, PDN GW host, realm and IPv4 or
                IPv6 address). Dynamic APNs are only stored when the wildcard APN at S6a and SWx interface is
                supported. In this case the attributes pdnGwHostName, pdnGwRealm, pdnGwIPv4, pdnGwIPv6 of the
                wildcard entry are empty. In general, it is possible to register multiple dynamic APN configurations in
                parallel for a wildcard APN. This attribute is written by the application FE (HSS-FE) after a request sent by
                the MME/AAA. It is NOT provisioned, but the dynamic APN configuration can be read over the provisioning
                interface (PGW GUI).
            </xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="pdnAccPointName" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="pdnGwHostName" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="pdnGwRealm" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="pdnGwIPV4" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="pdnGwIPV6" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
          <!-- Changes for RE123_107539 Support Visited-Network-Identifier AVP -->
          <xsd:element name="visitedNetworkId" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="1"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="PdnContextBlocking">
    <xsd:annotation>
      <xsd:documentation>
                This attribute indicates whether the PDN context subscription is blocked or not for a
                specific access type (eUTRAN/S6a or eHPPD/SWx). It consists of 8 bits. Currently only
                the first two bits are used. Bit 1 denotes whether the APN is blocked via the S6a
                interface, bit 2 denotes whether the APN is blocked via the SWx interface. The
                remaining bits are for future use.
                Possible values:
                Bit 1 value 0: APN is not blocked over S6a
                Bit 1 value 1: APN is blocked over S6a
                Bit 2 value 0: APN is not blocked over SWx
                Bit 2 value 1: APN is blocked over SWx
            </xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="flagAPNBlockedOverS6a" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagAPNBlockedOverSWx" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved2" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved3" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved4" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved5" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved6" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagReserved7" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="flagAPNBlockedOverN8N10" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <!-- IMS 7.2 -->
  <xsd:simpleType name="OdbPOAccessEps">
    <xsd:annotation>
      <xsd:documentation>
                Operator Determined Barring for packet oriented access (EPS) for usage over the S6a interface.
                0 NONE NONE
                1 ALLPOS Barr all PO service access
                2 HPLMNAP Barr access to HPLMN-AP while roaming in VPLMN
                3 VPLMNAP Barr access to AP that are within the roamed to VPLMN
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="NONE"/>
      <xsd:enumeration value="ALLPOS"/>
      <xsd:enumeration value="HPLMNAP"/>
      <xsd:enumeration value="VPLMNAP"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="MmeSuppFeatures">
    <xsd:annotation>
      <xsd:documentation>
                This read only attribute contains the list of features supported by the MME. It is composed of the feature list ID and
                the feature list. For the currently defined bits the feature list ID is 1. Curently 25
                features are defined (thus at the moment one feature list is sufficient for the S6a interface).
            </xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="featuredListId" type="subscriber:BitString32" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="featuredList" type="subscriber:BitString" minOccurs="0" maxOccurs="1"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="AaaSupportedFeatures">
    <xsd:annotation>
      <xsd:documentation>
	        This read only attribute contains the list of features supported by the AAA. It is composed of the feature list ID and
                the feature list as specified. For the currently defined bits the feature list ID is 1. Currently 4 features are defined  by 3GPP.
                Bit 0-2 are currently not supported.
                Bit 3 indicates the P-CSCF for WLAN capability of the AAA.
            </xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="featureListId" type="xsd:int" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="feature" type="subscriber:AaaFeatureList" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:simpleType name="AaaFeatureList">
    <xsd:annotation>
      <xsd:documentation>
                        This is an enumeration of all the supported features
                        of the origin hosts in AAA interface as defined by 3GPP.
                        </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="HSS Restoration"/>
      <xsd:enumeration value="Access-Network-Information-Retrieval"/>
      <xsd:enumeration value="UE Local Time Zone Retrieval"/>
      <xsd:enumeration value="P-CSCF Restoration for WLAN"/>
       <xsd:enumeration value="Emergency Services Continuity"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EnhancedBitString32">
        <xsd:annotation>
            <xsd:documentation>Keep the former 30 bits are always 0</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="subscriber:BitString32">
            <xsd:pattern value="[0]{30}(01|10|11)"/>
        </xsd:restriction>
    </xsd:simpleType>
  <xsd:simpleType name="BitString32">
    <xsd:annotation>
      <xsd:documentation>Bit string with length 32 characters exactly</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="subscriber:BitString">
      <xsd:minLength value="32"/>
      <xsd:maxLength value="32"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="HexadecimalString">
    <xsd:annotation>
      <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Fa-f0-9]*"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--************************************************************************-->
  <!--  Changes Related to FP: RE122_002211 Identity Swap (IMSI, MSISDN)      -->
  <!--************************************************************************-->
  <xs:simpleType name="SubscriberSwapAliasType">
    <xs:restriction base="xsd:string">
      <xs:enumeration value="imsi"/>
      <xs:enumeration value="msisdn"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SubscriberSwapIdentifier">
    <xs:simpleContent>
      <xs:extension base="spml:ID">
        <xs:attribute name="alias" type="subscriber:SubscriberSwapAliasType" use="required"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xsd:simpleType name="EPSOdbBarringRoam">
    <xsd:annotation>
      <xsd:documentation>
                Operator Determined Barring for Restriction of roaming .
                0 NONE    No barring of roaming
                1 ROAMOH  roaming outside the home PLMN is restricted.
                2 ROAMOHC roaming outside the home PLMN country is restricted.
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="NONE"/>
      <xsd:enumeration value="ROAMOH"/>
      <xsd:enumeration value="ROAMOHC"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSRfspType">
    <xsd:annotation>
      <xsd:documentation>Integer and the range is  1 to 256 </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:int">
      <xsd:minInclusive value="1"/>
      <xsd:maxInclusive value="256"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="EPSCsg">
    <xsd:annotation>
      <xsd:documentation>EPS CSG Data</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="mccMnc" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="csgId" type="subscriber:EPSCsgId" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="EPSPsRszi">
    <xsd:annotation>
      <xsd:documentation>EPS PS RSZI Data</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xs:element name="mcc" type="subscriber:NumericString3" minOccurs="0"/>
          <xs:element name="mnc" type="subscriber:NumericString3" minOccurs="0"/>
          <xs:element name="zoneCode01" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode02" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode03" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode04" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode05" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode06" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode07" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode08" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode09" type="subscriber:NumericString5" minOccurs="0"/>
          <xs:element name="zoneCode10" type="subscriber:NumericString5" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xs:complexType name="EPSCsgId">
    <xs:annotation>
      <xsd:documentation>CSG Identifier is string. Note: It is modeled as SCO to allow  modification of  individual value(s)</xsd:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="csgId" type="xsd:string" minOccurs="0"/>
          <xs:element name="apnName" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xsd:complexType name="EPSTracingProfile">
    <xsd:annotation>
      <xsd:documentation>EPS Tracing Profile</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="reference" type="xsd:string" minOccurs="0"/>
          <xsd:element name="depth" type="subscriber:EPSDepth" minOccurs="0"/>
          <xsd:element name="neTypeList" type="subscriber:EPSNeType" minOccurs="0"/>
          <xsd:element name="interfaceList" type="subscriber:EPSInterfaces" minOccurs="0"/>
          <xsd:element name="eventList" type="subscriber:EPSEvents" minOccurs="0"/>
          <xsd:element name="collectionEntityIp" type="subscriber:IPAddress" minOccurs="0"/>
          <xsd:element name="activationState" type="subscriber:EPSActivationState" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:simpleType name="EPSDepth">
    <xsd:annotation>
      <xsd:documentation>Integer in the range is 0 to 5 </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:int">
      <xsd:minInclusive value="0"/>
      <xsd:maxInclusive value="5"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSNeType">
    <xsd:annotation>
      <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Fa-f0-9]*"/>
      <xsd:minLength value="0"/>
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSInterfaces">
    <xsd:annotation>
      <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Fa-f0-9]*"/>
      <xsd:minLength value="0"/>
      <xsd:maxLength value="28"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSEvents">
    <xsd:annotation>
      <xsd:documentation>Hexadecimal values are allowed.a-f,A-F and 0-9.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Fa-f0-9]*"/>
      <xsd:minLength value="0"/>
      <xsd:maxLength value="18"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSActivationState">
    <xsd:annotation>
      <xsd:documentation>
                Depth of the tracing required
                0 active
                1 inactive
           </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="active"/>
      <xsd:enumeration value="inactive"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EPSLipaPermission">
    <xsd:annotation>
      <xsd:documentation>
            Allowed values for Lipa Permission
		0 LIPA-PROHIBITED
       	        1 LIPA-ONLY 
		2 LIPA-CONDITIONAL
           </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="LIPA-PROHIBITED"/>
      <xsd:enumeration value="LIPA-ONLY"/>
      <xsd:enumeration value="LIPA-CONDITIONAL"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--**************************************************************************************-->
  <!--Changes Related to Feature RE123_103651 (s6d Feature)                                 -->
  <!--**************************************************************************************-->
  <xsd:complexType name="SgsnSupportedFeatures">
    <xsd:annotation>
      <xsd:documentation>
		Complex type containing the list of features supported by the SGSN.
		It is composed of feature list ID and the feature. For the currently 
		defined bits the feature list ID is 1. Currently 30 features are defined 
		(thus at the moment one feature list is sufficient for the S6d interface).
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="featureListId" type="xsd:int" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="feature" type="subscriber:FeatureList" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:simpleType name="FeatureList">
    <xsd:annotation>
      <xsd:documentation>
			This is an enumeration of all the supported features 
			of the origin hosts in S6a/S6d interface as defined by 3GPP.
			</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="ODB-all-APN"/>
      <xsd:enumeration value="ODB-HPLMN-APN"/>
      <xsd:enumeration value="ODB-VPLMN-APN"/>
      <xsd:enumeration value="ODB-all-OG"/>
      <xsd:enumeration value="ODB-all-InternationalOG"/>
      <xsd:enumeration value="ODB-all-InternationalOGNotToHPLMN-Country"/>
      <xsd:enumeration value="ODB-all-InterzonalOG"/>
      <xsd:enumeration value="ODB-all-InterzonalOGNotToHPLMN-Country"/>
      <xsd:enumeration value="ODB-all-InterzonalOGAndInternationalOGNotToHPLMN-Country"/>
      <xsd:enumeration value="RegSub"/>
      <xsd:enumeration value="Trace"/>
      <xsd:enumeration value="LCS-all-PrivExcep"/>
      <xsd:enumeration value="LCS-Universal"/>
      <xsd:enumeration value="LCS-CallSessionRelated"/>
      <xsd:enumeration value="LCS-CallSessionUnrelated"/>
      <xsd:enumeration value="LCS-PLMNOperator"/>
      <xsd:enumeration value="LCS-ServiceType"/>
      <xsd:enumeration value="LCS-all-MOLR-SS"/>
      <xsd:enumeration value="LCS-BasicSelfLocation"/>
      <xsd:enumeration value="LCS-AutonomousSelfLocation"/>
      <xsd:enumeration value="LCS-TransferToThirdParty"/>
      <xsd:enumeration value="SM-MO-PP"/>
      <xsd:enumeration value="Barring-OutgoingCalls"/>
      <xsd:enumeration value="BAOC"/>
      <xsd:enumeration value="BOIC"/>
      <xsd:enumeration value="BOICExHC"/>
      <xsd:enumeration value="UE-Reachability-Notification"/>
      <xsd:enumeration value="T-ADS-Data-Retrieval"/>
      <xsd:enumeration value="State/Location-Information-Retrieval"/>
      <xsd:enumeration value="Partial Purge"/>
      <xsd:enumeration value="Local Time Zone Retrieval"/>
      <xsd:enumeration value="Additional MSISDN"/>
      <xsd:enumeration value="SMS in MME"/>
      <xsd:enumeration value="SMS in SGSN"/>
      <xsd:enumeration value="Dia-LCS-all-PrivExcep"/>
      <xsd:enumeration value="Dia-LCS-Universal"/>
      <xsd:enumeration value="Dia-LCS-CallSessionRelated"/>
      <xsd:enumeration value="Dia-LCS-CallSessionUnrelated"/>
      <xsd:enumeration value="Dia-LCS-PLMNOperator"/>
      <xsd:enumeration value="Dia-LCS-ServiceType"/>
      <xsd:enumeration value="Dia-LCS-all-MOLR-SS"/>
      <xsd:enumeration value="Dia-LCS-BasicSelfLocation"/>
      <xsd:enumeration value="Dia-LCS-AutonomousSelfLocation"/>
      <xsd:enumeration value="Dia-LCS-TransferToThirdParty"/>
      <xsd:enumeration value="Gdd-in-SGSN"/>
      <xsd:enumeration value="Optimized-LCS-Proc-Support"/>
      <xsd:enumeration value="SGSN CAMEL Capability"/>
      <xsd:enumeration value="ProSe Capability"/>
      <xsd:enumeration value="P-CSCF Restoration"/>
      <xsd:enumeration value="Reset-IDs"/>
      <xsd:enumeration value="Communication-pattern"/>
      <xsd:enumeration value="Dedicated-Code-Networks"/>
      <xsd:enumeration value="Monitoring Event"/>
			<xsd:enumeration value="NIDD Authorization"/>
			<xsd:enumeration value="Enhanced Coverage Restriction"/>
			<xsd:enumeration value="NIDD Authorization Update"/>
      <xsd:enumeration value="Report Eff MONTE"/>
      <xsd:enumeration value="Event Cancellation Report"/>
      <xsd:enumeration value="Config Eff CP"/>
      <xsd:enumeration value="Config Eff NP"/>
      <!-- REG-23411 -->
      <xsd:enumeration value="Non-IP PDN Type APNs"/>
      <xsd:enumeration value="Non-IP PDP Type APNs"/>
      <xsd:enumeration value="Removal of MSISDN"/>
      <xsd:enumeration value="Emergency Service Continuity"/>
      <xsd:enumeration value="V2X Capability"/>
      <xsd:enumeration value="External-Identifier"/>
      <xsd:enumeration value="NR as Secondary RAT"/>
      <xsd:enumeration value="Unlicensed Spectrum as Secondary RAT"/> 
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="AppSrvNotificationData">
    <xsd:annotation>
      <xsd:documentation>Notification Data</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
      <xsd:extension base="spml:SecondClassObject">
        <xsd:sequence>
          <xsd:element name="msisdn" type="subscriber:NumericString10_15" minOccurs="0"/>
          <xsd:element name="asHostName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="asRealm" type="xsd:string" minOccurs="0"/>
          <xsd:element name="asSubscribedInfo" type="subscriber:ASSubscribedInfo" minOccurs="0"/>
          <xsd:element name="expiryTime" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xs:complexType name="ASSubscribedInfo">
    <xs:annotation>
      <xsd:documentation>AS Subscribed Info</xsd:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="subscribedInfo" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EPSASNotificationInfo">
    <xs:annotation>
      <xsd:documentation>Application Server Notification Info's Instances</xsd:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="asHostName" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="notificationData" type="subscriber:EPSNotificationData" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EPSNotificationData">
    <xs:annotation>
      <xsd:documentation> Holds the Application Servers (AS) SUBSCRIPTION related Data </xsd:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="notificationIdValue" type="xsd:string" minOccurs="0"/>
          <xsd:element name="dataReferenceType" type="subscriber:EPSDataReferenceType" minOccurs="0"/>
          <xsd:element name="asRealm" type="xsd:string" minOccurs="0"/>
          <xsd:element name="serviceIndication" type="xsd:string" minOccurs="0"/>
          <xsd:element name="expiryTime" type="xsd:string" minOccurs="0"/>
          <xsd:element name="shUserIdentity" type="xsd:string" minOccurs="0"/>
          <xsd:element name="shUserName" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="EPSDataReferenceType">
    <xs:annotation>
      <xs:documentation>
                  Contains Data reference value
              </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="repositoryData"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="EPSIMSVoiceOverPSSupport">
    <xs:restriction base="xs:string">
      <xs:enumeration value="imsVoiceOverPSNotSupported"/>
      <xs:enumeration value="imsVoiceOverPSSupported"/>
      <xs:enumeration value="imsVoiceOverPSSupportUnknown"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="EPSPlmnStatus">
    <xs:annotation>
      <xs:documentation>
                  The plmnStatus indicates whether the LTE subscriber is roaming in HPLMN, HPLMNCountry or VPLMN.
                  0    None
                  1    HPLMN    Home PLMN
                  2     HPLMNCountry    Home PLMN Country
                  3     VPLMN    Visting PLMN
                  4     ROAM-NOT-ALLOWED    Roaming not allowed
              </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="NONE"/>
      <xs:enumeration value="HPLMN"/>
      <xs:enumeration value="HPLMNCountry"/>
      <xs:enumeration value="VPLMN"/>
      <xs:enumeration value="ROAM-NOT-ALLOWED"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- RE123_106765_MTC_interface_enhancements_for_HSS -->
  <!-- RE123_106765_MTC_interface_enhancements_for_HSS - start - Step 2 -->
  <xs:complexType name="RefScsGroupName">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="refScsGroupName" type="subscriber:PrintableString128" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- RE123_106765_MTC_interface_enhancements_for_HSS - end - Step 2 -->
  <xs:complexType name="EPSAdditionalHssServiceData">
    <xs:annotation>
      <xs:documentation>
                  Additional service Data details.
            </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <!-- RE123_106765_MTC_interface_enhancements_for_HSS - start - Step 2 -->
          <xsd:element name="refScsGroupName" type="subscriber:RefScsGroupName" minOccurs="0" maxOccurs="unbounded"/>
          <!-- RE123_106765_MTC_interface_enhancements_for_HSS - end - Step 2 -->
          <xsd:element name="deviceTriggerAllowed" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="refPrivacyProfileReg" type="xsd:int" minOccurs="0"/>
          <xsd:element name="monteTypeUeAllowed" type="subscriber:MonteTypeUeAllowed" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="monteConfiguration" type="subscriber:EpsMONTEConfiguration" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="servNodeConfigTimeStamp" type="xsd:dateTime" minOccurs="0"/>
          <xsd:element name="epsSuggNtwkConfParam" type="subscriber:EpsSuggNtwkConfParam" minOccurs="0" maxOccurs="unbounded"/>
		  </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- added by HSS-644 -->
  <xs:complexType name="EpsSuggNtwkConfParam">
      <xs:complexContent>
        <xs:extension base="spml:SecondClassObject">
          <xs:sequence>
            <xsd:element name="suggNtwkConfParamId" type="subscriber:PrintableString255" minOccurs="0"/>
            <xsd:element name="scefIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
            <xsd:element name="scefRealm" type="subscriber:PrintableString255" minOccurs="0"/>
            <xsd:element name="scefReferenceId" type="xsd:unsignedInt" minOccurs="0"/>
            <xsd:element name="externalId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="msisdn" type="subscriber:NumericString1_15" minOccurs="0"/>
            <xsd:element name="extDownLinkDataBuffering" type="xsd:int" minOccurs="0" />
            <xsd:element name="rauTauTimer" type="xsd:unsignedInt" minOccurs="0"/>
            <xsd:element name="activeTime" type="xsd:unsignedInt" minOccurs="0"/>
            <xsd:element name="eDRXCycleLength" type="subscriber:EDRXCycleLength" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="pagingTimeWindow" type="subscriber:PagingTimeWindow" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="externalGroupId" type="subscriber:PrintableString255" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
  </xs:complexType>
        
  <xs:complexType name="EDRXCycleLength">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="radioAccessType" type="xsd:unsignedInt" minOccurs="0"/>
          <xs:element name="cycleLengthVal" type="subscriber:UnsignedInt8bit" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="PagingTimeWindow">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="opMode" type="subscriber:UnsignedInt8bit" minOccurs="0"/>
          <xs:element name="pagingTimeLength" type="subscriber:UnsignedInt8bit" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- end added by hss-644 -->
  <!--************************************************************************-->
  <!-- Newly added reset SUP -->
  <!--************************************************************************-->
  <xs:complexType name="InvokeSUPRestoration">
    <xs:annotation>
      <xs:documentation> Defines the InvokeSUPRestoration Operation as extended request.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:AbstractOperation">
        <xs:sequence>
          <xs:element name="hssSharedUserProfileName" type="xsd:string"/>
          <xsd:element name="resetMME" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="resetSGSN" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
          <xsd:element name="enableRSRWithOutResetIdSupp" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!--************************************************************************-->

  <xsd:simpleType name="NonIpDataDeliveryMechType">
    <xsd:annotation>
      <xsd:documentation>
			    0: SGi-BASED-DATA-DELIVERY
				1: SCEF-BASED-DATA-DELIVERY
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="SGi-BASED-DATA-DELIVERY"/>
      <xsd:enumeration value="SCEF-BASED-DATA-DELIVERY"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!-- FC123_108057 Support external ID - End -->
  <xs:complexType name="hssExternalIdDataGroup">
    <xs:annotation>
      <xs:documentation>This is a group object class. It holds external ID Data.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="subscriber:AS">
        <xs:sequence>
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="externalIdData" type="subscriber:hssExternalIdData" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="hssExternalIdData">
    <xs:annotation>
      <xs:documentation>This object class holds external ID </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="externalId" type="xsd:string" minOccurs="0"/>
          <xs:element name="appPortId" type="xsd:unsignedInt" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- FC123_108057 Support external ID - Start -->
  <!-- FC123_107815_Self_Activation :: Start -->
  <xs:complexType name="EPSSelfActivation">
    <xs:annotation>
      <xs:documentation>Self activation data.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xs:element name="selfActivationStatus" type="subscriber:EPSSelfActivationStatus" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="EPSSelfActivationStatus">
    <xs:annotation>
      <xs:documentation>
        0: not allowed
        1: allowed
      </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:enumeration value="allowed"/>
      <xs:enumeration value="not allowed"/>
      <xs:enumeration value="0"/>
      <xs:enumeration value="1"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- FC123_107815_Self_Activation :: End -->
  <!--RE123_107772 SCEF Support-->
  <xs:complexType name="EpsMONTEConfiguration">
    <xs:complexContent>
      <xs:extension base="spml:SecondClassObject">
        <xs:sequence>
          <xsd:element name="scefRefIndex" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="scefIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="scefRealm" type="subscriber:PrintableString255" minOccurs="0"/>
          <xsd:element name="scefReferenceId" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="monteType" type="subscriber:MonteTypeUeAllowed" minOccurs="0"/>
          <xsd:element name="maxReports" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="monteDuration" type="xsd:dateTime" minOccurs="0"/>
          <xsd:element name="monteSuspendCondition" type="xsd:boolean" minOccurs="0"/>
          <xsd:element name="maxDetectTime" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="reachabilityType" type="subscriber:ReachabilityType" minOccurs="0" maxOccurs="unbounded"/>
          <xsd:element name="maxLatency" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="maxResponseTime" type="xsd:unsignedInt" minOccurs="0"/>
          <xsd:element name="monteLocationType" type="subscriber:MonteLocationType" minOccurs="0"/>
          <xsd:element name="locationAccuracy" type="subscriber:LocationAccuracy" minOccurs="0"/>
          <xsd:element name="externalId" type="xsd:string" minOccurs="0"/>
					<xsd:element name="msisdn" type="subscriber:NumericString1_15" minOccurs="0"/>
					<xsd:element name="associationType" type="subscriber:EpsAssociationType" minOccurs="0"/>
					<xsd:element name="externalGroupId" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="pdnAccPointName" type="subscriber:PrintableString255" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xsd:simpleType name="MonteTypeUeAllowed">
    <xsd:annotation>
      <xsd:documentation>Types of Allowed Monitoring Events for a subscriber</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="LOSS_OF_CONNECTIVITY"/>
      <xsd:enumeration value="UE_REACHABILITY"/>
      <xsd:enumeration value="LOCATION_REPORTING"/>
      <xsd:enumeration value="CHANGE_OF_IMSI_IMEI(SV)_ASSOCIATION"/>
      <xsd:enumeration value="ROAMING_STATUS"/>
      <xsd:enumeration value="COMMUNICATION_FAILURE"/>
      <xsd:enumeration value="AVAILABILITY_AFTER_DDN_FAILURE"/>
      <xsd:enumeration value="UE_REACHABILITY_AND_IDLE_STATUS_INDICATION"/>
      <xsd:enumeration value="AVAILABILITY_AFTER_DDN_FAILURE_AND_IDLE_STATUS_INDICATION"/>
      <xsd:enumeration value="PDN_CONNECTIVITY_STATUS"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReachabilityType">
    <xsd:annotation>
      <xsd:documentation>Specifies the type of UE Reachability to be configured.
				Reachability for SMS  : 0
				Reachability for Data : 1
			</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Reachability for SMS"/>
      <xsd:enumeration value="Reachability for Data"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MonteLocationType">
    <xsd:annotation>
      <xsd:documentation>Indicates whether the current location information is requested</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CURRENT_LOCATION"/>
      <xsd:enumeration value="LAST_KNOWN_LOCATION"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LocationAccuracy">
    <xsd:annotation>
      <xsd:documentation>Indicates the requested accuracy.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CGI-ECGI"/>
      <xsd:enumeration value="eNB"/>
      <xsd:enumeration value="LA-TA-RA"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MmeSupportedMONTE">
    <xsd:annotation>
      <xsd:documentation>
						This attribute contains a bit mask indicating the Monitoring events that MME supports. Hereafter bits, when set, indicate the support of the corresponding Monitoring event
			</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="UE and UICC and/or new IMSI-IMEI-SV association"/>
      <xsd:enumeration value="UE-reachability"/>
      <xsd:enumeration value="Location-of-the-UE"/>
      <xsd:enumeration value="Loss-of-connectivity"/>
      <xsd:enumeration value="Communication-failure"/>
      <xsd:enumeration value="Roaming-status"/>
      <xsd:enumeration value="Availability after DDN failure"/>
      <xsd:enumeration value="Idle Status Indication"/>
      <xsd:enumeration value="PDN Connectivity Status"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--END RE123_107772 SCEF Support-->
  <xsd:simpleType name="EpsAssociationType">
    <xsd:annotation>
     <xsd:documentation>Indicates the details of the reporting related to the IMEI-IMSI association</xsd:documentation>
      </xsd:annotation>
       <xsd:restriction base="xsd:string">
         <xsd:enumeration value="IMEI-CHANGE"/>
         <xsd:enumeration value="IMEISV-CHANGE"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xs:complexType name="SubscriberCancellation">
    <xs:annotation>
      <xs:documentation> Defines the subscriber cancellation firstclass object </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:FirstClassObject">
        <xs:sequence>
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="msisdn" type="xsd:string" minOccurs="0"/>
          <xs:element name="hssSharedUserProfileName" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfAddress" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SubscriberCancellationOperation">
    <xs:annotation>
      <xs:documentation> Defines the subscriber cancellation extended operation. </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="spml:AbstractOperation">
        <xs:sequence>
          <xs:element name="interface" type="subscriber:SupportedInterfaceType" minOccurs="0"/>
          <xs:element name="imsi" type="xsd:string" minOccurs="0"/>
          <xs:element name="msisdn" type="xsd:string" minOccurs="0"/>
          <xs:element name="privateUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="publicUserId" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfAddress" type="xsd:string" minOccurs="0"/>
          <xs:element name="scscfRealm" type="xsd:string" minOccurs="0"/>
          <xs:element name="reasonCode" type="subscriber:ReasonCodeType" minOccurs="0"/>
          <xs:element name="cancelCompleteSub" type="xsd:boolean" minOccurs="0"/>
          <xs:element name="aliasIdType" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xsd:simpleType name="SupportedInterfaceType">
    <xsd:annotation>
      <xsd:documentation>
				0: Cx
            </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Cx"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReasonCodeType">
    <xsd:annotation>
      <xsd:documentation>
				0: PERMANENT_TERMINATION
				1: NEW_SERVER_ASSIGNED
				2: SERVER_CHANGE
				3: REMOVE_S-CSCF
      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="PERMANENT_TERMINATION"/>
      <xsd:enumeration value="NEW_SERVER_ASSIGNED"/>
      <xsd:enumeration value="SERVER_CHANGE"/>
      <xsd:enumeration value="REMOVE_S-CSCF"/>
    </xsd:restriction>
  </xsd:simpleType>

<!-- BEGIN changes for HSS 18.0: RE123_108171 Outroamer Count -->
    <xs:simpleType name="ImsPlmnStatus">
          <xs:annotation>
              <xs:documentation>
                  The plmnStatus indicates whether the IMS subscriber is roaming in HPLMN, HPLMNCountry or VPLMN.
                  0    None
                  1    HPLMN    Home PLMN
                  2    HPLMNCountry    Home PLMN Country
                  3    VPLMN    Visting PLMN
                  4    ROAM-NOT-ALLOWED    Roaming not allowed
              </xs:documentation>
          </xs:annotation>
          <xs:restriction base="xs:string">
              <xs:enumeration value="NONE"/>
              <xs:enumeration value="HPLMN"/>
              <xs:enumeration value="HPLMNCountry"/>
              <xs:enumeration value="VPLMN"/>
              <xs:enumeration value="ROAM-NOT-ALLOWED"/>
          </xs:restriction>
      </xs:simpleType>
      <!-- END changes for HSS 18.0: RE123_108171 Outroamer Count -->

    <!-- START changes for HSS 18.0: RE123_107772 SCEF Support Step-2 -->
    <xs:complexType name="EpsSCEFInfo">
        <xs:annotation>
            <xs:documentation>
                  SCEF Information details.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="scefInfoIdentity" type="subscriber:PrintableString255"  minOccurs="0"/>
                    <xsd:element name="scefInfoRealm" type="subscriber:PrintableString255"  minOccurs="0"/>
                    <xsd:element name="scefSupportedFeatures" type="subscriber:ScefSupportedFeatures" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="niddAccPointName" type="subscriber:EpsNIDDAccPointName" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="EpsNIDDAccPointName">
        <xs:annotation>
            <xs:documentation>
                  NIDD APN details.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="pdnAccPointName" type="subscriber:PrintableString255" minOccurs="0"/>
                    <xsd:element name="niddContext" type="subscriber:EpsNIDDContext" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="EpsNIDDContext">
        <xs:annotation>
            <xs:documentation>
                  NIDD Context details.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xsd:element name="s6tUserIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
                    <xsd:element name="s6tUserIdentityType" type="subscriber:UserIdentityType" minOccurs="0"/>
                    <xsd:element name="expiryDate" type="xsd:dateTime" minOccurs="0"/>
                    <xsd:element name="externalGroupId" type="subscriber:PrintableString255" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xsd:complexType name="ScefSupportedFeatures">
        <xsd:annotation>
            <xsd:documentation>
                Complex type containing the list of features supported by the SCEF.It is composed of feature list ID and the feature. For the currently defined bits the feature list ID is 1. Currently 5 features are defined
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="featureListId" type="xsd:int" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="feature" type="subscriber:FeatureList" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="UserIdentityType">
        <xsd:annotation>
            <xsd:documentation>Specifies the type of User Identity to be configured.
                     MSISDN : 0
                 EXTERNALID : 1
                       IMSI : 2
        </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
           <xsd:enumeration value="MSISDN"/>
           <xsd:enumeration value="EXTERNALID"/>
           <xsd:enumeration value="IMSI"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xs:complexType name="DeleteExpiredData">
			<xs:annotation>
				<xs:documentation> Defines the Delete Expired Subscriber data Operation as extended request.</xs:documentation>
			</xs:annotation>
			<xs:complexContent>
				<xs:extension base="spml:AbstractOperation">
					<xs:sequence>
						<xsd:element name="objectClass" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
						<xsd:element name="aliasIdType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
    </xs:complexType>
    <!-- END changes for HSS 18.0: RE123_107772 SCEF Support Step-2 -->
	<!-- START changes for HSS 18.0SP3: RE123_108169_Emergency_Handover_Procedures -->
    <xs:complexType name="HssEmergencyInfo">
        <xs:annotation>
            <xs:documentation>
                  This readOnly Object will have the emergency PDN GW related data.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xsd:element name="pdnGw" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="pdnGwRealm" type="subscriber:PrintableString255" minOccurs="0"/>
					<xsd:element name="pdnGwIPv4" type="subscriber:IPv4" minOccurs="0"/>
                    <xsd:element name="pdnGwIPv6" type="subscriber:IPv6" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	<!-- END changes for HSS 18.0SP3: RE123_108169_Emergency_Handover_Procedures -->
  
  <!-- START changes for RE123_108401_RDSP enhancements to interface with EIR/EEIR/NAP - HSS Impacts-->
    <xs:complexType name="HssRdspMobilityRoamPlanInfo">
         <xs:annotation>
             <xs:documentation>The specific RDSP mobility roam plan information for each Subscriber data. This SCO is updated by FE. AEP does not support the provisioning for this SCO. </xs:documentation>
         </xs:annotation>
         <xs:complexContent>
             <xs:extension base="spml:SecondClassObject">
                 <xs:sequence>
                     <xs:element name="interfaceId" type="subscriber:RdspMobilityRPIInterface" minOccurs="0"/>
                     <xs:element name="mmeIdentity" type="subscriber:PrintableString255" minOccurs="0"/>
                     <xs:element name="mmeRealm" type="xsd:string" minOccurs="0"/>
                     <xs:element name="vplmnId" type="subscriber:PrintableString64" minOccurs="0"/>
                     <xs:element name="refroamPlanRouting" type="xsd:string" minOccurs="0"/>
                     <xs:element name="refroamPlanStandard" type="xsd:string" minOccurs="0"/>
                     <xs:element name="refRoamPlan" type="xsd:string" minOccurs="0"/>
                     <xs:element name="hlrRdspNsrIndicator" type="xsd:unsignedInt" minOccurs="0"/>
                     <xs:element name="epsRdspNsrIndicator" type="xsd:unsignedInt" minOccurs="0"/>
                     <xs:element name="rdspPriorityConfig" type="subscriber:RdspPriorityCfg" minOccurs="0"/>
                     <xs:element name="imeisv" type="xsd:string" minOccurs="0"/>
                 </xs:sequence>
             </xs:extension>
         </xs:complexContent>
    </xs:complexType>

   <xs:simpleType name="RdspMobilityRPIInterface">
        <xs:annotation>
            <xs:documentation>Definition of enumeration for the interface which will be used for the specific roam plan</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
             <xs:enumeration value="C"/>
             <xs:enumeration value="D_Gr"/>
             <xs:enumeration value="S6a"/>
             <xs:enumeration value="S6d"/>
             <xs:enumeration value="SWx"/>
             <xs:enumeration value="RSI_IMEI"/>
             <xs:enumeration value="RSI_IMEI_HSS"/>
        </xs:restriction>
   </xs:simpleType>

   <xs:simpleType name="RdspPriorityCfg">
        <xs:annotation>
            <xs:documentation>The attribute indicates which RDSP feature is chosen.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
             <xs:enumeration value="Location"/>
             <xs:enumeration value="IMEISV"/>
        </xs:restriction>
   </xs:simpleType>

   <xs:complexType name="MigrateImeisvEIRToHLR">
        <xs:annotation>
            <xs:documentation> Define the migrate imeisv operation as extended request.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:AbstractOperation">
                <xs:sequence>
                    <xsd:element name="aliasIdType" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- END changes for RE123_108401_RDSP enhancements to interface with EIR/EEIR/NAP - HSS Impacts-->
    <xs:complexType name="HssInterworkingWithUdm">
        <xs:annotation>
            <xs:documentation>HSS Interworking with UDM</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                     <xs:element name="interworkingType" type="subscriber:InterworkingType" minOccurs="0"/>
                     <xs:element name="eeSubscriptions" type="subscriber:HssEeSubscriptions" minOccurs="0"/>
                     <xs:element name="locationInformation5GS" type="subscriber:HssLocationInformation5GS" minOccurs="0"/>
					 <xs:element name="sdmSubscriptions" type="subscriber:HssSdmSubscriptions" minOccurs="0"/>
                     <xs:element name="hssSubscriptions" type="subscriber:HssSubscriptionDatas" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="InterworkingType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="hssInterworkingWithUdm"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="HssEeSubscriptions">
            <xs:annotation>
                <xs:documentation>HSS SDM Registrations</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                       <xs:element name="subscriptionType" type="subscriber:EeSubscriptionType" minOccurs="0"/>
                       <xs:element name="individualEeSubscription" type="subscriber:HssIndividualEeSubscription" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HssLocationInformation5GS">
            <xs:annotation>
                <xs:documentation>The location of the served subscriber in the AMF (for 3GPP access) if the requested domain is PS and the requested nodes is AMF.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="nrCellGlobalId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="trackingAreaId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="ageOfLocInfo" type="xsd:string" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>
   
   <xs:simpleType name="EeSubscriptionType">
                <xs:restriction base="xsd:string">
                    <xs:enumeration value="ee-subscriptions"/>
                </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="HssIndividualEeSubscription">
            <xs:annotation>
                <xs:documentation>Hss Individual SDM Registration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="eeSubsId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                        <xs:element name="monitConfig" type="subscriber:HssMonitoringConfiguration" minOccurs="0" maxOccurs="unbounded"/>
                       <xs:element name="reports" type="subscriber:HssReportingOptions" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HssMonitoringConfiguration">
            <xs:annotation>
                <xs:documentation>HSS Individual SDM Registration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="referenceId" type="subscriber:NumericString" minOccurs="0"/>
                        <xs:element name="eventType" type="xsd:string" minOccurs="0"/>
                        <xs:element name="immediateFlag" type="xsd:boolean" minOccurs="0"/>
                        <!-- HSS-1016 START-->
                        <xs:element name="reachabilityForSmsCfg" type="xsd:string" minOccurs="0"/>
                        <!-- HSS-1016 END-->
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>
     <xs:complexType name="HssReportingOptions">
                <xs:annotation>
                    <xs:documentation>Hss Individual SDM Registration</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                        <xs:element name="reportingOptions" type="subscriber:EeReportingOption" minOccurs="0"/>
                            <xs:element name="maxNumOfReports" type="subscriber:NumericString" minOccurs="0"/>
                            <!-- Start HSS-956 -->
                            <xs:element name="expiryDate" type="xsd:dateTime" minOccurs="0"/>
                            <!-- End HSS-956 -->
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="EeReportingOption">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="reporting-options"/>
        </xs:restriction>
    </xs:simpleType>
    <!--************************************************************************-->
    <!--    HSS External GROUP First Class Object START                        -->
    <!--************************************************************************-->
    <xs:complexType name="HssExternalGroupId">
        <xs:annotation>
            <xs:documentation>Definition of first object class HSS External Group</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                    <xsd:element name="imsiGroupIdName" type="xsd:string" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--    HSS External GROUP First Class Object END                           -->
    <!--************************************************************************-->
    <!-- FRS123_106725 RDSP enhancement in HSS - Start -->
    <xs:simpleType name="DomainSelectionForIms">
        <xs:annotation>
            <xs:documentation>
                Considering the VoLTE/VoWiFI/VoNR scenarios, HSS may need to determine whether it shall reach EPS/5GS profile to get the roaming status of the subscriber.
                0    IMS
                1    EPS
                2    Non3GPP
                3    5G
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IMS"/>
            <xs:enumeration value="EPS"/>
            <xs:enumeration value="Non3GPP"/>
            <xs:enumeration value="5G"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DomainSelectionStrategy">
        <xs:annotation>
            <xs:documentation>Domain Select Strategy</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="priority" type="subscriber:NumericString5" minOccurs="0"/>
                    <xs:element name="imsSelectionDomain" type="subscriber:DomainSelectionForIms" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ImsOrderOfSelection">
        <xs:annotation>
            <xs:documentation>This attributes indicates a symbolic name identifying the Ims Roaming Subscription Info entry.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="imsPriorityOrder" type="xsd:int" minOccurs="0"/>
                    <xs:element name="imsRoamSubscriptionInfoName" type="subscriber:PrintableString128" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FRS123_106725 RDSP enhancement in HSS - End -->

    <xs:complexType name="DocumentSpaceReset">
         <xs:annotation>
              <xs:documentation> Defines the Document Space Reset data Operation as extended request.</xs:documentation>
         </xs:annotation>
         <xs:complexContent>
              <xs:extension base="spml:AbstractOperation">
                 <xs:sequence>
                   <xsd:element name="application" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                   <xsd:element name="mmeIdentity" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                   <xsd:element name="sgsnIdentity" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                   <xsd:element name="vlrNumber" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                   <xsd:element name="sgsnNumber" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                   <xsd:element name="aliasIdType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                 </xs:sequence>
               </xs:extension>
          </xs:complexContent>
     </xs:complexType>

    <!-- HSS-4011 -->
    <xs:complexType name="HssSdmSubscriptions">
        <xs:annotation>
            <xs:documentation>HSS SDM Registrations</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriptionType" type="subscriber:SdmSubscriptionType" minOccurs="0"/>
                    <xs:element name="individualSdmSubscription" type="subscriber:HssIndividualSdmSubscription" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HssIndividualSdmSubscription">
        <xs:annotation>
            <xs:documentation>Hss Individual SDM Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="hssSdmSubsId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="hssNfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="hssMonitoredResourceUri" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="expiryDate" type="xsd:dateTime" minOccurs="0"/>
                    <xs:element name="hssDnn" type="xsd:string" minOccurs="0"/>
                    <xs:element name="hssImplicitUnsubscribe" type="xsd:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HssSubscriptionDatas">
        <xs:annotation>
            <xs:documentation>HSS SDM Registrations</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriptionType" type="subscriber:HssSubscriptionDataType" minOccurs="0"/>
                    <xs:element name="hssIndividualSubscriptionData" type="subscriber:HssIndividualSubscriptionData" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HssIndividualSubscriptionData">
        <xs:annotation>
            <xs:documentation>Hss Individual SDM Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="hssSubsDataId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="hssNfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="hssMonitoredResourceUri" type="xsd:string" minOccurs="0" maxOccurs="unbounded" />
                    <xs:element name="expiryDate" type="xsd:dateTime" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="SdmSubscriptionType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="hssSdmSubscriptions"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HssSubscriptionDataType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="hssSubscriptionDatas"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- HSS-4011 -->


<!-- ************************************************************************** -->
<!--                           UDM 5G AS SCO                                    -->
<!-- ************************************************************************** -->

  <xs:complexType name="Udm5gData">
        <xs:annotation>
            <xs:documentation>UDM 5G-DATA</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="data" type="subscriber:DataTypes" minOccurs="0"/>
			        <xs:element name="udmImsi" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="udmMsisdn" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="authenticationData" type="subscriber:UdmAuthenticationData" minOccurs="0"/>
                    <xs:element name="contextData" type="subscriber:UdmContextData" minOccurs="0"/>
                    <xs:element name="servingPlmnId" type="subscriber:UdmServingPlmnId" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="nokiaUdmCustomData" type="subscriber:UdmNokiaUdmCustomData" minOccurs="0"/>
					<xs:element name="OdbData" type="subscriber:UdmOdbData" minOccurs="0"/>
					<xs:element name="operatorSpecificData" type="subscriber:UdmOperatorSpecificData" minOccurs="0"/>
                    <xs:element name="authenticationStatus" type="subscriber:UdmAuthenticationStatus" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="DataTypes">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="5GSubscription"/>
        </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmAuthenticationData">
        <xs:annotation>
            <xs:documentation>UDM Authentication Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="authenticationMethod" type="xsd:string" minOccurs="0"/>
                    <xs:element name="protectionScheme" type="subscriber:UdmProtectionScheme" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmProtectionScheme">
        <xs:annotation>
            <xs:documentation>UDM Protection Scheme</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="prohibitNullScheme" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="dataIdentifier" type="subscriber:protectionSchemeIdentifier" minOccurs="0"/>
			    </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
    <xs:simpleType name="protectionSchemeIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="protection-scheme"/>
        </xs:restriction>
    </xs:simpleType>

     <xs:complexType name="UdmAuthenticationStatus">
        <xs:annotation>
            <xs:documentation>UDM Authentication Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="servingNetworkName" type="xsd:string" minOccurs="0"/>
                    <xs:element name="nfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="success" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="timeStamp" type="xsd:string" minOccurs="0"/>
                    <xs:element name="authType" type="xsd:string" minOccurs="0"/>
					<xs:element name="kausf" type="xsd:string" minOccurs="0"/>
                    <xs:element name="keyIndex" type="xsd:int" minOccurs="0"/>
                    <xs:element name="counterSor" type="xsd:string" minOccurs="0"/>
                    <xs:element name="counterUpu" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmContextData">
        <xs:annotation>
            <xs:documentation>UDM Context Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="dataIdentifier" type="subscriber:DataIdentifier" minOccurs="0"/>
				    <xs:element name="amf3gppAccessRegistration" type="subscriber:UdmAmf3gppAccessRegistration" minOccurs="0"/>
					<xs:element name="smfRegistrations" type="subscriber:UdmSmfRegistrations" minOccurs="0"/>
                    <xs:element name="sdmSubscriptions" type="subscriber:UdmSdmSubscriptions" minOccurs="0"/>
					<xs:element name="amfnon3gppAccessRegistration" type="subscriber:UdmAmfNon3gppAccessRegistration" minOccurs="0"/>
					<xs:element name="smsf3GppAccessRegistration" type="subscriber:UdmSmsf3GppAccessRegistration" minOccurs="0"/>
                    <xs:element name="smsfNon3GppAccessRegistration" type="subscriber:UdmSmsfNon3GppAccessRegistration" minOccurs="0"/>
					<xs:element name="eeSubscriptions" type="subscriber:UdmEeSubscriptions" minOccurs="0"/>
					<xs:element name="subscriptionDataSubscriptions" type="subscriber:Udm5GSubscriptionDataSubscriptions" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="DataIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="context-data"/>
        </xs:restriction>
    </xs:simpleType>
	
	<xs:complexType name="UdmAmf3gppAccessRegistration">
        <xs:annotation>
            <xs:documentation>UDM AMF 3GPP Access Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="amfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="purgeFlag" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="imsVoPS" type="xsd:string" minOccurs="0"/>
                    <xs:element name="deregCallbackUri" type="xsd:string" minOccurs="0"/>
                    <xs:element name="pcscfRestorationCallbackUri" type="xsd:string" minOccurs="0"/>
					<xs:element name="pei" type="xsd:string" minOccurs="0"/>
                    <xs:element name="amfId" type="subscriber:UdmAmf3gppGuami" minOccurs="0"/>
		            <xs:element name="backupamf" type="subscriber:UdmAmf3gppBackupInfo" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="ratType" type="xsd:string" minOccurs="0"/>
					<xs:element name="amfServiceNameDereg" type="xsd:string" minOccurs="0"/>
                    <xs:element name="amfServiceNamePcscfRest" type="xsd:string" minOccurs="0"/>
                    <xs:element name="initialRegistrationInd" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="drFlag" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="registrationTime" type="xsd:string" minOccurs="0"/>
                    <xs:element name="urrpIndicator" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="contextInfo" type="subscriber:UdmAmf3gppRegContextInfo" minOccurs="0"/>
					<xs:element name="epsInterworkingInfo" type="subscriber:UdmEpsInterworkingInfo" minOccurs="0"/>																							   
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

		<xs:complexType name="UdmAmf3gppGuami">
                <xs:annotation>
                    <xs:documentation>UDM AMF 3GPP ID</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                            <xs:element name="amfIdValue" type="xsd:string" minOccurs="0"/>
					        <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
        </xs:complexType>

		<xs:complexType name="UdmAmf3gppBackupInfo">
    	            <xs:annotation>
    	                <xs:documentation>UDM 3GPP Backup AMF </xs:documentation>
    	            </xs:annotation>
    	            <xs:complexContent>
    	                <xs:extension base="spml:SecondClassObject">
    	                    <xs:sequence>
    	                        <xs:element name="backupAmf" type="xsd:string" minOccurs="0"/>
    	                        <xs:element name="amfId" type="subscriber:UdmAmf3gppGuamiList" minOccurs="0" maxOccurs="unbounded"/>
    	                    </xs:sequence>
    	                </xs:extension>
    	            </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmAmf3gppGuamiList">
                <xs:annotation>
                    <xs:documentation>UDM 3GPP Guami List </xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                            <xs:element name="amfIdValue" type="xsd:string" minOccurs="0"/>
                            <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmAmf3gppRegContextInfo">
        <xs:annotation>
            <xs:documentation>UDM Amf 3GPP Registration Context Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

<xs:complexType name="UdmEpsInterworkingInfo">
        <xs:annotation>
            <xs:documentation>The associations between APN/DNN and PGW-C+SMF selected by the AMF for EPS interworking.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="dataIdentifier" type="subscriber:EpsInterworkingInfoIdentifier" minOccurs="0"/>
                    <xs:element name="epsIwkPgw" type="subscriber:UdmEpsIwkPgw" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmEpsIwkPgw">
        <xs:annotation>
            <xs:documentation>A map (list of key-value pairs where dnn serves as key) of EpsIwkPgws.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="dnnId" type="subscriber:PrintableStringAPN255" minOccurs="0"/>
                    <xs:element name="pgwFqdn" type="xsd:string" minOccurs="0"/>
                    <xs:element name="smfInstanceId" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="EpsInterworkingInfoIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="epsInterworkingInfo"/>
        </xs:restriction>
    </xs:simpleType>
	<xs:complexType name="UdmAmfNon3gppAccessRegistration">
        <xs:annotation>
            <xs:documentation>UDM AMF Non3GPP Access Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="amfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="purgeFlag" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="deregCallbackUri" type="xsd:string" minOccurs="0"/>
                    <xs:element name="pcscfRestorationCallbackUri" type="xsd:string" minOccurs="0"/>
					<xs:element name="pei" type="xsd:string" minOccurs="0"/>
					<xs:element name="amfId" type="subscriber:UdmAmfNon3gppGuami" minOccurs="0"/>
		            <xs:element name="backupamf" type="subscriber:UdmAmfNon3gppBackupInfo" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="ratType" type="xsd:string" minOccurs="0"/>
					<xs:element name="imsVoPS" type="xsd:string" minOccurs="0"/>
					<xs:element name="amfServiceNameDereg" type="xsd:string" minOccurs="0"/>
                    <xs:element name="amfServiceNamePcscfRest" type="xsd:string" minOccurs="0"/>
                    <xs:element name="registrationTime" type="xsd:string" minOccurs="0"/>
                    <xs:element name="urrpIndicator" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="contextInfo" type="subscriber:UdmAmfNon3gppRegContextInfo" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmAmfNon3gppGuami">
                <xs:annotation>
                    <xs:documentation>UDM AMF 3GPP ID</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                            <xs:element name="amfIdValue" type="xsd:string" minOccurs="0"/>
                            <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
        </xs:complexType>        

        <xs:complexType name="UdmAmfNon3gppBackupInfo">
    	            <xs:annotation>
    	                <xs:documentation>UDM 3GPP Backup AMF </xs:documentation>
    	            </xs:annotation>
    	            <xs:complexContent>
    	                <xs:extension base="spml:SecondClassObject">
    	                    <xs:sequence>
    	                        <xs:element name="backupAmf" type="xsd:string" minOccurs="0"/>
    	                        <xs:element name="amfId" type="subscriber:UdmAmfNon3gppGuamiList" minOccurs="0" maxOccurs="unbounded"/>
    	                    </xs:sequence>
    	                </xs:extension>
    	            </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmAmfNon3gppGuamiList">
                <xs:annotation>
                    <xs:documentation>UDM 3GPP Guami List </xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                            <xs:element name="amfIdValue" type="xsd:string" minOccurs="0"/>
                            <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmAmfNon3gppRegContextInfo">
        <xs:annotation>
            <xs:documentation>UDM Amf Non 3GPP Registration Context Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSmsf3GppAccessRegistration">
            <xs:annotation>
                <xs:documentation>UDM SMSF 3GPP Access Registrations</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
    				    <xs:element name="smsfRegType" type="subscriber:SmsfRegType" minOccurs="0"/>
    				    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
    				    <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsfInstanceId" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsfMAPAddress" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsf3gppDiameterAddress" type="subscriber:UdmSmsf3gppDiameterAddress" minOccurs="0"/>
    				    <xs:element name="smsfSetId" type="xsd:string" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSmsf3gppDiameterAddress">
                <xs:annotation>
                    <xs:documentation>SMSF DiameterAddress</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
							<xs:element name="smsfName" type="xsd:string" minOccurs="0"/>
                            <xs:element name="smsfRealm" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
     </xs:complexType>

	<xs:simpleType name="SmsfRegType">
            <xs:restriction base="xsd:string">
                <xs:enumeration value="smsf-3gpp-access"/>
            </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmSmsfNon3GppAccessRegistration">
            <xs:annotation>
                <xs:documentation>UDM SMSF Non3GPP Access Registrations</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
    				    <xs:element name="smsfNonRegType" type="subscriber:SmsfNonRegType" minOccurs="0"/>
    				    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
    				    <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsfInstanceId" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsfMAPAddress" type="xsd:string" minOccurs="0"/>
    				    <xs:element name="smsfNon3gppDiameterAddress" type="subscriber:UdmSmsfNon3gppDiameterAddress" minOccurs="0"/>
    				    <xs:element name="smsfSetId" type="xsd:string" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>
	
	<xs:simpleType name="SmsfNonRegType">
            <xs:restriction base="xsd:string">
                <xs:enumeration value="smsf-non-3gpp-access"/>
            </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmSmsfNon3gppDiameterAddress">
                <xs:annotation>
                    <xs:documentation>Non 3gpp SMSF DiameterAddress</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
							<xs:element name="smsfName" type="xsd:string" minOccurs="0"/>
                            <xs:element name="smsfRealm" type="xsd:string" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

	<xsd:simpleType name="UdmHexadecimalString">
        <xsd:annotation>
            <xsd:documentation>Hexadecimal values are allowed.A-F and 0-9.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-F0-9]*"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xs:complexType name="UdmSmfRegistrations">
        <xs:annotation>
            <xs:documentation>UDM SMF Registrations</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="registrationType" type="subscriber:RegistrationType" minOccurs="0"/>
				    <xs:element name="individualSmfRegistration" type="subscriber:UdmIndividualSmfRegistration" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
	<xs:simpleType name="RegistrationType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="smf-registrations"/>
        </xs:restriction>
    </xs:simpleType>
	
	<xs:complexType name="UdmIndividualSmfRegistration">
        <xs:annotation>
            <xs:documentation>UDM Individual SMF Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="pduSessionId" type="xsd:int" minOccurs="0"/>
                    <xs:element name="smfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="dnn" type="xsd:string" minOccurs="0"/>
                    <xs:element name="pcscfRestorationCallbackUri" type="xsd:string" minOccurs="0"/>
					<xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
					<xs:element name="pgwFqdn" type="xsd:string" minOccurs="0"/>
					<xs:element name="singleNssai" type="xsd:string" minOccurs="0"/>
					<xs:element name="emergencyServices" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="deregCallbackUri" type="xsd:string" minOccurs="0"/>
                    <xs:element name="registrationReason" type="xsd:string" minOccurs="0"/>
                    <xs:element name="registrationTime" type="xsd:string" minOccurs="0"/>
                    <xs:element name="smfSetId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="contextInfo" type="subscriber:UdmSmfRegContextInfo" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmSmfRegContextInfo">
        <xs:annotation>
            <xs:documentation>UDM Smf Registration Context Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSdmSubscriptions">
        <xs:annotation>
            <xs:documentation>UDM SDM Registrations</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="subscriptionType" type="subscriber:SubscriptionType" minOccurs="0"/>
				    <xs:element name="individualSdmSubscription" type="subscriber:UdmIndividualSdmSubscription" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmIndividualSdmSubscription">
        <xs:annotation>
            <xs:documentation>UDM Individual SDM Registration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subsId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="monitoredResourceUris" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="implicitUnsubscribe" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="expires" type="xsd:string" minOccurs="0"/>
					<xs:element name="nfInstanceId" type="xsd:string" minOccurs="0"/>
					<xs:element name="dnn" type="xsd:string" minOccurs="0"/>
					<xs:element name="singleNssai" type="xsd:string" minOccurs="0"/>
					<xs:element name="amfServiceName" type="xsd:string" minOccurs="0"/>
					<xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                                        <xs:element name="sdmSubscriptionContextInfo" type="subscriber:UdmSdmSubscriptionContextInfo" minOccurs="0"/>
					<xs:element name="sdmHssSubscriptionInfo" type="subscriber:UdmSdmHssSubscriptionInfo" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

        <xs:complexType name="UdmSdmSubscriptionContextInfo">
        <xs:annotation>
            <xs:documentation>ContextInfo in SDM Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
	<xs:complexType name="UdmSdmHssSubscriptionInfo">
        <xs:annotation>
            <xs:documentation>SdmHssSubscriptionInfo in SDM Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="hssInstanceId" type="xsd:string" minOccurs="0" />
                    <xs:element name="subscriptionId" type="xsd:string" minOccurs="0" />
                    <xs:element name="contextInfo" type="subscriber:UdmSdmHssSubscriptionContextInfo" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	
	<xs:complexType name="UdmSdmHssSubscriptionContextInfo">
        <xs:annotation>
            <xs:documentation>ContextInfo in SDM Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmEeSubscriptions">
            <xs:annotation>
                <xs:documentation>UDM SDM Registrations</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
    				   <xs:element name="subscriptionType" type="subscriber:EeSubscriptionType" minOccurs="0"/>
    				   <xs:element name="individualEeSubscription" type="subscriber:UdmIndividualEeSubscription" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="SubscriptionType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="sdm-subscriptions"/>
        </xs:restriction>
    </xs:simpleType>
	
	<xs:complexType name="UdmIndividualEeSubscription">
            <xs:annotation>
                <xs:documentation>UDM Individual EE Registration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="eesubsId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                        <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                        <xs:element name="monitConfig" type="subscriber:UdmMonitoringConfiguration" minOccurs="0" maxOccurs="unbounded"/>
                        <xs:element name="reports" type="subscriber:UdmReportingOptions" minOccurs="0"/>
                        <xs:element name="contextInfo" type="subscriber:UdmEeSubscriptionContextInfo" minOccurs="0"/>
                        <xs:element name="amfSubscriptionInfo" type="subscriber:UdmAmfSubscriptionInfo" minOccurs="0" maxOccurs="unbounded"/>
						<xs:element name="epcAppliedInd" type="xsd:boolean" minOccurs="0"/>
                        <xs:element name="scefDiamHost" type="xsd:string" minOccurs="0"/>
                        <xs:element name="scefDiamRealm" type="xsd:string" minOccurs="0"/>
                        <xs:element name="notifyCorrelationId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="eeHssSubscriptionInfo" type="subscriber:UdmEeHssSubscriptionInfo" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmMonitoringConfiguration">
            <xs:annotation>
                <xs:documentation>UDM Individual SDM Registration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="referenceId" type="subscriber:NumericString" minOccurs="0"/>
                        <xs:element name="immediateFlag" type="xsd:boolean" minOccurs="0"/>
                        <xs:element name="eventType" type="xsd:string" minOccurs="0"/>
                        <xs:element name="reachabilityForSmsCfg" type="xsd:string" minOccurs="0"/>
                        <xs:element name="locationReportingConfiguration" type="subscriber:UdmLocationReportingConfiguration" minOccurs="0"/>
                        <xs:element name="extendedReferenceId" type="subscriber:NumericString" minOccurs="0"/>
                        <xs:element name="associationType" type="xsd:string" minOccurs="0"/>
                        <xs:element name="afId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="lossConnectivityCfg" type="subscriber:UdmLossConnectivityCfg" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

        <xs:complexType name="UdmLocationReportingConfiguration">
            <xs:annotation>
                <xs:documentation>UDM Location Reporting Configuration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="currentLocation" type="xsd:boolean" minOccurs="0"/>
                        <xs:element name="oneTime" type="xsd:boolean" minOccurs="0"/>
                        <xs:element name="accuracy" type="xsd:string" minOccurs="0"/>
                        <xs:element name="n3gppAccuracy" type="xsd:string" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
		
		<xs:complexType name="UdmLossConnectivityCfg">
            <xs:annotation>
                <xs:documentation>UDM Loss Of Connectivity Configuration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
                        <xs:element name="dataIdentifier" type="subscriber:lossConnCfgIdentifier" minOccurs="0"/>
                        <xs:element name="maxDetectionTime" type="xsd:unsignedInt" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>

        <xs:simpleType name="lossConnCfgIdentifier">
            <xs:restriction base="xsd:string">
                <xs:enumeration value="loss-of-conn-configuration"/>
            </xs:restriction>
        </xs:simpleType>

	<xs:complexType name="UdmReportingOptions">
                <xs:annotation>
                    <xs:documentation>UDM Individual SDM Registration</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                        <xs:element name="reportingOptions" type="subscriber:EeReportingOption" minOccurs="0"/>
                            <xs:element name="maxNumOfReports" type="subscriber:NumericString" minOccurs="0"/>
                            <xs:element name="monitoringDuration" type="xsd:string" minOccurs="0"/>
							<xs:element name="reportMode" type="xsd:string" minOccurs="0"/>
                            <xs:element name="reportPeriod" type="xsd:unsignedInt" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmEeSubscriptionContextInfo">
        <xs:annotation>
            <xs:documentation>UDM Ee Subscription Context Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
        <xs:complexType name="UdmAmfSubscriptionInfo">
                <xs:annotation>
                    <xs:documentation>UDM Amf Subscription Information</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                        <xs:element name="amfInstanceId" type="xsd:string" minOccurs="0"/>
                            <xs:element name="subscriptionId" type="xsd:string" minOccurs="0"/>
                            <xs:element name="subsChangeNotifyCorrelationId" type="xsd:string" minOccurs="0"/>
                            <xs:element name="contextInfo" type="subscriber:UdmAmfSubscriptionContextInfo" minOccurs="0"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
    </xs:complexType>

        <xs:complexType name="UdmAmfSubscriptionContextInfo">
                <xs:annotation>
                    <xs:documentation>UDM Amf Subscription Context Info</xs:documentation>
                </xs:annotation>
                <xs:complexContent>
                    <xs:extension base="spml:SecondClassObject">
                        <xs:sequence>
                            <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
        </xs:complexType>
		
		<xs:complexType name="UdmEeHssSubscriptionInfo">
        <xs:annotation>
            <xs:documentation>EeHssSubscriptionInfo in EE HSS Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="hssInstanceId" type="xsd:string" minOccurs="0" />
                    <xs:element name="subscriptionId" type="xsd:string" minOccurs="0" />
                    <xs:element name="contextInfo" type="subscriber:UdmEeHssSubscriptionContextInfo" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmEeHssSubscriptionContextInfo">
        <xs:annotation>
            <xs:documentation>ContextInfo in EE Hss Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Udm5GSubscriptionDataSubscriptions">
        <xs:annotation>
            <xs:documentation>UDM Subcription Data Subscriptions</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriptionType" type="subscriber:SubscriptionDataSubscriptionType" minOccurs="0"/>
                    <xs:element name="individualSubscriptionDataSubscription" type="subscriber:Udm5GIndividualSubscriptionDataSubscription" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="SubscriptionDataSubscriptionType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="subs-to-notify"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Udm5GIndividualSubscriptionDataSubscription">
        <xs:annotation>
            <xs:documentation>UDM Individual Subscription Data Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subsId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="subscriptionId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="monitoredResourceUris" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="originalCallbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="ueId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="expiry" type="xsd:string" minOccurs="0"/>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="sbiBinding" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="sbiCorrelationInfo" type="xsd:string" minOccurs="0"/>
                    <xs:element name="subscribingNFType" type="xsd:string" minOccurs="0"/>
                    <xs:element name="activationStatus" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="sdmSubscription" type="subscriber:Udm5GSdmSubscriptionInSds" minOccurs="0"/>
                    <xs:element name="hssSubscriptionList" type="subscriber:UdmHssSubscriptionListInSds" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Udm5GSdmSubscriptionInSds">
        <xs:annotation>
            <xs:documentation>UDM SDM Subscription In Subscription Data Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriptionType" type="subscriber:SdmSubscriptionInSdsType" minOccurs="0"/>
                    <xs:element name="subscriptionId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="callbackReference" type="xsd:string" minOccurs="0"/>
                    <xs:element name="monitoredResourceUris" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="implicitUnsubscribe" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="expires" type="xsd:string" minOccurs="0"/>
                    <xs:element name="nfInstanceId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="dnn" type="xsd:string" minOccurs="0"/>
                    <xs:element name="singleNssai" type="xsd:string" minOccurs="0"/>
                    <xs:element name="amfServiceName" type="xsd:string" minOccurs="0"/>
                    <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="contextInfo" type="subscriber:UdmSdmSubscriptionContextInfoInSds" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="SdmSubscriptionInSdsType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="sdm-subscription"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="UdmSdmSubscriptionContextInfoInSds">
        <xs:annotation>
            <xs:documentation>ContextInfo in SDM Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmHssSubscriptionListInSds">
        <xs:annotation>
            <xs:documentation>Udm Hss Subscription List In Subscription Data Subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriptionType" type="subscriber:HssSubscriptionListSubscriptionType" minOccurs="0"/>
                    <xs:element name="hssSubscriptionItem" type="subscriber:UdmHssSubscriptionItem" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="HssSubscriptionListSubscriptionType">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="hssSubscriptionList"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="UdmHssSubscriptionItem">
        <xs:annotation>
            <xs:documentation>Udm Hss Subscription Item</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="hssInstanceId" type="xsd:string" minOccurs="0" />
                    <xs:element name="subscriptionId" type="xsd:string" minOccurs="0" />
                    <xs:element name="contextInfo" type="subscriber:UdmHssSubscriptionContextInfoInSds" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmHssSubscriptionContextInfoInSds">
        <xs:annotation>
            <xs:documentation>ContextInfo in Hss Subscription Item</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="origHeaders" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmServingPlmnId">
        <xs:annotation>
            <xs:documentation>UDM Serving PLMNID</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="plmnId" type="subscriber:NumericString" minOccurs="0"/>
                    <xs:element name="homePlmnIdIndication" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="provisionedData" type="subscriber:UdmProvisionedData" minOccurs="0"/>
					<xs:element name="hplmnSet" type="xsd:string" minOccurs="0"/>
                    <xs:element name="allowedVplmnSet" type="xsd:string" minOccurs="0"/>
                    <xs:element name="restrictedPlmnSet" type="xsd:string" minOccurs="0"/>																					
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmProvisionedData">
        <xs:annotation>
            <xs:documentation>UDM Provisioned Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="dataIdentifier" type="subscriber:ProvisionedDataIdentifier" minOccurs="0"/>
                    <xs:element name="accessAndMobilitySubscriptionData" type="subscriber:UdmAccessAndMobilitySubscriptionData" minOccurs="0"/>
                    <xs:element name="smfSelectionSubscriptionData" type="subscriber:UdmSmfSelectionSubscriptionData" minOccurs="0"/>
					<xs:element name="sessionManagementSubscriptionData" type="subscriber:UdmIndividualSessionManagementSubscriptionData" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="smsSubscriptionData" type="subscriber:UdmSmsSubscriptionData" minOccurs="0"/>
					<xs:element name="smsManagementSubscriptionData" type="subscriber:UdmSmsManagementSubscriptionData" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
	<xs:simpleType name="ProvisionedDataIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="provisioned-data"/>
        </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmAccessAndMobilitySubscriptionData">
        <xs:annotation>
            <xs:documentation>UDM Access and Mobility Subscription Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="genPublicSubscriptionIds" type="subscriber:UdmGpsi" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="internalGroupIds" type="subscriber:IntGroupIds" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="accessRestr" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="coreNetworkTypeRestriction" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="rfspIndex" type="subscriber:NumericString" minOccurs="0"/>
					<xs:element name="micoAllowed" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="subsRegTimer" type="subscriber:NumericString" minOccurs="0"/>
                    <xs:element name="ueUsageType" type="subscriber:NumericString" minOccurs="0"/>
                    <xs:element name="localAreaDataNtwInfo" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="mpsPriority" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="activeTime" type="subscriber:NumericString" minOccurs="0"/>
					<xs:element name="downLinkPktCount" type="xsd:integer" minOccurs="0"/>
					<xs:element name="nssai" type="subscriber:UdmNssai" minOccurs="0"/>
					<xs:element name="serviceAreaRestriction" type="subscriber:UdmServiceAreaRestriction" minOccurs="0"/>
					<xs:element name="forbiddenAreas" type="subscriber:UdmForbiddenAreas" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="mcsPriority" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="odbPacketServices" type="xsd:string" minOccurs="0"/>
					<xs:element name="subscribedDnnList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="ueAmbr" type="subscriber:UdmSubscribedUeAmbr" minOccurs="0"/>
                    <xs:element name="primaryRatRestrictions" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="secondaryRatRestrictions" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="nssaiInclusionAllowed" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="sharedAmDataIds" type="xsd:string" minOccurs="0"/>
		            <xs:element name="iabOperationAllowed" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="upuInfo" type="subscriber:UdmUpuInfo" minOccurs="0"/>
                    <xs:element name="sorInfo" type="subscriber:UdmSorInfo" minOccurs="0"/>
                    <xs:element name="sorInfoExpectInd" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="sorafRetrieval" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="sorUpdateIndicatorList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

	<xs:complexType name="UdmSubscribedUeAmbr">
      <xs:annotation>
        <xs:documentation>UDM Subscribed UeAmbr</xs:documentation>
      </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="ueAmbrUpLink" type="subscriber:UeAmbrUpLinkDownLink" minOccurs="0"/>
					<xs:element name="ueAmbrDownLink" type="subscriber:UeAmbrUpLinkDownLink" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmServiceAreaRestriction">
        <xs:annotation>
            <xs:documentation>UDM Service Area Restriction</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="restrictionType" type="xsd:string" minOccurs="0"/>
					<xs:element name="maxNumOfTAs" type="xsd:integer" minOccurs="0"/>
					<xs:element name="areas" type="subscriber:UdmAreas" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="maxNumOfTAsForNotAllowedAreas" type="xsd:integer" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmAreas">
        <xs:annotation>
            <xs:documentation>UDM Area</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="areaId" type="xsd:integer" minOccurs="0"/>
                    <xs:element name="tacs" type="subscriber:Tacs" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="areaCodes" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmForbiddenAreas">
        <xs:annotation>
            <xs:documentation>Udm Forbidden Areas</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="forbiddenAreasId" type="xsd:integer" minOccurs="0"/>
                    <xs:element name="tacs" type="subscriber:Tacs" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="areaCodes" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="UpLinkDownLinkUnits">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="bps"/>
			<xs:enumeration value="Kbps"/>
			<xs:enumeration value="Mbps"/>
			<xs:enumeration value="Gbps"/>
			<xs:enumeration value="Tbps"/>
        </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmNssai">
        <xs:annotation>
            <xs:documentation>UDM Network Slice Selection Assistance Information</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
					<xs:element name="defaultSingleNssais" type="subscriber:UdmSingleNssai" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="singleNssais" type="subscriber:UdmSingleNssai" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xsd:simpleType name="UdmSingleNssai">
        <xsd:annotation>
            <xsd:documentation>SingleNssais and defaultSingleNssais
                Examples:
                55-adeeee
                123
                255-Ab12ef
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="([0-9]|[1-9][0-9]|1[0-9][0-9]|2([0-4][0-9]|5[0-5]))(-[A-Fa-f0-9]{6})"/>
            <xsd:pattern value="([0-9]|[1-9][0-9]|1[0-9][0-9]|2([0-4][0-9]|5[0-5]))"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xs:complexType name="UdmUpuInfo">
        <xs:annotation>
            <xs:documentation>UDM UE Parameter Update info </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="upuData" type="subscriber:UdmUpuData" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="upuRegInd" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="upuAckInd" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="provisioningTime" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmUpuData">
        <xs:annotation>
            <xs:documentation>UDM UE Parameter Update data </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="upuDataId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="routingId" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmSorInfo">
        <xs:annotation>
            <xs:documentation>The information for Steering of Roaming of the subscriber</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="ackInd" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="steeringInfo" type="subscriber:UdmSteeringInfo" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="securedPacket" type="xsd:string" minOccurs="0"/>
                    <xs:element name="provisioningTime" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UdmSteeringInfo">
        <xs:annotation>
            <xs:documentation>The list of PLMN/AccessTechnologies combinations</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="plmnId" type="xsd:string" minOccurs="0"/>
                    <xs:element name="accessTechList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSmfSelectionSubscriptionData">
        <xs:annotation>
            <xs:documentation>UDM SMF Selection Subscription Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                  <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
					        <xs:element name="sNssaiInfo" type="subscriber:UdmSNssaiInfo" minOccurs="0" maxOccurs="unbounded"/>
                  <xs:element name="sharedSnssaiInfosId" type="subscriber:SharedDataId" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSNssaiInfo">
        <xs:annotation>
            <xs:documentation>NSSAI Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="nssaiId" type="subscriber:UdmSingleNssai" minOccurs="0"/>
					<xs:element name="dnnInfo" type="subscriber:UdmDnnInfo" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmDnnInfo">
        <xs:annotation>
            <xs:documentation>DNN Info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="dnnId" type="subscriber:PrintableStringAPN255" minOccurs="0"/>
					<xs:element name="defaultIndication" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="localAreaDataNtwIndicator" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="interworkingEPSIndication" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="localBrkOutRoamingAllowed" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="dnnBarred" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="nokiaDnnInfoAttributes" type="subscriber:UdmNokiaDnnInfoAttributes" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmNokiaDnnInfoAttributes">
        <xs:annotation>
            <xs:documentation>Include all custom attributes within DNN info</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="supportedStaticSmfAddress" type="subscriber:PrintableString255" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmIndividualSessionManagementSubscriptionData">
        <xs:annotation>
            <xs:documentation>UDM Session Management Subscription Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="singleNssai" type="subscriber:UdmSingleNssai" minOccurs="0"/>
					<xs:element name="dnnConfiguration" type="subscriber:UdmDnnConfiguration" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="internalGroupIds" type="subscriber:IntGroupIds" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="odbPacketServices" type="xsd:string" minOccurs="0"/>
					<xs:element name="sharedDnnConfigurationsId" type="subscriber:SharedDataId" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmDnnConfiguration">
        <xs:annotation>
            <xs:documentation>UDM DNN Configuration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="dnnId" type="subscriber:PrintableStringAPN255" minOccurs="0"/>
					<xs:element name="interworkingEPSIndication" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="udm3gppChargingCharacteristics" type="subscriber:UdmHexadecimalString" minOccurs="0"/>
					<xs:element name="sessionAmbr" type="subscriber:SessionAmbrUpLinkDownLink" minOccurs="0"/>
					<xs:element name="staticIpAddress" type="subscriber:IpAddress" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="udm5gQosProfile" type="subscriber:Udm5gQosProfile" minOccurs="0"/>
					<xs:element name="upSecurity" type="subscriber:upIntegrConfId" minOccurs="0"/>
					<xs:element name="pduSessionTypes" type="subscriber:UdmPduSessionTypes" minOccurs="0"/>
					<xs:element name="sscModes" type="subscriber:UdmSscModes" minOccurs="0"/>
					<xs:element name="vzDnnConf" type="subscriber:UdmVzDnnConf" minOccurs="0"/>
					<xs:element name="ipv6FrameRouteList" type="subscriber:ipv6Prefix" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmPduSessionTypes">
        <xs:annotation>
            <xs:documentation>UDM Pdu SessionTypes</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="defaultSessionType" type="xsd:string" minOccurs="0"/>
					<xs:element name="allowedSessionType" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>	

<xs:complexType name="UdmSscModes">
        <xs:annotation>
            <xs:documentation>UDM ssc Modes</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="defaultSscMode" type="xsd:string" minOccurs="0"/>
					<xs:element name="allowedSscMode" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="Udm5gQosProfile">
        <xs:annotation>
            <xs:documentation>UDM 5G QoS Profile</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="udm5Qi" type="xsd:integer" minOccurs="0"/>
					<xs:element name="arp" type="subscriber:PriorityPreemption" minOccurs="0"/>
					<xs:element name="priorityLevel" type="xsd:integer" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmVzDnnConf">
        <xs:annotation>
            <xs:documentation>UDM Verizon Dnn Configuration</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="framedIpv4PoolName" type="subscriber:PrintableString128" minOccurs="0"/>
					<xs:element name="framedIpv6PoolName" type="subscriber:PrintableString128" minOccurs="0"/>
					<xs:element name="virtualApnName" type="subscriber:PrintableString255" minOccurs="0"/>
					<xs:element name="flexibleServiceField" type="subscriber:PrintableString255" minOccurs="0"/>
					<xs:element name="dnAaaList" type="subscriber:PrintableString255" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="subscriberPermission" type="subscriber:PrintableString255" minOccurs="0"/>
					<xs:element name="idleTimeout" type="xsd:unsignedInt" minOccurs="0"/>
					<xs:element name="sessionTimeout" type="xsd:unsignedInt" minOccurs="0"/>
					<xs:element name="dnnPcscfAddress" type="subscriber:PrintableString255" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSmsSubscriptionData">
        <xs:annotation>
            <xs:documentation>UDM Sms Subscription Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="smsSubscribed" type="xsd:boolean" minOccurs="0"/>
				    <xs:element name="sharedSmsSubsDataId" type="subscriber:SharedDataId" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmSmsManagementSubscriptionData">
        <xs:annotation>
            <xs:documentation>UDM Sms Management Subscription Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
				    <xs:element name="supportedFeatures" type="subscriber:HexadecimalString" minOccurs="0"/>
                    <xs:element name="mtSmsSubscribed" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="mtSmsBarringAll" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="mtSmsBarringRoaming" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="moSmsSubscribed" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="moSmsBarringAll" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="moSmsbarringRoaming" type="xsd:boolean" minOccurs="0"/>
			    <xs:element name="sharedSmsMngDataIds" type="xsd:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

    <!--************************************************************************-->
    <!--                        UDM 5G  Simple Types                            -->
    <!--************************************************************************-->
	<xsd:simpleType name="Tacs">
        <xsd:annotation>
            <xsd:documentation>Tacs 
                Examples:
                abc1/abcd12
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Fa-f0-9]{4}"/>
			<xsd:pattern value="[A-Fa-f0-9]{6}"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="IntGroupIds">
        <xsd:annotation>
            <xsd:documentation>IntGroupIds
                Example:  123456Aa-123-123-1122334455667788BBaa
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Fa-f0-9]{8}-[0-9]{3}-[0-9]{2,3}-([A-Fa-f0-9][A-Fa-f0-9]){1,10}"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="SharedDataId">
        <xsd:annotation>
            <xsd:documentation>SharedDataId
                Example:  12345-aaaaaaaaaaaaaaaaa
                Example:  123456-abcdefghijklmnop
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[0-9]{5,6}-.+"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="UeAmbrUpLinkDownLink">
        <xsd:annotation>
            <xsd:documentation>for ueAmbrUpLink and ueAmbrDownLink there will a value along with unit. 123 Kbps</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="\d+(\.\d+)? (bps|Kbps|Mbps|Gbps|Tbps)"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="SessionAmbrUpLinkDownLink">
        <xsd:annotation>
        <xsd:documentation>SessionAmbrUpLinkDownLink
                Example:  12 Kbps|13 Mbps
                Example:  129 bps|135 Gbps
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="(\d+(\.\d+)? (bps|Kbps|Mbps|Gbps|Tbps))\|(\d+(\.\d+)? (bps|Kbps|Mbps|Gbps|Tbps))"/>
        </xsd:restriction>
    </xsd:simpleType>

<xsd:simpleType name="IpAddress">
        <xsd:annotation>
            <xsd:documentation>IpAddress
                Example:  ************||
                Example:  |2001:db8:85a3::8a2e:370:7334|
                Example:  ||2001:db8:abcd:12::0/64
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\|\|"/>
            <xsd:pattern value="\|((:|(0?|([1-9a-f][0-9a-f]{0,3}))):)((0?|([1-9a-f][0-9a-f]{0,3})):){0,6}(:|(0?|([1-9a-f][0-9a-f]{0,3})))\|"/>
            <xsd:pattern value="\|((([^:]+:){7}([^:]+))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?))\|"/>
            <xsd:pattern value="\|\|((([^:]+:){7}([^:]+))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?))(\/.+)"/>
            <xsd:pattern value="\|\|((:|(0?|([1-9a-f][0-9a-f]{0,3}))):)((0?|([1-9a-f][0-9a-f]{0,3})):){0,6}(:|(0?|([1-9a-f][0-9a-f]{0,3})))(\/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="upIntegrConfId">
        <xsd:annotation>
            <xsd:documentation>upIntegrConfId
                Example:  11|12
                Example:  abcd|defg
                Example:  REQUIRED|PREFERRED
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value=".+\|.+"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="PriorityPreemption">
        <xsd:annotation>
        <xsd:documentation>PriorityPreemption
                Example:  15|aaaa|aaaaaaaasdd
                Example:  1|aaaa|aaaaaaaasdd
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="([1-9]|1[0-5])\|.+\|.+"/>       
        </xsd:restriction>
    </xsd:simpleType>

	<xsd:simpleType name="UdmGpsi">
        <xsd:annotation>
        <xsd:documentation>Gpsi
                Example:  msisdn-12345555
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="msisdn-[0-9]{5,15}"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="ipv6Prefix">
        <xsd:annotation>
            <xsd:documentation>ipv6Prefix
                Example:  2001:db8:abcd:12::0/64
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="((:|(0?|([1-9a-f][0-9a-f]{0,3}))):)((0?|([1-9a-f][0-9a-f]{0,3})):){0,6}(:|(0?|([1-9a-f][0-9a-f]{0,3})))(\/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))"/>
            <xsd:pattern value="((([^:]+:){7}([^:]+))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?))(\/.+)"/>
        </xsd:restriction>
    </xsd:simpleType>

	<xs:complexType name="UdmNokiaUdmCustomData">
        <xs:annotation>
            <xs:documentation>Nokia Udm Custom Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="customDataIdentifier" type="subscriber:CustomDataIdentifier" minOccurs="0"/>
					<xs:element name="eeAmfSubsIdMapping" type="subscriber:UdmEeAmfSubsIdMapping" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:complexType name="UdmOdbData">
        <xs:annotation>
            <xs:documentation>ODB Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="dataIdentifier" type="subscriber:ODBDataIdentifier" minOccurs="0"/>
					<xs:element name="RoamingOdb" type="xsd:string" minOccurs="0"/>
				</xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="ODBDataIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="operator-determined-barring-data"/>
        </xs:restriction>
    </xs:simpleType>
		
	<xs:complexType name="UdmOperatorSpecificData">
        <xs:annotation>
            <xs:documentation>OperatorSpecificData Data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="dataIdentifier" type="subscriber:OperatorSpecificDataIdentifier" minOccurs="0"/>
					<xs:element name="ODderegistration" type="xsd:boolean" minOccurs="0"/>
				</xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="OperatorSpecificDataIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="operator-specific-data"/>
        </xs:restriction>
    </xs:simpleType>

	<xs:simpleType name="CustomDataIdentifier">
        <xs:restriction base="xsd:string">
            <xs:enumeration value="nokia-udm-custom-data"/>
        </xs:restriction>
    </xs:simpleType>
	<xs:complexType name="UdmEeAmfSubsIdMapping">
            <xs:annotation>
                <xs:documentation>Udm EeAmfSubsId Mapping</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
						<xs:element name="amfsubscriptionIdMappingType" type="subscriber:AmfsubscriptionIdMappingType" minOccurs="0"/>
    				   <xs:element name="individualEeAmfSubsId" type="subscriber:UdmIndividualEeAmfSubsId" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>

	<xs:simpleType name="AmfsubscriptionIdMappingType">
	        <xs:restriction base="xsd:string">
	            <xs:enumeration value="ee-amf-subsId-mapping"/>
	        </xs:restriction>
    </xs:simpleType>

	<xs:complexType name="UdmIndividualEeAmfSubsId">
            <xs:annotation>
                <xs:documentation>UDM Individual SDM Registration</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="spml:SecondClassObject">
                    <xs:sequence>
						<xs:element name="eeAmfSubsId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="ngpAmfCorrelationId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="amfCorrelationId" type="xsd:string" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
    </xs:complexType>
	
	<!-- migration start -->	
	<xs:complexType name="Migration5gData">
		<xs:annotation>
			<xs:documentation> Defines 5G subscriber migration firstclass object </xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:FirstClassObject">
				<xs:sequence>
					<xs:element name="imsi" type="xsd:string" minOccurs="0"/>
					<xs:element name="msisdn" type="xsd:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	
	<xs:complexType name="Migration5gDataOperation">
		<xs:annotation>
			<xs:documentation> Defines 5G subscriber migration extended operation. </xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="spml:AbstractOperation">
				<xs:sequence>
					<xs:element name="identifier" type="xsd:string" minOccurs="0"/>
					<xs:element name="imsi" type="xsd:string" minOccurs="0"/>
					<xs:element name="msisdn" type="xsd:string" minOccurs="0"/>
					<xsd:element name="aliasIdType" type="xsd:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>	
	<!-- migration end -->
	
	 <!--************************************************************************-->
    <!--                         EIR Application Service                        -->
    <!--                  Contribution to Subscriber schema                     -->
    <!--************************************************************************-->
    
	<!--************************************************************************-->
    <!--                                     EIR aliases                     -->
    <!--************************************************************************--> 
   
   <xs:simpleType name="EIRIdentifierAliasType">
        <xs:restriction base="xsd:string">
	    <xs:enumeration value="imsi"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="EIRIdentifier">
        <xs:simpleContent>
            <xs:restriction base="spml:Identifier">
                <xs:attribute name="alias" type="subscriber:EIRIdentifierAliasType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="EIRIdentifierFileNameType">
        <xs:simpleContent>
            <xs:restriction base="spml:IdentifierFileNameType">
                <xs:attribute name="alias" type="subscriber:EIRIdentifierAliasType" use="required"/>
                <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>       
    <xs:complexType name="EIRAliasType">
        <xs:complexContent>
            <xs:restriction base="spml:AliasType">
                <xs:attribute name="name" type="subscriber:EIRIdentifierAliasType" use="required"/>
                <xs:attribute name="value" type="xsd:string" use="required"/>
            </xs:restriction>
        </xs:complexContent>
    </xs:complexType>
	
    <!--************************************************************************-->
    <!--           EIR AS SCO                                                   -->
    <!--************************************************************************-->
    
    <xsd:complexType name="EIR">
        <xsd:annotation>
            <xsd:documentation>EIR Application Service of the Subscriber</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
		    <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="fastBlackList" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="lockedImsiImei" type="xsd:boolean" minOccurs="0"/>                    
                    <xsd:element name="dualImsi" type="xsd:boolean" minOccurs="0"/>                    
                    <xs:element name="imeiHistory" type="subscriber:ImeiHistory" minOccurs="0" maxOccurs="100"/>
                    <xs:element name="deviceSetReference" type="subscriber:PrintableString32" minOccurs="0"/>
                    <xs:element name="newDeviceSetReference" type="subscriber:PrintableString32" minOccurs="0"/>
                    <xs:element name="newDeviceSetRefActTime" type="xsd:dateTime" minOccurs="0"/>
		    <xsd:element name="imsi" type="xsd:string" minOccurs="0"/>
		    <xsd:element name="eirDeviceCharacteristicsScreening" type="xsd:boolean" minOccurs="0"/>
		    <xs:element name="EIRDeviceCharacteristics" type="subscriber:EIRDeviceCharacteristics" minOccurs="0"/>
                    <xs:element name="eirAllowConfiguredImei" type="xsd:boolean" minOccurs="0"/>
		    <xs:element name="eirAllowedIMEI" type="subscriber:PrintableString14" minOccurs="0" maxOccurs="100"/>
                    <xs:element name="eirSSHTRLargePrefixes" type="xsd:boolean" minOccurs="0"/>
                    <xs:element name="allowedChar" type="subscriber:PrintableString128" minOccurs="0" maxOccurs="100"/>
                    <xs:element name="notAllowedChar" type="subscriber:PrintableString128" minOccurs="0" maxOccurs="100"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
	<xs:complexType name="EIRDeviceCharacteristics">
        <xs:annotation>
            <xs:documentation>EIRDeviceCharacteristics</xs:documentation>
        </xs:annotation>
        <xs:complexContent>           
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
					<xs:element name="allowedDeviceManufacturer" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceManufacturer" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceModel" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceModel" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="allowedDeviceBands" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceBands" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceScreenResolution" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="blockedDeviceScreenResolution" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceTechnologies" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceTechnologies" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="allowedDeviceDesignationType" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceDesignationType" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedBeforedeviceAllocationDate" type="xsd:dateTime" minOccurs="0"/> 
					<xs:element name="blockedAfterdeviceAllocationDate" type="xsd:dateTime" minOccurs="0"/>
					<xs:element name="allowedDeviceRadioInterface" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceRadioInterface" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="allowedDeviceCountryCode" type="subscriber:eirNumericString3" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceCountryCode" type="subscriber:eirNumericString3" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceFixedCode" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="blockedDeviceFixedCode" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceManufacturerCode" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceManufacturerCode" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="allowedDeviceBrandName" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceBrandName" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceOperatingSystem" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="blockedDeviceOperatingSystem" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="allowedDeviceMarketingName" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceMarketingName" type="xsd:string" minOccurs="0" maxOccurs="100"/> 
					<xs:element name="allowDeviceNFC" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="allowDeviceBluetooth" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="allowDeviceWLAN" type="xsd:boolean" minOccurs="0"/>
					<xs:element name="allowedDeviceType" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="blockedDeviceType" type="xsd:string" minOccurs="0" maxOccurs="100"/>
					<xs:element name="groupingPolicy" type="subscriber:EirPolicyType" minOccurs="0"/> 
              </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
       
    <xsd:complexType name="ImeiHistory">
        <xsd:annotation>
            <xsd:documentation>IMEI History</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="timestamp" type="xsd:dateTime" minOccurs="0"/>
                    <xsd:element name="imei" type="subscriber:IMEI" minOccurs="0"/>                    
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
        

	<xsd:simpleType name="eirNumericString3">
        <xsd:annotation>
            <xsd:documentation>Numeric string with maximum 3 digits</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="subscriber:NumericString">
            <xsd:maxLength value="3"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<xsd:simpleType name="OctetString">
		<xsd:annotation>
			<xsd:documentation>Only octet digits allowed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9A-Fa-f]+"/>
		</xsd:restriction>
	</xsd:simpleType>
    
    <xsd:simpleType name="IMEI">
        <xsd:annotation>
            <xsd:documentation>IMEI</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="subscriber:OctetString">
            <xsd:minLength value="14"/>
            <xsd:maxLength value="16"/>
        </xsd:restriction>
    </xsd:simpleType>
    
     
	<xs:simpleType name="EirPolicyType">
    <xs:restriction base="xsd:string">
        <xs:enumeration value="and"/>
        <xs:enumeration value="or"/>
    </xs:restriction>
	</xs:simpleType>
	
<xsd:simpleType name="PrintableString14">
        <xsd:annotation>
            <xsd:documentation>String of length 1 .. 14</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="subscriber:PrintableString">
            <xsd:maxLength value="14"/>
        </xsd:restriction>
    </xsd:simpleType>
	
	<!--************************************************************************-->
    <!--           PCF AS SCO                                                   -->
    <!--************************************************************************-->
	
	<xs:complexType name="PCF">
		<annotation>
			<documentation>Policy Control Function Service Data</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="policySubscriptionId" type="xs:int" minOccurs="0"/>
					<element name="imsi" type="subscriber:IMSI" minOccurs="0" maxOccurs="unbounded"/>
					<element name="msisdn" type="subscriber:MSISDN" minOccurs="0" maxOccurs="unbounded"/>
					<element name="smPolicyData" type="subscriber:SmPolicyData" minOccurs="0"/>
					<element name="operatorSpecificData" type="subscriber:OperatorSpecificData" minOccurs="0" maxOccurs="unbounded"/>
					<element name="amPolicyData" type="subscriber:AmPolicyData" minOccurs="0"/>
					<element name="uePolicySet" type="subscriber:UePolicySet" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>

    <xs:simpleType name="MSISDN">
		<xs:annotation>
			<xs:documentation>Only digits allowed</xs:documentation>
		</xs:annotation>
		<xs:restriction base="subscriber:NumericString">
			<xs:minLength value="3"/>
			<xs:maxLength value="15"/>
		</xs:restriction>
    </xs:simpleType>
	
	<xs:complexType name="SmPolicyData">
		<annotation>
			<documentation>SM Policy Data</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="smPolicySnssaiData" type="subscriber:SmPolicySnssaiData" minOccurs="0" maxOccurs="unbounded"/>
					<element name="umDataLimits" type="subscriber:UsageMonDataLimit" minOccurs="0" maxOccurs="unbounded"/>
					<element name="umData" type="subscriber:UsageMonData" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	
	<xs:complexType name="AmPolicyData">
		<annotation>
			<documentation>AM Policy Data</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="praInfos" type="subscriber:PresenceInfo" minOccurs="0" maxOccurs="unbounded"/>
					<element name="subscCats" type="xs:string" minOccurs="0"  maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="UePolicySet">
		<annotation>
			<documentation>UE Policy Set data</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="praInfos" type="subscriber:PresenceInfo" minOccurs="0" maxOccurs="unbounded"/>
					<element name="subscCats" type="xs:string" minOccurs="0"  maxOccurs="unbounded"/>
					<element name="uePolicySections" type="subscriber:UePolicySection" minOccurs="0" maxOccurs="unbounded"/>
					<element name="upsis" type="xs:string" minOccurs="0"  maxOccurs="unbounded"/>
					<element name="allowedRouteSelDescs" type="subscriber:PlmnRouteSelectionDescriptor" minOccurs="0" maxOccurs="unbounded"/>
					<element name="andspInd" type="xs:boolean" minOccurs="0"/>
					<element name="pei" type="subscriber:Pei" minOccurs="0"/>
					<element name="osIds" type="subscriber:OsId" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="OsId">
			<annotation>
				<documentation>Operating System supported by the UE.</documentation>
			</annotation>
			<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="Pei">
		<annotation>
			<documentation>Permanent Equipment Identifier</documentation>
		</annotation>
		<restriction base="xs:string">
			<!-- TODO enhance the pattern
			<pattern value="imei-[0-9]{15}"/>
			<pattern value="imeisv-[0-9]{16}"/>
			<pattern value=".+"/>
			-->
			
		</restriction>
	</xs:simpleType>
	<xs:complexType name="PlmnRouteSelectionDescriptor">
		<annotation>
			<documentation>PLMN Route selection descriptor</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="servingPlmn" type="subscriber:PlmnId" minOccurs="0"/>
					<element name="servingPlmnKey" type="xs:string" minOccurs="0"/>
					<element name="snssaiRouteSelDescs" type="subscriber:SnssaiRouteSelectionDescriptor" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="SnssaiRouteSelectionDescriptor">
		<annotation>
			<documentation>Contains the route selection descriptor parameters (DNNs, PDU session types and SSC modes) per SNSSAI.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="snssaiKey" type="xs:string" minOccurs="0"/>
					<element name="snssai" type="subscriber:Snssai" minOccurs="0"/>
					<element name="dnnRouteSelDescs" type="subscriber:DnnRouteSelectionDescriptor" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="DnnRouteSelectionDescriptor">
		<annotation>
			<documentation>Contains the route selection descriptor parameters (PDU session types and SSC modes) per DNN.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="dnn" type="subscriber:Dnn" minOccurs="0"/>
					<element name="sscModes" type="subscriber:SscMode" minOccurs="0" maxOccurs="unbounded"/>
					<element name="pduSessTypes" type="subscriber:PduSessionType" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	
	<xs:simpleType name="SscMode">
		<annotation>
			<documentation>The enumeration SscMode represents the service and session continuity mode.</documentation>
		</annotation>
		<restriction base="xsd:string">
			<enumeration value="SSC_MODE_1"/>
			<enumeration value="SSC_MODE_2"/>
			<enumeration value="SSC_MODE_3"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="PduSessionType">
		<annotation>
			<documentation>The enumeration PduSessionType indicates the type of a PDU session.</documentation>
		</annotation>
		<restriction base="xsd:string">
			<enumeration value="IPV4"/>
			<enumeration value="IPV6"/>
			<enumeration value="IPV4V6"/>
			<enumeration value="UNSTRUCTURED"/>
			<enumeration value="ETHERNET"/>
		</restriction>
	</xs:simpleType>
	
	<xs:complexType name="UePolicySection">
		<annotation>
			<documentation>UE Policy Section Data</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="upsi" type="xs:string" minOccurs="0"/>
					<element name="uePolicySectionInfo" type="xs:string" minOccurs="0"/>

				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	
	<xs:complexType name="OperatorSpecificData">
			<annotation>
				<documentation>Operator Specific Data</documentation>
			</annotation>
			<complexContent>
				<extension base="spml:SecondClassObject">
					<sequence>
						<element name="name" type="xs:string" minOccurs="0"/>
						<element name="dataType" type="subscriber:OSDDataType" minOccurs="0" />
						<element name="value" type="xs:string" minOccurs="0"/>
						<element name="dataTypeDefinition" type="xs:string" minOccurs="0"/>
					</sequence>
				</extension>
			</complexContent>
	</xs:complexType>
	
	<xs:simpleType name="OSDDataType">
		<annotation>
			<documentation>Name of the actual data type of the "value" attribute.</documentation>
		</annotation>
		<restriction base="xsd:string">
			<enumeration value="string"/>
			<enumeration value="integer"/>
			<enumeration value="number"/>
			<enumeration value="boolean"/>
			<enumeration value="object"/>
		</restriction>
	</xs:simpleType>
	<xs:complexType name="SmPolicySnssaiData">
		<annotation>
			<documentation>SmPolicySnssaiData</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="snssai" type="subscriber:Snssai" minOccurs="0"/>
					<element name="snssaiKey" type="xs:string" minOccurs="0"/>
					<element name="smPolicyDnnData" type="subscriber:SmPolicyDnnData" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="Snssai">
		<annotation>
			<documentation>Single Network Slice Selection Assistance Information(S-NSSAI) associated with the data.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="sst" type="subscriber:Ssttype" minOccurs="0"/>
					<element name="sd" type="subscriber:Sdtype" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="Sdtype">
		<annotation>
			<documentation>3-octet string, representing the Slice Differentiator, in hexadecimal representation. Each character in the string shall take a value of "0" to "9" or "A" to "F" and shall represent 4 bits. The most significant character representing the 4 most significant bits of the SD shall appear first in the string, and the character representing the 4 least significant bit of the SD shall appear last in the string.
			This is an optional parameter that complements the Slice/Service type(s) to allow to differentiate amongst multiple Network Slices of the same Slice/Service type.
			</documentation>
		</annotation>
		<restriction base="xs:string">
			<pattern value="[A-Fa-f0-9]{6}"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="Ssttype">
		<annotation>
			<documentation>Unsigned integer, within the range 0 to 255,Unsigned integer, within the range 0 to 255, representing the Slice/Service Type. It indicates the expected Network Slice behaviour in terms of features and services.Values 0 to 127 correspond to the standardized SST range. Values 128 to 255 correspond to the Operator-specific range</documentation>
		</annotation>
		<restriction base="xs:int">
			<minInclusive value="0"/>
			<maxInclusive value="255"/>
		</restriction>
	</xs:simpleType>
	<xs:complexType name="SmPolicyDnnData">
		<annotation>
			<documentation>Session Management Policy data per DNN for all the DNNs of the indicated S-NSSAI.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="dnn" type="subscriber:Dnn" minOccurs="0"/>
					<element name="allowedServices" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
					<element name="subscCats" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
					<element name="gbrUI" type="subscriber:BitRate" minOccurs="0"/>
					<element name="gbrDl" type="subscriber:BitRate" minOccurs="0"/>
					<element name="adcSupport" type="xs:boolean" minOccurs="0"/>
					<element name="subscSpendingLimits" type="xs:boolean" minOccurs="0"/>
					<element name="ipv4Index" type="subscriber:IpIndex" minOccurs="0"/>
					<element name="ipv6Index" type="subscriber:IpIndex" minOccurs="0"/>
					<element name="offline" type="xs:boolean" minOccurs="0"/>
					<element name="online" type="xs:boolean" minOccurs="0"/>
					<element name="chfInfo" type="subscriber:ChargingInformation" minOccurs="0"/>
					<element name="refUmdLimitIds" type="subscriber:LimitIdToMonitoringKey" minOccurs="0" maxOccurs="unbounded"/>
					<element name="mpsPriority" type="xs:boolean" minOccurs="0"/>
					<element name="imsSignallingPrio" type="xs:boolean" minOccurs="0"/>
					<element name="mpsPriorityLevel" type="xs:int" minOccurs="0"/>
					<element name="praInfos" type="subscriber:PresenceInfo" minOccurs="0" maxOccurs="unbounded"/>
					<element name="vendorSpecific-012951" type="subscriber:VzPolicyItem" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="Dnn">
		<annotation>
			<documentation>String representing a Data Network as defined in clause 9A of 3GPP TS 23.003</documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="BitRate">
		<annotation>
			<documentation>String representing a bit rate. Examples:"125 Mbps", "0.125 Gbps", "125000 Kbps"</documentation>
		</annotation>
		<restriction base="xs:string">
			<pattern value="\d+(\.\d+)? (bps|Kbps|Mbps|Gbps|Tbps)"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="IpIndex">
		<annotation>
			<documentation>Information that identifies which IP pool or external server is used to allocate the IP address.</documentation>
		</annotation>
		<restriction base="xs:int"/>
	</xs:simpleType>
	<xs:complexType name="ChargingInformation">
		<annotation>
			<documentation>The address of the Charging Function.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="primaryChfAddress" type="subscriber:Uri" minOccurs="0"/>
					<element name="secondaryChfAddress" type="subscriber:Uri" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="Uri">
		<annotation>
			<documentation>String providing an URI formatted according to IETF RFC 3986</documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="LimitIdToMonitoringKey">
		<annotation>
			<documentation>A reference to the "UsageMonitoringDataLimit" or "UsageMonitoringData" instances for this DNN and SNSSAI that may also include the related monitoring key(s)</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="limitId" type="xs:string" minOccurs="0"/>
					<element name="monKeys" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="PresenceInfo">
		<annotation>
			<documentation>Presence reporting area information. Each PresenceInfo element shall include the Presence Reporting Area Identifier within the "praId" attribute and, for a UE-dedicated presence reporting area, may also include the list of elements composing the presence reporting area.A "praId" may indicate a Presence Reporting Area Set.The "praId" attribute within the PresenceInfo data type shall also be the key of the map.The attribute "presenceState" shall not be present.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="praId" type="xs:string" minOccurs="0"/>
					<element name="trackingAreaList" type="subscriber:Tai" minOccurs="0" maxOccurs="unbounded"/>
					<element name="ecgiList" type="subscriber:Ecgi" minOccurs="0" maxOccurs="unbounded"/>
					<element name="ncgiList" type="subscriber:Ncgi" minOccurs="0" maxOccurs="unbounded"/>
					<element name="globalRanNodeIdList" type="subscriber:GlobalRanNodeId" minOccurs="0" maxOccurs="unbounded"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	
	<xs:complexType name="Tai">
		<annotation>
			<documentation>Represents the list of tracking areas that constitutes the area. This IE shall be present if the subscription or the event report is for tracking UE presence in the tracking areas. For non 3GPP access the TAI shall be the N3GPP TAI.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="plmnIdKey" type="xs:string" minOccurs="0"/>
					<element name="plmnId" type="subscriber:PlmnId" minOccurs="0"/>
					<element name="tac" type="subscriber:Tac" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="PlmnId">
		<annotation>
			<documentation>PLMN Identity.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="mcc" type="subscriber:Mcc" minOccurs="0"/>
					<element name="mnc" type="subscriber:Mnc" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="Mcc">
		<annotation>
			<documentation>MCC Numeric String(len: 3)</documentation>
		</annotation>
		<restriction base="subscriber:NumericString">
			<minLength value="3"/>
			<maxLength value="3"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="Mnc">
		<annotation>
			<documentation>MNC Numeric String(len: 2..3)</documentation>
		</annotation>
		<restriction base="subscriber:NumericString">
			<minLength value="2"/>
			<maxLength value="3"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="Tac">
		<annotation>
			<documentation>2 or 3-octet string identifying a tracking area code as specified in clause 9.3.3.10 of 3GPP TS 38.413 [11], in hexadecimal representation. Each character in the string shall take a value of "0" to "9" or "A" to "F" and shall represent 4 bits. The most significant character representing the 4 most significant bits of the TAC shall appear first in the string, and the character representing the 4 least significant bit of the TAC shall appear last in the string.Examples:A legacy TAC 0x4305 shall be encoded as "4305".An extended TAC 0x63F84B shall be encoded as "63F84B"</documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="Ecgi">
		<annotation>
			<documentation>Represents the list of EUTRAN cell Ids that constitutes the area. This IE shall be present if the Area of Interest subscribed is a list of EUTRAN cell Ids.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="plmnIdKey" type="xs:string" minOccurs="0"/>	
					<element name="plmnId" type="subscriber:PlmnId" minOccurs="0"/>
					<element name="eutraCellId" type="subscriber:EutraCellId" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="EutraCellId">
		<annotation>
			<documentation>28-bit string identifying an E-UTRA Cell Id as specified in clause 9.3.1.9 of 3GPP TS 38.413 [11], in hexadecimal representation. Each character in the string shall take a value of "0" to "9" or "A" to "F" and shall represent 4 bits. The most significant character representing the 4 most significant bits of the Cell Id shall appear first in the string, and the character representing the 4 least significant bit of the Cell Id shall appear last in the string.Example:An E-UTRA Cell Id 0x5BD6007 shall be encoded as "5BD6007".</documentation>
		</annotation>
		<restriction base="xs:string">
			<pattern value="[A-Fa-f0-9]{7}"/>
		</restriction>
	</xs:simpleType>
	<xs:complexType name="Ncgi">
		<annotation>
			<documentation>Represents the list of NR cell Ids that constitutes the area. This IE shall be present if the Area of Interest subscribed is a list of NR cell Ids..</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="plmnIdKey" type="xs:string" minOccurs="0"/>
					<element name="plmnId" type="subscriber:PlmnId" minOccurs="0"/>
					<element name="nrCellId" type="subscriber:NrCellId" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="NrCellId">
		<annotation>
			<documentation>36-bit string identifying an NR Cell Id as specified in clause 9.3.1.7 of 3GPP TS 38.413 [11], in hexadecimal representation. Each character in the string shall take a value of "0" to "9" or "A" to "F" and shall represent 4 bits. The most significant character representing the 4 most significant bits of the Cell Id shall appear first in the string, and the character representing the 4 least significant bit of the Cell Id shall appear last in the string.Example:An NR Cell Id 0x225BD6007 shall be encoded as "225BD6007".</documentation>
		</annotation>
		<restriction base="xs:string">
			<pattern value="[A-Fa-f0-9]{9}"/>
		</restriction>
	</xs:simpleType>
	<xs:complexType name="GlobalRanNodeId">
		<annotation>
			<documentation>Represents the NG RAN node identifier that constitutes the area. This IE shall be present if the Area of Interest subscribed is a list of NG RAN node identifiers.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="plmnIdKey" type="xs:string" minOccurs="0"/>
					<element name="plmnId" type="subscriber:PlmnId" minOccurs="0"/>
					<choice>
						<sequence>
							<element name="n3IwfId" type="subscriber:N3IwfId" minOccurs="0"/>
							<element name="gNbId" type="subscriber:GNbId" minOccurs="0"/>
							<element name="ngeNbId" type="subscriber:NgeNbId" minOccurs="0"/>
						</sequence>
					</choice>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="N3IwfId">
		<annotation>
			<documentation>This IE shall be included if the RAN node belongs to non 3GPP access (i.e a N3IWF).</documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="GNbId">
		<annotation>
			<documentation>This IE shall be included if the RAN Node Id represents a gNB. When present, this IE shall contain the identifier of the gNB.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="bitLength" type="subscriber:BitLength" minOccurs="0"/>
					<element name="gNbValue" type="subscriber:GNbValue" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="BitLength">
		<annotation>
			<documentation>Unsigned integer representing the bit length of the gNB ID as defined in clause 9.3.1.6 of 3GPP TS 38.413 [11], within the range 22 to 32</documentation>
		</annotation>
		<restriction base="xs:int">
			<minInclusive value="22"/>
			<maxInclusive value="32"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="GNbValue">
		<annotation>
			<documentation>This represents the identifier of the gNB.The value of the gNB ID shall be encoded in hexadecimal representation. Each character in the string shall take a value of "0" to "9" or "A" to "F" and shall represent 4 bits. The padding 0 shall be added to make multiple nibbles, the most significant character representing the padding 0 if required together with the 4 most significant bits of the gNB ID shall appear first in the string, and the character representing the 4 least significant bit of the gNB ID shall appear last in the string.Examples: A 30 bit value "382A3F47" indicates a gNB ID with value 0x382A3F47.A 22 bit value "2A3F47" 				indicates a gNB ID with value 0x2A3F47.</documentation>
		</annotation>
		<restriction base="xs:string">
			<pattern value="[A-Fa-f0-9]{6,8}"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="NgeNbId">
		<annotation>
			<documentation>This IE shall be included if the RAN Node Id represents a NG-eNB. When present, this IE shall contain the identifier of an NG-eNB</documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="UsageMonDataLimit">
		<annotation>
			<documentation>Usage monitoring profile associated with the subscriber. The limit identifier is used as the key.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="limitId" type="xs:string" minOccurs="0"/>
					<element name="scopes" type="subscriber:UsageMonDataScope" minOccurs="0" maxOccurs="unbounded"/>
					<element name="umLevel" type="subscriber:UsageMonLevel" minOccurs="0"/>
					<element name="startDate" type="subscriber:DateTime" minOccurs="0"/>
					<element name="endDate" type="subscriber:DateTime" minOccurs="0"/>
					<element name="usageLimit" type="subscriber:UsageThreshold" minOccurs="0"/>
					<!-- commented as there is inconsistency in defining the type of this attribute between the 3GPP 29.519 description and YAML
					<element name="resetPeriod" type="subscriber:TimePeriod" minOccurs="0"/>
					-->
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="UsageMonData">
		<annotation>
			<documentation>Contains the remaining allowed usage data associated with the subscriber.The limit identifier is used as the key.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="limitId" type="xs:string" minOccurs="0"/>
					<element name="scopes" type="subscriber:UsageMonDataScope" minOccurs="0" maxOccurs="unbounded"/>
					<element name="umLevel" type="subscriber:UsageMonLevel" minOccurs="0"/>
					<element name="allowedUsage" type="subscriber:UsageThreshold" minOccurs="0"/>
					<!-- commented as there is inconsistency in defining the type of this attribute between the 3GPP 29.519 description and YAML
					<element name="resetTime" type="subscriber:DateTime" minOccurs="0"/>
					-->
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="VzPolicyItem">
			<annotation>
				<documentation>Object class representing Verizon-specific policy data</documentation>
			</annotation>
			<complexContent>
				<extension base="spml:SecondClassObject">
					<sequence>
						<element name="vzPolicyItem" type="subscriber:VzPolicyItemId" minOccurs="0" maxOccurs="unbounded"/>
					</sequence>
				</extension>
			</complexContent>
	</xs:complexType>
		<xs:complexType name="VzPolicyItemId">
				<annotation>
					<documentation>Object class representing Verizon-specific policy data items (blobs)</documentation>
				</annotation>
				<complexContent>
					<extension base="spml:SecondClassObject">
						<sequence>
							<element name="vzPolicyItemId" type="xs:string" minOccurs="0"/>
							<element name="blobValue" type="xs:string" minOccurs="0"/>
						</sequence>
					</extension>
				</complexContent>
	</xs:complexType>
	<xs:complexType name="UsageMonDataScope">
		<annotation>
			<documentation>Identifies the SNSSAI and DNN combinations to which the usage monitoring data limit applies. The S-NSSAI is the key of the map. If omitted, the limit applies to every S-NSSAI and DNN.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="snssai" type="subscriber:Snssai" minOccurs="0"/>
					<element name="dnn" type="subscriber:Dnn" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="snssaiKey" type="xs:string" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="UsageMonLevel">
		<annotation>
			<documentation>Indicates the level of the usage monitoring instance (PDU Session level or per Service</documentation>
		</annotation>
		<restriction base="xsd:string">
			<enumeration value="SESSION_LEVEL"/>
			<enumeration value="SERVICE_LEVEL"/>
		</restriction>
	</xs:simpleType>
	<xs:simpleType name="DateTime">
		<annotation>
			<documentation>String with format "date-time" as defined in OpenAPI Specification As defined by date-time - RFC3339 </documentation>
		</annotation>
		<restriction base="xs:string"/>
	</xs:simpleType>
	<xs:complexType name="UsageThreshold">
		<annotation>
			<documentation>Usage monitoring profile associated with the subscriber. The limit identifier is used as the key.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="duration" type="xs:unsignedInt" minOccurs="0"/>
					<choice>
						<sequence>
							<element name="totalVolume" type="xs:unsignedInt" minOccurs="0"/>
							<element name="downlinkVolume" type="xs:unsignedInt" minOccurs="0"/>
							<element name="uplinkVolume" type="xs:unsignedInt" minOccurs="0"/>
							<!-- Unsigned integer identifying a volume in units of bytes. -->
						</sequence>
					</choice>
					<!-- Unsigned integer identifying a period of time in units of seconds. -->
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:complexType name="TimePeriod">
		<annotation>
			<documentation>Time period to reset the remaining allowed usage for a periodic usage monitoring instance (postpaid subscriptions). This attribute along with the startDate determine the actual absolute time at which usage needs to be reset.</documentation>
		</annotation>
		<complexContent>
			<extension base="spml:SecondClassObject">
				<sequence>
					<element name="period" type="subscriber:Periodicity" minOccurs="0"/>
					<element name="maxNumPeriod" type="xs:unsignedInt" minOccurs="0"/>
				</sequence>
			</extension>
		</complexContent>
	</xs:complexType>
	<xs:simpleType name="Periodicity">
		<annotation>
			<documentation>Indicates periodicity</documentation>
		</annotation>
		<restriction base="xsd:string">
			<enumeration value="YEARLY"/>
			<enumeration value="MONTHLY"/>
			<enumeration value="WEEKLY"/>
			<enumeration value="DAILY"/>
			<enumeration value="HOURLY"/>
		</restriction>
	</xs:simpleType>
 	
<!--************************************************************************-->
<!--  Changes Related to Identity Swap (IMSI, MSISDN)      -->
<!--************************************************************************-->


	<xs:simpleType name="PCFSwapAliasType">
       <xs:restriction base="xsd:string">
          <xs:enumeration value="imsi"/>
		  <xs:enumeration value="msisdn"/>
       </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="PCFSwapIdentifier">
       <xs:simpleContent>
          <xs:extension base="spml:ID">
             <xs:attribute name="alias" type="subscriber:PCFSwapAliasType" use="required"/>
          </xs:extension>
       </xs:simpleContent>
    </xs:complexType>
	
</xs:schema>