<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/HSS/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!-- Common types 2010-04-27 -->
	<xs:simpleType name="privateUserIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="70" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="publicIdValueType">
		<xs:restriction base="xs:string">
			<xs:pattern	value="(sip:.*)|(tel:.*)" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="impiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="70" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="secondPrivateUserIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="associationIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="72" />
		</xs:restriction>
	</xs:simpleType>
	<!-- EPS types -->
	<xs:simpleType name="epsProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsOdbType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NONE" />
			<xs:enumeration value="ODB-ALL" />
			<xs:enumeration value="ODB-HPLMN-APN" />
			<xs:enumeration value="ODB-VPLMN-APN" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsRoamingAllowedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="epsLocationStateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PURGED" />
			<xs:enumeration value="LOCATED" />
			<xs:enumeration value="UNKNOWN" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualApnOperatorIdentifierReplacementType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualDefaultContextIdType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualContextIdType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualSubscribedChargingCharacteristicType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="epsIndividualAmbrMaximalUplinkIpFlowType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualAmbrMaximalDownlinkIpFlowType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualRatFrequencyPriorityIdType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualUeUsageTypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="mmeAddressType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsRoamingRestrictionType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="epsRegionalRoamingServiceAreaIdType">
		<xs:restriction base="xs:unsignedInt">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAccessRestrictionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="E-UTRAN-DENIED" />
			<xs:enumeration value="NON-3GPP-ACC-DENIED" />
			<xs:enumeration value="ALL-DENIED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAaaRegistrationStateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="REGISTERED" />
			<xs:enumeration value="NOT_REGISTERED" />
			<xs:enumeration value="AAA_ASSIGNED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="setReqEpsAaaRegistrationStateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NOT_REGISTERED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAaaAddressType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAaaRealmType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsDynamicPdnInformationType">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsUserIpV4AddressType">
		<xs:restriction base="xs:string">
			<xs:pattern
				value=".*[$]((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsUserIpV6AddressType">
		<xs:restriction base="xs:string">
			<!-- Fully specified address -->
      		<xs:pattern 
      			value=".*[$][0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<!-- Double colon start -->
      		<xs:pattern value=".*[$]:(:[0-9A-Fa-f]{1,4}){1,7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<!-- Double colon middle -->
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,6}(:[0-9A-Fa-f]{1,4}){1}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,5}(:[0-9A-Fa-f]{1,4}){1,2}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1,3}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,4}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,5}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,6}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<!-- Double colon end -->
     		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,7}:([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
     		<!--  IPv4-Embedded IPv6 Address -->
     		<!-- Fully specified address -->
     		<xs:pattern value=".*[$]([0-9a-fA-F]{1,4}:){6,6}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
     		<!-- Double colon start -->
     		<xs:pattern value=".*[$]::([0-9a-fA-F]{1,4}:){0,5}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
     		<!-- Double colon middle -->
     		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,2}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,3}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
      		<xs:pattern value=".*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,4}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
     		<!-- Double colon end -->
     		<xs:pattern value=".*[$]([0-9a-fA-F]{1,4}:){1,5}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsTenantIdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="500" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAutomaticProvisionedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="epsLastUpdateLocationDateType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsReattachRequiredType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>

	<xs:simpleType name="epsSetSgsnLocationStateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UNKNOWN" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsGetSgsnLocationStateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UNKNOWN" />
			<xs:enumeration value="LOCATED" />
			<xs:enumeration value="PURGED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsMmeRealmType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsSgsnAddressType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsSgsnRealmType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsSgsnLastUpdateLocationDateType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<!-- AVG types -->
	<xs:simpleType name="avgA4KeyIndType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="512" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="avgAmfType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{4}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="avgEncryptedKType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="avgEncryptedOPcType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="avgFSetIndType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="15" />
		</xs:restriction>
	</xs:simpleType>
	<!-- IMS types -->
	<xs:simpleType name="accessIdentifierType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="allowedAuthMechanismType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DIGEST" />
			<xs:enumeration value="SSO" />
			<xs:enumeration value="NBA" />
			<xs:enumeration value="" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="applicationServerType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="asHostingPSIType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="authMechanismType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NBA" />
			<xs:enumeration value="NBA_LINE_PROFILE" />
			<xs:enumeration value="DIGEST_LINE_PROFILE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="authorizedVisitedAccessLineType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="chargingProfIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="conditionTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AND" />
			<xs:enumeration value="OR" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="configuredServiceProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="defaultHandlingType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SESSION_CONTINUED" />
			<xs:enumeration value="SESSION_TERMINATED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="defaultRemoteReferenceAccessLocationType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="detectionPointType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="diaServIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="diaServRealmType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="e164Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="groupIdType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="groupRequestedURIType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="isActiveType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="isDefaultType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="isPsiType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="implicitRegSetType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="individualCapabilityType">
        <xs:restriction base="xs:unsignedInt">
	        <xs:minInclusive value="1"/>
	        <xs:maxInclusive value="100"/>
	    </xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="individualOptionalCapabilityType">
        <xs:restriction base="xs:unsignedInt"/>
	</xs:simpleType>
	<xs:simpleType name="individualServiceProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="63" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="lineNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="maxNumberOfContactsType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="200" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="maxNumberSessionsType">
		<xs:restriction base="xs:unsignedInt" />
	</xs:simpleType>
	<xs:simpleType name="negatedDetectionPointType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="negatedRequestedURIType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="numOfFailedSipAuthAttemptsType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="numOfFailedXcapAuthAttemptsType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="originatingCallsS-CSCFType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="phoneContextType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="privacyIndicatorType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="referenceAccessLocationType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="registrationTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIAL_REGISTRATION" />
			<xs:enumeration value="RE-REGISTRATION" />
			<xs:enumeration value="DE-REGISTRATION" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="requestedURIType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="roamingAllowedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="serviceProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="66" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sessionBarringIndType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="sessionDescriptionType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sessionCaseType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ORIGINATING" />
			<xs:enumeration value="TERMINATING_REGISTERED" />
			<xs:enumeration value="TERMINATING_UNREGISTERED" />
			<xs:enumeration value="ORIGINATING_UNREGISTERED" />
			<xs:enumeration value="NOT_ORIGINATING" />
			<xs:enumeration value="NOT_TERMINATING_REGISTERED" />
			<xs:enumeration value="NOT_TERMINATING_UNREGISTERED" />
			<xs:enumeration value="NOT_ORIGINATING_UNREGISTERED" />
			<xs:enumeration value="ORIGINATING_CDIV" />
			<xs:enumeration value="NOT_ORIGINATING_CDIV" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sipHeaderType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sipMethodType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="stateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="REGISTERED" />
			<xs:enumeration value="NOT_REGISTERED" />
			<xs:enumeration value="UNREGISTERED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="subscribedMediaProfileType">
		<xs:restriction base="xs:unsignedInt" />
	</xs:simpleType>
	<xs:simpleType name="terminatingCallsS-CSCFType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="triggerDescriptionType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="69" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="triggerPriorityType">
		<xs:restriction base="xs:unsignedInt">
			<xs:pattern value="\d*[02468]"></xs:pattern>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="triggerTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ORIGINATING" />
			<xs:enumeration value="TERMINATING_REGISTERED" />
			<xs:enumeration value="TERMINATING_UNREGISTERED" />
			<xs:enumeration value="ORIGINATING_UNREGISTERED" />
			<xs:enumeration value="NOT_ORIGINATING" />
			<xs:enumeration value="NOT_TERMINATING_REGISTERED" />
			<xs:enumeration value="NOT_TERMINATING_UNREGISTERED" />
			<xs:enumeration value="NOT_ORIGINATING_UNREGISTERED" />
			<xs:enumeration value="ORIGINATING_CDIV" />
			<xs:enumeration value="NOT_ORIGINATING_CDIV" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userBarringIndType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="userPasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="4" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userPrimaryHA1PasswordType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userSecondaryHA1PasswordType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="valueType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="wirelineAccessAllowedType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ALLOWED_FROM_ANY_LOCATION" />
			<xs:enumeration value="ALLOWED_ONLY_FROM_RAL" />
			<xs:enumeration value="ALLOWED_FROM_AUTHORIZED_LOCATIONS" />
			<xs:enumeration value="ALLOWED_FROM_ANY_LOCATION_EXCEPT_IF_NO_PANI" />
			<xs:enumeration value="ALLOWED_ONLY_IF_PANI_INCL_AUTH_LOCATION" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="xcapAllowedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="xcapPasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="4" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="esrNumberType">
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-9]{7})|([0-9]{10})" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="tenantIdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="100" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="priorityLevelType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="4" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsExtendedAccessRestrictionType">
		<xs:restriction base="xs:unsignedInt">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="383" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsMultimediaPriorityServiceType">
		<xs:restriction base="xs:unsignedInt" />
	</xs:simpleType>
	<xs:simpleType name="epsZoneCodeSetIdType">
		<xs:restriction base="xs:unsignedInt" />
	</xs:simpleType>
	<xs:simpleType name="epsCommonMsisdnType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsImeiSvType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
			<xs:maxLength value="16" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsSessionTransferNumberType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="includeRegisterRequestType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="includeRegisterResponseType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="epsUeSrVccCapType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="looseRouteIdType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LOOSE_ROUTE_REQUIRED" />
			<xs:enumeration value="LOOSE_ROUTE_NOT_REQUIRED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="isWildcardExtendedType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="aliasGroupIdType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="displayNameType">
		<xs:restriction base="xs:string" >
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualMappingContextIdType">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAaaOdbType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NONE" />
			<xs:enumeration value="ODB-ALL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="zoneIdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>
	<!--  featureId ="featureId_NAMProvisioningforEPS"  -->
	<xs:simpleType name="epsNamType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsAaaMIP6FeatureVectorType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NO_CAPABILITY"/>
			<xs:enumeration value="MIP6_INTEGRATED"/>
			<xs:enumeration value="LOCAL_HOME_AGENT_ASSIGNMENT"/>
			<xs:enumeration value="PMIP6_SUPPORTED"/>
			<xs:enumeration value="IP4_HOA_SUPPORTED"/>
			<xs:enumeration value="LOCAL_MAG_ROUTING_SUPPORTED"/>
			<xs:enumeration value="ASSIGN_LOCAL_IP"/>
			<xs:enumeration value="MIP4_SUPPORTED"/>
			<xs:enumeration value="OPTIMIZED_IDLE_MODE_MOBILITY"/>
			<xs:enumeration value="GTPv2_SUPPORTED"/>
		</xs:restriction>
	</xs:simpleType>
		<xs:simpleType name="epsIndividualRauTauTimerType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/> 
			<xs:maxInclusive value="**********"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsMdtUserConsentType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/> 
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="epsIndividualAdditionalDefaultContextIdType">
		<xs:restriction base="xs:unsignedLong"/>
	</xs:simpleType>
		<xs:simpleType name="epsIndividualAmbrMaximalUplinkIpExtendedType">
		<xs:restriction base="xs:unsignedLong">
		    <xs:minInclusive value="4294967"/> 
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualAmbrMaximalDownlinkIpExtendedType">
		<xs:restriction base="xs:unsignedLong">
		    <xs:minInclusive value="4294967"/> 
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epsIndividualUserTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="epcMBB" />
			<xs:enumeration value="epcEMBB" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>