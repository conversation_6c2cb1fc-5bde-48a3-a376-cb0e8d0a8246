<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/hlr/" xmlns:ns="http://schemas.ericsson.com/ma/hlr/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://schemas.ericsson.com/ma/hlr/">
<xs:element name="CreateSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element minOccurs="0" name="profile" type="xs:unsignedByte"/>
<xs:element minOccurs="0" name="SubscriberData" type="SubscriberDataType"/>
<xs:element minOccurs="0" name="SupplementaryServiceData" type="SupplementaryServiceDataType"/>
<xs:element minOccurs="0" name="GprsServiceData" type="GprsServiceDataType"/>
<xs:element minOccurs="0" name="AMSISDNServiceData" type="AMSISDNServiceDataType"/>
<xs:element minOccurs="0" name="LocationServicesData" type="LocationServicesDataType"/>
<xs:element minOccurs="0" name="LocationServicesAddressData" type="LocationServicesAddressDataType"/>
<xs:element minOccurs="0" name="MMINTServiceData" type="MMINTServiceDataType"/>
<xs:element minOccurs="0" name="ClosedUserGroupServiceData" type="ClosedUserGroupServiceDataType"/>
<xs:element minOccurs="0" name="SpatialTriggersServiceData" type="SpatialTriggersServiceDataType"/>
<xs:element minOccurs="0" name="multiSim" type="MultiSimType"/>
<xs:element minOccurs="0" name="SpamSMSData" type="SpamSMSDataType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="CamelSubscriptionData" type="CamelTriggerDetectionPointType"/>
<xs:element minOccurs="0" name="CamelTriggeringCriteria" type="CamelConditionalTriggerType"/>
<xs:element minOccurs="0" name="ResetMobileSubscriberLocation" type="ResetMobileSubscriberLocationType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required"/>
<xs:attribute name="imsi" type="imsiType" use="required"/>
</xs:complexType>
<xs:key name="msisdnKey_HLRSUB_Create">
<xs:selector xpath="./ns:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="msisdnKeyRef_HLRSUB_Create" refer="msisdnKey_HLRSUB_Create">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="imsiKey_HLRSUB_Create">
<xs:selector xpath="./ns:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="imsiKeyRef_HLRSUB_Create" refer="imsiKey_HLRSUB_Create">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:element name="SetSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="profile" type="xs:unsignedByte"/>
<xs:element minOccurs="0" name="removeReferences" type="xs:boolean"/>
<xs:element minOccurs="0" name="SubscriberData" type="SubscriberDataType"/>
<xs:element minOccurs="0" name="SupplementaryServiceData" type="SupplementaryServiceDataType"/>
<xs:element minOccurs="0" name="GprsServiceData" type="GprsServiceDataType"/>
<xs:element minOccurs="0" name="AMSISDNServiceData" type="AMSISDNServiceDataType"/>
<xs:element minOccurs="0" name="LocationServicesData" type="LocationServicesDataSetType"/>
<xs:element minOccurs="0" name="LocationServicesAddressData" type="LocationServicesAddressDataType"/>
<xs:element minOccurs="0" name="MMINTServiceData" type="MMINTServiceDataType"/>
<xs:element minOccurs="0" name="ClosedUserGroupServiceData" type="ClosedUserGroupServiceDataType"/>
<xs:element minOccurs="0" name="SpatialTriggersServiceData" type="SpatialTriggersServiceDataType"/>
<xs:element minOccurs="0" name="multiSim" type="MultiSimType"/>
<xs:element minOccurs="0" name="SpamSMSData" type="SpamSMSDataType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="CamelSubscriptionData" nillable="true" type="CamelTriggerDetectionPointType"/>
<xs:element minOccurs="0" name="CamelExtendedData" type="CamelExtendedDataType"/>
<xs:element minOccurs="0" name="CamelTriggeringCriteria" type="CamelConditionalTriggerType"/>
<xs:element minOccurs="0" name="ResetMobileSubscriberLocation" type="ResetMobileSubscriberLocationType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="SubscriberData"/>
<xs:element minOccurs="0" name="SupplementaryServiceData"/>
<xs:element minOccurs="0" name="GprsServiceData"/>
<xs:element minOccurs="0" name="AMSISDNServiceData"/>
<xs:element minOccurs="0" name="locationData"/>
<xs:element minOccurs="0" name="LocationServicesData"/>
<xs:element minOccurs="0" name="LocationServicesAddressData"/>
<xs:element minOccurs="0" name="MMINTServiceData"/>
<xs:element minOccurs="0" name="ClosedUserGroupServiceData"/>
<xs:element minOccurs="0" name="SpatialTriggersServiceData"/>
<xs:element minOccurs="0" name="SpamSMSData"/>
<xs:element minOccurs="0" name="camel"/>
<xs:element minOccurs="0" name="multiSim"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetResponseSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="imeisv" type="ImeisvType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element minOccurs="0" name="SubscriberData" type="SubscriberDataResponseType"/>
<xs:element minOccurs="0" name="SupplementaryServiceData" type="SupplementaryServiceDataType"/>
<xs:element minOccurs="0" name="GprsServiceData" type="GprsServiceGetDataType"/>
<xs:element minOccurs="0" name="AMSISDNServiceData" type="AMSISDNServiceDataType"/>
<xs:element minOccurs="0" name="LocationServicesData" type="LocationServicesDataType"/>
<xs:element minOccurs="0" name="LocationServicesAddressData" type="LocationServicesAddressDataType"/>
<xs:element minOccurs="0" name="MMINTServiceData" type="MMINTServiceDataType"/>
<xs:element minOccurs="0" name="ClosedUserGroupServiceData" type="ClosedUserGroupServiceDataType"/>
<xs:element minOccurs="0" name="SpatialTriggersServiceData" type="SpatialTriggersServiceDataType"/>
<xs:element minOccurs="0" name="SpamSMSData" type="SpamSMSDataType"/>
<xs:element minOccurs="0" name="camel" type="CamelType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required"/>
<xs:attribute name="imsi" type="imsiType" use="required"/>
</xs:complexType>
<xs:key name="msisdnKey_HLRSUB_Get">
<xs:selector xpath="./ns:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="msisdnKeyRef_HLRSUB_Get" refer="msisdnKey_HLRSUB_Get">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="imsiKey_HLRSUB_Get">
<xs:selector xpath="./ns:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="imsiKeyRef_HLRSUB_Get" refer="imsiKey_HLRSUB_Get">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:simpleType name="msisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ridType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="63"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="zoneidType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="SubscriberDataType">
<xs:sequence>
<xs:element minOccurs="0" name="BasicService" type="BasicServiceType"/>
<xs:element minOccurs="0" name="OperatorDeterminedBarringService" type="OperatorDeterminedBarringServiceType"/>
<xs:element minOccurs="0" name="EmlppService" type="EmlppServiceType"/>
<xs:element minOccurs="0" name="NetworkAccessMode" type="NetworkAccessModeType"/>
<xs:element minOccurs="0" name="SubData" type="SubDataType"/>
<xs:element minOccurs="0" name="locationData" type="LocationDataType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SubscriberDataResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="BasicService" type="BasicServiceType"/>
<xs:element minOccurs="0" name="OperatorDeterminedBarringService" type="OperatorDeterminedBarringServiceType"/>
<xs:element minOccurs="0" name="EmlppService" type="EmlppServiceType"/>
<xs:element minOccurs="0" name="NetworkAccessMode" type="NetworkAccessModeType"/>
<xs:element minOccurs="0" name="SubData" type="SubDataType"/>
<xs:element minOccurs="0" name="SubscriptionOption" type="SubscriptionOptionType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="BasicServiceType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="bs21" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs22" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs23" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs24" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs25" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs26" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs2g" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs2f" type="ZeroFiveType"/>
<xs:element default="0" minOccurs="0" name="bs31" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs32" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs33" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs34" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs3g" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bs3f" type="ZeroFourType"/>
<xs:element default="0" minOccurs="0" name="ts11" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ts21" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ts22" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ts61" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ts62" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="OperatorDeterminedBarringServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="oba" type="BinaryType"/>
<xs:element minOccurs="0" name="obcc" type="BinaryType"/>
<xs:element minOccurs="0" name="obct" type="ZeroFourType"/>
<xs:element minOccurs="0" name="obdct" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="obi" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="obmct" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="obo" type="ZeroFourType"/>
<xs:element default="0" minOccurs="0" name="obopre" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="obopri" type="BinaryType"/>
<xs:element minOccurs="0" name="obp" type="ZeroThreeType"/>
<xs:element default="0" minOccurs="0" name="obr" type="Zero99Type"/>
<xs:element default="0" minOccurs="0" name="obrf" type="ZeroFiveType"/>
<xs:element default="0" minOccurs="0" name="obssm" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="obzi" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="obzo" type="ZeroFiveType"/>
<xs:element default="0" minOccurs="0" name="osb1" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="osb2" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="osb3" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="osb4" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="EmlppServiceType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="emlpp" type="BinaryType"/>
<xs:element default="4" minOccurs="0" name="demlpp" type="ZeroSixType"/>
<xs:element default="4" minOccurs="0" name="memlpp" type="ZeroSixType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="NetworkAccessModeType">
<xs:sequence>
<xs:element minOccurs="0" name="nam" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SubDataType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="acc" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="acr" type="ZeroTwoType"/>
<xs:element default="0" minOccurs="0" name="aoc" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="ard" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="baic" type="BinaryType"/>
<xs:element minOccurs="0" name="baoc" type="BinaryType"/>
<xs:element minOccurs="0" name="bicro" type="BinaryType"/>
<xs:element minOccurs="0" name="boic" type="BinaryType"/>
<xs:element minOccurs="0" name="boiexh" type="BinaryType"/>
<xs:element minOccurs="0" name="caw" type="BinaryType"/>
<xs:element minOccurs="0" name="cfb" type="BinaryType"/>
<xs:element minOccurs="0" name="cfnrc" type="BinaryType"/>
<xs:element minOccurs="0" name="cfnry" type="BinaryType"/>
<xs:element minOccurs="0" name="cfu" type="BinaryType"/>
<xs:element minOccurs="0" name="rdp" type="Zero32Type"/>
<xs:element minOccurs="0" name="grdp" type="Zero32Type"/>
<xs:element default="0" minOccurs="0" name="capl" type="ZeroFifteenType"/>
<xs:element default="0" minOccurs="0" name="cat">
<xs:simpleType>
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="256"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="clip" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="clir" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="colp" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="colr" type="BinaryType"/>
<xs:element minOccurs="0" name="csp" type="CamelProfileIdType"/>
<xs:element minOccurs="0" name="dbsg">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="dcsi" type="BinaryType"/>
<xs:element minOccurs="0" name="ect" type="BinaryType"/>
<xs:element minOccurs="0" name="gsap" type="xs:unsignedByte"/>
<xs:element default="0" minOccurs="0" name="hold" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="hpn" type="ZeroTwoType"/>
<xs:element default="0" minOccurs="0" name="gprsci" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ici" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ist">
<xs:simpleType>
<xs:union>
<xs:simpleType>
<xs:restriction base="BinaryType">
<xs:enumeration value="0"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="15"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="istcso" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="istgso" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="istvso" type="BinaryType"/>
<xs:element minOccurs="0" name="mca" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="mcsi" type="BinaryType"/>
<xs:element minOccurs="0" name="mpty" type="BinaryType"/>
<xs:element minOccurs="0" name="mrdpid">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="([1-9]|1[0-5])-(3[0-2]|[1-2][0-9]|[1-9])|3[0-2]|[1-2][0-9]|[0-9]"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="mrdmch">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="ocsi" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ofa">
<xs:simpleType>
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="512"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="oick" type="Zero999Type"/>
<xs:element default="0" minOccurs="0" name="osmci" type="BinaryType"/>
<xs:element minOccurs="0" name="ora">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="tcsi" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="tick" type="Zero999Type"/>
<xs:element default="0" minOccurs="0" name="tsmci" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="oin" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="tin" type="BinaryType"/>
<xs:element minOccurs="0" name="pdpcp" type="GPRSProfileIdType"/>
<xs:element minOccurs="0" name="pici" type="piciType"/>
<xs:element minOccurs="0" name="pici2" type="piciType"/>
<xs:element minOccurs="0" name="pici3" type="piciType"/>
<xs:element default="0" minOccurs="0" name="prbt" type="BinaryType"/>
<xs:element default="0000" minOccurs="0" name="pwd">
<xs:simpleType>
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="regser">
<xs:simpleType>
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65534"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="rsa" type="rsaType"/>
<xs:element minOccurs="0" name="rtca" type="BinaryType"/>
<xs:element minOccurs="0" name="schar">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,2}(-[0-9]{1,4})?"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="smshr1">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="(12[0-8]|1[0-1][0-9]|[0-9]?[0-9])(-(3[0-2]|[1-2][0-9]|[0-9]))?"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="smshr2">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="(12[0-8]|1[0-1][0-9]|[0-9]?[0-9])(-(3[0-2]|[1-2][0-9]|[0-9]))?"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="mpid" type="mpidType"/>
<xs:element minOccurs="0" name="smspam" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
<xs:element default="0" minOccurs="0" name="socfb" type="ZeroThreeType"/>
<xs:element default="0" minOccurs="0" name="socfrc" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="socfry" type="ZeroThreeType"/>
<xs:element default="0" minOccurs="0" name="socfu" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="soclip" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="soclir" type="ZeroTwoType"/>
<xs:element default="0" minOccurs="0" name="socolp" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="sodcf" type="ZeroThreeType"/>
<xs:element default="0" minOccurs="0" name="soplcs" type="BinaryType"/>
<xs:element minOccurs="0" name="sosdcf">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="stype">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="127"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element default="0" minOccurs="0" name="tsmo" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="vtcsi" type="BinaryType"/>
<xs:element minOccurs="0" name="cbnf" type="BinaryType"/>
<xs:element minOccurs="0" name="cfnf" type="BinaryType"/>
<xs:element minOccurs="0" name="chnf" type="BinaryType"/>
<xs:element minOccurs="0" name="clipnf" type="BinaryType"/>
<xs:element minOccurs="0" name="clirnf" type="BinaryType"/>
<xs:element minOccurs="0" name="cwnf" type="BinaryType"/>
<xs:element minOccurs="0" name="dcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="ectnf" type="BinaryType"/>
<xs:element minOccurs="0" name="gprscsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="mcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="ocsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="odbnf" type="BinaryType"/>
<xs:element minOccurs="0" name="osmcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="tcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="tifcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="tsmcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="vtcsinf" type="BinaryType"/>
<xs:element minOccurs="0" name="dcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="gprscsist" type="BinaryType"/>
<xs:element minOccurs="0" name="mcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="ocsist" type="BinaryType"/>
<xs:element minOccurs="0" name="osmcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="tcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="tsmcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="vtcsist" type="BinaryType"/>
<xs:element minOccurs="0" name="ics" type="BinaryType"/>
<xs:element minOccurs="0" name="redmch" type="OneTwoType"/>
<xs:element minOccurs="0" name="shplmn" type="shplmnType"/>
<xs:element minOccurs="0" name="mderbt" type="BinaryType"/>
<xs:element minOccurs="0" name="pai" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SubscriptionOptionType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="mmint" type="BinaryType"/>
<xs:element minOccurs="0" name="ste" type="BinaryType"/>
<xs:element minOccurs="0" name="cug" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="asl" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="bsl" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="crel" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="cunrl" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="plmno" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="ttp" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="univ" type="BinaryType"/>
<xs:element minOccurs="0" name="servtl" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="tsd1" type="BinaryType"/>
<xs:element default="0" minOccurs="0" name="passwordBarred" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SupplementaryServiceDataType">
<xs:sequence>
<xs:element minOccurs="0" name="allss" type="AllSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfb" type="CallForwardingUntimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnrc" type="CallForwardingUntimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnry" type="CallForwardingTimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfs" type="CallForwardingTimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="ccfs" type="CallForwardingTimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfu" type="CallForwardingUntimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="dcf" type="CallForwardingTimedSupplementaryServiceType"/>
<xs:element minOccurs="0" name="spn" type="SinglePersonNumberSupplementaryServiceType"/>
<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="bic" type="BarringSupplementaryServiceTypeWithMandatoryProvisioningState"/>
<xs:element minOccurs="0" name="boc" type="BarringSupplementaryServiceTypeWithMandatoryProvisioningState"/>
<xs:element minOccurs="0" name="bac" type="BarringSupplementaryServiceTypeWithMandatoryProvisioningState"/>
<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="caw" type="CallWaitingSupplementaryServiceType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="BarringSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts10" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts20" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="ActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="ActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="BarringSupplementaryServiceTypeWithMandatoryProvisioningState">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts10" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts20" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="ActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="ActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="AllSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="ActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallWaitingSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts10" type="ActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="ActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="ActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="ActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallForwardingTimedSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="ts10" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="ts60" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="tsd0" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="bs20" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="bs30" type="CallForwardingTimedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallForwardingTimedSupplementaryServiceTypeWithMandatoryProvisioningState">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="ts10" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="ts60" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="tsd0" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="bs20" type="CallForwardingTimedType"/>
<xs:element minOccurs="0" name="bs30" type="CallForwardingTimedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallForwardingTimedType">
<xs:sequence>
<xs:element name="activationState" type="ActivatationCodeType"/>
<xs:sequence minOccurs="0">
<xs:element name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="time">
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="5"/>
<xs:maxInclusive value="30"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="sadd" type="saddType"/>
</xs:sequence>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallForwardingUntimedSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="CallForwardingUntimedType"/>
<xs:element minOccurs="0" name="ts10" type="CallForwardingUntimedType"/>
<xs:element minOccurs="0" name="ts60" type="CallForwardingUntimedType"/>
<xs:element minOccurs="0" name="tsd0" type="CallForwardingUntimedType"/>
<xs:element minOccurs="0" name="bs20" type="CallForwardingUntimedType"/>
<xs:element minOccurs="0" name="bs30" type="CallForwardingUntimedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallForwardingUntimedType">
<xs:sequence>
<xs:element name="activationState" type="ActivatationCodeType"/>
<xs:sequence minOccurs="0">
<xs:element name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="sadd" type="saddType"/>
</xs:sequence>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SinglePersonNumberSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="common" type="SinglePersonNumberType"/>
<xs:element minOccurs="0" name="ts10" type="SinglePersonNumberType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SinglePersonNumberType">
<xs:sequence>
<xs:element name="activationState" type="ActivatationCodeType"/>
<xs:sequence minOccurs="0">
<xs:element name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ActivationStateType">
<xs:sequence>
<xs:element name="activationState" type="ActivatationCodeType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="ActivatationCodeType">
<xs:restriction base="xs:string">
<xs:enumeration value="ACTIVE-OP"/>
<xs:enumeration value="ACTIVE-QS"/>
<xs:enumeration value="NOT ACTIVE"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fnumType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9abc*#]{1,18}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="saddType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-2]-[0-9A-F]{2,40}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ofaType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="512"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="rsaType">
<xs:restriction base="xs:nonNegativeInteger">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="4096"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="eqosidType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="4095"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="apnidType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="16383"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
<xs:enumeration value="NS"/>
<xs:enumeration value="WILDCARD"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="pdpaddType">
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="pdptyType">
<xs:restriction base="xs:string">
<xs:enumeration value="IPV4"/>
<xs:enumeration value="IPV6"/>
<xs:enumeration value="PPP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pdpchType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,2}(-[0-9]{1,4})?"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:complexType name="GprsServiceDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="PDPContext" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pdpid" type="pdpidType"/>
<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element minOccurs="0" name="vpaa" type="BinaryType"/>
</xs:sequence>
<xs:attribute name="pdpid" type="pdpidType" use="optional"/>
<xs:attribute name="apnid" type="apnidType" use="optional"/>
<xs:attribute name="pdpadd" use="optional">
<xs:simpleType>
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="GprsServiceGetDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="PDPContext" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pdpid" type="pdpidType"/>
<xs:element name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
<xs:element minOccurs="0" name="eqosid">
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="4095"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="apnid">
<xs:simpleType>
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="16383"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="WILDCARD"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="pdpadd">
<xs:simpleType>
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="pdpty">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="IPV4"/>
<xs:enumeration value="IPV6"/>
<xs:enumeration value="PPP"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="pdpch">
<xs:simpleType>
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,2}(-[0-9]{1,4})?"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="vpaa" type="BinaryType"/>
</xs:sequence>
<xs:attribute name="pdpid" type="pdpidType" use="required"/>
</xs:complexType>
<xs:key name="pdpidKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="@pdpid"/>
</xs:key>
<xs:keyref name="pdpidKeyRef_Get" refer="pdpidKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="ns:pdpid"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="epdpaddType">
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ERASE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="epdpindType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pdpidType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="NA"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="50|[1-4][0-9]|[1-9](&amp;(50|[1-4][0-9]|[1-9])){0,}"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:complexType name="AMSISDNServiceDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="AMSISDNData" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="amsisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="bs">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="4"/>
<xs:maxLength value="4"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element name="bc">
<xs:simpleType>
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="8"/>
<xs:maxInclusive value="65534"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
</xs:sequence>
<xs:attribute name="amsisdn" type="msisdnType" use="required"/>
</xs:complexType>
<xs:key name="amsisdnKey">
<xs:selector xpath="."/>
<xs:field xpath="@amsisdn"/>
</xs:key>
<xs:keyref name="amsisdnKeyRef" refer="amsisdnKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:amsisdn"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="LocationServicesDataType">
<xs:sequence>
<xs:element minOccurs="0" name="univ" type="BinaryType"/>
<xs:element minOccurs="0" name="CallRelGroupData" type="CallRelGroupDataType"/>
<xs:element minOccurs="0" name="PublicLandData" type="PublicLandDataType"/>
<xs:element minOccurs="0" name="MoclData" type="MoclDataType"/>
<xs:element minOccurs="0" name="ExternalAddressData" type="ExternalAddressDataType"/>
<xs:element minOccurs="0" name="ServiceTypeData" type="ServiceTypeDataType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="LocationServicesDataSetType">
<xs:sequence>
<xs:element minOccurs="0" name="univ" type="BinaryType"/>
<xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="CallRelGroupData" type="CallRelGroupDataType"/>
<xs:element minOccurs="0" name="PublicLandData" type="PublicLandDataType"/>
</xs:sequence>
<xs:element minOccurs="0" name="allpcl" type="PositiveType"/>
</xs:choice>
<xs:element minOccurs="0" name="MoclData" type="MoclDataType"/>
<xs:element minOccurs="0" name="ExternalAddressData" type="ExternalAddressDataType"/>
<xs:element minOccurs="0" name="ServiceTypeData" type="ServiceTypeDataType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CallRelGroupDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="CallRelGroup" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="name" type="callRelGroupNameType"/>
<xs:element minOccurs="0" name="notf" type="ZeroFourType"/>
</xs:sequence>
<xs:attribute name="name" type="callRelGroupNameType" use="required"/>
</xs:complexType>
<xs:key name="groupNameKey">
<xs:selector xpath="."/>
<xs:field xpath="@name"/>
</xs:key>
<xs:keyref name="groupNameKeyRef" refer="groupNameKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:name"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="intIdStringType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-4](&amp;[0-4]){0,4}|ALL|NA"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="PublicLandDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="PublicLand" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="plmno" type="PositiveType"/>
<xs:element minOccurs="0" name="intid" type="intIdStringType"/>
</xs:sequence>
<xs:attribute name="plmno" type="PositiveType" use="required"/>
<xs:attribute name="intid" type="intIdStringType" use="required"/>
</xs:complexType>
<xs:key name="plmnoKey">
<xs:selector xpath="."/>
<xs:field xpath="@plmno"/>
</xs:key>
<xs:keyref name="plmnoKeyRef" refer="plmnoKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:plmno"/>
</xs:keyref>
<xs:key name="intIdKey">
<xs:selector xpath="."/>
<xs:field xpath="@intid"/>
</xs:key>
<xs:keyref name="intIdKeyRef" refer="intIdKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:intid"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="moclStringType">
<xs:restriction base="xs:string">
<xs:pattern value="((ASL|BSL|TTP)(&amp;(ASL|BSL|TTP)){0,2})|ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="MoclDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="Mocl" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="mocl" type="moclStringType"/>
</xs:sequence>
<xs:attribute name="mocl" type="moclStringType" use="required"/>
</xs:complexType>
<xs:key name="moclKey">
<xs:selector xpath="."/>
<xs:field xpath="@mocl"/>
</xs:key>
<xs:keyref name="moclKeyRef" refer="moclKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:mocl"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ExternalAddressDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ExternalAddress" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="eadd" type="eaddAttrType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element minOccurs="0" name="group" type="callRelGroupNameType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
</xs:sequence>
<xs:attribute name="eadd" type="eaddAttrType" use="required"/>
<xs:attribute name="gres" type="gresType" use="required"/>
<xs:attribute name="group" type="callRelGroupNameType" use="required"/>
</xs:complexType>
<xs:key name="eaddKey">
<xs:selector xpath="."/>
<xs:field xpath="@eadd"/>
</xs:key>
<xs:keyref name="eaddKeyRef" refer="eaddKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:eadd"/>
</xs:keyref>
<xs:key name="eaddGresKey">
<xs:selector xpath="."/>
<xs:field xpath="@gres"/>
</xs:key>
<xs:keyref name="eaddGresKeyRef" refer="eaddGresKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:gres"/>
</xs:keyref>
<xs:key name="eaddGroupKey">
<xs:selector xpath="."/>
<xs:field xpath="@group"/>
</xs:key>
<xs:keyref name="eaddGroupKeyRef" refer="eaddGroupKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:group"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ServiceTypeDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ServiceType" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="servt" type="servtAttrType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
</xs:sequence>
<xs:attribute name="servt" type="servtAttrType" use="required"/>
<xs:attribute name="gres" type="gresType" use="required"/>
</xs:complexType>
<xs:key name="servtKey">
<xs:selector xpath="."/>
<xs:field xpath="@servt"/>
</xs:key>
<xs:keyref name="servtKeyRef" refer="servtKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:servt"/>
</xs:keyref>
<xs:key name="servtGresKey">
<xs:selector xpath="."/>
<xs:field xpath="@gres"/>
</xs:key>
<xs:keyref name="servtGresKeyRef" refer="servtGresKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:gres"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="eaddAttrType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{3,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="servtAttrType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,3}|ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gresType">
<xs:restriction base="xs:string">
<xs:pattern value="[019]|NA"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="callRelGroupNameType">
<xs:restriction base="xs:string">
<xs:enumeration value="crel"/>
<xs:enumeration value="cunrl"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="LocationServicesAddressDataType">
<xs:sequence>
<xs:element minOccurs="0" name="GMLCAddressData" type="GMLCAddressDataType"/>
<xs:element minOccurs="0" name="HomeGMLCAddressData" type="HomeGMLCAddressDataType"/>
<xs:element minOccurs="0" name="PrivacyProfileRegisterAddressData" type="PrivacyProfileRegisterAddressDataType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="notfType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-4]"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="GMLCAddressDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="GMLCAddress" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="gmlcid" type="gmlcidType"/>
<xs:element minOccurs="0" name="gmlcadd" type="gmlcaddType"/>
</xs:sequence>
<xs:attribute name="gmlcid" type="gmlcidType" use="required"/>
</xs:complexType>
<xs:key name="gmlcidKey">
<xs:selector xpath="."/>
<xs:field xpath="@gmlcid"/>
</xs:key>
<xs:keyref name="gmlcidKeyRef" refer="gmlcidKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:gmlcid"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="HomeGMLCAddressDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="HomeGMLCAddress" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="hgmlcid" type="hgmlcidType"/>
<xs:element minOccurs="0" name="hgmlcadd" type="hgmlcaddType"/>
</xs:sequence>
<xs:attribute name="hgmlcid" type="hgmlcidType" use="required"/>
</xs:complexType>
<xs:key name="hgmlcidKey">
<xs:selector xpath="."/>
<xs:field xpath="@hgmlcid"/>
</xs:key>
<xs:keyref name="hgmlcidKeyRef" refer="hgmlcidKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:hgmlcid"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="PrivacyProfileRegisterAddressDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="PrivacyProfileRegisterAddress" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pprid" type="ppridType"/>
<xs:element minOccurs="0" name="ppradd" type="ppraddType"/>
</xs:sequence>
<xs:attribute name="pprid" type="ppridType" use="required"/>
</xs:complexType>
<xs:key name="ppridKey">
<xs:selector xpath="."/>
<xs:field xpath="@pprid"/>
</xs:key>
<xs:keyref name="ppridKeyRef" refer="ppridKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:pprid"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="gmlcidType">
<xs:union memberTypes="xs:unsignedByte">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="([0-9]{1,3}((&amp;&amp;[0-9]{1,3})|(&amp;[0-9]{1,3})+))|ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="gmlcaddType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{3,15}|ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="hgmlcidType">
<xs:union memberTypes="xs:unsignedByte">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="hgmlcaddType">
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="ppridType">
<xs:union memberTypes="xs:unsignedByte">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="ppraddType">
<xs:union memberTypes="IPv4Type IPv6Type">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:complexType name="MMINTServiceDataType">
<xs:sequence>
<xs:element minOccurs="0" name="MobilityMgmtInTriggeringData" type="MobilityMgmtInTriggeringDataType"/>
<xs:element minOccurs="0" name="MobilityMgmtInTriggeringDataActivation" type="MobilityMgmtInTriggeringDataActivationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="MobilityMgmtInTriggeringDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="DetectionPoint" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="dp" type="DPType"/>
<xs:element minOccurs="0" name="sk">
<xs:simpleType>
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="gsa">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="3"/>
<xs:maxLength value="15"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="dpstatus">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ACTIVE"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
</xs:sequence>
<xs:attribute name="dp" type="DPType" use="required"/>
</xs:complexType>
<xs:key name="dpKey">
<xs:selector xpath="."/>
<xs:field xpath="@dp"/>
</xs:key>
<xs:keyref name="dpKeyRef" refer="dpKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:dp"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="MobilityMgmtInTriggeringDataActivationType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="Activation" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="dp" type="DPType"/>
</xs:sequence>
<xs:attribute name="dp" type="DPType" use="required"/>
</xs:complexType>
<xs:key name="activationDpKey">
<xs:selector xpath="."/>
<xs:field xpath="@dp"/>
</xs:key>
<xs:keyref name="activationDpKeyRef" refer="activationDpKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:dp"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="DPType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="([0-1]((&amp;&amp;[0-1])|(&amp;[0-1])+))|NA"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:complexType name="ClosedUserGroupServiceDataType">
<xs:sequence>
<xs:element minOccurs="0" name="ClosedUserGroup" type="ClosedUserGroupType"/>
<xs:element minOccurs="0" name="ClosedUserGroupBSGOptions" type="ClosedUserGroupBSGOptionsType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ClosedUserGroupType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="CUG" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="index" type="indexType"/>
<xs:element minOccurs="0" name="ic">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="([0-9]{4}-)[0-9]{1,5}"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="restr">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ICB"/>
<xs:enumeration value="OCB"/>
<xs:enumeration value="NONE"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
<xs:element minOccurs="0" name="erase" type="PositiveType"/>
</xs:sequence>
<xs:attribute name="index" type="indexType" use="required"/>
</xs:complexType>
<xs:key name="indexKey">
<xs:selector xpath="."/>
<xs:field xpath="@index"/>
</xs:key>
<xs:keyref name="indexKeyRef" refer="indexKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:index"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="bsgType">
<xs:restriction base="xs:string">
<xs:pattern value="(TS10|TS60|TSD0|BS20|BS30)(&amp;(TS10|TS60|TSD0|BS20|BS30)){0,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="indexType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:complexType name="ClosedUserGroupBSGOptionsType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="CUGBSGOption">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
<xs:element minOccurs="0" name="access">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="OA"/>
<xs:enumeration value="IA"/>
<xs:enumeration value="OIA"/>
<xs:enumeration value="NONE"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="pcug">
<xs:simpleType>
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="NONE"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SpatialTriggersServiceDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="STEData" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="ste" type="steType"/>
<xs:element name="gmlcid" type="xs:unsignedByte"/>
<xs:element minOccurs="0" name="gmlca" type="gmlcaddType"/>
</xs:sequence>
<xs:attribute name="ste" type="steType" use="required"/>
</xs:complexType>
<xs:key name="steKey">
<xs:selector xpath="."/>
<xs:field xpath="@ste"/>
</xs:key>
<xs:keyref name="steKeyRef" refer="steKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:ste"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="steType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="15"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="SpamSMSDataType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="SCAddsData" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="smspam">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ACTIVE"/>
<xs:enumeration value="NACTIVE"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="scadds" type="scaddsType"/>
</xs:sequence>
<xs:attribute name="scadds" type="scaddsType" use="required"/>
</xs:complexType>
<xs:key name="scaddsKey">
<xs:selector xpath="."/>
<xs:field xpath="@scadds"/>
</xs:key>
<xs:keyref name="scaddsKeyRef" refer="scaddsKey">
<xs:selector xpath="."/>
<xs:field xpath="ns:scadds"/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CamelType">
<xs:sequence>
<xs:element name="ctdp" type="CamelTriggerDetectionPointType"/>
<xs:element minOccurs="0" name="eoinci" type="EoinciType"/>
<xs:element minOccurs="0" name="eoick" type="eoickType"/>
<xs:element minOccurs="0" name="etinci" type="etinciType"/>
<xs:element minOccurs="0" name="etick" type="etickType"/>
<xs:element minOccurs="0" name="gcso" type="gcsoType"/>
<xs:element minOccurs="0" name="sslo" type="ssloType"/>
<xs:element minOccurs="0" name="mcso" type="mcsoType"/>
<xs:element minOccurs="0" name="gc2so" type="gc2soType"/>
<xs:element minOccurs="0" name="mc2so" type="mc2soType"/>
<xs:element minOccurs="0" name="tif" type="tifType"/>
<xs:element minOccurs="0" name="gc3so" type="gc3soType"/>
<xs:element minOccurs="0" name="mc3so" type="mc3soType"/>
<xs:element minOccurs="0" name="gprsso" type="gprssoType"/>
<xs:element minOccurs="0" name="osmsso" type="osmssoType"/>
<xs:element minOccurs="0" name="tsmsso" type="tsmssoType"/>
<xs:element minOccurs="0" name="mmso" type="mmsoType"/>
<xs:element minOccurs="0" name="gc4so" type="gc4soType"/>
<xs:element minOccurs="0" name="mc4so" type="mc4soType"/>
<xs:element name="ccamel" type="CamelConditionalTriggerType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CamelTriggerDetectionPointType">
<xs:sequence>
<xs:element minOccurs="0" name="triggerPoint" type="TriggerPointType"/>
<xs:element minOccurs="0" name="detectionPoint" type="DetectionPointType"/>
<xs:element minOccurs="0" name="gsa" type="GsaType"/>
<xs:element minOccurs="0" name="serviceKey" type="ServiceKeyType"/>
<xs:element minOccurs="0" name="defaultErrorHandling" type="DefaultErrorHandlingType"/>
<xs:element minOccurs="0" name="cch" type="CchType"/>
<xs:element minOccurs="0" name="i" type="IType"/>
<xs:element minOccurs="0" name="dialnum" type="DialNumType"/>
</xs:sequence>
<xs:attribute name="triggerPoint" type="TriggerPointType" use="optional"/>
<xs:attribute name="detectionPoint" type="DetectionPointType" use="optional"/>
<xs:attribute name="cch" type="CchType" use="optional"/>
</xs:complexType>
<xs:simpleType name="TriggerPointType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DetectionPointType">
<xs:restriction base="xs:string">
<xs:maxLength value="3"/>
<xs:minLength value="1"/>
<xs:pattern value="[0-9]|10|11|12|13|14|ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="GsaType">
<xs:restriction base="xs:string">
<xs:maxLength value="15"/>
<xs:minLength value="3"/>
<xs:pattern value="[0-9]*"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ServiceKeyType">
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DefaultErrorHandlingType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="CchType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IType">
<xs:restriction base="xs:string">
<xs:enumeration value="Y"/>
<xs:enumeration value="N"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DialNumType">
<xs:restriction base="xs:string">
<xs:maxLength value="17"/>
<xs:pattern value="[0-4]-[0-9,*,#,a,b,c]*"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CamelExtendedDataType">
<xs:sequence>
<xs:element minOccurs="0" name="eoinci" type="EoinciType"/>
<xs:element minOccurs="0" name="eoick" type="eoickType"/>
<xs:element minOccurs="0" name="etinci" type="etinciType"/>
<xs:element minOccurs="0" name="etick" type="etickType"/>
<xs:element minOccurs="0" name="gcso" type="gcsoType"/>
<xs:element minOccurs="0" name="sslo" type="ssloType"/>
<xs:element minOccurs="0" name="mcso" type="mcsoType"/>
<xs:element minOccurs="0" name="gc2so" type="gc2soType"/>
<xs:element minOccurs="0" name="mc2so" type="mc2soType"/>
<xs:element minOccurs="0" name="tif" type="tifType"/>
<xs:element minOccurs="0" name="gc3so" type="gc3soType"/>
<xs:element minOccurs="0" name="mc3so" type="mc3soType"/>
<xs:element minOccurs="0" name="gprsso" type="gprssoType"/>
<xs:element minOccurs="0" name="osmsso" type="osmssoType"/>
<xs:element minOccurs="0" name="tsmsso" type="tsmssoType"/>
<xs:element minOccurs="0" name="mmso" type="mmsoType"/>
<xs:element minOccurs="0" name="gc4so" type="gc4soType"/>
<xs:element minOccurs="0" name="mc4so" type="mc4soType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="EoinciType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="eoickType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="etinciType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="etickType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gcsoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ssloType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mcsoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gc2soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mc2soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="tifType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gc3soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mc3soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gprssoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="osmssoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="tsmssoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mmsoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gc4soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mc4soType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CamelConditionalTriggerType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="octdp2" nillable="true" type="CCamelOctdp2Type"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="tctdp12" nillable="true" type="CCamelTctdp12Type"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CCamelOctdp2Type">
<xs:sequence>
<xs:element minOccurs="0" name="id" type="xs:unsignedByte"/>
<xs:element minOccurs="0" name="cch" type="CchType"/>
<xs:element minOccurs="0" name="mty" type="MtyType"/>
<xs:element minOccurs="0" name="dnum" type="DnumType"/>
<xs:element minOccurs="0" name="dlgh" type="DlghType"/>
<xs:element minOccurs="0" name="bs" type="BsType"/>
<xs:element minOccurs="0" name="bsg" type="BsgType"/>
<xs:element minOccurs="0" name="ftc" type="FtcType"/>
</xs:sequence>
<xs:attribute name="cch" type="CchType" use="optional"/>
<xs:attribute name="dnum" type="DnumType" use="optional"/>
<xs:attribute name="dlgh" type="DlghType" use="optional"/>
<xs:attribute name="bs" type="BsType" use="optional"/>
<xs:attribute name="bsg" type="BsgType" use="optional"/>
<xs:attribute name="ftc" type="FtcType" use="optional"/>
</xs:complexType>
<xs:simpleType name="MtyType">
<xs:restriction base="xs:string">
<xs:enumeration value="I"/>
<xs:enumeration value="E"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DnumType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[0-4]-[0-9*#]{1,15}(&amp;([0-4]-[0-9*#]{1,15}))*"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="DlghType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="15"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[\w&amp;]+"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="BsType">
<xs:restriction base="xs:string">
<xs:enumeration value="TS11"/>
<xs:enumeration value="TS61"/>
<xs:enumeration value="TS62"/>
<xs:enumeration value="TSD1"/>
<xs:enumeration value="BS21"/>
<xs:enumeration value="BS22"/>
<xs:enumeration value="BS23"/>
<xs:enumeration value="BS24"/>
<xs:enumeration value="BS25"/>
<xs:enumeration value="BS26"/>
<xs:enumeration value="BS2G"/>
<xs:enumeration value="BS31"/>
<xs:enumeration value="BS32"/>
<xs:enumeration value="BS33"/>
<xs:enumeration value="BS34"/>
<xs:enumeration value="BS3G"/>
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BsgType">
<xs:restriction base="xs:string">
<xs:enumeration value="TS10"/>
<xs:enumeration value="TS60"/>
<xs:enumeration value="TSD0"/>
<xs:enumeration value="TS20"/>
<xs:enumeration value="TS30"/>
<xs:enumeration value="BS20"/>
<xs:enumeration value="BS30"/>
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="FtcType">
<xs:union>
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="F"/>
<xs:enumeration value="N"/>
<xs:enumeration value=""/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType>
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
</xs:union>
</xs:simpleType>
<xs:simpleType name="CamelProfileIdType">
<xs:restriction base="xs:nonNegativeInteger">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="8160"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="GPRSProfileIdType">
<xs:restriction base="xs:nonNegativeInteger">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="8160"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CCamelTctdp12Type">
<xs:sequence>
<xs:element minOccurs="0" name="id" type="xs:unsignedByte"/>
<xs:element minOccurs="0" name="cch" type="CchType"/>
<xs:element minOccurs="0" name="bs" type="BsType"/>
<xs:element minOccurs="0" name="bsg" type="BsgType"/>
</xs:sequence>
<xs:attribute name="cch" type="CchType" use="optional"/>
<xs:attribute name="bs" type="BsType" use="optional"/>
<xs:attribute name="bsg" type="BsgType" use="optional"/>
</xs:complexType>
<xs:simpleType name="ResetMobileSubscriberLocationType">
<xs:restriction base="xs:string">
<xs:enumeration value="YES"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scaddsType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,15}|NA"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IPv4Type">
<xs:restriction base="xs:string">
<xs:minLength value="7"/>
<xs:maxLength value="15"/>
<xs:pattern value="(:?\d{1,3}\.){3}\d{1,3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IPv6Type">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9A-Fa-f:]{2,39}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BinaryType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="PositiveType">
<xs:restriction base="BinaryType">
<xs:enumeration value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroTwoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroThreeType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroFourType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroFiveType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="5"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroSixType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroFifteenType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="15"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Zero32Type">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Zero99Type">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Zero999Type">
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ImeisvType">
<xs:annotation>
<xs:documentation>
				International Mobile Equipment Identity and Software
				Version
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9F]{16}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="piciType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,3}(-[0-2])?"/>
</xs:restriction>
</xs:simpleType>
<xs:element name="CreateImsiChangeover">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
<xs:element name="nimsi" type="imsiType"/>
<xs:element minOccurs="0" name="date" type="dateType"/>
<xs:element minOccurs="0" name="deleteOldRef" type="PositiveType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
<xs:key name="msisdnKey_IMSICH_Create">
<xs:selector xpath="./ns:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="msidsnKeyRef_IMSICH_Create" refer="msisdnKey_IMSICH_Create">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="imsiKey_IMSICH_Create">
<xs:selector xpath="./ns:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="imsiKeyRef_IMSICH_Create" refer="imsiKey_IMSICH_Create">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:element name="SetImsiChangeover">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="date" type="dateType"/>
<xs:element minOccurs="0" name="deleteOldRef" type="BinaryType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="nimsi" type="imsiType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetResponseImsiChangeover">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="nimsi" type="imsiType"/>
<xs:element minOccurs="0" name="date" type="dateType"/>
<xs:element minOccurs="0" name="state" type="ZeroThreeType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required"/>
<xs:attribute name="imsi" type="imsiType" use="required"/>
</xs:complexType>
</xs:element>
<xs:simpleType name="dateType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9][0-9][0-1][0-9][0-3][0-9]|now"/>
</xs:restriction>
</xs:simpleType>
<xs:element name="GetResponseMessageWaiting">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="mce">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="NO"/>
<xs:enumeration value="YES"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="mnrf">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="REACH"/>
<xs:enumeration value="NREACH"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="mnrg">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:enumeration value="REACH"/>
<xs:enumeration value="NREACH"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="scadd">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:pattern value="[34]{1}-[0-9]*"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required"/>
<xs:attribute name="imsi" type="imsiType" use="required"/>
</xs:complexType>
</xs:element>
<xs:simpleType name="mceType">
<xs:restriction base="xs:string">
<xs:enumeration value="NO"/>
<xs:enumeration value="YES"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mnrfType">
<xs:restriction base="xs:string">
<xs:enumeration value="REACH"/>
<xs:enumeration value="NREACH"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mnrgType">
<xs:restriction base="xs:string">
<xs:enumeration value="REACH"/>
<xs:enumeration value="NREACH"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="LocationDataType">
<xs:sequence>
<xs:element minOccurs="0" name="vlrAddress">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="6"/>
<xs:maxLength value="20"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="msrn">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="15"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="mscNumber">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="20"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="lmsid">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="8"/>
<xs:maxLength value="8"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="sgsnNumber">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="6"/>
<xs:maxLength value="20"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element minOccurs="0" name="locState">
<xs:simpleType>
<xs:restriction base="xs:string"/>
</xs:simpleType>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:complexType name="LinkedImsiType">
<xs:sequence>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="active" type="xs:string"/>
</xs:sequence>
<xs:attribute name="imsi" type="xs:string" use="required"/>
</xs:complexType>
<xs:complexType name="MultiSimType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="imsis" nillable="true" type="LinkedImsiType"/>
<xs:element minOccurs="0" name="mch" type="mchType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element minOccurs="0" name="acimsi" type="imsiType"/>
<xs:element minOccurs="0" name="mMsisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="delall" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="mchType">
<xs:restriction base="xs:string">
<xs:enumeration value="LOC"/>
<xs:enumeration value="USSD"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mpidType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="OneTwoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="shplmnType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="31"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
