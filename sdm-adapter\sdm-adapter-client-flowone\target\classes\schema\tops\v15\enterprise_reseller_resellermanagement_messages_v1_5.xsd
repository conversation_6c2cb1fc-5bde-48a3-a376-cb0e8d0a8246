<!-- 
<PERSON>ra_Tracking_ID: PR002564-17
Build_Label: JDeveloperBuild
Build_Date: 2023-05-04T15:23:20.605-0500
-->
<xsd:schema xmlns:enterprise_reseller_resellermanagement_messages="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/messages" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:enterprise_reseller_resellermanagement_xsd="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/messages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_1">
	<xsd:annotation>
		<xsd:documentation>Contains Messages used for operations in ResellerManagement service..</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types" schemaLocation="enterprise_reseller_resellermanagement_types_v1_5.xsd"/>
	<xsd:element name="activate_Request" type="enterprise_reseller_resellermanagement_messages:activate_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for activate() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="activate_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'activate_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="customerId" type="enterprise_common_xsd:CustomerIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Customer ID.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN to be added in the billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="name" type="enterprise_reseller_resellermanagement_xsd:NameType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Name of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="address" type="enterprise_reseller_resellermanagement_xsd:AddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Physical address of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="esn" type="enterprise_common_xsd:EsnType">
				<xsd:annotation>
					<xsd:documentation>ESN of the device being activated.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="uiccId" type="enterprise_common_xsd:UICCIDType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identification of Universal Integrated Circuit Card(UICC).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="offers" type="enterprise_reseller_resellermanagement_xsd:OfferType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Offers to be added to the subscriber. As per the element definition is allows more than one offers but the service is implemented to consider first offer only. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ipProfile" type="enterprise_reseller_resellermanagement_xsd:ipProfileType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Type of option which subscriber is assigned for IP allocation for data services.
					Option 0-Private IP
					Option 1- CustomerProvided IP
					Option 2- Public IP
					Option 3- Static IP					
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ipAddress" type="enterprise_common_xsd:ipAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>IP address assigned based on chosen IP profile.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="APN" type="enterprise_reseller_resellermanagement_xsd:APNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation> Access Point Name formatted as 'uscc#####.enterprise#.usc-cdp'.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="activate_Response" type="enterprise_reseller_resellermanagement_messages:activate_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for activate() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="activate_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'activate_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="min" type="enterprise_common_xsd:MinType">
				<xsd:annotation>
					<xsd:documentation>Member Identification Number returned by backend.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>Output response returned from ordering system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="suspend_Request" type="enterprise_reseller_resellermanagement_messages:suspend_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for suspend() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="suspend_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'suspend_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber to be suspended.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="suspend_Response" type="enterprise_reseller_resellermanagement_messages:suspend_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for suspend() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="suspend_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'suspend_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>Status of suspension from billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="resume_Request" type="enterprise_reseller_resellermanagement_messages:resume_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for resume() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="resume_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'resume_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber to be resumed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="resume_Response" type="enterprise_reseller_resellermanagement_messages:resume_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for resume() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="resume_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'resume_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>CTN resume status from billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="cancel_Request" type="enterprise_reseller_resellermanagement_messages:cancel_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for cancel() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="cancel_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'cancel_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber to be canceled.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="cancel_Response" type="enterprise_reseller_resellermanagement_messages:cancel_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for cancel() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="cancel_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'cancel_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>CTN cancel status.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeCTN_Request" type="enterprise_reseller_resellermanagement_messages:changeCTN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for changeCTN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeCTN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeCTN_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="newCtn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>New CTN of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="oldCtn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>Old CTN of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeCTN_Response" type="enterprise_reseller_resellermanagement_messages:changeCTN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for changeCTN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeCTN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeCTN_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>CTN change status from billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeESN_Request" type="enterprise_reseller_resellermanagement_messages:changeESN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for changeESN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeESN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeESN_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber whose ESN has to be changed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="oldEsn" type="enterprise_common_xsd:EsnType">
				<xsd:annotation>
					<xsd:documentation>Old ESN of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="newEsn" type="enterprise_common_xsd:EsnType">
				<xsd:annotation>
					<xsd:documentation>New ESN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeESN_Response" type="enterprise_reseller_resellermanagement_messages:changeESN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for changeESN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeESN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeESN_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>CTN change status from billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeFeatures_Request" type="enterprise_reseller_resellermanagement_messages:changeFeatures_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for changeFeatures() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeFeatures_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeFeatures_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber to which features have to be changed. Can change features for only one offer at this time.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:choice>
				<xsd:element name="modifyFeatures" type="enterprise_reseller_resellermanagement_messages:modifyFeaturesType">
					<xsd:annotation>
						<xsd:documentation>List of features to be added/removed</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="modifySubscriptions" type="enterprise_reseller_resellermanagement_messages:modifySubscriptionsType">
					<xsd:annotation>
						<xsd:documentation>List of subscriptions to be added/removed</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="changeFeatures_Response" type="enterprise_reseller_resellermanagement_messages:changeFeatures_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for changeFeatures() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="changeFeatures_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'changeFeatures_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="status" type="enterprise_reseller_resellermanagement_xsd:OrderConfirmationType">
				<xsd:annotation>
					<xsd:documentation>Status of this transaction returned by the billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="modifyFeaturesType">
		<xsd:sequence>
			<xsd:element name="removeFeatures" type="enterprise_reseller_resellermanagement_xsd:FeatureType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of features to be removed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="addFeatures" type="enterprise_reseller_resellermanagement_xsd:FeatureType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of features to be added.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="modifySubscriptionsType">
		<xsd:sequence>
			<xsd:element name="removeSubscriptions" type="enterprise_reseller_resellermanagement_xsd:SubscriptionType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of Subscriptions to be removed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="addSubscriptions" type="enterprise_reseller_resellermanagement_xsd:SubscriptionType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of Subscriptions to be added.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="retrieveFeatures_Request" type="enterprise_reseller_resellermanagement_messages:retrieveFeatures_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for retrieveFeatures() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="retrieveFeatures_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'retrieveFeatures_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber whose features are to be retrieved.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="retrieveFeatures_Response" type="enterprise_reseller_resellermanagement_messages:retrieveFeatures_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for retrieveFeatures() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="retrieveFeatures_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'retrieveFeatures_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="offers" type="enterprise_reseller_resellermanagement_xsd:OfferType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of offers and corresponding features retrieved from the billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getInfoByCTN_Request" type="enterprise_reseller_resellermanagement_messages:getInfoByCTN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for getInfoByCTN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getInfoByCTN_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getInfoByCTN_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ctn" type="enterprise_common_xsd:CtnType">
				<xsd:annotation>
					<xsd:documentation>CTN of the subscriber whose features are to be retrieved.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getInfoByCTN_Response" type="enterprise_reseller_resellermanagement_messages:getInfoByCTN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for getInfoByCTN() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getInfoByCTN_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getInfoByCTN_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="accountNumber" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Account number of subscriber. It will be customer ID in the new system, or BAN from the old system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="esn" type="enterprise_common_xsd:EsnType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Associated ESN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="min" type="enterprise_common_xsd:MinType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Member Identification Number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ctnStatus" type="enterprise_reseller_resellermanagement_xsd:CtnStatusType">
				<xsd:annotation>
					<xsd:documentation>Status of the subscriber- canceled, active, suspended.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="offers" type="enterprise_reseller_resellermanagement_xsd:OfferType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of offers and corresponding features retrieved from the billing system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="dataBlock" type="enterprise_common_xsd:IndicatorYesOrNoType" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Attribute (Yes or No ) value based on the customer data blocked status on their MDN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="dataRoamingBlock" type="enterprise_common_xsd:IndicatorYesOrNoType" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Attribute (Yes or No) value based on the customer data roaming blocked status on their MDN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="isThrottled" type="enterprise_common_xsd:IndicatorYesOrNoType" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Attribute (Yes or No) value based on the customer's data speed is throttled on their MDN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="isRoamingThrottled" type="enterprise_common_xsd:IndicatorYesOrNoType" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Attribute (Yes or No) value based on the customer's data roaming speed is throttled on their MDN.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="searchOrder_Request" type="enterprise_reseller_resellermanagement_messages:searchOrder_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for searchOrder() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="searchOrder_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'searchOrder_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="mdn" type="enterprise_common_xsd:MdnType">
				<xsd:annotation>
					<xsd:documentation>Mobile Directory Number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="searchOrder_Response" type="enterprise_reseller_resellermanagement_messages:searchOrder_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for searchOrder() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="searchOrder_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'searchOrder_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="products" type="enterprise_reseller_resellermanagement_xsd:productsType">
				<xsd:annotation>
					<xsd:documentation>Customer assigned products list.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
