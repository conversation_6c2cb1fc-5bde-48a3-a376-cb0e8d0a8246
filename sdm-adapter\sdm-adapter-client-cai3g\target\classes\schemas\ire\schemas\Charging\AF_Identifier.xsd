<xs:schema targetNamespace="http://schemas.ericsson.com/ma/CS/AF/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ma/CS/AF/" xmlns:af="http://schemas.ericsson.com/ma/CS/AF/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb">
   <xs:element name="msisdn" type="msisdnType"/>
   <xs:element name="identifier" type="identifierType"/>
   <xs:element name="createIdentifier">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="msisdn" type="msisdnType"/>
            <xs:element name="identifier" type="identifierType"/>
            <xs:element name="type" type="typeType"/>
            <xs:element name="ttl" type="ttlType" minOccurs="0"/>
            <xs:element name="prerequisite" type="prerequisiteType" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
         <xs:attribute name="identifier" type="identifierType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="identifierAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
   </xs:element>
   <xs:element name="setIdentifier">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="type" type="typeType"/>
            <xs:element name="ttl" type="ttlType" minOccurs="0"/>
            <xs:element name="prerequisite" type="prerequisiteType" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
         <xs:attribute name="identifier" type="identifierType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="identifierAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
   </xs:element>
   <xs:element name="deleteIdentifier">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="type" type="typeType"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
         <xs:attribute name="identifier" type="identifierType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="identifierAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
   </xs:element>
   <xs:element name="getIdentifier">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="type" type="typeType"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
         <xs:attribute name="identifier" type="identifierType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="identifierAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
   </xs:element>
   <xs:element name="getIdentifierResponse">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="identifier" type="identifierType"/>
            <xs:element name="type" type="typeType"/>
            <xs:element name="msisdn" type="msisdnType"/>
            <xs:element name="class" type="classType"/>
            <xs:element name="ttl" type="ttlType"/>
            <xs:element name="sdpHostName" type="sdpHostNameType"/>
            <xs:element name="sdpIP" type="sdpIPType"/>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
   <!--simple type definition-->
   <xs:simpleType name="identifierType">
      <xs:restriction base="xs:string">
         <xs:minLength value="1" />
         <xs:maxLength value="128" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="typeType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="imsi"/>
         <xs:enumeration value="private"/>
         <xs:enumeration value="nai"/>
         <xs:enumeration value="sip-uri"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="msisdnType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="imsiType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[0-9]{1,15}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="privateType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="naiType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="sipuriType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ttlType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="2147483647"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="sdpHostNameType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="prerequisiteType">
      <xs:restriction base="xs:boolean"/>
   </xs:simpleType>
   <xs:simpleType name="classType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="sdpIPType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
</xs:schema>