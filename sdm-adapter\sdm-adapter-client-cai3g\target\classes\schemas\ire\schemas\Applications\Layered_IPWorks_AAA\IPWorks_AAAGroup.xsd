<xs:schema xmlns="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:x="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	targetNamespace="http://schemas.ericsson.com/ma/IPWORKS/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/aaala_types.xsd" />
	<xs:element name="aaaGroupName" type="aaaGroupNameType" />

	<xs:element name="CreateAAAGroup">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaGroupName" type="aaaGroupNameType" />
				<xs:element name="aaaSharedPolicyName" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType" />
						</xs:sequence>
						<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="aaaSharedPolicyNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_aaaSharedPolicyName">
						<xs:selector xpath="." />
						<xs:field xpath="@aaaSharedPolicyName" />
					</xs:key>
					<xs:keyref name="keyref_create_aaaSharedPolicyName" refer="key_create_aaaSharedPolicyName">
						<xs:selector xpath="./x:aaaSharedPolicyName" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaGroupNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_aaaGroupName">
			<xs:selector xpath="./x:aaaGroupName" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_aaaGroupName" refer="key_aaaGroupName">
			<xs:selector xpath="." />
			<xs:field xpath="@aaaGroupName" />
		</xs:keyref>
	</xs:element>

	<xs:element name="GetResponseAAAGroup">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaGroupName" type="aaaGroupNameType" />
				<xs:element name="aaaSharedPolicyName" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType" />
						</xs:sequence>
						<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="aaaSharedPolicyNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaGroupNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<xs:element name="SetAAAGroup">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaSharedPolicyName" nillable="true" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType" />
						</xs:sequence>
						<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="aaaSharedPolicyNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_aaaSharedPolicyName">
						<xs:selector xpath="." />
						<xs:field xpath="@aaaSharedPolicyName" />
					</xs:key>
					<xs:keyref name="keyref_set_aaaSharedPolicyName" refer="key_set_aaaSharedPolicyName">
						<xs:selector xpath="./x:aaaSharedPolicyName" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaGroupNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_aaaGroupName">
			<xs:selector xpath="." />
			<xs:field xpath="@aaaGroupName" />
		</xs:key>
	</xs:element>
</xs:schema>
