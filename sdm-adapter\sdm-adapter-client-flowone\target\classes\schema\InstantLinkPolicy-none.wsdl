<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- 
	This is an empty policy file the enforces no restrictions on InstantLink SOA 
	If more strict policies are needed replace this file with more appropriate one
	e.g. InstantLinkPolicy-password.wsdl
	-->
<definitions targetNamespace="http://soa.comptel.com/2011/02/instantlink"
	name="InstantLinkWebServices" xmlns="http://schemas.xmlsoap.org/wsdl/"
	xmlns:tns="http://soa.comptel.com/2011/02/instantlink" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
	xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
	xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsap10="http://www.w3.org/2006/05/addressing/wsdl"
	xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex"
	xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702"
	xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa10="http://www.w3.org/2005/08/addressing">
	<wsp:Policy
		Name="http://soa.comptel.com/2011/02/instantlink/InstantLinkWebServices_policy">
	</wsp:Policy>
</definitions>