<!-- Subscriber PBX Line -->

<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" jaxb:extensionBindingPrefixes="xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/hlrla_types.xsd" />
	<xs:import namespace="http://schemas.ericsson.com/cai3g1.2/" schemaLocation="../../Generic/cai3g1.2_provisioning.xsd" />

	<!-- SetSubscriberPBXLine MOId: msisdn MOType: SubscriberPBXLine@http://schemas.ericsson.com/pg/hlr/13.5/ -->
	<xs:element name="SetSubscriberPBXLine">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="vlradds" type="vlraddsType" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

</xs:schema>