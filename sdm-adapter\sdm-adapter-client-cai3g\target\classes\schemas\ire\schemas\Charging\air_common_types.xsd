<!--(<PERSON><PERSON> (China) Communications Company Ltd) 2015-->
<xs:schema targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/"
           elementFormDefault="qualified" attributeFormDefault="unqualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://schemas.ericsson.com/ma/CS/AIR/"
           xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/">

   <xs:complexType name="subDedicatedAccountRefillInformationType">
      <xs:sequence>
         <xs:element name="startDate" type="startDateType" minOccurs="0"/>
         <xs:element name="expiryDate" type="expiryDateType" minOccurs="0"/>
         <xs:element name="refillAmount1" type="refillAmountType" minOccurs="0"/>
         <xs:element name="refillAmount2" type="refillAmountType" minOccurs="0"/>
         <xs:element name="clearedValue1" type="clearedValueType" minOccurs="0"/>
         <xs:element name="clearedValue2" type="clearedValueType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterSetInformationType">
      <xs:sequence>
         <xs:element name="serviceProvider" type="serviceProviderType" minOccurs="0" />
         <xs:element name="treeParameterInformation" type="treeParameterInformationType" minOccurs="0" maxOccurs="unbounded" />
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterUpdateInformationType">
      <xs:sequence>
         <xs:element name="treeParameterName" type="treeParameterNameType" />
         <xs:element name="treeParameterUpdateAction" type="treeParameterUpdateActionType" />
         <!-- Note: One of the values needs to be included for ADD, SET or DELETE. For action CLEAR neither of these values are to be included.-->
         <xs:choice minOccurs="0">
            <xs:element name="treeParameterValueString" type="treeParameterValueStringType" minOccurs="0"/>
            <xs:element name="treeParameterValueDate" type="treeParameterValueDateType" minOccurs="0"/>
            <xs:element name="treeParameterValueDecimal" type="treeParameterValueDecimalType" minOccurs="0"/>
            <xs:element name="treeParameterValueInteger" type="treeParameterValueIntegerType" minOccurs="0"/>
            <xs:element name="treeParameterValueDayOfWeek" type="treeParameterValueDayOfWeekType" minOccurs="0"/>
            <xs:element name="treeParameterValueDateRange" type="treeParameterValueDateRangeType" minOccurs="0"/>
            <xs:element name="treeParameterValueTime" type="treeParameterValueTimeType" minOccurs="0"/>
            <xs:element name="treeParameterValueSchedule" type="treeParameterValueScheduleType" minOccurs="0"/>
         </xs:choice>
         <xs:element name="associatedPartyID" type="associatedPartyIDType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterSetUpdateInformationType">
      <xs:sequence>
         <xs:element name="serviceProvider" type="serviceProviderType" />
         <xs:element name="treeParameterUpdateInformation" type="treeParameterUpdateInformationType" minOccurs="0" maxOccurs="unbounded" />
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterInformationType">
      <xs:sequence>
         <xs:element name="treeParameterName" type="treeParameterNameType" minOccurs="0"/>
         <xs:choice minOccurs="0">
            <xs:element name="treeParameterValueString" type="treeParameterValueStringType" minOccurs="0"/>
            <xs:element name="treeParameterValueDate" type="treeParameterValueDateType" minOccurs="0"/>
            <xs:element name="treeParameterValueDecimal" type="treeParameterValueDecimalType" minOccurs="0"/>
            <xs:element name="treeParameterValueInteger" type="treeParameterValueIntegerType" minOccurs="0"/>
            <xs:element name="treeParameterValueSchedule" type="treeParameterValueScheduleType" minOccurs="0"/>
            <xs:element name="treeParameterValueDayOfWeek" type="treeParameterValueDayOfWeekType" minOccurs="0"/>
            <xs:element name="treeParameterValueDateRange" type="treeParameterValueDateRangeType" minOccurs="0"/>
            <xs:element name="treeParameterValueTime" type="treeParameterValueTimeType" minOccurs="0"/>
         </xs:choice>
         <xs:element name="treeParameterSource" type="treeParameterSourceType" minOccurs="0"/>
         <xs:element name="associatedPartyID" type="associatedPartyIDType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterValueScheduleType">
      <xs:sequence>
         <xs:element name="scheduledFrequency" type="scheduledFrequencyType"/>
         <xs:element name="scheduledInterval" type="scheduledIntervalType" minOccurs="0"/>
         <xs:element name="scheduledMonth" type="scheduledMonthType" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="scheduledDayOfMonth" type="scheduledDayOfMonthType" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="scheduledDayOfWeek" type="scheduledDayOfWeekType" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="scheduledHour" type="scheduledHourType" minOccurs="0"/>
         <xs:element name="scheduledMinute" type="scheduledMinuteType" minOccurs="0"/>
         <xs:element name="scheduledSecond" type="scheduledSecondType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterValueDateRangeType">
      <xs:sequence>
         <xs:element name="treeParameterStartDate" type="treeParameterStartDateType"/>
         <xs:element name="treeParameterEndDate" type="treeParameterEndDateType"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="treeParameterValueDecimalType">
      <xs:sequence>
         <xs:element name="treeParameterValueNumber" type="treeParameterValueNumberType" />
         <xs:element name="numberOfDecimals" type="numberOfDecimalsType" />
      </xs:sequence>
   </xs:complexType>

   <xs:complexType name="offerSelectionType">
      <xs:sequence>
         <xs:element name="offerIDFirst" type="offerIDType" />
         <xs:element name="offerIDLast" type="offerIDType" minOccurs="0" />
      </xs:sequence>
   </xs:complexType>

   <xs:complexType name="predefinedOfferValuesInformationType">
      <xs:sequence>
         <xs:element name="offerID" type="offerIDType" />
         <xs:element name="attributeInformation" type="attributeInformationType" minOccurs="0" maxOccurs="unbounded" />
         <xs:element name="treeParameterSetInformation" type="treeParameterSetInformationType" minOccurs="0" maxOccurs="unbounded" />
         <xs:element name="productOfferingName" type="productOfferingNameType" minOccurs="0" />
         <xs:element name="offerServiceID" type="offerServiceIDType" minOccurs="0" />
      </xs:sequence>
   </xs:complexType>

   <xs:complexType name="attributeUpdateInformationType">
      <xs:sequence>
         <xs:element name="attributeName" type="attributeNameType" />
         <xs:element name="attributeUpdateAction" type="attributeUpdateActionType" />
         <xs:choice minOccurs="0">
            <xs:element name="attributeValueString" type="attributeValueStringType" minOccurs="0" />
            <xs:element name="attributeValueDate" type="attributeValueDateType" minOccurs="0" />
            <xs:element name="attributeValueDecimal" type="attributeValueDecimalType" minOccurs="0" />
            <xs:element name="attributeValueInteger" type="attributeValueIntegerType" minOccurs="0" />
         </xs:choice>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="attributeInformationType">
      <xs:sequence>
         <xs:element name="attributeName" type="attributeNameType"/>
         <xs:choice minOccurs="0">
            <xs:element name="attributeValueString" type="attributeValueStringType" minOccurs="0"/>
            <xs:element name="attributeValueDate" type="attributeValueDateType" minOccurs="0"/>
            <xs:element name="attributeValueDecimal" type="attributeValueDecimalType" minOccurs="0"/>
            <xs:element name="attributeValueInteger" type="attributeValueIntegerType" minOccurs="0"/>
         </xs:choice>
         <xs:element name="attributeSource" type="attributeSourceType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="attributeValueDecimalType">
      <xs:sequence>
         <xs:element name="attributeValueNumber" type="attributeValueNumberType" minOccurs="0"/>
         <xs:element name="numberOfDecimals" type="numberOfDecimalsType" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>

   <xs:complexType name="dedicatedAccountUpdateInformationType">
      <xs:sequence>
         <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType" />
         <!--subscription & offer bothe have this, and a little different-->
         <!-- Duplicate definition: one productID is Useless -->
         <xs:element name="productID" type="productIDType" minOccurs="0" />
         <xs:choice minOccurs="0">
            <xs:element name="dedicatedAccountPamPeriodInformation" type="dedicatedAccountPamPeriodInformationType" minOccurs="0" />
            <xs:element name="updateAction" type="updateActionType" minOccurs="0" />
         </xs:choice>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="dedicatedAccountPamPeriodInformationType">
      <xs:sequence>
         <xs:element name="productID" type="productIDType" minOccurs="0" />
         <xs:choice minOccurs="0">
            <xs:element name="adjustmentAmountRelative" type="adjustmentAmountRelativeType" minOccurs="0" />
            <xs:element name="dedicatedAccountValueNew" type="dedicatedAccountValueType" minOccurs="0" />
         </xs:choice>
         <xs:choice minOccurs="0">
            <xs:element name="adjustmentDateRelative" type="adjustmentDateRelativeType" minOccurs="0" />
            <xs:element name="expiryDate" type="dateTimeType" minOccurs="0" />
            <xs:element name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType" minOccurs="0" />
         </xs:choice>
         <xs:choice minOccurs="0">
            <xs:element name="startDate" type="dateTimeType" minOccurs="0" />
            <xs:element name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType" minOccurs="0" />
            <xs:element name="startPamPeriodIndicator" type="startPamPeriodIndicatorType" minOccurs="0" />
         </xs:choice>
         <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0" />
         <xs:element name="expiryDateCurrent" type="dateTimeType" minOccurs="0" />
         <xs:element name="startDateCurrent" type="dateTimeType" minOccurs="0" />
         <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0" />
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="dateInformationType">
      <xs:sequence>
         <xs:choice minOccurs="0">
            <xs:element name="startDate" type="dateTimeType" minOccurs="0" />
            <xs:element name="startDateRelative" type="dateRelativeType" minOccurs="0" />
            <xs:element name="startPamPeriod" type="startPamPeriodType" />
         </xs:choice>
         <xs:choice minOccurs="0">
            <xs:element name="expiryDate" type="dateTimeType" minOccurs="0" />
            <xs:element name="expiryDateRelative" type="dateRelativeType" minOccurs="0" />
            <xs:element name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType" minOccurs="0" />
         </xs:choice>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="dateTimeInformationType">
      <xs:sequence>
         <xs:choice minOccurs="0">
            <xs:element name="startDateTime" type="dateTimeType" minOccurs="0" />
            <xs:element name="startDateTimeRelative" type="dateTimeRelativeType" minOccurs="0" />
         </xs:choice>
         <xs:choice minOccurs="0">
            <xs:element name="expiryDateTime" type="dateTimeType" minOccurs="0" />
            <xs:element name="expiryDateTimeRelative" type="dateTimeRelativeType" minOccurs="0" />
         </xs:choice>
      </xs:sequence>
   </xs:complexType>
   <xs:complexType name="startPamPeriodType">
      <xs:sequence>
         <xs:element name="startPamPeriodIndicator" type="startPamPeriodIndicatorType" />
         <xs:element name="currentTimeOffset" type="currentTimeOffsetType" minOccurs="0" />
      </xs:sequence>
   </xs:complexType>

   <!--simple type definition-->
   <xs:simpleType name="startDateType">
      <xs:restriction base="xs:dateTime"/>
   </xs:simpleType>
   <xs:simpleType name="expiryDateType">
      <xs:restriction base="xs:dateTime"/>
   </xs:simpleType>
   <xs:simpleType name="refillAmountType">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="9223372036854775807"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dateTimeType">
      <xs:restriction base="xs:dateTime"/>
   </xs:simpleType>
   <xs:simpleType name="dateRelativeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-999"/>
         <xs:maxInclusive value="999"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dateTimeRelativeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-99999999"/>
         <xs:maxInclusive value="99999999"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="numberOfDecimalsType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="15"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="associatedPartyIDType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[0-9]{1,28}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="currentTimeOffsetType">
      <xs:restriction base="xs:boolean"/>
   </xs:simpleType>
   <!--add 0 for original 6.0-->
   <xs:simpleType name="offerIDType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="**********" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="offerTypeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="7"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="productIDType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="**********" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="productOfferingNameType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[A-Za-z0-9\-_+ ]{1,128}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="pamServiceIDType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="99"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="originHostNameType">
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:maxLength value="255"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="serviceProviderType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1"/>
         <xs:maxInclusive value="9"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterNameType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[A-Za-z0-9\-_]{1,128}"/>
      </xs:restriction>
   </xs:simpleType>
   <!--extended alphanumeric-->
   <xs:simpleType name="treeParameterValueStringType">
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:maxLength value="128"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterValueNumberType">
      <xs:restriction base="xs:long">
         <xs:pattern value="[-]{0,1}[0-9]{1,15}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterValueIntegerType">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="-9223372036854775807" />
         <xs:maxInclusive value="9223372036854775807" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterSourceType">
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterUpdateActionType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="ADD" />
         <xs:enumeration value="DELETE" />
         <xs:enumeration value="CLEAR" />
         <xs:enumeration value="SET" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterValueDateType">
      <xs:restriction base="xs:dateTime" />
   </xs:simpleType>
   <xs:simpleType name="startPamPeriodIndicatorType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="99"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="expiryPamPeriodIndicatorType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="**********"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dedicatedAccountIDType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1" />
         <xs:maxInclusive value="**********" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dedicatedAccountValueType">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="9223372036854775807" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dedicatedAccountUnitTypeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="6" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="adjustmentAmountRelativeType">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="-9223372036854775807" />
         <xs:maxInclusive value="9223372036854775807" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="adjustmentDateRelativeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-32767" />
         <xs:maxInclusive value="32767" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="adjustmentStartDateRelativeType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-999" />
         <xs:maxInclusive value="999" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="updateActionType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="EXPIRE" />
      </xs:restriction>
   </xs:simpleType>
   <!--duplicated with the one in subscription-->
   <!--xs:simpleType name="attributeNameType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[A-Za-z0-9  ]{1,128}"/>
      </xs:restriction>
   </xs:simpleType-->
   <xs:simpleType name="attributeNameType">
       <xs:restriction base="xs:string">
         <xs:pattern value="[A-Za-z0-9\-_]{1,128}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="attributeValueStringType">
      <xs:restriction base="xs:string">
         <xs:minLength value="1" />
         <xs:maxLength value="128" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="attributeUpdateActionType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="ADD" />
         <xs:enumeration value="DELETE" />
         <xs:enumeration value="CLEAR" />
         <xs:enumeration value="SET" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="attributeValueNumberType">
      <xs:restriction base="xs:long">
         <xs:pattern value="[-]{0,1}[0-9]{1,15}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="attributeValueIntegerType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-2147483648" />
         <xs:maxInclusive value="**********" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="attributeValueDateType">
      <xs:restriction base="xs:dateTime"/>
   </xs:simpleType>
   <xs:simpleType name="attributeSourceType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1"/>
         <xs:maxInclusive value="255"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="usageCounterResourceConnectedType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="true"/>
         <xs:enumeration value="1"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="dedicatedAccountResourceConnectedType">
      <xs:restriction base="xs:string">
         <xs:enumeration value="true"/>
         <xs:enumeration value="1"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="clearedValueType">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="-9223372036854775807"/>
         <xs:maxInclusive value="9223372036854775807"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledFrequencyType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1" />
         <xs:maxInclusive value="7" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledIntervalType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1" />
         <xs:maxInclusive value="99999" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledMonthType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1" />
         <xs:maxInclusive value="12" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledDayOfMonthType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="-1" />
         <xs:maxInclusive value="31" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledDayOfWeekType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1" />
         <xs:maxInclusive value="7" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledHourType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="23" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledMinuteType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="59" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="scheduledSecondType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0" />
         <xs:maxInclusive value="59" />
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterValueDayOfWeekType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="1"/>
         <xs:maxInclusive value="7"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="treeParameterStartDateType">
      <xs:restriction base="dateTimeType"/>
   </xs:simpleType>
   <xs:simpleType name="treeParameterEndDateType">
      <xs:restriction base="dateTimeType"/>
   </xs:simpleType>
   <xs:simpleType name="treeParameterValueTimeType">
      <xs:restriction base="xs:string">
         <xs:pattern value="(0\d{1}|1\d{1}|2[0-3]):[0-5]\d{1}:([0-5]\d{1})"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="externalProductIDType">
      <xs:restriction base="xs:string">
         <xs:pattern value="(EXT.{1,52})"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:complexType name="aggregatedBalanceInformationCommonType">
      <xs:sequence>
         <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
         <xs:element name="totalBalance1" type="balanceTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="totalBalance2" type="balanceTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="totalActiveBalance1" type="balanceTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="totalActiveBalance2" type="balanceTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0"/>
         <xs:element name="closestExpiryDateTime" type="dateTimeType" minOccurs="0"/>
         <xs:element name="closestExpiryValue1" type="closestExpiryValueTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="closestExpiryValue2" type="closestExpiryValueTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="closestAccessibleDateTime" type="dateTimeType" minOccurs="0"/>
         <xs:element name="closestAccessibleValue1" type="closestAccessibleValueTypeForAggregatedBal" minOccurs="0"/>
         <xs:element name="closestAccessibleValue2" type="closestAccessibleValueTypeForAggregatedBal" minOccurs="0"/>
      </xs:sequence>
   </xs:complexType>
   <xs:simpleType name="balanceTypeForAggregatedBal">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="9223372036854775807"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="closestExpiryValueTypeForAggregatedBal">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="-9223372036854775807"/>
         <xs:maxInclusive value="9223372036854775807"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="closestAccessibleValueTypeForAggregatedBal">
      <xs:restriction base="xs:long">
         <xs:minInclusive value="-9223372036854775807"/>
         <xs:maxInclusive value="9223372036854775807"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="bundleIDType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="offerServiceIDType">
    <xs:restriction base="xs:string">
      <xs:minLength value="0"/>
      <xs:maxLength value="128"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="bundleInstanceIDType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="externalBundleProductIDType">
    <xs:restriction base="xs:string">
      <xs:minLength value="1"/>
      <xs:maxLength value="128"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>