<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Header>
        <Security xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <UsernameToken>
                <Username>${#Project#OUA_SOAP_USER}</Username>
                <Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${#Project#OUA_SOAP_TOKEN}</Password>
            </UsernameToken>
        </Security>
    </soapenv:Header>
    <soapenv:Body>

<ns12:createAndStartRequestByValueRequest xmlns="http://ossj.org/xml/Common/v1-5" xmlns:ns1="http://xmlns.aua.oss.amdocs.com/requestcontroller" xmlns:ns10="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:ns11="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:ns12="http://ossj.org/xml/OrderManagement/v1-0" xmlns:ns13="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:ns14="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:ns15="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:ns16="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:ns17="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:ns18="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:ns19="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:ns2="http://amdocs/core/ossj-Inventory/dat/3" xmlns:ns20="http://ossj.org/xml/Inventory/v1-2" xmlns:ns21="http://ossj.org/xml/Common-CBEProductOffering/v1-5" xmlns:ns22="http://ossj.org/xml/Common-CBEReport/v1-5" xmlns:ns23="http://ossj.org/xml/Common-CBEUser/v1-5" xmlns:ns3="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:ns4="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:ns5="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:ns6="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:ns7="http://amdocs/core/ossj-Common/dat/3" xmlns:ns8="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:ns9="http://ossj.org/xml/Common-CBEDatatypes/v1-5" xmlns:rc="http://xmlns.aua.oss.amdocs.com/requestcontroller">
    <ns12:requestValue xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns18:ServiceOrderValue">
        <key xsi:type="ns18:ServiceOrderKey">
            <type>Service</type>
            <primaryKey>
                <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">Resume-Wireless-${=java.util.UUID.randomUUID()}</primaryKey>
            </primaryKey>
        </key>
        <ns18:priority_Request>3</ns18:priority_Request>
        <ns12:serviceOrderItems>
            <ns12:item xsi:type="ns18:ServiceOrderItemValue">
                <ns8:action>resume</ns8:action>
                <ns12:service xsi:type="ns5:ServiceValue">
                    <key xsi:type="ns5:ServiceKey">
                        <type>Service</type>
                        <primaryKey>
                            <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">75ad4ef4-944d-4023-934e-47b75c8f2f05</primaryKey>
                        </primaryKey>
                    </key>
                    <ns3:describedBy xsi:type="ns7:ArrayOfCharacteristicValue">
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>market</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">TNK</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>acctStatus</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>subMarket</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">050</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>terminatingClassOfService</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">2</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>originatingClassOfService</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">WLS</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>resubmitMode</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">failed</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>operator</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">BSSe</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>roamingRestriction</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">INTL</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>gprsRoamingRestriction</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">DREP_INTL</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>iccid</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">89019504330000127551</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>billingAccountNumber</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">************</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>dcap</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">${#Project#dcaps}</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>orderCreationDate</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">2024-08-13T10:51:25.890Z</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>priority</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">3</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>msisdn</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">${#Project#MSISDN}</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>imsi</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">***************</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>accountType</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">I</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>hssGroupId</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">LBL</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>hssId</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">HC7B</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>orderSubmissionDate</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">2024-08-18T15:30:30Z</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>paymentType</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">PO</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>imei</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">123456789012590</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>chfGroupId</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">20</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>sliceServiceType</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">1</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>voiceMail</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>wifiCalling</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>ir94VideoCalls</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">false</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>sms</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns7:values>
                        </ns3:item>
                        <ns3:item xsi:type="ns7:CharacteristicValue">
                            <ns3:characteristic>mms</ns3:characteristic>
                            <ns7:values xmlns:xs="http://www.w3.org/2001/XMLSchema" xsi:type="xs:string">true</ns7:values>
                        </ns3:item>
                    </ns3:describedBy>
                    <ns5:serviceType>Mobility</ns5:serviceType>
                    <ns5:activationTargets>
                        <ns7:item>
                            <ns7:id>Activation</ns7:id>
                        </ns7:item>
                    </ns5:activationTargets>
                </ns12:service>
                <ns18:subAction />
            </ns12:item>
        </ns12:serviceOrderItems>
          <dox-om:maxResponseWaitTime xmlns:dox-om="http://amdocs/core/ossj-OrderManagement/dat/3">420</dox-om:maxResponseWaitTime>
        <ns18:operator>BSSe</ns18:operator>
        <ns18:resubmitMode>failed</ns18:resubmitMode>
    </ns12:requestValue>
</ns12:createAndStartRequestByValueRequest>

    </soapenv:Body>
</soapenv:Envelope>