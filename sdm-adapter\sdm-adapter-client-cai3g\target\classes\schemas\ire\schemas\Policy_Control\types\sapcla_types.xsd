<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/SAPC/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!-- SAPC types - PG - Provisioning Interface for SAPC data, EAB/K-10:1883 
		Uen, PB13 -->
	<xs:simpleType name="pcSubscriberIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="32" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcTrafficIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSubscribedServiceType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcBlacklistServiceType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcFamilyIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="32" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGroupIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGroupPriorityType">
		<xs:restriction base="xs:nonNegativeInteger" />
	</xs:simpleType>
	<xs:simpleType name="pcGroupStartDateType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGroupEndDateType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGroupDurationType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGroupInstancesContractedType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="2147483647" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcNotificationSMSType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcNotificationEmailType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcLimitUsageType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="2680" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcAccumulatedDataType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="65536" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcQoSProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentFilteringProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcCustomerIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcRoutingProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSubscriberChargingProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSubscriberChargingProfileNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMaxBearerQoSProfileNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMinBearerQoSProfileNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcServicestoRedirectType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcGeneralProfileNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSubscriberChargingSystemNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMpsProfileNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcFamilyDescriptionType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcUePolicyProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcServiceAreaRestrictionProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMaxAllowedTAsType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="-2147483648" />
			<xs:maxInclusive value="2147483647" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSpIdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="256" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPresenceAreaNameType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="16777215" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPdnGwNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcEnableMascType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="pcEventTriggersType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="23" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcOperatorSpecificInfoDataType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="pcDataplanNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcDescriptionType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcNotificationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="sms"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMaxBearerQosProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcMinBearerQosProfileIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentFilteringType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPresenceReportingAreaNameType">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPdnGwListNameType">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcRedirectType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="pcDeniedContentType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcUsageLimitsType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="2680" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcResourceNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContextNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPolicyType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcEventTriggerType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPolicyNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcRuleCombiningAlgorithmType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="permit-overrides" />
			<xs:enumeration value="deny-overrides" />
			<xs:enumeration value="single-match" />
			<xs:enumeration value="multiple-match" />
			<xs:enumeration value="all-permit" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcRuleType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPccRuleIdType">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:simpleType name="pcPccRuleTypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcAdcRuleTypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcAdcRuleNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPccRuleNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcTdfAppIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcPrecedenceType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="31" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcFlowNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcFlowStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="enableUplink" />
			<xs:enumeration value="enableDownlink" />
			<xs:enumeration value="enable" />
			<xs:enumeration value="disable" />
			<xs:enumeration value="removed" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSourceIpAddrType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcSourcePortType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcDestIpAddrType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcDestPortType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcProtocolType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcDirectionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ul" />
			<xs:enumeration value="dl" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentQosProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentChargingProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentAdcRedirectProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentAdcMuteNotificationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="muted" />
			<xs:enumeration value="unmuted" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcContentMonitoringKeyType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pcAggregableType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="pcDefQosFlowIndicationType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="pcGlobalScopeType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
    <xs:simpleType name="pcOngoingSessionType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="65536" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="pcClosedSessionType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="65536" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="pcDefaultPriorityType">
        <xs:restriction base="xs:integer">
	    	<xs:minInclusive value="0" />
	    	<xs:maxInclusive value="2147483647" />
		</xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="pcInstancesContractedType">
        <xs:restriction base="xs:integer">
	    	<xs:minInclusive value="0" />
	    	<xs:maxInclusive value="2147483647" />
		</xs:restriction>
    </xs:simpleType>
</xs:schema>
