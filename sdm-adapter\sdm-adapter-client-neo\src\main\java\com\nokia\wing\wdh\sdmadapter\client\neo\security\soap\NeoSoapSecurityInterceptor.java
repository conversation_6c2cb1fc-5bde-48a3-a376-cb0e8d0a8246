package com.nokia.wing.wdh.sdmadapter.client.neo.security.soap;

import com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties;
import com.nokia.wing.wdh.sdmadapter.client.neo.security.oauth.AuthenticationStrategy;
import com.nokia.wing.wdh.sdmadapter.client.neo.security.oauth.NeoOAuthTokenManager;
import com.nokia.wing.wdh.sdmadapter.client.neo.exception.AuthenticationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.context.MessageContext;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.saaj.SaajSoapMessage;

import javax.xml.namespace.QName;
import jakarta.xml.soap.*;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

/**
 * Spring WebService SOAP Security Interceptor for NEO OSSJ adapter.
 *
 * This interceptor adds security headers to outbound SOAP messages:
 * - OAuth Bearer tokens in WS-Security Password field (as per user preferences)
 * - WS-Security UsernameToken headers
 * - WS-Security Timestamp headers (optional)
 *
 * This interceptor works with Spring's WebServiceTemplate, unlike the JAX-WS handler.
 * Updated to use AuthenticationStrategy for flexible authentication methods.
 */
@Slf4j
public class NeoSoapSecurityInterceptor implements ClientInterceptor {

    private static final String WSSE_NAMESPACE = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd";
    private static final String WSU_NAMESPACE = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd";
    private static final String PASSWORD_TEXT_TYPE = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText";

    private final AuthenticationStrategy authenticationStrategy;
    private final NeoClientProperties properties;

    // Backward compatibility - keep reference to token manager for legacy usage
    private final NeoOAuthTokenManager tokenManager;
    
    /**
     * Constructor using AuthenticationStrategy (preferred).
     *
     * @param authenticationStrategy Authentication strategy for obtaining tokens
     * @param properties NEO client properties
     */
    public NeoSoapSecurityInterceptor(AuthenticationStrategy authenticationStrategy,
                                     NeoClientProperties properties) {
        this.authenticationStrategy = authenticationStrategy;
        this.properties = properties;
        this.tokenManager = null; // Not used with strategy pattern
        log.info("SOAP Security Interceptor initialized with authentication strategy: {}",
                authenticationStrategy.getAuthenticationMethod());
    }

    /**
     * Constructor using NeoOAuthTokenManager (backward compatibility).
     *
     * @param tokenManager OAuth token manager
     * @param properties NEO client properties
     * @deprecated Use constructor with AuthenticationStrategy instead
     */
    @Deprecated
    public NeoSoapSecurityInterceptor(NeoOAuthTokenManager tokenManager,
                                     NeoClientProperties properties) {
        this.tokenManager = tokenManager;
        this.properties = properties;
        this.authenticationStrategy = null; // Not used with legacy approach
        log.warn("SOAP Security Interceptor initialized with deprecated NeoOAuthTokenManager. " +
                "Consider migrating to AuthenticationStrategy.");
    }
    
    @Override
    public boolean handleRequest(MessageContext messageContext) throws WebServiceClientException {
        if (messageContext.getRequest() instanceof SoapMessage) {
            SoapMessage soapMessage = (SoapMessage) messageContext.getRequest();
            addSecurityHeaders(soapMessage);
        }
        return true;
    }
    
    @Override
    public boolean handleResponse(MessageContext messageContext) throws WebServiceClientException {
        // No processing needed for responses
        return true;
    }
    
    @Override
    public boolean handleFault(MessageContext messageContext) throws WebServiceClientException {
        // No processing needed for faults
        return true;
    }
    
    @Override
    public void afterCompletion(MessageContext messageContext, Exception ex) throws WebServiceClientException {
        // No cleanup needed
    }
    
    /**
     * Adds security headers to outbound SOAP messages.
     */
    private void addSecurityHeaders(SoapMessage soapMessage) {
        try {
            // Cast to SaajSoapMessage to access underlying SOAP API
            SaajSoapMessage saajSoapMessage = (SaajSoapMessage) soapMessage;
            SOAPMessage soapMsg = saajSoapMessage.getSaajMessage();
            SOAPPart soapPart = soapMsg.getSOAPPart();
            SOAPEnvelope soapEnvelope = soapPart.getEnvelope();
            SOAPHeader soapHeader = soapEnvelope.getHeader();

            if (soapHeader == null) {
                soapHeader = soapEnvelope.addHeader();
            }

            // Add WS-Security if enabled
            if (properties.getSoapSecurity().getEnableWsUsernameToken()) {
                addWsSecurityHeader(soapHeader);
                log.debug("Added WS-Security UsernameToken to SOAP header");
            }

            soapMsg.saveChanges();
            log.debug("Security headers added to SOAP message");

        } catch (Exception e) {
            log.error("Failed to add security headers to SOAP message", e);
            throw new RuntimeException("SOAP security header configuration failed", e);
        }
    }
    
    /**
     * Adds WS-Security headers including UsernameToken and optional Timestamp.
     *
     * UPDATED: Modified to match expected format:
     * - No namespace prefixes on child elements
     * - No mustUnderstand attribute
     * - Namespace declared only on Security element
     */
    private void addWsSecurityHeader(SOAPHeader soapHeader) throws SOAPException {
        // Add Security element with namespace declaration but no prefix on child elements
        SOAPElement securityElement = soapHeader.addChildElement("Security", "", WSSE_NAMESPACE);
        // Removed mustUnderstand attribute to match expected format

        // Add UsernameToken
        addUsernameToken(securityElement);

        // Add Timestamp if enabled (disabled by default to match expected format)
        if (properties.getSoapSecurity().getEnableWsTimestamp()) {
            addTimestamp(securityElement);
        }

        log.debug("Added WS-Security headers to SOAP message (format: no prefixes, no mustUnderstand)");
    }
    
    /**
     * Adds WS-Security UsernameToken element.
     * Uses OAuth token as password instead of configured password (as per user preferences).
     *
     * UPDATED: Modified to match expected format - no namespace prefixes on child elements.
     */
    private void addUsernameToken(SOAPElement securityElement) throws SOAPException {
        // Add UsernameToken element without namespace prefix
        SOAPElement usernameTokenElement = securityElement.addChildElement("UsernameToken");

        // Add Username without namespace prefix
        SOAPElement usernameElement = usernameTokenElement.addChildElement("Username");
        usernameElement.addTextNode(properties.getSoapSecurity().getUsername());

        // Add Password without namespace prefix - use OAuth access token (as per user preferences)
        SOAPElement passwordElement = usernameTokenElement.addChildElement("Password");
        passwordElement.setAttribute("Type", PASSWORD_TEXT_TYPE);

        // Get access token using appropriate authentication method
        String accessToken = getAccessToken();
        passwordElement.addTextNode(accessToken);

        log.debug("Added WS-Security UsernameToken with OAuth access token as password (no namespace prefixes)");
    }

    /**
     * Gets access token using the appropriate authentication method.
     * Supports both new AuthenticationStrategy and legacy NeoOAuthTokenManager.
     *
     * @return Valid access token
     * @throws AuthenticationException if token acquisition fails
     */
    private String getAccessToken() throws AuthenticationException {
        if (authenticationStrategy != null) {
            // Use new authentication strategy (preferred)
            return authenticationStrategy.getAccessToken();
        } else if (tokenManager != null) {
            // Use legacy token manager for backward compatibility
            try {
                return tokenManager.getAccessToken();
            } catch (Exception e) {
                throw new AuthenticationException("Failed to get access token from legacy token manager", e);
            }
        } else {
            throw new AuthenticationException("No authentication method configured");
        }
    }
    
    /**
     * Adds WS-Security Timestamp element.
     */
    private void addTimestamp(SOAPElement securityElement) throws SOAPException {
        // Add Timestamp element
        SOAPElement timestampElement = securityElement.addChildElement("Timestamp", "wsu", WSU_NAMESPACE);

        Instant now = Instant.now();
        Instant expires = now.plusSeconds(300); // 5 minutes validity

        // Add Created timestamp
        SOAPElement createdElement = timestampElement.addChildElement("Created", "wsu");
        createdElement.addTextNode(DateTimeFormatter.ISO_INSTANT.format(now));

        // Add Expires timestamp
        SOAPElement expiresElement = timestampElement.addChildElement("Expires", "wsu");
        expiresElement.addTextNode(DateTimeFormatter.ISO_INSTANT.format(expires));

        log.debug("Added WS-Security Timestamp to SOAP message");
    }
}
