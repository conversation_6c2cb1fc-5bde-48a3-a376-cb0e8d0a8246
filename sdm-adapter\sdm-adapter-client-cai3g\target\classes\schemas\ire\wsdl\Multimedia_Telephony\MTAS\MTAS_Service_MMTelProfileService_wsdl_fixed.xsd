<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:mmtel-profile-serv="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelProfileService/" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelProfileService/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelProfileService/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:annotation>
		<xs:documentation xml:lang="en">
      MTAS17A/MTASv 1.0          since MA16.2 CP1 
          updated:
            user-common-data with subscription
          added:
          	dialog-event-notifier
          
          MTAS14B          since MA7.1 CP3 (PC Verizon)
          updated:
          communication-distribution by adding in-sip-request
          malicious-communication-identification-type by update mcid-mode adding inactive
          malicious-communication-identification-type by adding mcid-orig-mode
          session-transfer-to-own-device by adding in-sip-request
          user-common-data by adding auto-answer-avoidance-condition
          user-common-data by adding in-sip-request-condition
          user-common-data by adding  in-sip-request-condition-list
          user-common-data by adding auto-answer-avoidance

      MTAS 14A		   since MA7.1 CP2
        Added:
          distinctive-ring
          number-portability-announcement
          validate
        Updated:
          user-common-data by adding mmtel-charging-profile
          communication-distribution by adding served-identity-condition and served-identity
          communication-diversion by adding served-identity-condition and served-identity
          incoming-communication-barring by adding served-identity-condition and served-identity
          outgoing-communication-barring by adding served-identity-condition and served-identity
          flexible-identity-presentation by adding msn-fip-identity
          identityPresentationModeType in originating-identity-presentation and terminating-identity-presentation by adding ad-hoc-temporary-presentation-restricted and ad-hoc-temporary-presentation-not-restricted
          Following services shall support "nillable = true" of operator configuration:
            abbreviated-dialing
            calling-name-identity-presentation
            communication-distribution
            communication-diversion
            communication-diversion-no-answer-timer
            communication-waiting
            flexible-identity-presentation
            hotline
            incoming-communication-barring
            originating-identity-presentation
            originating-identity-presentation-restriction
            outgoing-barring-programs
            outgoing-communication-barring
            terminating-identity-presentation
            terminating-identity-presentation-restriction
            user-common-data
      MTAS 13B         since EMA7.0 CP3:
        Added Closed User Group service
        Update: Communication Completion, by adding CCNL
        Update: User Common Data, by adding Time-Zone-Area
      MTAS 13A         since EMA7.0 CP1
        Corrected Call Return service
        Added Call Return service
        Added Originatig Calling Name Identity Presentation service
        Added Hot Line service
        Updated : Communication Distribution, by adding
          user-no-reply-timer
          fcd-op-conditions
          fcd-op-actions
        Updated: Northbound Call Control, by adding
          px-originating-trigger
          px-terminating-trigger
        Updated: Common Data, by adding
          display-name
        Updated: User Common Data, by adding
          home-location
      MTAS12A/B        since EMA7.0
        Added the following services:
          Customized Alerting Tones service
          Scheduled conference
          Service Number
          Flexible identity presentation
          Customized Alerting Tones
          Northbound call control
          Session transfer to own device
      MTAS11A/B        since EMA6.3
        Update: Common Data, by adding support of "MMTel service profile"
        added session-transfer-to-own-device
        added explicit-communication-transfer
        Added calling-party-category
        added user-common-data
        Added malicious-communication-rejection
        Commented out keyref definitions not supported by the MTAS toolset
      MTAS3.1          since EMA6.0
        Added communication-distribution
        Added dynamic-black-list
        advice-of-charge
        carrier-select-rn
        carrier-pre-select-rn
        call-completion
        call-completion-monitor-opt-out
        call-admission-control-group-membership
        user-call-admission-control
        added priority-call, three-pty, common-data
        added abbreviated-dialing
        added malicious-communication-identification
        the following new services are added:
          call-waiting
          calling-name-identity-presentation
          carrier-pre-select
          carrier-select
          communication-diversion-no-answer-timer
          dial-tone-management
          malicious-communication-identification
          operator-controlled-outgoing-barring-programs
          voice-mail
        Remove
          malicious-communication-identification
        Renamed call-waiting to communication-waiting
      </xs:documentation>
	</xs:annotation>
	<xs:element name="publicId" type="publicIdentityType"/>
	<xs:element name="createService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Creating MMTel Profile Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType"/>
				<xs:element name="validate" type="xs:boolean" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
                            The validate is used when the Create/Set request must be validated but not stored in the HSS.
                        </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="services" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must be maintained all new services shall be optional and inserted in alphabetical order within the existing list where possible
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="abbreviated-dialing" type="abbreviated-dialing-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The abbreviated dialing service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="advice-of-charge" type="advice-of-charge-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The advice-of-charge service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-admission-control-group-membership" type="call-admission-control-group-membership-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user membership of call admission control group service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion" type="call-completion-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion-monitor-opt-out" type="call-completion-monitor-opt-out-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion monitor opt out service. This allows a subscriber to be opted out of being monitored to support call completion services to that subscriber. This is specified as an opt-out because the call completion is more valuable the more targets for which call completion is possible. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-return" type="call-return-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>The call return service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-name-identity-presentation" type="calling-name-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The calling name identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-party-category" type="calling-party-category-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Calling Party Category service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select" type="carrier-pre-select-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select-rn" type="carrier-pre-select-rn-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select rn service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select" type="carrier-select-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select-rn" type="carrier-select-rn-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select rn service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="closed-user-group" type="closed-user-group-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation> The closed user group service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="common-data" type="common-data-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">Common data available across services. This data is available to the operator
                    rather than the user. Unlike services this should never be withdrawn so it is not nillable.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-distribution" type="communication-distribution-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion" type="communication-diversion-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion-no-answer-timer" type="communication-diversion-no-answer-timer-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion no answer timer service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-waiting" type="communication-waiting-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication waiting service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="conference" type="conference-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The conference service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The customized alerting tones service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dial-tone-management" type="dial-tone-management-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dial tone management service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dialog-event-notifier" type="dialog-event-notifier-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dialog event notifier service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="distinctive-ring" type="distinctive-ring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The distinctive ring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dynamic-black-list" type="dynamic-black-list-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dynamic black list service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The explicit communication transfer service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="flexible-identity-presentation" type="mmtel-profile-serv:flexible-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The flexible identity presentation service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="hotline" type="hotline-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Hotline service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The incoming communication barring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication identification service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-rejection" type="malicious-communication-rejection-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication rejection service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="media-policy" type="media-policy-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The media policy service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="northbound-call-control" type="northbound-call-control-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Northbound Call Control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="number-portability-announcement" type="number-portability-announcement-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Number Portability (NP) Announcement service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The operator controlled outgoing barring programs service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing barring programs service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing communication-barring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The originating calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identityp resentation restriction service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="priority-call" type="priority-call-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The priority call service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="scheduled-conference" type="scheduled-conference-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The scheduled conference service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="session-transfer-to-own-device" type="session-transfer-to-own-device-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The session transfer to own device service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="supplementary-service-codes" type="supplementary-service-codes-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The supplementary service codes service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation restriction service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="three-pty" type="three-pty-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The three party service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-call-admission-control" type="user-call-admission-control-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-common-data" type="user-common-data-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="voice-mail" type="voice-mail-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The voice-mail service.</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyCreate">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="setService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Setting MMTel Profile Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="validate" type="xs:boolean" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
                            The validate is used when the Create/Set request must be validated but not stored in the HSS.
                        </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The concurrency-control element is an optional element to control concurrent updates. If present then the set request will be accepted only if the service data version is still at the value given in this element i.e. no other updates have been performed. It is of type integer.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="services" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must be maintained all new services shall be optional and inserted in alphabetical order within the existing list where possible</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="abbreviated-dialing" type="abbreviated-dialing-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The abbreviated dialing service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="advice-of-charge" type="advice-of-charge-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The advice-of-charge service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-admission-control-group-membership" type="call-admission-control-group-membership-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user membership of call admission control group service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion" type="call-completion-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion-monitor-opt-out" type="call-completion-monitor-opt-out-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion monitor opt out service. This allows a subscriber to be opted out of being monitored to support call completion services to that subscriber. This is specified as an opt-out because the call completion is more valuable the more targets for which call completion is possible. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-return" type="call-return-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>The call return service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-name-identity-presentation" type="calling-name-identity-presentation-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-party-category" type="calling-party-category-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Calling Party Category service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select" type="carrier-pre-select-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select-rn" type="carrier-pre-select-rn-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select rn service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select" type="carrier-select-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select-rn" type="carrier-select-rn-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select rn service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="closed-user-group" type="closed-user-group-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation> The closed user group service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="common-data" type="common-data-type" nillable="false" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">Common data available across services. This data is available to the operator
                    rather than the user. Unlike services this should never be withdrawn so it is not nillable.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-distribution" type="communication-distribution-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion" type="communication-diversion-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion-no-answer-timer" type="communication-diversion-no-answer-timer-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion no answer timer service. Use xsi:nil="true" to
                    withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-waiting" type="communication-waiting-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication waiting service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="conference" type="conference-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The conference service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The customized alerting tones service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dial-tone-management" type="dial-tone-management-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dial tone management service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dialog-event-notifier" type="dialog-event-notifier-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dialog event notifier service. Use xsi:nil=”true” to withdraw the entire service.
									</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="distinctive-ring" type="distinctive-ring-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The distinctive ring service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dynamic-black-list" type="dynamic-black-list-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dynamic black list service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The explicit communication transfer service. Use xsi:nil="true" to withdraw
                    the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="flexible-identity-presentation" type="flexible-identity-presentation-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The flexible identity presentation service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="hotline" type="hotline-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Hotline service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The incoming communication barring service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication identification service. Use xsi:nil="true" to
                    withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-rejection" type="malicious-communication-rejection-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication rejection service. Use xsi:nil="true" to withdraw
                    the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="media-policy" type="media-policy-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The media policy service. Use xsi:nil="true" to withdraw
                    the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="northbound-call-control" type="northbound-call-control-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Northbound Call Control service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="number-portability-announcement" type="number-portability-announcement-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Number Portability (NP) Announcement service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The operator controlled outgoing barring programs service. Use xsi:nil="true"
                    to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing barring programs service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing communication-barring service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The originating calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identity presentation service. Use xsi:nil="true" to withdraw
                    the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identityp resentation restriction service. Use xsi:nil="true"
                    to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="priority-call" type="priority-call-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The priority call service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="scheduled-conference" type="scheduled-conference-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The scheduled conference service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="session-transfer-to-own-device" type="session-transfer-to-own-device-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The session transfer to own device service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="supplementary-service-codes" type="supplementary-service-codes-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The supplementary service codes service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation service. Use xsi:nil="true" to withdraw
                    the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation restriction service. Use xsi:nil="true"
                    to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="three-pty" type="three-pty-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The three party service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-call-admission-control" type="user-call-admission-control-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-common-data" type="user-common-data-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="voice-mail" type="voice-mail-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The voice-mail service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeySet">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="getResponseService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Getting MMTel Profile Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType">
					<xs:annotation>
						<xs:documentation xml:lang="en">The default public user identity for the subscriber</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The concurrency-control element is an optional element to control concurrent
              updates. If present then the set request will be accepted only if the service data version is still at the value given in this
              element i.e. no other updates have been performed. It is of type integer.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="services" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must be maintained all
              new services shall be optional and inserted in alphabetical order within the existing list where possible
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<!-- the relative order of the existing services must be maintained -->
							<!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
							<xs:element name="abbreviated-dialing" type="abbreviated-dialing-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The abbreviated dialing service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="advice-of-charge" type="advice-of-charge-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The advice-of-charge service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-admission-control-group-membership" type="call-admission-control-group-membership-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user membership of call admission control group service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion" type="call-completion-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-completion-monitor-opt-out" type="call-completion-monitor-opt-out-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The call completion monitor opt out service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="call-return" type="call-return-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>The call return service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-name-identity-presentation" type="calling-name-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The calling name identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="calling-party-category" type="calling-party-category-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Calling Party Category service. Use xsi:nil="true" to withdraw the entire
                    service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select" type="carrier-pre-select-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-pre-select-rn" type="carrier-pre-select-rn-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier pre-select rn service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select" type="carrier-select-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="carrier-select-rn" type="carrier-select-rn-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The carrier select rn service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="closed-user-group" type="closed-user-group-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation> The closed user group service. Use xsi:nil="true" to withdraw the entire service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="common-data" type="common-data-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">Common data available across services.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-distribution" type="communication-distribution-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion" type="communication-diversion-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-diversion-no-answer-timer" type="communication-diversion-no-answer-timer-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication diversion no answer timer service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="communication-waiting" type="communication-waiting-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The communication waiting service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="conference" type="conference-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The conference service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The customized alerting tones service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dial-tone-management" type="dial-tone-management-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dial tone management service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dialog-event-notifier" type="dialog-event-notifier-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dialog event notifier service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="distinctive-ring" type="distinctive-ring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The distinctive ring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="dynamic-black-list" type="dynamic-black-list-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The dynamic black list service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The explicit communication transfer service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="flexible-identity-presentation" type="flexible-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The flexible identity presentation service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="hotline" type="hotline-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Hotline service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The incoming communication barring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication identification service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="malicious-communication-rejection" type="malicious-communication-rejection-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The malicious communication rejection service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="media-policy" type="media-policy-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The media policy service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="northbound-call-control" type="northbound-call-control-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The Northbound Call Control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="number-portability-announcement" type="number-portability-announcement-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The Number Portability (NP) Announcement service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The operator controlled outgoing barring programs service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing barring programs service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The outgoing communication-barring service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
                    The originating calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The originating identityp resentation restriction service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="priority-call" type="priority-call-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The priority call service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="session-transfer-to-own-device" type="session-transfer-to-own-device-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The session transfer to own device service. Use xsi:nil="true" to withdraw the
                    entire service.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="supplementary-service-codes" type="supplementary-service-codes-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The supplementary service codes service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The terminating identity presentation restriction service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="three-pty" type="three-pty-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The three party service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-call-admission-control" type="user-call-admission-control-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="user-common-data" type="user-common-data-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The user call admission control service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="voice-mail" type="voice-mail-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The voice-mail service.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="scheduled-conference" type="scheduled-conference-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The scheduled conference service.</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyGetResp">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:simpleType name="publicIdentityType">
		<xs:restriction base="xs:anyURI">
			<xs:pattern value="sip:.*"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?comon_types? -->
	<xs:simpleType name="activatedType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="activated"/>
			<xs:enumeration value="deactivated"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ccMonitorQueueSizeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="15"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="empty-element-type"/>
	<xs:complexType name="identity-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The identity element is a grouping element for conditions which are based on a user's
        identity. The condition is satisfied if any of the one or many elements within it is matched.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="one" type="one-type" nillable="true">
					<xs:annotation>
						<xs:documentation xml:lang="en">The one element specifies an individual identity to be matched. The one element is a
              sub-MO allowing multiple instances with "id" as the unique key.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="many" type="many-type" nillable="true">
					<xs:annotation>
						<xs:documentation xml:lang="en">The many element specifies a match for a set of identities. The many element is a
              sub-MO allowing multiple instances with "domain" as the unique key
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="number-match" type="number-match-type" nillable="true">
					<xs:annotation>
						<xs:documentation xml:lang="en">The number-match element specifies a match for a set of numerical identities. The
              number-match element is a sub-MO allowing multiple instances with "starts-with" as the unique key.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'id' -->
	<xs:complexType name="one-type">
		<xs:sequence>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The individual identity to be matched. This takes the form of a normalized sip: or
            tel: URI.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:anyURI" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="many-type">
		<xs:sequence>
			<xs:element name="domain" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The individual domain to be matched. A many element with an explicit domain value
            matches all identities within that domain. A many element with the special wildcard value "*" matches all identities.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice minOccurs="0" maxOccurs="unbounded">
				<xs:element name="except-domain" type="except-domain-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">An individual domain to be excluded from a many with special value "*" that would
              otherwise match all identities. The except-domain element is a sub-MO allowing multiple instances with "domain" as the unique
              key.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="except-id" type="except-id-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">An individual identity to be excluded from the identities matching the enclosing
              many. The except-id element is a sub-MO allowing multiple instances with "id" as the unique key.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="domain" type="xs:string" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="domainAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'id' -->
	<xs:complexType name="except-id-type">
		<xs:sequence>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The individual identity to be excluded from the match. If this is within a many
            element with a specific domain then the excluded identity must be a normalized sip: URI within that domain. If this is within a
            many element with the special wildcard value of "*", then it can be any normalized sip: or tel: URI
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:anyURI" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'domain' -->
	<xs:complexType name="except-domain-type">
		<xs:sequence>
			<xs:element name="domain" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The individual domain to be excluded from the match.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="domain" type="xs:string" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="domainAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="number-match-type">
		<xs:sequence>
			<xs:element name="starts-with" type="starts-with-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The first few characters of the normalised form of the number to be matched. This must
            be present on the creation of a number-match element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="starts-with" type="starts-with-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="starts-withAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="starts-with-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:pattern value="\+{0,1}\d{0,32}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="validityType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The validity element is a grouping element for time periods (intervals) within which the
        rule is valid.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence maxOccurs="unbounded">
			<xs:element name="interval" type="interval-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The interval element specifies a date and time period within which the validity
            condition is satisfied. The interval element is a sub-MO allowing multiple instances with "from" as the unique key
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="invalidityType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The validity element is a grouping element for time periods (intervals) within which the
        rule is NOT valid.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence maxOccurs="unbounded">
			<xs:element name="interval" type="interval-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The intervals element specifies a date and time periods within which the invalidity
            condition is NOT satisfied. The interval element is a sub-MO allowing multiple instances with "from" as the unique key
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- sub-MO with key attribute "from" -->
	<xs:complexType name="interval-type">
		<xs:sequence>
			<xs:element name="from" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The date and time that specifies the start of the valid interval. It is a standard
            dateTime value e.g. "2008-11-27T20:00:00Z" for a UTC time or "2008-10-12T20:00:00-08:00" for a time with 8 hours offset from
            UTC.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="until" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The date and time that specifies the end of the valid interval. It is a standard
            dateTime value e.g. "2008-11-27T20:00:00Z" for a UTC time or "2008-10-12T20:00:00-08:00" for a time with 8 hours offset from
            UTC.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="from" type="xs:dateTime" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="fromAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="call-barring-actions-type">
		<xs:sequence>
			<xs:element name="allow" type="xs:boolean">
				<xs:annotation>
					<xs:documentation xml:lang="en">The allow element has values "true" or "false". If set to "false" then any
            communications satisfying the corresponding conditions will be barred unless overridden by another rule with allow set to
            "true". If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. not barred.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The play-announcement element has string values from 0 to 32 characters. When the
              play-announcement action is set with the string value containing characters with the length between 1 to 32, if there is any
              communications satisfying the corresponding conditions and being barred (allow=false), the caller will be presented with the
              announcement associated with the announcement code pointed by the string value. When the play-announcement action is set with
              the string value containing character with the length of 0, any play-announcement action element in the rule will be deleted
              from the rule.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="play-segmented-announcement" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">If there is any communications satisfying the corresponding conditions, the caller
              will be presented with the segmented announcement associated with the announcement code pointed by the "announcement-name"
              attribute of the element. Before trying to invoke any, the segmented (generic) announcement must be configured in MTAS with
              the same name as given in the "announcement-name" attribute. The segmented announcement may contain contain embedded
              variables,
              which can be presented in the "announcement-variable" child element. The configured segmented (generic)
              announencement shall contain as many standalone voice variable segments as many "announcement-variable" child elements are
              defined for the "play-segmented-announcement" action. The keyed "play-segmented-announcement" action with the
              "announcement-name" attribute can be deleted from the list of actions by setting the "xs:nil" attribute to true.
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="play-segmented-announcement-type">
								<xs:attribute name="announcement-name" type="announcement-name-type" use="required">
									<xs:annotation>
										<xs:appinfo>
											<jaxb:property name="announcement-nameAttr"/>
										</xs:appinfo>
									</xs:annotation>
								</xs:attribute>
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
					<xs:key name="AnnouncementVariableNameKey_IncomingCommunicationBarring">
						<xs:annotation>
							<xs:documentation xml:lang="en">An announcement variable can be embedded into a segmented announcemet only once.
                Announcement variables, under the scope of a segmented announcement, are made unique by the "variable-name" attribute.
              </xs:documentation>
						</xs:annotation>
						<xs:selector xpath="./announcement-variable"/>
						<xs:field xpath="@variable-name"/>
					</xs:key>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-barring-actions-type-incoming">
		<xs:sequence>
			<xs:element name="allow" type="xs:boolean">
				<xs:annotation>
					<xs:documentation xml:lang="en">The allow element has values "true" or "false". If set to "false" then any
            communications satisfying the corresponding conditions will be barred unless overridden by another rule with allow set to
            "true". If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. not barred.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="do-not-disturb" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The do-not-disturb element has values "true" and "false". If it's set to "true" the
            element is added into the actions part of the rule. If it's set to "false" the element is removed from the actions part of the
            rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The play-announcement element has string values from 0 to 32 characters. When the
              play-announcement action is set with the string value containing characters with the length between 1 to 32, if there is any
              communications satisfying the corresponding conditions and being barred (allow=false), the caller will be presented with the
              announcement associated with the announcement code pointed by the string value. When the play-announcement action is set with
              the string value containing character with the length of 0, any play-announcement action element in the rule will be deleted
              from the rule.
            </xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="play-segmented-announcement" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">If there is any communications satisfying the corresponding conditions, the caller
              will be presented with the segmented announcement associated with the announcement code pointed by the "announcement-name"
              attribute of the element. Before trying to invoke any, the segmented (generic) announcement must be configured in MTAS with
              the same name as given in the "announcement-name" attribute. The segmented announcement may contain contain embedded
              variables,
              which can be presented in the "announcement-variable" child element. The configured segmented (generic)
              announencement shall contain as many standalone voice variable segments as many "announcement-variable" child elements are
              defined for the "play-segmented-announcement" action. The keyed "play-segmented-announcement" action with the
              "announcement-name" attribute can be deleted from the list of actions by setting the "xs:nil" attribute to true.
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="play-segmented-announcement-type">
								<xs:attribute name="announcement-name" type="announcement-name-type" use="required">
									<xs:annotation>
										<xs:appinfo>
											<jaxb:property name="announcement-nameAttr"/>
										</xs:appinfo>
									</xs:annotation>
								</xs:attribute>
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
					<xs:key name="AnnouncementVariableNameKey_IncomingCommunicationBarring_Incoming">
						<xs:annotation>
							<xs:documentation xml:lang="en">An announcement variable can be embedded into a segmented announcemet only once.
                Announcement variables, under the scope of a segmented announcement, are made unique by the "variable-name" attribute.
              </xs:documentation>
						</xs:annotation>
						<xs:selector xpath="./announcement-variable"/>
						<xs:field xpath="@variable-name"/>
					</xs:key>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-barring-op-actions-type">
		<xs:sequence>
			<xs:element name="allow-true-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The allow-true-action element has values "activated" or "deactivated". When set to
            "activated" it allows the subscriber to use the allow action with the value of "true" in the associated communication barring
            rules to explicitly allow communications that match the associated conditions. With this absent or set to "deactivated" the
            subscriber is only permitted to use the allow action with the value of "false" to bar communications.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The play-announcement-action element has values "activated" or "deactivated". When set
            to "activated" it allows the subscriber to use the play-announcement action element by adding or removing the element into or
            from the rule. With this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The play-segmented-announcement-action element has values "activated" or
            "deactivated". When set to "activated" it allows the subscriber to use the play-segmented-announcement action element by adding
            or removing the element into or from the rule. With this absent or set to "deactivated" the subscriber is not permitted to use
            the play-segmented-announcement action.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-barring-op-actions-type-incoming">
		<xs:sequence>
			<xs:element name="allow-true-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The allow-true-action element has values "activated" or "deactivated". When set to
            "activated" it allows the subscriber to use the allow action with the value of "true" in the associated communication barring
            rules to explicitly allow communications that match the associated conditions. With this absent or set to "deactivated" the
            subscriber is only permitted to use the allow action with the value of "false" to bar communications.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="do-not-disturb-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The do-not-disturb-action element has values "activated" or "deactivated". When set to
            "activated" it allows the subscriber to use the do-not-disturb action in communication diversion rules to control whether the
            caller is handled by do-not-disturb service (e.g. treated with specific charging scheme, etc.)
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The play-announcement-action element has values "activated" or "deactivated". When set
            to "activated" it allows the subscriber to use the play-announcement action element by adding or removing the element into or
            from the rule. With this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The play-segmented-announcement-action element has values "activated" or
            "deactivated". When set to "activated" it allows the subscriber to use the play-segmented-announcement action element by adding
            or removing the element into or from the rule. With this absent or set to "deactivated" the subscriber is not permitted to use
            the play-segmented-announcement action.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="rule-deactivated-type">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="category-name-type">
		<xs:restriction base="xs:string">
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="multiple-program-type">
		<xs:sequence minOccurs="0" maxOccurs="16">
			<xs:element name="category-name" type="category-name-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The category-name element contains the name of a category of calls to be barred. This
            is a multi-value parameter and can appear between 0 and 16 times to cover each category of outgoing communications to be barred.
            The value of each category-name element is a string of up to 32 characters.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-return-type">
		<xs:sequence>
			<xs:element name="call-return-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the call return service that are available to the operator rather than the user. This must be present on the creation of the call-return service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the call return service. This must be present on the creation of the call-return service. If set to "profile" this user service is provisioned via a Service Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?calling_name_identity_presentation? -->
	<xs:complexType name="calling-name-identity-presentation-type">
		<xs:sequence>
			<xs:element name="cnip-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the calling name identity presentation service that
            are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the calling name identity presentation service. If set to "false" this will withdraw the user service and the cnip-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="cnip-user-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the calling name identity presentation service that
            are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be
            present if the service is provisioned i.e. cnip-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="active" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Controls whether the calling name identity presentation service is active or not
                  for this subscriber. The calling name identity presentation service requires that the user also has the originating
                  identity presentation service active.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?calling_party_category? -->
	<xs:complexType name="calling-party-category-type">
		<xs:sequence>
			<xs:element name="cpc-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the Calling Party Category service that are available
            to the operator rather than the user. This must be present on the creation of the Calling Party Category service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the calling party category service. If set to "false" this will withdraw the service from the user. This must be present on the creation of the calling party category service. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cpc-value" type="cpc-value-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">This corresponds to 3GPP TS 24.229: cpc-value = "ordinary" / "test" / "operator"
                  / "payphone" / "unknown" / "mobile-hplmn" / "mobile-vplmn" / genvalue genvalue = 1*(alphanum / "-" / "." ) When the
                  cpc-value element is set with the string value containing character with the length of 0, cpc-value element will be
                  deleted from the operator configuration.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?carrier_pre_select? -->
	<xs:complexType name="carrier-pre-select-type">
		<xs:sequence>
			<xs:element name="cps-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the carrier pre-select service that are only
            available to the operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the carrier pre-select service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="call-type-carrier" type="call-type-carrier-type" nillable="true" minOccurs="0" maxOccurs="17">
							<xs:annotation>
								<xs:documentation xml:lang="en">The call-type-carrier element specifies a mapping between a call type and the
                  carrier code to be pre-selected for calls of that type. The call-type-carrier element is a sub-MO allowing multiple
                  instances with "call-type" as the unique key.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="call-type-carrier-key">
					<xs:selector xpath="./mmtel-profile-serv:call-type-carrier"/>
					<xs:field xpath="@call-type"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="cpc-value-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="32"/>
			<xs:pattern value="([a-zA-Z0-9.\-])*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="call-type-carrier-type">
		<xs:sequence>
			<xs:element name="call-type" type="call-type-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The type of call. This is a string of between 1 and 32 characters that should match a
            call type configured into the node level configuration
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="carrier-code" type="carrier-code-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The carrier code to be use for a call of the given type. This is a string beginning
            with a '+' and followed by between 3 and 8 digits.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="call-type" type="call-type-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="call-typeAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="carrier-code-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\+\d{3,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="call-type-name-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?carrier-select? -->
	<xs:complexType name="carrier-select-type">
		<xs:sequence>
			<xs:element name="cs-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the carrier select service that are only available to
            the operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the carrier select service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?communication-distribution? -->
	<xs:complexType name="communication-distribution-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The communication distribution service. Use xsi:nil="true" to withdraw the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="fcd-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the flexible communication distribution service that
            are available to the operator rather than the user. This must be present on the creation of the communication-distribution
            service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the communication distribution service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the communication-distribution service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-targets" type="max-fcd-targets-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The max-targets element controls the maximum number of distinct targets that the
                  user can have for communication distribution in addition to the PRIMARY identity. Integer value between 2 and 10. This
                  must be present on the creation of the communication-distribution service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="primary-hosting" type="hosting-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The primary-hosting element defines where the primary identity is hosted. This
                  must be present on the creation of the communication-distribution service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The maximum number of allowed FCD rules in the user document. Not specified or
                  zero limit means no limit
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="fcd-divert-primary" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The fcd-divert-primary element has values "activated" or "deactivated". When set
                  to "activated" the user is able to use divert-primary element to divert the "incoming communication distributed to
                  PRIMARY" to different target. If set to "deactivated" this will withdraw the divert primary service from the user.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="user-no-reply-timer" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The user-no-reply-timer has values "activated" or "deactivated". When set to "activated" it allows the subscriber to control
                  the length of the no reply timer for the user.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="fcd-op-conditions" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The fcd-op-conditions element is a grouping element for fine-grain provisioning options that control which conditions
                  the subscriber is permitted to use in communication distribution rules
                </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The anonymous-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the anonymous condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="busy-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The busy-condition element has values "activated" or "deactivated". When set to "activated" it allows the
                        subscriber to use the fcd-call-state condition with the value of "busy" in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="identity-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the identity condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="media-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The media-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use media conditions in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="not-registered-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The not-registered-condition element has values "activated" or "deactivated". When set to "activated" it
                        allows the subscriber to use the fcd-call-state condition with the value of "not-registered" in communication
                        distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="no-answer-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The no-answer-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the fcd-call-state condition with the value of "no-answer" in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The presence-status-condition element has values "activated" or "deactivated". When set to "activated" it
                        allows the subscriber to use presence-status conditions in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="validity-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The validity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the validity condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="not-reachable-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The not-reachable-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the fcd-call-state condition with the value of "not-reachable" in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The valid-periods-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the valid-periods condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The invalidity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the invalidity condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the served-identity condition in communication distribution rules.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="fcd-op-actions" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The fcd-op-actions element is a grouping element for fine-grain provisioning options to control which actions
                  the user is permitted to use for communication distribution rules.
                </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="rule-no-reply-timer" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The rule-no-reply-timer has values "activated" or "deactivated". When set to "activated it allows the subscriber to use the
                        no reply timer in the action of communication distribution rules to control the length of the no reply timer on a per rule basis.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
										<xs:annotation>
											<xs:documentation>
                        The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows
                        the subscriber to use the play-announcement action in communication distribution rules to control whether the caller
                        is presented by specific announcement handled by generic announcement service.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="fcd-user-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the flexible communication distribution service that are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e. fcd-operator-configuration is present and activated is "true".
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="active" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Controls whether the flexible communication distribution service is active or not for this subscriber.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="divert-primary" type="divert-primary-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The divert-primary element is used for diverting the "incoming communication distributed to PRIMARY" to an alternative target.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="fcd-service-options" type="fcd-service-options-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation> Grouping element for a set of zero or more service options </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="target-list" type="target-list-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A list defining related targets that can be included in communication distribution. The target-list in user-common-data is the preferred way to define related targets so they are available across multiple services. The target-list is retained within communication-distribution for backwards compatibility.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="fcd-ruleset" type="fcd-ruleset-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Grouping element for a set of zero or more flexible communication distribution user rules
                </xs:documentation>
							</xs:annotation>
							<xs:key name="fcd-rule-key">
								<xs:selector xpath="./mmtel-profile-serv:fcd-rule"/>
								<xs:field xpath="@id"/>
							</xs:key>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="divert-primary-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The "active" element has values "true" or "false". It indicates whether FCD divert primary service is activated or not. This must be present on the creation of divert-primary element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="forward-to" type="forward-to-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The forward-to element is a grouping element with details of the target to which the communication towards the PRIMARY should be diverted and optional control of notifications and which identities are revealed to whom. This must be present on the creation of divert-primary.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="fcd-service-options-type">
		<xs:sequence>
			<xs:element name="NoReplyTimer" type="fcd-no-reply-timer-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The NoReplyTimer element specifies the time that must expire without any response before the no-answer
            condition is triggered. The value is an integer giving the timer in the range of 5 to 180 seconds. This
            value applies to rules with no-answer conditions which do not contain their own individual timer.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="target-list-type">
		<xs:sequence>
			<xs:element name="fixed-targets" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">If fixed-targets is set to "true" then the target identities are set by the operator and cannot be changed by the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="target" type="target-type" nillable="true" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">The target element is a sub-MO allowing multiple instances with "name" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ucd-target-list-type">
		<xs:sequence>
			<xs:element name="fixed-targets" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">If fixed-targets is set to "true" then the target identities are set by the operator and cannot be changed by the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="target" type="ucd-target-type" nillable="true" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">The target element is a sub-MO allowing multiple instances with "name" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="target-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name for the distribution target. This is the name by which distribution rules refer to targets. This must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The id is the identity of the target. It is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number. This must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ucd-target-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name for the distribution target. This is the name by which distribution rules refer to targets. This must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The id is the identity of the target. It is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number. This must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="auto-answer-avoidance" type="xs:boolean" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
                    The auto-answer-avoidance flag marks the target as applicable for the auto-answer avoidance feature.
                    If set to true DTMF confirmation will be required to confirm the call establishment.
                </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="fcd-ruleset-type">
		<xs:sequence>
			<xs:element name="fcd-rule" type="fcd-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">An individual rule controlling communication distribution behaviour. The fcd-rule element is a sub-MO allowing multiple instances with "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="fcd-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A unique identifier for an individual rule. This must be unique within the scope of
            the complete document. This must be present on the creation of an fcd-rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="fcd-conditions" type="fcd-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The fcd-conditions element is a grouping element for conditions for a rule. All
            conditions must be satisfied for the rule to take effect. If no conditions are present then the rule is always applicable.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!--? NOTE fcd-actions is optional but not nillable. Every FCD rule must have fcd-actions/parallel-distribution ? -->
			<!--? or fcd-actions/serial-distribution to be valid but cai3g:Set could just update conditions so actions ? -->
			<!--? must be optional ? -->
			<xs:element name="fcd-actions" type="fcd-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The fcd-actions element is a grouping element for the actions for a rule. This must be
            present on the creation of an fcd-rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="fcd-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="rule-deactivated-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The rule-deactivated element has values "true" or "false". If present with the value
            "true" this has the effect of deactivating the individual rule. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-periods element is a grouping element for recurring time periods (intervals)
            within which the rule is valid.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The validity element is a grouping element for absolute time periods (intervals)
            within which the rule is valid.
          </xs:documentation>
				</xs:annotation>
				<xs:key name="fcd-interval-key">
					<xs:selector xpath="./mmtel-profile-serv:interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The invalidity element is a grouping element for time periods (intervals) within which
            the rule is NOT valid. The invalidity condition must contain at least one interval.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
				<xs:key name="fcd-invalidity-interval-key">
					<xs:selector xpath="./interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="fcd-call-state" type="fcd-call-state-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The fcd-call-state condition controls which state the user must be in for the rule to apply. The value "busy"
            is satisfied if the user is busy in other calls. The value "no-answer" applies when there is no answer from the
            user. The value "not-registered" applies when the user is not registered on the MTAS. The value "not-reachable"
            applies when the user is not reachable because either a specific response has been received or the not reachable timer expires.
            The value "unconditional" is used to clear the other call state values so that the condition is satisfied regardless of the
            user's call state.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- A structured parameter is used to cover all possible caller identity values - being structured it can be nilled -->
			<xs:element name="fcd-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The fcd-caller-identity element is a grouping element for conditions which are based on the caller's identity
            (or lack of an identity in the case of anonymous).
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="anonymous" type="empty-element-type">
							<xs:annotation>
								<xs:documentation>
                  The anonymous element is an empty element specifying a condition which is satisfied if the caller
                  is anonymous. This can be removed by deleting the enclosing fcd-caller-identity element or by
                  replacing it with an identity element. The elements anonymous and identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on fcd-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation>
                  The identity element is a grouping element for conditions which are based on the caller's identity.
                  The condition is satisfied if any of the included one or many elements within it is matched. The
                  elements anonymous and identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
							<xs:key name="fcd-one-key">
								<xs:selector xpath="./one"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="fcd-many-key">
								<xs:selector xpath="./many"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="fcd-except-id-key">
								<xs:selector xpath=".//except-id"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="fcd-except-domain-key">
								<xs:selector xpath=".//except-domain"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="fcd-number-match-key">
								<xs:selector xpath="./number-match"/>
								<xs:field xpath="@starts-with"/>
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<!-- media is a multiple value parameter -->
			<xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The media element contains a media type that the session must include for the condition to be matched e.g.
            "audio" or "video". This is a multi-value parameter so it can appear more than once with several media
            values that must all be satisfied for the overall condition to be matched.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The presence-status element contains a presence status value that the user must satisfy for the condition to
            be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can appear more
            than once with several presence status values that must all be satisfied for the overall condition to be matched.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The served-identity element is a grouping element for conditions which are based on the user's
            served identity. The condition is satisfied if any of the included elements within it is matched.
          </xs:documentation>
				</xs:annotation>
				<xs:key name="fcd-served-identity-one-key">
					<xs:selector xpath="./one"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
			<xs:element name="in-sip-request" type="in-sip-request-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
                    The in-sip-request element is a grouping element for regexp conditions on contents of a SIP request.
                    It evaluates to true if ALL of the conditions included within it are fulfilled.
                </xs:documentation>
				</xs:annotation>
				<xs:key name="fcd-in-sip-request-flexcondition-key">
					<xs:selector xpath="./flexcondition"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="served-identity-type">
		<xs:annotation>
			<xs:documentation>
        The served-identity element is a grouping element for conditions which shall match one of the served users MSN alias PUIs
        The condition is satisfied if any of the MSN alias PUIs is matched.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="one" type="one-type" nillable="true" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The one element specifies an individual identity to be matched. The one element is a sub-MO allowing
            multiple instances with "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="valid-periods-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The valid-periods element is a grouping element for recurring time periods (intervals)
        within which the rule is valid. In order for the valid-periods condition to be satisfied the current date/time must match one of the
        valid-days if present and one of the valid-times if present.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="utc-offset" type="time-offset-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The utc-offset element specifies the offset to be taken from UTC when determining
            times of day and when each day starts and ends. If utc-offset is omitted then days and times are based on UTC
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-days" type="days-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-days element specifies each of the days on which the condition would match,
            subject to also meeting other subconditions if present. If valid-days is omitted then the condition applies to all days of the
            week.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-times" type="intervals-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-times specifies periods of the day in which the condition would match,
            subject to also meeting other subconditions, if present. If valid-times is omitted then the condition applies to all times of
            the day. The valid-times condition must contain at least one interval.
          </xs:documentation>
				</xs:annotation>
				<xs:key name="valid-times-key">
					<xs:selector xpath="./interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="valid-months" type="months-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-months element specifies each of the months on which the condition would
            match, subject to also meeting other sub-conditions if present. If valid-months is omitted then it applies to all months of the
            year.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-weeks" type="weeks-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-weeks element specifies each of the weeks on which the condition would
            match, subject to also meeting other sub-conditions if present. If valid-weeks is omitted then it applies to all weeks of the
            year.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-daily" type="repeat-daily-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repeat-daily element specifies start day and repetition interval for the days on
            which the condition would match, subject to also meeting other sub-conditions if present. If repeat-daily is omitted then it
            applies to all days of the year
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-weekly" type="repeat-weekly-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repeat-weekly element specifies start week and repetition interval for the weeks
            on which the condition would match, subject to also meeting other sub-conditions if present. If repeat-weekly is omitted then it
            applies to all weeks of the year
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-monthly" type="repeat-monthly-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repeat-monthly element specifies start month and repetition interval for the
            months on which the condition would match, subject to also meeting other sub-conditions if present. If repeat-monthly is omitted
            then it applies to all months of the year
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-monthdays" type="monthdays-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-monthdays element specifies each of the days on which the condition would
            match, subject to also meeting other sub-conditions if present. If valid-monthdays is omitted then the condition applies to all
            days of the month.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="except-holidays" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The except-holidays element specifies that if the current day matches to the holidays
            provisioned for the user, then the valid-periods condition is evaluated to false.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="days-type">
		<xs:sequence>
			<xs:element name="day" type="weekday-type" maxOccurs="7">
				<xs:annotation>
					<xs:documentation xml:lang="en">The day of the week. This is a multi-value parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="intervals-type">
		<xs:sequence>
			<xs:element name="interval" type="time-interval-type" nillable="true" maxOccurs="8">
				<xs:annotation>
					<xs:documentation xml:lang="en">A time interval. The interval element is a sub-MO allowing multiple instances with
            "from" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="time-interval-type">
		<xs:sequence>
			<xs:element name="from" type="hours-minutes-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The time of day at which the interval starts. The format is HH:MM in the 24 hour
            clock. This must be present on the creation of an interval element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="until" type="hours-minutes-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The time of day at which the interval ends. The format is HH:MM in the 24 hour clock.
            The interval applies until the end of the specified minute. This must be present on the creation of an interval element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="from" type="hours-minutes-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="fromAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="fcd-actions-type">
		<xs:sequence>
			<xs:choice>
				<xs:element name="parallel-distribution" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The parallel-distribution element is a grouping element with details of the targets to
              which the communication should be distributed in parallel. Either parallel-distribution or serial-distribution must be present
              on the creation of an fcd-rule.
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="ring-period" type="parallel-ring-timer-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">The maximum time period for which the targets shall be left ringing in parallel
                    without an answer.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="target" type="parallel-target-type" nillable="true" minOccurs="0" maxOccurs="10">
								<xs:annotation>
									<xs:documentation xml:lang="en">The target element is a sub-MO allowing multiple instances with "name" as the
                    unique key. It is a reference by name to a target identity to which the communication should be distributed. At least one
                    target must be present on creation of a parallel-distribution element.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
					<xs:key name="fcd-parallel-target-key">
						<xs:selector xpath="./mmtel-profile-serv:target"/>
						<xs:field xpath="@name"/>
					</xs:key>
				</xs:element>
				<xs:element name="serial-distribution" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The serial-distribution element is a grouping element with details of the targets to
              which the communication should be distributed in series. Either parallel-distribution or serial-distribution must be present on
              the creation of an fcd-rule.
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="target" type="serial-target-type" nillable="true" minOccurs="0" maxOccurs="10">
								<xs:annotation>
									<xs:documentation xml:lang="en">The target element is a sub-MO allowing multiple instances with "name" as the
                    unique key. It is a reference by name to a target identity to which the communication should be distributed. At least one
                    target must be present on creation of a serial-distribution element.
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
					<xs:key name="fcd-serial-target-key">
						<xs:selector xpath="./mmtel-profile-serv:target"/>
						<xs:field xpath="@name"/>
					</xs:key>
				</xs:element>
				<xs:element name="flexible-distribution" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
              The flexible-distribution element is a grouping element with details of the targets
              to which the communication
              should be distributed in a flexible way.
            </xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="target" type="flexible-target-type" nillable="true" minOccurs="0" maxOccurs="10">
								<xs:annotation>
									<xs:documentation>
                    The target element is a sub-MO allowing multiple instances.
                    It is a reference by name to a target identity to which the communication
                    should be distributed.
                    At least one target must be present on creation of a flexible-distribution
                    element.
                    A target element with the same name cannot be present more than once in the same
                    parallel group. (There is always one member in a serial group.)
                  </xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:choice>
			<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0"/>
			<!-- fcd-action-options is a structured parameter allowing it to be made nillable -->
			<xs:element name="fcd-action-options" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Grouping element for a set of zero or more action options
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<!-- New rule level CFNR timer config -->
						<xs:element name="NoReplyTimer" type="fcd-no-reply-timer-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The NoReplyTimer element specifies the time that must expire without answer before the no-answer
                  condition is triggered. The value is an integer giving the timer in the range of 5 to 180
                  seconds. This shall only be present in rules with the value "no-answer" in an fcd-call-state condition.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="parallel-target-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name of a target identity. The name must be the value of the name of a target in
            the target-list or the special value PRIMARY. The name must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="serial-target-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name of a target identity. The name must be the value of the name of a target in
            the target-list or the special value PRIMARY. The name must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ring-period" type="serial-ring-timer-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum time period for which this target shall be left ringing in without an
            answer before switching to the next target.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="flexible-target-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementName"/>
					</xs:appinfo>
					<xs:documentation>
            The name of a target identity. The name must be one of the following: the name of a
            target
            defined in user-common-data; the name of a target-device defined in user-common-data;
            the
            special value PRIMARY for all of the user\u2019s devices or, in the case of
            communication distribution
            the name of a target defined in the target-list within that service.
            The name must be present on the creation of a target element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ring-mode" type="ring-mode-type">
				<xs:annotation>
					<xs:documentation>
            The ring mode type - serial or parallel - that is to be used within the flexible
            distribution.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ring-period" type="flexible-ring-timer-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The maximum time period for which this target shall be left ringing in without an
            answer
            before switching to the next target.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required"/>
	</xs:complexType>
	<xs:simpleType name="hosting-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IMS"/>
			<xs:enumeration value="non-IMS"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="rule-limit-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="max-fcd-targets-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="2"/>
			<xs:maxInclusive value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="parallel-ring-timer-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="15"/>
			<xs:maxInclusive value="360"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="serial-ring-timer-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="15"/>
			<xs:maxInclusive value="360"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="flexible-ring-timer-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="15"/>
			<xs:maxInclusive value="360"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="target-name-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
			<!-- TBD Any restriction on content? -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ring-mode-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="serial"/>
			<xs:enumeration value="parallel"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="time-offset-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[+\-]([0-1][0-9]|2[0-3]):[0-5][0-9]"/>
			<!-- offset can be + or - with respect to UTC i.e. [+-] hour is 00-23 ([0-1][0-9]|2[0-3]) minute is 00-59 i.e [0-5][0-9] -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="weekday-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Sunday"/>
			<xs:enumeration value="Monday"/>
			<xs:enumeration value="Tuesday"/>
			<xs:enumeration value="Wednesday"/>
			<xs:enumeration value="Thursday"/>
			<xs:enumeration value="Friday"/>
			<xs:enumeration value="Saturday"/>
			<xs:enumeration value="NonWorkday"/>
			<xs:enumeration value="Holiday"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="hours-minutes-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-1][0-9]|2[0-4]):[0-5][0-9]"/>
			<!-- hour can be up to 24 to allow the time period right up to midnight to be specified TBD hour is 00-24 ([0-1][0-9]|2[0-4]) minute is 00-59 i.e [0-5][0-9] -->
		</xs:restriction>
	</xs:simpleType>
	<!--?communication-diversion? -->
	<xs:complexType name="communication-diversion-type">
		<xs:sequence>
			<xs:element name="cdiv-operator-configuration" type="cdiv-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication diversion service that are
            available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-user-configuration" type="cdiv-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication diversion service that are
            available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present
            if the service is provisioned i.e. cdiv-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator Part of Communication Diversion</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the communication diversion service. If set to "false" this will withdraw the user service and the cdiv-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-no-reply-timer" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The user-no-reply-timer has values "activated" or "deactivated". When set to
            "activated" it allows the subscriber to control the length of the no reply timer for the user, thus overriding the configured
            CFNR nodal timer.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-op-conditions element is a grouping element for fine-grain provisioning
            options that control which conditions the subscriber is permitted to use in communication diversion rules
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The anonymous-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the anonymous condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="busy-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The busy-condition element has values "activated" or "deactivated". When set to
                  "activated" it allows the subscriber to use the cdiv-call-state condition with the value of "busy" in communication
                  diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the identity condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="media-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The media-condition element has values "activated" or "deactivated". When set to
                  "activated" it allows the subscriber to use media conditions in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="not-registered-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The not-registered-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the cdiv-call-state condition with the value of "not-registered"
                  in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="no-answer-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The no-answer-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the cdiv-call-state condition with the value of "no-answer" in
                  communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The presence-status-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use presence-status conditions in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="validity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The validity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the validity condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="not-reachable-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The not-reachable-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the cdiv-call-state condition with the value of "not-reachable" in
                  communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The valid-periods-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the valid-periods condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The invalidity-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the invalidity condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                  the subscriber to use the served-identity condition in communication diversion rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="cdiv-op-actions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-op-actions element is a grouping element for fine-grain provisioning options
            to control which actions the user is permitted to use for communication diversion rules.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="notify-caller-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The notify-caller-action element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the notify-caller action in communication diversion rules to control
                  whether the caller is notified that the call is being forwarded
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="notify-served-user-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The notify-served-user-action element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the notify-served-user action in communication diversion rules to
                  control whether the served user is notified that the call is being forwarded
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="notify-served-user-on-outbound-call-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The notify-served-user-on-outbound-call-action element has values "activated" or
                  "deactivated". When set to "activated" it allows the subscriber to use the notify-served-user-on-outbound-call action in
                  communication diversion rules to control whether the served user is notified that calls are being forwarded when he makes
                  a call attempt
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="reveal-identity-to-caller-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The reveal-identity-to-caller-action has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the reveal-identity-to-caller action in communication diversion
                  rules to control whether the caller being notified that the call is being forwarded receives the diverting party's
                  identity information
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="reveal-identity-to-target-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The reveal-identity-to-target-action has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the reveal-identity-to-target action in communication diversion
                  rules to control whether the diverted-to party receives identity information of the diverting party.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="rule-no-reply-timer" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The rule-no-reply-timer has values "activated" or "deactivated". When set to
                  "activated“ it allows the subscriber to use the no reply timer in the action of communication diversion rules to control
                  the length of the no reply timer on a per rule basis.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="do-not-disturb-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The do-not-disturb-action element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the do-not-disturb action in communication diversion rules to control
                  whether the caller is handled by do-not-disturb service (e.g. treated with specific charging scheme, etc.)
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The play-announcement-action element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the play-announcement action in communication diversion rules to
                  control whether the caller is presented by specific announcement handled by generic announcement service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum number of allowed CDIV rules in the user document. Not specified or zero
            limit means no limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the communication
            diversion service is active or not for this subscriber
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- cdiv-service-options is a structured parameter allowing it to be made nilable -->
			<xs:element name="cdiv-service-options" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more service options</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<!-- New user level CFNR timer config -->
						<xs:element name="NoReplyTimer" type="no-reply-timer-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The NoReplyTimer element specifies the time that must expire without answer
                  before the no answer condition is triggered. The value is an integer giving the timer in the range of 5 to 180 seconds.
                  This value applies to rules with no-answer conditions which do not contain their own individual timer.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!-- ruleset is a structured parameter that is optional and nillable -->
			<xs:element name="cdiv-ruleset" type="cdiv-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more user rules</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="cdiv-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:cdiv-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-ruleset-type">
		<xs:sequence>
			<xs:element name="cdiv-rule" type="cdiv-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">An individual rule controlling communication diversion behaviour. The cdiv-rule
            element is a sub-MO allowing multiple instances with "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
				<!-- sub MOs need a key. The key for rule is id. -->
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- rule is a sub-MO -->
	<xs:complexType name="cdiv-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A unique identifier for an individual rule. This must be unique within the scope of
            the complete document
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-conditions" type="cdiv-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-conditions element is a grouping element for conditions for a rule. All
            conditions must be satisfied for the rule to take effect. If no conditions are present then the rule is always applicable. The
            conditions that are permitted depend on the fine grain provisioning options in cdiv-op-conditions
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cdiv-actions is optional but not nillable. Every CDIV rule must have actions/forward-to/target to be valid but cai3g:Set -->
			<!-- could just update conditions so actions must be optional -->
			<xs:element name="cdiv-actions" type="cdiv-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-actions element is a grouping element for the actions for a rule. For
            communication diversion a forward-to action must be present.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="cdiv-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="rule-deactivated-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The rule-deactivated element has values "true" or "false". If present with the value
            "true" this has the effect of deactivating the individual rule. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-call-state" type="cdiv-call-state-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-call-state condition controls which state the user must be in for the rule to
            apply. The value "busy" is satisfied if the user is busy in other calls. The value "no-answer" applies when there is no answer
            from the user. The value "not-registered" applies when the user is not registered on the MTAS. The value "not-reachable" applies
            when the user is not reachable because either a specific response has been received or the not reachable timer
            expires. The
            value "unconditional" is used to clear the other call state values so that the condition is satisfied regardless of the user's
            call state.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- A structured parameter is used to cover all possible caller identity values - being structured it can be nilled -->
			<xs:element name="cdiv-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cdiv-caller-identity element is a grouping element for conditions which are based
            on the caller's identity (or lack of an identity in the case of anonymous).
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="anonymous" type="empty-element-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The anonymous element is an empty element specifying a condition which is
                  satisfied if the caller is anonymous. This can be removed by deleting the enclosing cdiv-caller-identity element or by
                  replacing it with an identity element. The elements anonymous and identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on cdiv-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity element is a grouping element for conditions which are based on the
                  caller's identity. The condition is satisfied if any of the included one or many elements within it is matched.
                </xs:documentation>
							</xs:annotation>
							<xs:key name="cdiv-one-key">
								<xs:selector xpath="./mmtel-profile-serv:one"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="cdiv-many-key">
								<xs:selector xpath="./mmtel-profile-serv:many"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="cdiv-except-id-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-id"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="cdiv-except-domain-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-domain"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="cdiv-number-match-key">
								<xs:selector xpath="./mmtel-profile-serv:number-match"/>
								<xs:field xpath="@starts-with"/>
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<!-- media is a multiple value parameter -->
			<xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The media element contains a media type that the session must include for the
            condition to be matched e.g. "audio" or "video". This is a multi-value parameter so it can appear more than once with each of
            several media types that must be included.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- validity is a structured parameter so it can be nillable -->
			<xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The validity element is a grouping element for time periods (intervals) within which
            the rule is valid.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing validity element -->
				<xs:key name="cdiv-interval-key">
					<xs:selector xpath="./mmtel-profile-serv:interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence-status element contains a presence status value that the user must
            satisfy for the condition to be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can
            appear more than once with several presence status values that must all be satisfied for the overall condition to be matched.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-periods element is a grouping element for recurring time periods (intervals)
            within which the rule is valid.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The invalidity element is a grouping element for time periods (intervals) within which
            the rule is NOT valid. The invalidity condition must contain at least one interval.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
				<xs:key name="cdiv-invalidity-interval-key">
					<xs:selector xpath="./interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The served-identity element is a grouping element for conditions which are based on the user's 
            served identity. The condition is satisfied if any of the included elements within it is matched. 
          </xs:documentation>
				</xs:annotation>
				<xs:key name="cdiv-served-identity-one-key">
					<xs:selector xpath="./mmtel-profile-serv:one"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="cdiv-call-state-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="unconditional"/>
			<xs:enumeration value="busy"/>
			<xs:enumeration value="no-answer"/>
			<xs:enumeration value="not-registered"/>
			<xs:enumeration value="not-reachable"/>
			<!-- EMA does not support nilling a single value parameter so assigning the value of "unconditional" means none of the three conditions apply -->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="forward-to-type">
		<xs:sequence>
			<!-- 'target' is optional on CAI3G to allow individual updates of other elements but is mandatory in the updated document -->
			<xs:element name="target" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The target element specifies the identity to which the communication should be
            diverted. This takes the form of a normalized sip: or tel: URI or "voicemail:internal" for forwarding to voice mail
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- There is no need for these elements to be nillable because they can be set to false to achieve the same meaning -->
			<xs:element name="notify-caller" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The notify-caller element has values "true" or "false". It controls whether the caller
            is notified that the call is being forwarded. If it is not included then the default behaviour is to notify the caller (true).
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reveal-identity-to-caller" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The reveal-identity-to-caller element has values "true" or "false". It controls
            whether the caller being notified that the call is being forwarded receives the diverting party's identity information. If it is
            not included then the default behaviour is not to reveal the diverting party's identity to the caller (true).
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="notify-served-user" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The notify-served-user element has values "true" or "false". It controls whether the
            served user is notified that the call is being forwarded. If it is not included then the default behaviour is not to notify the
            served user (false).
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="notify-served-user-on-outbound-call" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The notify-served-user-on-outbound-call element has values true or false. It controls
            whether the served user is notified that calls are being forwarded when he makes a call attempt. If it is not included then the
            default behaviour is not to notify the service user on outbound calls (false).
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reveal-identity-to-target" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The reveal-identity-to-target element has values "true" and "false". It controls
            whether the diverted-to party receives identity information of the diverting party. If it is not included then the default
            behaviour is not to reveal the diverting party's identity to the target (false).
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- Although it would be possible to skip actions and go straight to forward-to this structure matches that stored on HSS to make the mappings between CAI3G and Sh schemas clear -->
	<xs:complexType name="cdiv-actions-type">
		<xs:sequence>
			<xs:element name="forward-to" type="forward-to-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The forward-to element is a grouping element with details of the target to which the
            communication should be diverted and optional control of notifications and which identities are revealed to whom. A target is
            mandatory in a rule (although subsequent updates could just modify conditions). The other elements within forward-to are
            optional and depend on the fine grain provisioning options in cdiv-op-actions
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="do-not-disturb" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The do-not-disturb element has values "true" and "false". If it's set to "true" the
            element is added into the actions part of the rule. If it's set to "false" the element is removed from the actions part of the
            rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The play-announcement element has string values from 0 to 32 characters. When the
            play-announcement action is set with the string value containing characters with the length between 1 to 32, if there is any
            satisfying corresponding conditions and being diverted, the caller will be presented with the specific announcement handled by
            generic announcement service. When the play-announcement action is set with the string value containing character with the
            length of 0, any play-announcement action element in the rule will be deleted from the rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- cdiv-action-options is a structured parameter allowing it to be made nillable -->
			<xs:element name="cdiv-action-options" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more action options</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<!-- New rule level CFNR timer config -->
						<xs:element name="NoReplyTimer" type="no-reply-timer-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The NoReplyTimer element specifies the time that must expire without answer
                  before the no answer condition is triggered. The value is an integer giving the timer in the range of 5 to 180 seconds.
                  This value applies to no answer rules which do not contain their own individual timer.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="no-reply-timer-type">
		<xs:restriction base="xs:positiveInteger">
			<xs:minInclusive value="5"/>
			<xs:maxInclusive value="180"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?communication-diversion-no-answer-timer? -->
	<xs:simpleType name="no-answer-timeout-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="5"/>
			<xs:maxInclusive value="60"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="communication-diversion-no-answer-timer-type">
		<xs:sequence>
			<xs:element name="cdiv-no-answer-timer-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication diversion no answer timer service
            that are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the communication diversion no answer timer service. If set to "false" this will withdraw the user service and the cdiv-no-answer-timer-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="cdiv-no-answer-timer-user-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication diversion no answer timer service
            that are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be
            present if the service is provisioned i.e. cdiv-no-answer-timer-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="active" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the
                  communication diversion no answer timer service is active or not for this subscriber. If active is set to "false" the
                  timer configured at node level will apply.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="no-answer-timeout" type="no-answer-timeout-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The no-answer-timeout element specifies the time that must expire without answer
                  before the no answer condition is triggered . The value is an integer giving the timer in the range of 5 to 60 seconds
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?communication-waiting? -->
	<xs:complexType name="communication-waiting-type">
		<xs:sequence>
			<xs:element name="cw-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication waiting service that are available
            to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the communication waiting service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="cw-user-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the communication waiting service that are available
            for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present if the
            service is provisioned i.e. cw-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="active" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Controls whether the communication waiting service is active or not
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?conference? -->
	<xs:complexType name="conference-type">
		<xs:sequence>
			<xs:element name="conf-operator-configuration" type="conf-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the conference service that are only available to the
            operator
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="conf-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the conference service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="max-number-of-parties" type="max-number-of-parties-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum number of parties allowed in a conference created by this user. This is an
            integer in the range 3-32
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="max-number-of-parties-type">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="3"/>
			<xs:maxInclusive value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?dial-tone-management? -->
	<xs:complexType name="dial-tone-management-type">
		<xs:sequence>
			<xs:element name="dtm-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the dial tone management service that are only
            available to the operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the dial tone management service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?dialog-event-notifier? -->
	<xs:complexType name="dialog-event-notifier-type">
		<xs:sequence>
			<xs:element name="den-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the dialog event notifier service that are available 
					to the operator rather than the user. 
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values “true” or “false”. When set to “true” the user 
								is provisioned with the dialog event notifier service. This must be present on the creation of the dialog-event-notifier service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="block-device-group-usage" type="block-device-group-usage-type" minOccurs="0" nillable="true">
								<xs:annotation>
									<xs:documentation xml:lang="en">When the element is present a device belonging to the device group is blocked 
									from using the dialog event notifier service. Allowed device groups are “MOBILE” and “FIXED”.
									Use xsi:nil="true" to delete this element. This element is optional.
									</xs:documentation>
								</xs:annotation>
							</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="block-device-group-usage-type">
		<xs:annotation>
			<xs:documentation>
			MTAS allows
			- FIXED
			- MOBILE
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="FIXED"/>
			<xs:enumeration value="MOBILE"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?distinctive-ring?-->
	<xs:complexType name="distinctive-ring-type">
		<xs:sequence>
			<xs:element name="dr-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the distinctive ring service that are available to the operator rather than the user.
            This must be present on the creation of the distinctive-ring service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true" or "false". When set to "true" the user is provisioned with the distinctive  
                  ring service. If set to "false" this will withdraw the user service and the dr-user-configuration element must be
                  deleted at the same time. This must be present on the creation of the distinctive-ring service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="dr-user-configuration" type="dr-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the distinctive ring service that are available for the user to set directly. These can also be set on the user's 
            behalf by the operator. This shall only be present if the service is provisioned i.e. dr-operator-configuration is present and activated is "true".
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="dr-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The active element has values "true" or "false". It controls whether the distinctive ring service is
            active or not for this subscriber
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dr-ruleset" type="dr-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Grouping element for a set of zero or more user rules
          </xs:documentation>
				</xs:annotation>
				<xs:key name="dr-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:dr-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="dr-ruleset-type">
		<xs:sequence>
			<xs:element name="dr-rule" type="dr-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            An individual rule controlling distinctive ring behavior. The dr-rule element is a sub-MO allowing
            multiple instances with "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="dr-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
            A unique identifier for an individual rule. This must be unique within the scope of the complete document. This
            must be present on the creation of a dr-rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dr-conditions" type="dr-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The dr-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
            for the rule to take effect. If no conditions are present then the rule is always applicable. 
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dr-actions" type="dr-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The dr-actions element is a grouping element for the actions for a rule. This must be present on the creation of a dr-rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required"/>
	</xs:complexType>
	<xs:complexType name="dr-conditions-type">
		<xs:sequence>
			<xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The served-identity element is a grouping element for conditions which are based on the served user's identity.
            The condition is satisfied if any of the included elements within it is matched.
          </xs:documentation>
				</xs:annotation>
				<xs:key name="dr-served-one-key">
					<xs:selector xpath="./mmtel-profile-serv:one"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="dr-actions-type">
		<xs:sequence>
			<xs:element name="alert-info" type="alert-info-type">
				<xs:annotation>
					<xs:documentation>
            The alert-info element specifies the name which will be used to find value of the Alert-info header for 
            INVITE message. This must be present on the creation of a dr-rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="alert-info-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="128"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="hotline-type">
		<xs:sequence>
			<xs:element name="hotline-operator-configuration" type="hotline-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the Hotline service that are available to the operator rather than the user. This must be present on the creation of the Hotline service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="hotline-user-configuration" type="hotline-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the Hotline service that are available to the user rather than the operator. This must be present on the creation of the Hotline service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="hotline-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>Operator Part of Hotline.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The activated element has values "true" or "false". When set to "true" the user is provisioned with the Hotline service. If set to "false" this will withdraw the user service and the hotline-user-configuration element must be deleted at the same time. This must be present on the creation of the Hotline service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="unconditional-condition" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The unconditional-condition element groups parameters for Unconditional Hotline (Automatic Re-routing to Customer Care).
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true" or "false". When set to "true" the user is provisioned with the unconditional Hotline service. If set to "false" the sevice is not active to the user. This must be present on the creation of the Hotline service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="hotline-number" type="stored-number-type">
							<xs:annotation>
								<xs:documentation>
                  The stored number in its full form, which is substituted when the user dials the corresponding hotline service code. The stored-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC. This must be present on the creation of a number-mapping element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="instant-condition" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The instant-condition element groups parameters for Instant Hotline.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true" or "false". When set to "true" the user is provisioned with the Instant Hotline service. If set to "false" this will withdraw the user service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="hotline-number" type="stored-number-type">
							<xs:annotation>
								<xs:documentation>
                  The stored number in its full form, which is substituted when the user dials the corresponding hotline service code. The stored-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC. This must be present on the creation of a number-mapping element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="delayed-condition" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The delayed-condition element groups parameters for Delayed Hotline. Currently it contains only one element: activated.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true" or "false". When set to "true" the user is provisioned with the Delayed Hotline service. If set to "false" this will withdraw the user service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="hotline-user-configuration-type">
		<xs:annotation>
			<xs:documentation>User Part of Hotline.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The active element has values "true" or "false". It controls whether the Delayed Hotline service is active or not for this subscriber. It can be active only if it is active in the operator part as well.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="hotline-number" type="stored-number-type">
				<xs:annotation>
					<xs:documentation>
            The stored number in its full form, which is substituted when the user dials the corresponding hotline service code. The stored-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized after removing a
            dynamic ad-hoc presentation SSC and/or a CSC.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="called-number" type="stored-number-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The triggering number allows to specify additional criteria for Hotline service triggering (apart the service code defined in CM mtasHotlineServiceCode). The triggering number takes precedence over mtasHotlineServiceCode CM. The format of CDATA of this element is further specified in the documentation of mct:stored-number-type.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?incoming-communication-barring? -->
	<xs:complexType name="incoming-communication-barring-type">
		<xs:sequence>
			<xs:element name="icb-operator-configuration" type="icb-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the incoming communication barring service that are only available to the operator
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-user-configuration" type="icb-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the incoming communication barring service that are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e. icb-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the incoming communication barring service. If set to "false" this will withdraw the user service and the icb-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-ruleset" type="icb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more operator rules. These rules apply regardless of whether activated is "true" or "false".
          </xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="icb-op-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:icb-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
			<xs:element name="icb-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The icb-op-conditions element is a grouping element for fine-grain provisioning
            options that control which condition elements the user is permitted to use in incoming communication barring rules.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The anonymous-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the anonymous condition in incoming communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="roaming-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The roaming-condition element has values "activated" or "deactivated". When set to "deactivated" the international calls to the user are barred.  
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="communication-diverted-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The communication-diverted-condition element has values "activated" or
                  "deactivated". When set to "activated" it allows the subscriber to use the communication-diverted condition in incoming
                  communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the identity condition in incoming communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="media-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The media-condition element has values "activated" and "deactivated".. When set
                  to "activated" it allows the subscriber to use media conditions in incoming communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The other-identity-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the other-identity condition in incoming communication barring
                  rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The presence-status-condition element has values "activated" and "deactivated".
                  When set to "activated" it allows the subscriber to use presence-status conditions in incoming communication barring
                  rules. This is not currently supported by incoming communication barring and should be omitted or set to "deactivated"
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="validity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The validity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the validity condition in incommunication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The valid-periods-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the valid-periods condition in incoming communication barring
                  rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The invalidity-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the invalidity condition in incoming communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The invalidity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                  the subscriber to use the served-identity condition in incoming communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="icb-op-actions" type="call-barring-op-actions-type-incoming" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The icb-op-actions element is a grouping element for fine-grain provisioning options
            to control which action elements the user is permitted to use in incoming communication barring rules.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum number of allowed incoming communication barring rules in the user
            document. Not specified or zero limit means no limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the incoming
            communication barring service is active or not for this subscriber. Note that this controls the user rules but has no effect on
            the operator rules.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-ruleset" type="icb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more user rules.</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="icb-user-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:icb-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-ruleset-type">
		<xs:sequence>
			<xs:element name="icb-rule" type="icb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">An individual rule controlling incoming communication barring behaviour. The icb-rule
            element is a sub-MO allowing multiple instances with "id" as the unique key
          </xs:documentation>
				</xs:annotation>
				<!-- sub MOs need a key. The key for rule is id. -->
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- rule is a sub-MO -->
	<xs:complexType name="icb-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A unique identifier for an individual rule. This must be unique within the scope of
            the complete document
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-conditions" type="icb-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The icb-conditions element is a grouping element for conditions for a rule. All
            conditions must be satisfied for the rule to take effect. If no conditions are present then the rule is always applicable.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set -->
			<!-- could just update conditions so actions must be optional -->
			<xs:element name="cb-actions" type="call-barring-actions-type-incoming" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cb-actions element is a grouping element for the actions for a rule. For
            communication barring an allow action must be present in each rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="icb-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="rule-deactivated-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The rule-deactivated element has values "true" or "false". If present with the value
            "true" this has the effect of deactivating the individual rule. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The icb-caller-identity element is a grouping element for conditions which are based
            on the caller's identity (or lack of an identity in the case of anonymous).
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="anonymous" type="empty-element-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The anonymous element is an empty element specifying a condition which is
                  satisfied if the caller is anonymous. This can be removed by deleting the enclosing icb-caller-identity element or by
                  replacing it with an identity or other-identity element. The elements anonymous, identity and other-identity are mutually
                  exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity" type="empty-element-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The other-identity element is an empty element which matches any identity that
                  has not been specified by any of the other rules in the ruleset. It allows for setting a default policy. This can be
                  removed by deleting the enclosing icb-caller-identity element or by replacing it with an anonymous or identity element.
                  The elements anonymous, identity and other-identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on icb-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity element is a grouping element for conditions which are based on the
                  caller's identity. The condition is satisfied if any of the included one or many elements within it is matched. This can
                  be removed by deleting the enclosing icb-caller-identity element or by replacing it with an anonymous or other-identity
                  element. The elements anonymous, identity and other-identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
							<xs:key name="icb-one-key">
								<xs:selector xpath="./mmtel-profile-serv:one"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="icb-many-key">
								<xs:selector xpath="./mmtel-profile-serv:many"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="icb-except-id-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-id"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="icb-except-domain-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-domain"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="icb-number-match-key">
								<xs:selector xpath="./mmtel-profile-serv:number-match"/>
								<xs:field xpath="@starts-with"/>
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="roaming" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The roaming element has values "true" or "false". If present with the value "true", the incoming calls when roaming are barred. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="communication-diverted" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The communication-diverted element has values "true" or "false". If present with the
            value "true", this condition is satisfied if the incoming communication has been diverted. Set to "false" to remove this
            condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The media element contains a media type that the session must include for the
            condition to be matched e.g. "audio" or "video". This is a multi-value parameter so it can appear more than once with each of
            several media types that must be included.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- validity is a structured parameter so it can be nillable -->
			<xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The validity element is a grouping element for time periods (intervals) within which
            the rule is valid.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing validity element -->
				<xs:key name="icb-interval-key">
					<xs:selector xpath="./mmtel-profile-serv:interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence-status element contains a presence status value that the user must
            satisfy for the condition to be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can
            appear more than once with several presence status values that must all be satisfied for the overall condition to be matched.
            This condition is not currently supported by incoming communication barring and will always evaluate to false.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-periods element is a grouping element for recurring time periods (intervals)
            within which the rule is valid.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The invalidity element is a grouping element for time periods (intervals) within which
            the rule is NOT valid. The invalidity condition must contain at least one interval.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
				<xs:key name="icb-invalidity-interval-key">
					<xs:selector xpath="./interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The served-identity element is a grouping element for conditions which are based on the user's 
            served identity. The condition is satisfied if any of the included elements within it is matched. 
          </xs:documentation>
				</xs:annotation>
				<xs:key name="icb-served-identity-one-key">
					<xs:selector xpath="./mmtel-profile-serv:one"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?user-call-admission-control? -->
	<xs:complexType name="user-call-admission-control-type">
		<xs:sequence>
			<xs:element name="ucac-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the user call admission control service that are
            available to the operator rather than the user. This must be present on the creation of the user-call-admission-control service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the user call admission control service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the user-call-admission-control service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="orig-active-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of originating, active sessions for this user. This must be
                  present on the creation of the user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="term-active-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of terminating, active sessions for this user. This must be
                  present on the creation of the user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="total-active-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of active sessions (i.e. the sum of originating and
                  terminating active sessions) for this user. This must be present on the creation of the
                  user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!--<xs:element name="fixed-active-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of active sessions (i.e. the sum of originating and
                          terminating active sessions) for this user. This must be present on the creation of the
                          user-call-admission-control-service.
                      </xs:documentation>
							</xs:annotation>
						</xs:element>-->
						<xs:element name="orig-all-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of all originating sessions (i.e. the sum of active and
                  inactive originating sessions) for this user. This must be present on the creation of the
                  user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="term-all-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of all terminating sessions (i.e. the sum of active and
                  inactive terminating sessions) for this user. This must be present on the creation of the
                  user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="total-all-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of all sessions (i.e. the sum of all originating and
                  terminating sessions) for this user. This must be present on the creation of the user-call-admission-control-service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="waiting-limit" type="user-cac-limit-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the limit of waiting sessions for this user. This must be present on the
                  creation of the user-call-admission-control-service. The waiting limit can only be set greater than zero if the user also
                  has the communication waiting service activated. Due to the mutual dependency with the communication waiting service both
                  services must be updated in the same request on setting the waiting limit between zero and non-zero values.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?user-common-data-type? -->
	<xs:complexType name="user-common-data-type">
		<xs:sequence>
			<xs:element name="ucd-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the user common data that are available to the
            operator rather than the user. This must be present on the creation of user-common-data.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the user common data. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.This must be present on the creation of user-common-data.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-targets" type="max-ucd-targets-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The max-targets element controls the maximum number of distinct targets that the
                  user can have in the target-list. Integer value between 2 and 10. This must be present on the creation of
                  user-common-data.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-device-targets" type="max-ucd-device-targets-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The max-device-targets element controls the maximum number of distinct devices
                  that the user can have in the target-device-list. Integer value between 2 and 10. This must be present on the creation of
                  the user common data.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="target-device-list" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The target-device-list element has values "activated" or "deactivated". When set
                  to "activated" the user is allowed to use the target-device-list element of the user common data. This must be present on
                  the creation of the user common data.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="holiday-list" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The holiday-list element has values "activated" or "deactivated". When set to "activated" the user is allowed to use the holiday-list element of the user common data.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="home-location" type="home-location-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  Confirming to P-Access-Network-Info header ABNF syntax described in 3GPP TS 24.229: IP Multimedia Call Control Protocol based on Session Initiation Protocol (SIP) and Session Description Protocol (SDP); Stage 3 (Release 11) section 7.2A.4.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="time-zone-area" type="time-zone-area-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation> The time-zone-area is the user home time zone area.  The time-zone-area is in the form "Area/Location and must be included in the list of time zones in IANA Time Zone Database. Example: "Asia/Tokyo", "Canada/Central", "Europe/Copenhagen", "Australia/Canberra", "America/Los_Angeles".</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mmtel-charging-profile" type="xs:string" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation> 
                  The mmtel-charging-profile element specifies the name of the mmtel charging profile to be used. The mmtel charging profile must have been configured in MTAS.
				</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="auto-answer-avoidance-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                          The auto-answer-avoidance-condition element has values "activated" or "deactivated".
                          When set to "activated" the user is provisioned with the Auto-Answer Avoidance feature.
                      </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="in-sip-request-condition" type="activatedType" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                          The in-sip-request-condition element has values "activated" or "deactivated".
                          When set to "activated" it allows the subscriber to use the in-sip-request
                          condition in supplementary service rules.
                      </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="in-sip-request-condition-list" type="in-sip-request-condition-list-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                          The in-sip-request-condition-list is a grouping element for definitions
                          of SIP regexp conditions.
                      </xs:documentation>
							</xs:annotation>
							<xs:key name="in-sip-request-condition-list-flexcondition-definition-key">
								<xs:selector xpath="./flexcondition-definition"/>
								<xs:field xpath="@id"/>
							</xs:key>
						</xs:element>
						<xs:element name="subscription" type="subscription-type" nillable="true" minOccurs="0">
						    <xs:annotation>
						        <xs:documentation xml:lang="en">
						        The subscription element specifies the group of subscriber identity attributes. This element is a sub-MO 
						        allowing multiple instances with “id” as the unique key. Currently it is limited to one instance but is 
						        designed to be extended. Use xsi:nil="true" to delete this element. This element is optional.
						        </xs:documentation>
						   </xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ucd-user-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the user common data that are available for the user
            to set directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
            provisioned i.e. user common data is present and activated is "true".
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="target-device-list" type="target-device-list-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A list of all of the devices associated with the user’s identity which can be
                  selected individually for distribution of calls. Up to 10 entries can be included.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="target-list" type="ucd-target-list-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A list of all of the related targets that can be included in communication
                  distribution rules in addition to the PRIMARY number itself i.e. that of the served user. Up to 10 entries can be
                  included.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="utc-offset" type="time-offset-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The utc-offset element specifies the offset to be taken from UTC when determining
            	  times of day and when each day starts and ends. This element is used for valid-periods conditions when utc-offset is not specified in the valid-periods
				  condition. If utc-offset element is omitted then the offset from the node CM attribute is used. It is also used for validity and invalidity conditions,
       			  when they are given with local time. If utc-offset element is omitted then the offset from the node CM attribute is used.
          		</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="start-day-of-week" type="weekday-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The start-day-of-weekelement specifies the starting day of the week, 
	    		  used when evaluating time conditions related to weeks of year or containing weekly repetition.
	    		  If start-day-of-week is omitted then the starting day  from the node CM attribute is used.
	    		</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="non-workday-list" type="workdays-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A list of weekdays considered as workday during evaluation of the  time conditions associated with the 
				  user’s identity. Up to 7 entries can be included. If workday-list is omitted then the node CM attribute defining the workday list is used.
	    		</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="holiday-list" type="holidays-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A list of private holidays to be used during evaluation of the time conditions associated with the 
			      user’s identity. Up to 20 entries can be included. Also inheritance of the public holidays configured on node level can be specified.
	    		</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="max-ucd-targets-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="2"/>
			<xs:maxInclusive value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="max-ucd-device-targets-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="2"/>
			<xs:maxInclusive value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="target-device-list-type">
		<xs:sequence>
			<xs:element name="fixed-targets" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">If fixed-targets is set to "true" then the target identities are set by the operator
            and cannot be changed by the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="target-device" type="target-device-type" nillable="true" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">The target-device element is a sub-MO allowing multiple instances with "name" as the
            unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="target-device-type">
		<xs:sequence>
			<xs:element name="name" type="target-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name for the target device. This is the name by which distribution rules refer to
            devices as targets. This must be present on the creation of a target-device element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="terminal-selector" type="terminal-selector-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The terminal selector is the way that the individual device is selected. It is an
            alphanumeric string of between 1 and 300 characters. This must be present on the creation of a target-device element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="name" type="target-name-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="terminal-selector-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="300"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="workdays-type">
		<xs:sequence>
			<xs:element name="weekday" type="weekday-type" minOccurs="0" maxOccurs="7">
				<xs:annotation>
					<xs:documentation xml:lang="en">The workday element specifies the weekday used as workday in the valid-periods condition.
			This is a multi-value parameter.
		  </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="holidays-type">
		<xs:sequence>
			<xs:element name="holiday" type="xs:date" minOccurs="0" maxOccurs="20">
				<xs:annotation>
					<xs:documentation>
					The holiday element specifies one private holiday for the user.
					This is a multi-value parameter.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="use-national" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					When the use-national is set to TRUE, beside the private holidays set in element holiday,
					Also the public holidays configured on node level are used during evaluation of the time conditions 
					associated with the user’s identity
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?three-pty? -->
	<xs:complexType name="three-pty-type">
		<xs:sequence>
			<xs:element name="three-pty-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the three party service that are available to the
            operator rather than the user. This must be present on the creation of the three-pty service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the three party service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the three-pty service.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?priority-call? -->
	<xs:complexType name="priority-call-type">
		<xs:sequence>
			<xs:element name="priority-call-operator-configuration" type="priority-call-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the priority call service that are available to the
            operator rather than the user. This must be present on the creation of the priority-call service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="priority-call-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the priority call service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.This must be present on the creation of the priority-call service.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?malicious-communication-identification? -->
	<xs:complexType name="malicious-communication-identification-type">
		<xs:sequence>
			<xs:element name="mcid-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the malicious communication identification service
            that are available to the operator rather than the user. This must be present on the creation of the
            malicious-communication-identification service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the malicious communication identification service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the malicious-communication-identification service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mcid-mode" type="mcidModeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The mcid-mode element has values "permanent" or "temporary". If set to
                  "permanent" then all communications are logged. If set to "temporary" this allows a recent communication to be logged on
                  user request. This must be present on the creation of the malicious-communication-identification service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mcid-orig-mode" type="mcidOrigModeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                          The mcid-orig-mode element has values "permanent" or "inactive". If set to "permanent" then all originating
                          communications are logged. If set to "inactive" then logging of originating communications is disabled.
                      </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="mcidModeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Mode of MCID operation - permanent - logs all communications - temporary - allows the last
        communication to be logged on user request
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="permanent"/>
			<xs:enumeration value="temporary"/>
			<xs:enumeration value="inactive"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="mcidOrigModeType">
		<xs:annotation>
			<xs:documentation>
                Mode of originating MCID operation
                - permanent - logs all originating communications
                - inactive - logging of originating calls is disabled
            </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="permanent"/>
			<xs:enumeration value="inactive"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="malicious-communication-rejection-type">
		<xs:sequence>
			<xs:element name="mcr-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the malicious communication rejection service that
            are available to the operator rather than the user. This must be present on the creation of the malicious-communication-
            rejection service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the malicious communication rejection service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the malicious-communication-rejection service.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?dynamic-black-list? -->
	<xs:complexType name="dynamic-black-list-type">
		<xs:sequence>
			<xs:element name="dbl-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the dynamic black list service that are available to
            the operator rather than the user. This must be present on the creation of the dynamic-black-list service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the dynamic black list service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the dynamic-black-list service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="caller-details" type="caller-details-type" nillable="true" minOccurs="0" maxOccurs="30">
							<xs:annotation>
								<xs:documentation xml:lang="en">Details of a caller</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="caller-details-key">
					<xs:selector xpath="./mmtel-profile-serv:caller-details"/>
					<xs:field xpath="@insertion-time"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?explicit-communication-transfer? -->
	<xs:complexType name="explicit-communication-transfer-type">
		<xs:sequence>
			<xs:element name="ect-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the explicit communication transfer service that are
            available to the operator rather than the user. This must be present on the creation of the explicit communication transfer
            service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to
                  "true" the user is provisioned with the explicit communication transfer service. If set to "false" this will withdraw the
                  service from the user. This must be present on the creation of the explicit communication transfer service. If set to
                  "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines
                  if the user service
                  is activated or not.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="caller-details-type">
		<xs:sequence>
			<xs:element name="insertion-time" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The insertion-time element records the time that the caller-details element was added
            to dynamic-black-list. This must be present on the creation of a caller-details element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="identity-list" type="dbl-identity-list-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The identity-list element is a list of identities of the caller that is to be barred.
            This must be present on the creation of a caller-details element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="expiry-time" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The expiry-time element records the time that the caller-details element expires and
            will no longer be used to bar calls. Absence of this element from a caller-details element, means that the caller-details
            element will not expire.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reason" type="dbl-reason-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The reason element records the service that added the caller-details element to the
            dynamic-black-list. A value of “DBL�?indicates that the caller-details element was added by an invocation of the Dynamic Black
            List service. A value of “MCR�?indicates that the caller-details element was added by an invocation of the Malicious
            Communication Rejection service. This must be present on the creation of a caller-details element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="insertion-time" type="xs:dateTime" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="insertion-timeAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="dbl-identity-list-type">
		<xs:sequence>
			<xs:element name="identity" type="xs:anyURI" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">The identity element records one of the public identities of the caller that is to be
            barred. The identity element is restricted to be a sip: URI, as defined in RFC 3261, or a tel: URI, as defined in RFC 3966. tel:
            URIs, and sip: URIs that have been converted from a tel: URI in accordance with section 19.1.6 of RFC 3261, must be normalized.
            This is a multi-value parameter.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?common-data? -->
	<xs:complexType name="common-data-type">
		<xs:sequence>
			<!-- new common data elements should be optional and added in alphabetical position within this sequence -->
			<xs:element name="area-code" type="area-code-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Area code 0-6 digits. Leave empty for numbering plans to which it does not apply.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="country-code" type="country-code-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Country code 1-4 digits.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="display-name" type="display-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Name of subscriber of length between 0-64 characters.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="integration" nillable="true" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">The integration element specifies the mapping between a key and a corresponding value.
            This can be used for transparent storage of values required for integration with other systems. The integration element is a
            sub-MO allowing multiple instances with "key" as the unique key. There can be between 0 and 5 integration elements.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="key" type="transparent-key-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The key for integration data. String of 1-20 characters. This must be present on
                  the creation of an integration element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="value" type="transparent-value-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The value of the integration data that corresponds to the key. String of 0-50
                  characters. This must be present on the creation of an integration element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="key" type="transparent-key-type" use="required">
						<xs:annotation>
							<xs:appinfo>
								<jaxb:property name="keyAttr"/>
							</xs:appinfo>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="language-tag" type="language-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Preferred language 0-64 characters</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-global-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum number of allowed rules in the user document. Not specified or zero limit
            means no limit.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="service-profile-identity" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The identity of the service profile. This must be an SIP URI. If this element is
            empty, it is deleted.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="language-type">
		<xs:restriction base="xs:language">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?carrier-select-rn? -->
	<xs:complexType name="carrier-select-rn-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The carrier select rn service. Use xsi:nil="true" to withdraw the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="csrn-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the carrier select rn service that are available to
            the operator rather than the user. This must be present on the creation of the carrier-select-rn service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the carrier select rn service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the carrier-select-rn service.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?closed-user-group? -->
	<xs:complexType name="closed-user-group-type">
		<xs:sequence>
			<xs:element name="cug-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The configuration parameters for the closed user group service that are available to the operator rather than the user. This must be present on the creation of the closed user group service.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The activated element has values "true", "false" or profile. When set to "true" the user is provisioned with the closed user group service.  If set to "false" this will withdraw the service from the user. This must be present on the creation of the closed user group service. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cug-interlock-code" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation> The cug element specifies a closed user group that the user is a member of. The cug element is a sub-MO allowing multiple instances with "cug-index" as the unique key.  This is limited to one member but is designed to be extended.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="cug-index" type="cug-index-type" minOccurs="0">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="elementCug-index"/>
											</xs:appinfo>
											<xs:documentation> The local index for the CUG currently limited to 1. This must be present on the creation of a cug element.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cug-network-identity" type="cug-Network-Identity-Type" minOccurs="0">
										<xs:annotation>
											<xs:documentation xml:lang="en"> The network identity for the CUG.  This must be present on the creation of the cug element.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cug-binary-code" type="cug-Interlock-Binary-Code-Type" minOccurs="0">
										<xs:annotation>
											<xs:documentation xml:lang="en">The interlock code for the CUG.  This must be present on the creation of the cug element.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="cug-index" type="cug-index-type" use="required"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="cug-index-key">
					<xs:selector xpath="./mmtel-profile-serv:cug-interlock-code"/>
					<xs:field xpath="@cug-index"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?carrier-pre-select-rn? -->
	<xs:complexType name="carrier-pre-select-rn-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The carrier pre-select rn service. Use xsi:nil="true" to withdraw the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="cpsrn-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the carrier pre-select-rn service that are available
            to the operator rather than the user. This must be present on the creation of the carrier-pre-select-rn service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the carrier pre-select rn service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the carrier-pre-select-rn service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="call-type-carrier-rn" type="call-type-carrier-rn-type" nillable="true" maxOccurs="2">
							<xs:annotation>
								<xs:documentation xml:lang="en">The call-type-carrier-rn element specifies a mapping between a call type and the
                  global carrier id to be pre-selected for calls of that type. The call-type-carrier-rn element is a sub-MO allowing either
                  one or two instances with "call-type" as the unique key.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="call-type-carrier-rn-key">
					<xs:selector xpath="./mmtel-profile-serv:call-type-carrier-rn"/>
					<xs:field xpath="@call-type"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-type-carrier-rn-type">
		<xs:sequence>
			<xs:element name="call-type" type="call-type-rn-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The type of call either “LOCAL�?or “REMOTE�? This must be present on the creation of a
            call-type-carrier-rn. The value “LOCAL�?corresponds to calls to numbers with the same area code as the user. The value
            “REMOTE�?corresponds to all other calls.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="global-carrier-code" type="carrier-code-rn-id-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The global carrier to be use for a call of the given type. This is a string of between
            3 and 8 digits. This must be present on the creation of a call-type-carrier-rn.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="call-type" type="call-type-rn-type" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="call-typeAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--?call-completion-monitor-opt-out? -->
	<xs:complexType name="call-completion-monitor-opt-out-type">
		<xs:sequence>
			<xs:element name="cc-monitor-opt-out-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the call completion monitor opt out service that are
            available to the operator rather than the user. This must be present on the creation of the call-completion-monitor-opt-out
            service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the call completion monitor opt out service. If set to "false" this withdraws the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the call-completion-monitor-opt-out service. Note: There are also optional sub-options for this service that are of the activation type. If a sub-option is present and is set to the "deactivated" value, then the opt out is overridden and call completion is offered for that sub-option of call completion. If a sub-option is present and is set to the "activated" value, or the sub-option is not provided at all, then the opt out applies and no call completion is offered.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccbs" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The ccbs element has values "activated" or "deactivated". When set to
                  "deactivated" it disables the monitor opt out for the communication completion on busy service and ccbs is offered from
                  the served user. When set to the value "activated", the opt out applies.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccnr" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The ccnr element has values "activated" or "deactivated". When set to
                  "deactivated" it disables the monitor opt out for the communication completion on busy service and ccnr is offered from
                  the served user. When set to the value "activated", the opt out applies.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccnl" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation> The ccnl element has values "activated" or "deactivated".  When set to "deactivated" it disables the monitor opt out for the communication completion on not logged in service and ccnl is offered from the served user.  When set to the value "activated", the opt out applies.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?call-completion? -->
	<xs:complexType name="call-completion-type">
		<xs:sequence>
			<xs:element name="cc-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the call completion service that are available to the
            operator rather than the user. This must be present on the creation of the call-completion service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the call completion service. If set to "false" this withdraws the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the call-completion service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccbs" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The ccbs element has values "activated" or "deactivated". When set to
                  "activated" it provisions the user with the call completion on busy service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccnr" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The ccnr element has values "activated" or "deactivated". When set to
                  "activated" it provisions the user with the call completion by no reply service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccnl" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The ccnl element has values "activated" or "deactivated". When set to "activated" it provisions the user with the communication completion not logged-in service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ccivr" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The ccivr element has values "activated" or "deactivated". When set to
                  "activated" it provisions the user with the call completion ivr feature.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cc-monitor-queue-size" type="ccMonitorQueueSizeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The ccMonitorQueueSize element is an optional element to set the size of the monitor queue on the terminating MTAS.
                  If present, it provisions the subscriber with an alternative queue size which overrides the CM attribute mtasCcMonitorQueueSize.
                  It is of type integer .
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-number-of-ccbs-requests-in-monitor-queue" type="ccMonitorQueueSizeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The maxNumberOfCcbsRequestsInMonitorQueue element is an optional element to set the limit on the number of CCBS service requests
                  in the monitor queue on the terminating MTAS. This element is mandatory in case ccMonitorQueueSize has been provisioned and not
                  allowed in case ccMonitorQueueSize has not been provisioned. It is of type integer.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-number-of-ccnr-requests-in-monitor-queue" type="ccMonitorQueueSizeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The maxNumberOfCcnrRequestsInMonitorQueue element is an optional element to set the limit on the number of CCNR service requests
                  in the monitor queue on the terminating MTAS. This element is mandatory in case ccMonitorQueueSize has been provisioned and not
                  allowed in case ccMonitorQueueSize has not been provisioned. It is of type integer.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="max-number-of-ccnl-requests-in-monitor-queue" type="ccMonitorQueueSizeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The max-number-of-ccnl-requests-in-monitor-queue element is an optional element to set the limit on the number of CCNL service requests in the monitor queue on the terminating MTAS. This element is mandatory in case cc-monitor-queue-size has been provisioned and not allowed in case cc-monitor-queue-size has not been provisioned. It is of type integer.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?call-admission-control-group-membership? -->
	<xs:complexType name="call-admission-control-group-membership-type">
		<xs:sequence>
			<xs:element name="cac-group-membership-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the user membership of call admission group service
            that are available to the operator rather than the user. This must be present on the creation of the
            call-admission-control-group-membership service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the user membership of call admission group service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.This must be present on the creation of the call-admission-control-group-membership service.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cac-group-identity" type="xs:anyURI" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity of the Call Admission Control Group that this user is a member of.
                  Should be a SIP URI (RFC 3261) or a tel URI (RFC 3966). This must be present on the creation of the
                  call-admission-control-group-membership service
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?advice-of-charge? -->
	<xs:complexType name="advice-of-charge-type">
		<xs:sequence>
			<xs:element name="aoc-operator-configuration" type="aoc-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the advice-of-charge service that are available to
            the operator rather than the user. This must be present on the creation of the advice-of-charge service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="aoc-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the advice of charge service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the advice-of-charge service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="service-type" type="service-typeType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This element holds which AOC service types are provisioned with possible values are
            aoc-s, aoc-d and aoc-e. This must be present on the creation of the advice-of-charge service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="currency-or-units" type="currency-or-unitsType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The currency-or-units element contains the user’s choice of how advice of charge data
            should be presented. This element must be present on the creation of the advice-of-charge service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="service-typeType">
		<xs:sequence>
			<xs:element name="operator-aoc-s" type="aoc-service-typeType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence of the operator-aoc-s element indicates that the user is provisioned with
            the AOC-S(tart) service type.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="operator-aoc-d" type="aoc-service-typeType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence of the operator-aoc-d element indicates that the user is provisioned with
            the AOC-D(uring) service type.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="operator-aoc-e" type="aoc-service-typeType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence of the operator-aoc-e element indicates that the user is provisioned with
            the AOC-E(nd) service type.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="aoc-service-typeType">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the service type is activated. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the parent service type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="aoc-service-obligatory" type="aoc-service-obligatory-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The aoc-service-obligatory indicates that the user is provisioned with the obligatory
            type of AOCI(information) for this service type. This must be present on the creation of the parent service type.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="currency-or-unitsType">
		<xs:choice>
			<xs:element name="currency-as-ISO-4217-numeric" type="currency-as-ISO-4217-numeric-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence of the currency-as-ISO-4217-numeric element indicates that the user
            prefers to receive his advice of charge data in currency. The element content is a numeric value that indicates the actual
            currency as defined by ISO 4217. The elements currency-as-ISO-4217-numeric and units are mutually exclusive.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="units" type="empty-element-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence of the units element indicates that the user prefers to receive his
            advice of charge data in units. The elements currency-as-ISO-4217-numeric and units are mutually exclusive.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<!--?abbreviated-dialing? -->
	<xs:complexType name="abbreviated-dialing-type">
		<xs:sequence>
			<xs:element name="abbreviated-dialing-operator-configuration" type="abbreviated-dialing-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the abbreviated dialing service that are available to
            the operator rather than the user. This must be present on the creation of the abbreviated-dialing service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="abbreviated-dialing-user-configuration" type="abbreviated-dialing-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the abbreviated dialing service that are available
            for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present if the
            service is provisioned i.e. abbreviated-dialing-operator-configuration is present and activated is "true".
          </xs:documentation>
				</xs:annotation>
				<xs:key name="abbreviated-dialing-type-key">
					<xs:selector xpath="./mmtel-profile-serv:number-mapping"/>
					<xs:field xpath="@abbreviated-number"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="abbreviated-dialing-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the abbreviated dialing service. If set to "false" this will withdraw the user service and the abbreviated-dialing-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the abbreviated-dialing service.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="abbreviated-dialing-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the abbreviated
            dialing service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="number-mapping" nillable="true" minOccurs="0" maxOccurs="100">
				<xs:annotation>
					<xs:documentation xml:lang="en">The number-mapping element specifies the mapping between an abbreviated number and the
            full stored number to be substituted when the abbreviated number is dialed. The number-mapping element is a sub-MO allowing
            multiple instances with "abbreviated-number" as the unique key.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="abbreviated-number" type="abbreviated-number-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The abbreviated form of a number between 0 and 99. This must be present on the
                  creation of a number-mapping element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="stored-number" type="stored-number-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The stored number in its full form, which is substituted when the user dials the
                  corresponding abbreviated number. The stored-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted
                  from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized
                  after removing a dynamic ad-hoc presentation SSC and/or a CSC. This must be present on the creation of a number-mapping
                  element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="abbreviated-number" type="abbreviated-number-type" use="required">
						<xs:annotation>
							<xs:appinfo>
								<jaxb:property name="abbreviated-numberAttr"/>
							</xs:appinfo>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="northbound-call-control-type">
		<xs:sequence>
			<xs:element name="ncc-operator-configuration" type="ncc-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the Northbound Call Control service are available to
            the operator only. This element must be present on the creation of the Northbound Call Control service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ncc-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the
            user is provisioned with
            the northbound call control service. If set to "false" this will withdraw the service from the user.
            This must be present on the creation of the northbound call control service.
            If set to "profile" this user service is provisioned
            via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:sequence>
					<xs:element name="gsm-scf-address" type="E164-number-type">
						<xs:annotation>
							<xs:documentation xml:lang="en">This E.164 number is the address of the gsmSCF.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="originating-service-key" type="xs:int">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the key that identifies the application within the gsmSCF. A value of -1 can
                be provisioned, which means that CAMEL application will not be triggered for the user on the originating side.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="terminating-service-key" type="xs:int">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the key that identifies the application within the gsmSCF. A value of -1 can
                be provisioned, which means that CAMEL application will not be triggered for the user on the terminating side.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="default-call-handling" type="default-call-handling-type">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines how the call shall proceed in case of signaling failure towards the gsmSCF.
                Possible values are continue and release.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:sequence>
					<xs:element name="px-originating-trigger" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                This is the trigger that defines if the originating MTAS shall contact the Parlay X application server.
              </xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="px-application-address" type="xs:anyURI">
									<xs:annotation>
										<xs:documentation>
                      This is the URL, including the port, to the Parlay X application server.
                    </xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:sequence>
									<xs:element name="px-call-notification" type="px-call-notification-type" maxOccurs="6">
										<xs:annotation>
											<xs:documentation>
                        Defines the possible Call Events that shall be reported on the CallNotification interface.
                        Possible values are busy, not-reachable, no-answer, called-number, answer and disconnected.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="px-terminating-trigger" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                This is the trigger that defines if the terminating MTAS shall contact the Parlay X application server.
              </xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="px-application-address" type="xs:anyURI">
									<xs:annotation>
										<xs:documentation>
                      This is the URL, including the port, to the Parlay X application server.
                    </xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:sequence>
									<xs:element name="px-call-notification" type="px-call-notification-type" maxOccurs="6">
										<xs:annotation>
											<xs:documentation>
                        Defines the possible Call Events that shall be reported on the CallNotification interface.
                        Possible values are busy, not-reachable, no-answer, called-number, answer and disconnected.
                      </xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!--?number-portability-announcement? -->
	<xs:complexType name="number-portability-announcement-type">
		<xs:sequence>
			<xs:element name="npa-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the NP announcement service that are available to the operator rather than the user. 
			This must be present on the creation of the NP announcement service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
					The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the NP announcement service.
					This must be present on the creation of the NP announcement service.
					If set to "profile" this user service is provisioned via a Service Profile. 
					Thus, it is the Service Profile document that determines if the user service is activated or not.
					</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?operator-controlled-outgoing-barring-programs? -->
	<xs:complexType name="operator-controlled-outgoing-barring-programs-type">
		<xs:sequence>
			<xs:element name="ocobp-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the operator controlled outgoing barring programs
            service that are only available to the operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the operator controlled outgoing barring programs service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="operator-barring-program" type="operator-barring-program-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The operator-barring-program element is a container for each of the categories
                  of outgoing communications that is to be barred by the service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="operator-permitted-program" type="operator-permitted-program-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The operator-permitted-program element is a container for each of the categories
                  of outgoing communications that is to be allowed by the service - any identity not matched by one of these categories or
                  the global white list is barred. The operator-barring-program and operator-permitted-program are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="operator-diversion-barring-program" type="operator-diversion-barring-program-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The operator-diversion-barring-program element is a container for each of the
                  categories of outgoing communications that should be barred as diversion targets.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="operator-barring-program-type">
		<xs:sequence minOccurs="0" maxOccurs="83">
			<xs:element name="category-name" type="category-name-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The category-name element contains the name of a category of calls to be barred. This
            is a multi-value parameter and can appear between 0 and 83 times to cover each category of outgoing communications to be barred.
            The value of each category-name element is a string of up to 32 characters.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="operator-permitted-program-type">
		<xs:sequence minOccurs="0" maxOccurs="83">
			<xs:element name="category-name" type="category-name-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The category-name element contains the name of a category of calls to be permitted.
            This is a multi-value parameter and can appear between 0 and 83 times to cover each category of outgoing communications to be
            permitted. The value of each category-name element is a string of up to 32 characters.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="operator-diversion-barring-program-type">
		<xs:sequence minOccurs="0" maxOccurs="83">
			<xs:element name="category-name" type="category-name-type" nillable="true">
				<xs:annotation>
					<xs:documentation xml:lang="en">The category-name element contains the name of a category of calls to be barred for
            diverted communications. This is a multi-value parameter and can appear between 0 and 83 times to cover each category of
            outgoing communications to be barred. The value of each category-name element is a string of up to 32 characters.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?outgoing-barring-programs? -->
	<xs:complexType name="outgoing-barring-programs-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The outgoing barring programs service. Use xsi:nil="true" to withdraw the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="obp-operator-configuration" type="bp-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the outgoing barring programs service that are
            available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="obp-user-configuration" type="bp-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the outgoing barring programs service that are
            available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present
            if the service is provisioned i.e. obp-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="bp-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator part of outgoing-barring-programs</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the outgoing barring programs service. If set to "false" this will withdraw the user service and the obp-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="scheme" type="program-scheme-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The element scheme has values "single" and "multiple" and controls which type of
            barring programs apply to the subscriber. The "single" scheme allows one program at a time. With the "multiple" scheme, several
            programs can be combined at any time, allowing the individual programs to be simpler.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="default-barring-program" type="program-number" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The element specifies the default barring program. Only one barring program can be provisioned at the time. It is an integer in the range 0-255, when the scheme is set to single. If the scheme is set to multiple the allowed range is 0-49. When the user via SSC codes activates barring program without specifying a barring program the value in this element, if present, will be added to the user part. In case of multiple scheme the number in this element will be translated to a category before written to the user part. The attribute is nillable which means it can be deleted.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="program-scheme-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="single"/>
			<xs:enumeration value="multiple"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="bp-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">User part of outgoing-barring-programs</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the outgoing
            barring programs service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- EMA does not support nilling of single value parameters so the wrapping structured parameter provisioned-program is included to allow nilling at that level -->
			<xs:element name="provisioned-program" type="provisioned-program-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The provisioned-program element is a containing element allowing the choice between
            either single-program or multiple-programs. The choice must reflect the provisioned value of scheme
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="provisioned-program-type">
		<xs:choice>
			<xs:element name="single-program" type="program-number" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The single-program element contains the number of the combined barring program to be
            used. It is an integer in the range 0-255. The elements multiple-programs and single-program are mutually exclusive.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="multiple-programs" type="multiple-program-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The multiple-programs element is a container for each of the categories of outgoing
            communications that is to be barred by the service. The elements multiple-programs and single-program are mutually exclusive.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="program-number">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:maxInclusive value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?outgoing-communication-barring? -->
	<xs:complexType name="outgoing-communication-barring-type">
		<xs:sequence>
			<xs:element name="ocb-operator-configuration" type="ocb-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the outgoing communication barring service that are
            only available to the operator
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-user-configuration" type="ocb-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the outgoing communication barring service that are
            available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be present
            if the service is provisioned i.e. ocb-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the outgoing communication barring service. If set to "false" this will withdraw the user service and the ocb-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-ruleset" type="ocb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more operator rules. These rules apply
            regardless of whether activated is "true" or "false".
          </xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="ocb-op-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:ocb-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
			<xs:element name="ocb-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The ocb-op-conditions element is a grouping element for fine-grain provisioning
            options that control which condition elements the user is permitted to use in outgoing communication barring rules.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the identity condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="roaming-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The roaming-condition element has values "activated" or "deactivated". When set to "activated" it allows the subscriber to use roaming  conditions in outgoing communication barring rules. This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="international-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The international-condition element has values "activated" or "deactivated". When set to "activated" it allows the subscriber to use international conditions in outgoing communication barring rules. This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="international-exHC-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The international-exHC-condition element has values "activated" or "deactivated". When set to "activated" it allows the subscriber to use international-exHC conditions in outgoing communication barring rules. This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="media-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The media-condition element has values "activated" or "deactivated". When set to
                  "activated" it allows the subscriber to use media conditions in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The other-identity-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the other-identity condition in outgoing communication barring
                  rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The presence-status-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use presence-status conditions in outgoing communication barring
                  rules. This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="validity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The validity-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the validity condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The valid-periods-condition element has values "activated" or "deactivated".
                  When set to "activated" it allows the subscriber to use the valid-periods condition in outgoing communication barring
                  rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The invalidity-condition element has values "activated" or "deactivated". When
                  set to "activated" it allows the subscriber to use the invalidity condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The carrier-condition element has values "activated" or "deactivated". When set
                  to "activated" it allows the subscriber to use the carrier condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-select-code" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The carrier-select-code-condition element has values "activated" or
                  "deactivated". When set to "activated" it allows the subscriber to use the carrier-select-code element of the carrier
                  condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                  the subscriber to use the served-identity condition in outgoing communication barring rules.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ocb-op-actions" type="call-barring-op-actions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The ocb-op-actions element is a grouping element for fine-grain provisioning options
            to control which action elements the user is permitted to use in outgoing communication barring rules.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The maximum number of allowed outgoing communication barring rules in the user
            document. Not specified or zero limit means no limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en"/>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the outgoing
            communication barring service is active or not for this subscriber. Note that this controls the user rules but has no effect on
            the operator rules.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-ruleset" type="ocb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Grouping element for a set of zero or more user rules.</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="ocb-user-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:ocb-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-ruleset-type">
		<xs:sequence>
			<xs:element name="ocb-rule" type="ocb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">An individual rule controlling outgoing communication barring behaviour. The ocb-rule
            element is a sub-MO allowing multiple instances with "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A unique identifier for an individual rule. This must be unique within the scope of
            the complete document
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-conditions" type="ocb-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The ocb-conditions element is a grouping element for conditions for a rule. All
            conditions must be satisfied for the rule to take effect. If no conditions are present then the rule is always applicable.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set could just update conditions so actions must be optional -->
			<xs:element name="cb-actions" type="call-barring-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The cb-actions element is a grouping element for the actions for a rule. For outgoing
            communication barring an allow action must be present in each rule.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ocb-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="rule-deactivated-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The rule-deactivated element has values "true" or "false". If present with the value
            "true" this has the effect of deactivating the individual rule. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The ocb-caller-identity element is a grouping element for conditions which are based
            on the called party's identity.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="other-identity" type="empty-element-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The other-identity element is an empty element which matches any identity that
                  has not been specified by any of the other rules in the ruleset. It allows for setting a default policy. This can be
                  removed by deleting the enclosing ocb-caller-identity element or by replacing it with an identity element. The elements
                  identity and other-identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on ocb-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity element is a grouping element for conditions which are based on the
                  called party's identity. The condition is satisfied if any of the included one or many elements within it is matched. This
                  can be removed by deleting the enclosing ocb-caller-identity element or by replacing it with an other-identity element.
                  The elements identity and other-identity are mutually exclusive.
                </xs:documentation>
							</xs:annotation>
							<xs:key name="ocb-one-key">
								<xs:selector xpath="./mmtel-profile-serv:one"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="ocb-many-key">
								<xs:selector xpath="./mmtel-profile-serv:many"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="ocb-except-id-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-id"/>
								<xs:field xpath="@id"/>
							</xs:key>
							<xs:key name="ocb-except-domain-key">
								<xs:selector xpath=".//mmtel-profile-serv:except-domain"/>
								<xs:field xpath="@domain"/>
							</xs:key>
							<xs:key name="ocb-number-match-key">
								<xs:selector xpath="./mmtel-profile-serv:number-match"/>
								<xs:field xpath="@starts-with"/>
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="roaming" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The roaming element has values "true" or "false". If present with the value "true", then outgoing calls when roaming are barred. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="international" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The international element has values "true" or "false". If present with the value "true", the outgoing calls if they are international are barred. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="international-exHC" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The international-exHC element has values "true" or "false". If present with the value "true", the outgoing calls if they are international excluding calls to Home Country are barred. Set to "false" to remove this condition.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The media element contains a media type that the session must include for the
            condition to be matched e.g. "audio" or "video". This is a multi-value parameter so it can appear more than once with each of
            several media types that must be included.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- validity is a structured parameter so it can be nillable -->
			<xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The validity element is a grouping element for time periods (intervals) within which
            the rule is valid.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing validity element -->
				<xs:key name="ocb-interval-key">
					<xs:selector xpath="./mmtel-profile-serv:interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The presence-status element contains a presence status value that the user must
            satisfy for the condition to be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can
            appear more than once with several presence status values that must all be satisfied for the overall condition to be matched.
            This condition is not currently supported by outgoing communication barring and will always evaluate to false.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The valid-periods element is a grouping element for recurring time periods (intervals)
            within which the rule is valid.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The invalidity element is a grouping element for time periods (intervals) within which
            the rule is NOT valid. The invalidity condition must contain at least one interval.
          </xs:documentation>
				</xs:annotation>
				<!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
				<xs:key name="ocb-invalidity-interval-key">
					<xs:selector xpath="./interval"/>
					<xs:field xpath="@from"/>
				</xs:key>
			</xs:element>
			<xs:element name="carrier" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The carrier element is a grouping element for conditions which are based on the
            carrier selected for the call on call-by-call basis. If no sub-element is specified, all carriers are matched. The carriers that
            match to the pre-subscribed carriers for the current call-type are subject to this condition.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="carrier-select-code" type="carrier-select-code-type" nillable="true" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">The carrier-select-code element contains the dialed Carrier Select Code. This is
                  a multi-value parameter so it can appear more than once with several Carrier Select Codes. If any of them is matches, the
                  carrier condition is fulfilled.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-name" type="carrier-name-type" nillable="true" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">The carrier-name element contains an alias name of the carrier selected for the
                  call on call-by-call basis. This is a multi-value parameter so it can appear more than once with several carrier names. If
                  any of them is matches, the carrier condition is fulfilled.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The served-identity element is a grouping element for conditions which are based on the user's 
            served identity. The condition is satisfied if any of the included elements within it is matched. 
          </xs:documentation>
				</xs:annotation>
				<xs:key name="ocb-served-identity-one-key">
					<xs:selector xpath="./mmtel-profile-serv:one"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?presentation-identity? -->
	<xs:complexType name="originating-calling-name-identity-presentation-type">
		<xs:sequence>
			<xs:element name="ocnip-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            The configuration parameters for the originating calling name identity presentation service that are available to the operator rather than the user.
            This must be present on the creation of the originating-calling-name-identity-presentation service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the originating calling name identity presentation service. If set to "false" this will withdraw the user service, but the ocnip-user-configuration element is kept. This must be present on the creation of the originating-calling-name-identity-presentation service. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<!--<xs:element name="external-query-type" type="external-query-type-type" minOccurs="0"/>-->
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="originating-identity-presentation-type">
		<xs:sequence>
			<xs:element name="oip-operator-configuration" type="oip-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the originating identity presentation service that
            are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="oip-user-configuration" type="oip-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the originating identity presentation service that
            are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be
            present if the service is provisioned i.e. oip-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oip-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">User part of OIP (Originating Identity Presentation)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the originating
            identity presentation service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oip-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator part of OIP (Originating Identity Presentation)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the originating identity presentation service. If set to "false" this will withdraw the user service and the oip-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The restriction-override element has values "override-active" or
            "override-not-active". The value "override-active" means that the originating identity will be presented even if the calling
            party has requested for their presentation to be restricted.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="originating-identity-presentation-restriction-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The originating identity presentation restriction service. Use xsi:nil="true" to withdraw
        the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="oir-operator-configuration" type="oir-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the originating identity presentation restriction
            service that are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="oir-user-configuration" type="oir-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">TBD</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oir-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The configuration parameters for the originating identity presentation restriction service
        that are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be
        present if the service is provisioned i.e. oir-operator-configuration is present and activated is "true"
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the originating
            identity presentation restriction service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The default-behaviour element has values "presentation-restricted" or
            "presentation-not-restricted". It selects the default behaviour in temporary mode when the user does not select explicitly
            within the call whether to restrict their identity or not.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oir-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator part of originating identity presentation restriction (OIR)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the originating identity presentation restriction service. If set to "false" this will withdraw the user service and the oir-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mode" type="identityPresentationModeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The mode element has values "permanent" or "temporary". The value "permanent" is used
            to give the user a permanent restriction service. In this case there must be no oir-user-configuration element. The value
            "temporary" gives an identity presentation restriction service where the user can choose a default behaviour and also whether to
            override this on a per-call basis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction" type="identityPresentationRestrictionType" default="all-private-information" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The restriction element has values "only-identity" or "all-private-information" and
            selects whether just the identity of the user is restricted or all private information.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- Terminating Identity Presentation/Restriction -->
	<xs:complexType name="terminating-identity-presentation-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The terminating identity presentation service. Use xsi:nil="true" to withdraw the entire
        service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tip-operator-configuration" type="tip-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the terminating identity presentation service that
            are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="tip-user-configuration" type="tip-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the terminating identity presentation service that
            are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall only be
            present if the service is provisioned i.e. tip-operator-configuration is present and activated is "true"
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tip-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">User part of terminating identity presentation (TIP)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" or "false". It controls whether the terminating
            identity presentation service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tip-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator part of terminating identity presentation (TIP)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the terminating identity presentation service. If set to "false" this will withdraw the user service and the tip-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The restriction-override element has values "override-active" or
            "override-not-active". The value "override-active" means that the terminating identity will be presented even if the called
            party has requested for their presentation to be restricted.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="terminating-identity-presentation-restriction-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The terminating identity presentation restriction service. Use xsi:nil="true" to withdraw
        the entire service.
      </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tir-operator-configuration" type="tir-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the terminating identity presentation restriction
            service that are available to the operator rather than the user.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="tir-user-configuration" type="tir-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the terminating identity presentation restriction
            service that are available for the user to set directly. These can also be set on the user's behalf by the operator. This shall
            only be present if the service is provisioned i.e. tir-operator-configuration is present and activated="true".
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tir-operator-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Operator part of terminating identity presentation restriction (TIPR)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the terminating identity presentation restriction service. If set to "false" this will withdraw the user service and the tir-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mode" type="identityPresentationModeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The mode element has values "permanent" or "temporary". The value "permanent" is used
            to give the user a permanent restriction service. In this case there must be no tir-user-configuration element. The value
            "temporary" gives an identity presentation restriction service where the user can choose a default behaviour and also whether to
            override this on a per-call basis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tir-user-configuration-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">User part of terminating identity presentation restriction (TIPR)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The active element has values "true" and "false". It controls whether the terminating
            identity presentation restriction service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The default-behaviour element has values "presentation-restricted" and
            "presentation-not-restricted". It selects the default behaviour in temporary mode when the user does not select explicitly
            within the call whether to restrict their identity or not
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!--Common Types are defined below -->
	<xs:simpleType name="abbreviated-number-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Abbreviated number from 0 to 99</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{1,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="stored-number-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The stored-number-type is a sip: or tel: URI. Each tel: URI and sip: URI that was
        converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized
        after removing a dynamic ad-hoc presentation supplementary service code and/or a carrier select code.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:anyURI"/>
	</xs:simpleType>
	<xs:simpleType name="country-code-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="display-name-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="area-code-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{0,6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="user-cac-limit-type">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:maxInclusive value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="group-cac-limit-type">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="call-type-rn-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LOCAL"/>
			<xs:enumeration value="REMOTE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="carrier-code-rn-id-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{3,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="currency-as-ISO-4217-numeric-type">
		<xs:restriction base="xs:integer">
			<xs:pattern value="\d{3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="aoc-service-obligatory-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AoCI">
				<xs:annotation>
					<xs:documentation xml:lang="en">AoCI is advisory only</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<!-- AoCC to be added here in future -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dbl-reason-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DBL"/>
			<xs:enumeration value="MCR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="identityPresentationRestrictionOverrideType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="override-active"/>
			<xs:enumeration value="override-not-active"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationModeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="permanent"/>
			<xs:enumeration value="temporary"/>
			<xs:enumeration value="ad-hoc-temporary-presentation-restricted"/>
			<xs:enumeration value="ad-hoc-temporary-presentation-not-restricted"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationRestrictionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="only-identity"/>
			<xs:enumeration value="all-private-information"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationDefaultBehaviourType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="presentation-restricted"/>
			<xs:enumeration value="presentation-not-restricted"/>
		</xs:restriction>
	</xs:simpleType>
	<!--?supplementary-service-codes? -->
	<xs:complexType name="supplementary-service-codes-type">
		<xs:sequence>
			<xs:element name="ssc-operator-configuration" type="ssc-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the supplementary service codes service that are only
            available to the operator
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ssc-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the supplementary service codes service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="pin-code" type="pin-code-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The pin-code element holds the PIN code that the user must enter to authorize any
            feature access codes requiring a PIN. It consists of a string of between 4 and 6 digits.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="pin-failures" type="pin-failures-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Element to keep track of failed PIN attempts. Delete this element with xsi:nil="true"
            to re-enable the PIN with the maximum number of attempts
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="pin-code-type">
		<xs:restriction base="xs:token">
			<xs:pattern value="\d{4,6}|[A-Fa-f0-9]{32}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="pin-failures-type">
		<xs:sequence>
			<xs:element name="count" type="xs:integer" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The count of consecutive failed PIN attempts. Once this reaches the maximum number of
            attempts, the PIN is disabled.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="first-fault" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The date and time of the first failed PIN attempt. This is used to determine when the
            PIN should be re-enabled.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--?voice-mail? -->
	<xs:complexType name="voice-mail-type">
		<xs:sequence>
			<xs:element name="vm-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the voice mail service that are only available to the
            operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the voice mail service. This allows the user to include the special identity "voicemail:internal" as the target for communication diversion rules. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="voice-mail-address" type="xs:anyURI" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The voice-mail-address element specifies the target identity to be used if a
                  user's communication diversion rule specifies diversion to "voicemail:internal". It takes the form of a normalized sip: or
                  tel: URI or the special value "voicemail:internal". In the case of the special value of "voicemail:internal" the
                  communication diversion will be sent to the identity specified in the node level configuration parameter.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="scheduled-conference-type">
		<xs:sequence>
			<xs:element name="scheduled-conference-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the scheduled conference service that are only
            available to the operator
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true" or "false". When set to "true" the
                  scheduled conference is ready for traffic operation. This element must be present on the creation of the
                  scheduled-conference service.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="service-number" type="xs:anyURI" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity of the Scheduled Conference Service Number that this user is served
                  by. Must be a TEL URI (RFC 3966) The element must be present on the creation of the scheduled-conference service. In
                  addition, the service number must be a valid PSI in HSS.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="months-type">
		<xs:sequence>
			<xs:element name="month" type="month-type" minOccurs="0" maxOccurs="12">
				<xs:annotation>
					<xs:documentation xml:lang="en">The month of the year. The format is integer of the month number. This is a
            multi-value parameter.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="weeks-type">
		<xs:sequence>
			<xs:element name="week" type="week-type" minOccurs="0" maxOccurs="52">
				<xs:annotation>
					<xs:documentation xml:lang="en">The week of the year. The format is integer of the week number. This is a multi-value
            parameter.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="month-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="12"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="week-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="53"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="repeat-daily-type">
		<xs:sequence>
			<xs:element name="begin-day" type="xs:date">
				<xs:annotation>
					<xs:documentation xml:lang="en">The start day of the repetition. The format is YYYY-MM-DD.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-interval" type="repeat-interval-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repetition interval in days. The format is integer.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="repeat-weekly-type">
		<xs:sequence>
			<xs:element name="begin-day" type="xs:date">
				<xs:annotation>
					<xs:documentation xml:lang="en">The start day of the repetition. The format is YYYY-MM-DD.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-interval" type="repeat-interval-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repetition interval in weeks. The format is integer.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="repeat-monthly-type">
		<xs:sequence>
			<xs:element name="begin-day" type="xs:date">
				<xs:annotation>
					<xs:documentation xml:lang="en">The start day of the repetition. The format is YYYY-MM-DD.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="repeat-interval" type="repeat-interval-type">
				<xs:annotation>
					<xs:documentation xml:lang="en">The repetition interval in months. The format is integer.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="flexible-identity-presentation-type">
		<xs:sequence>
			<xs:element name="fip-operator-configuration" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the flexible identity presentation service that are
            available to the operator rather than the user. This must be present on the creation of the flexible-identity-presentation
            service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to "true" the user
                  is provisioned with the calling name identity presentation service. If set to "false" this will withdraw the user service
                  and the cnip-user-configuration element must be deleted at the same time. If set to "profile" this user service is provisioned
                  via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="fip-user-configuration" type="fip-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the flexible identity presentation service that are
            available for the user to set directly.These can also be set on the user's behalf by the operator. This shall only be present if
            the service is provisioned.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="fip-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Controls whether the flexible identity presentation service is active or not for this
            subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="fip-identity" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This element is read-only on the user interface. It can be written only on the
            operator interface.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="msn-fip-identity" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The msn-fip-identity element specifies the mapping between an id and the identity to be substituted
            when the id is used. The msn-fip-identity element is a sub-MO allowing multiple instances with
            "id" as the unique key.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="id" type="msn-number-type" minOccurs="0">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="elementId"/>
								</xs:appinfo>
								<xs:documentation>
                  The id must be present on the creation of a msn-fip-identity element.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="identity" type="xs:anyURI" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
                  This element is read-only on the user interface. It can be written only on the
                  operator interface.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="id" type="msn-number-type" use="required"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="msn-number-type">
		<xs:annotation>
			<xs:documentation>
        MSN number
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="customized-alerting-tone-type">
		<xs:sequence>
			<xs:element name="cat-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration parameters for the customized alerting tones service that are
            available to the operator rather than the user. This must be present on the creation of the customized alerting tones service.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated element has values "true", "false" or "profile". When set to
                  "true" the user is provisioned with the customized alerting tones service. If set to "false" this will withdraw the
                  service from the user. This must be present on the creation of the customized alerting tones service. If set to "profile"
                  this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the
                  user service is
                  activated or not.
                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="session-transfer-to-own-device-type">
		<xs:sequence>
			<xs:element name="stod-operator-configuration" type="stod-operator-configuration-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The configuration parameters for the session transfer to own device service that are available to the operator
            rather than the user. This must be present on the creation of the session-transfer-own-device service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="stod-user-configuration" type="stod-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The configuration parameters for the session transfer to own device service that are available for the user to
            set directly.These can also be set on the user's behalf by the operator. This shall only be present if the service is
            provisioned i.e. stod-operator-configuration is present and activated is "true".
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="stod-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned with the session transfer to own device service. If set to "false" this will withdraw the service from the user. If set to "profile" this user service is provisioned via a Sevice Profile. Thus, it is the Service Profile document that determines if the user service is activated or not. This must be present on the creation of the session-transfer-own-device service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="max-targets" type="max-stod-targets-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The max-targets element controls the maximum number of distinct targets that the user can have for session
            transfer to own device. Integer value between 2 and 10. This must be present on the creation of the session-transfer-own-device
            service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="primary-hosting" type="hosting-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The primary-hosting element defines where the primary identity is hosted with values "IMS" for users hosted on
            the IMS network the MTAS is serving and "non-IMS" for users who have communication distribution performed by the IMS network but
            are not registered on the IMS network e.g. users on a separate circuit-switched network. This must be present on the creation of
            the communication-distribution service.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The maximum number of allowed FCD rules in the user document. Not specified or zero limit means no limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="stod-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Controls whether the session transfer to own device service is active or not for this subscriber.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="target-list" type="target-list-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A list defining related targets that can be included in session transfer to own device. The target-list in
            user-common-data is the preferred way to define related targets so they are available across multiple services.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="stod-ruleset" type="fcd-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Grouping element for a set of zero or more flexible communication distribution user rules</xs:documentation>
				</xs:annotation>
				<xs:key name="stod-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:fcd-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="max-stod-targets-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="repeat-interval-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="999"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="monthdays-type">
		<xs:sequence>
			<xs:element name="monthday" type="monthday-type" minOccurs="0" maxOccurs="31">
				<xs:annotation>
					<xs:documentation xml:lang="en">The day of the month. Allowed formats:1..31, -1..-31, [-1..-5|1..5][Monday..Sunday]
            This is a multi-value parameter.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="monthday-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="-*((3[0-1])|((1|2){1}[0-9])|([1-5](Sunday|Monday|Tuesday|Wednesday|Thursday|Friday|Saturday)|([1-9])))"/>
			<!-- monthday has the format of [-]1..31 or [-][1-5]weekday, where -1 means the last day of the month, 2Monday means second Monday, -1Wednesday means last Wednesday -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="play-announcement-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="play-segmented-announcement-type">
		<xs:sequence>
			<xs:element name="announcement-name" type="announcement-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name of the announcement to be played. This must be present on the creation of a
            play-segmented-announcement element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="announcement-variable" type="announcement-variable-type" nillable="true" minOccurs="0" maxOccurs="32">
				<xs:annotation>
					<xs:documentation xml:lang="en">The announcement variable to be embedded into the announcement. It's use is optional,
            i.e. a segmented announcement may or may not contain any variable segment. Maximum 32 announcement variables can be embedded
            into a segmented announcement. A keyed "announcement-variable" element with the "variable-name" attribute can be deleted from
            the list of announcement variables by setting the "xs:nil" attribute to true.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="announcement-variable-type">
		<xs:complexContent>
			<xs:extension base="announcement-variable-base-type">
				<xs:attribute name="variable-name" type="announcement-variable-name-type" use="required">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="variable-nameAttr"/>
						</xs:appinfo>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="announcement-variable-base-type">
		<xs:sequence>
			<xs:element name="variable-name" type="announcement-variable-name-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name of the announcement variable to be embedded. This must be present on the
            creation of an announcement-variable element inside a play-segmented-announcement element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="variable-value" type="announcement-variable-value-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The variable value is defined in the variable-value child element of the
            announcement-variable element. According to H.248.9, the allowed characters in place of a variable value are ASCII 0x09,
            0x20-0x7E.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="announcement-name-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The allowed characters in an announcement name are restricted to a-z, A-Z, _ and 0-9.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
			<xs:pattern value="[0-9a-zA-Z_]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="announcement-variable-name-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The allowed characters in a variable name are restricted to a-z, A-Z, _ and 0-9.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
			<xs:pattern value="[0-9a-zA-Z_]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="announcement-variable-value-type">
		<xs:annotation>
			<xs:documentation xml:lang="en">The allowed characters in a variable value are restricted to ASCII 0x09, 0x20-0x7e and
        ASCII.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:pattern value="[&#x9; -~]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="carrier-select-code-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="8"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="carrier-name-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="activated-enum-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="true"/>
			<xs:enumeration value="false"/>
			<xs:enumeration value="profile"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="cug-index-type">
		<xs:annotation>
			<xs:documentation> CUG index currently limited to the value 1 Designed to be extended </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="cug-Network-Identity-Type">
		<xs:restriction base="xs:hexBinary">
			<xs:length value="2"/>
			<!-- Defined to be the same as Network Identity (G.763) , i.e. twice the size of the SIP attribute networkIndicator -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="cug-Interlock-Binary-Code-Type">
		<xs:restriction base="xs:hexBinary">
			<xs:length value="2"/>
			<!-- Defined to be the same as SIP sixteenbitType (3GPP TS 24.654). This is consistent with G.763 Binary Code -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="transparent-key-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="transparent-value-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="E164-number-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{4,15}"/>
			<!--A E.164 number can have 4 to 15 digits. -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="default-call-handling-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="release"/>
			<xs:enumeration value="continue"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="px-call-notification-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="busy"/>
			<xs:enumeration value="not-reachable"/>
			<xs:enumeration value="no-answer"/>
			<xs:enumeration value="called-number"/>
			<xs:enumeration value="answer"/>
			<xs:enumeration value="disconnected"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="home-location-type">
		<xs:annotation>
			<xs:documentation>
        Confirming to P-Access-Network-Info header ABNF syntax described in 3GPP TS 24.229:
        IP Multimedia Call Control Protocol based on Session Initiation Protocol (SIP) and
        Session Description Protocol (SDP); Stage 3 (Release 11) section 7.2A.4.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[^;]+;[^=]+=[^;]+; *network-provided *"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="time-zone-area-type">
		<xs:annotation>
			<xs:documentation>
        The time-zone-area is the user home time zone area. 
        The time-zone-area is in the form "Area/Location and must be included in the list 
        of time zones in IANA Time Zone Database. Example: "Asia/Tokyo", "Canada/Central", 
        "Europe/Copenhagen", "Australia/Canberra", "America/Los_Angeles".
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9a-zA-Z/_\+\-]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="fcd-call-state-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="unconditional"/>
			<xs:enumeration value="busy"/>
			<xs:enumeration value="no-answer"/>
			<xs:enumeration value="not-registered"/>
			<xs:enumeration value="not-reachable"/>
			<!-- EMA does not support nilling a single value parameter so assigning the value of "unconditional" means none of the three conditions apply -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="fcd-no-reply-timer-type">
		<xs:restriction base="xs:positiveInteger">
			<xs:minInclusive value="5"/>
			<xs:maxInclusive value="180"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="in-sip-request-type">
		<xs:annotation>
			<xs:documentation>
                The in-sip-request element is a grouping element for regexp conditions on contents of a SIP request.
                It evaluates to true if ALL of the conditions included within it are fulfilled.
            </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="flexcondition" type="flexcondition-type" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
                        The flexcondition element refers to the actual definition of the SIP regexp condition in the User Common Data.
                        It evaluates to true when a value of the specified header or header parameter in the SIP request triggering
                        service matches the regular expression (or if it does not match if the "match-inverse" attribute
                        in the condition definition is set to true). The flexcondition element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="flexcondition-type">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
                        This element holds reference to actual definition of the SIP regexp condition in the User Common Data.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="in-sip-request-condition-list-type">
		<xs:sequence>
			<xs:element name="flexcondition-definition" type="flexcondition-definition-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
                        The flexcondition-definition element is a grouping element for attributes which actually define
                        a SIP regexp condition. The flexcondition-definition element is a sub-MO allowing multiple instances
                        with "id" as the unique key.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="flexcondition-definition-type">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
                        A key uniquely identifying the condition.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="header" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
                        A SIP header matched.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="parameter" type="xs:string" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
                        A SIP header parameter matched.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="value" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
                        A regular expression to match against a given header or header parameter value.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="match-inverse" type="xs:boolean" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
                        If set to "true", the SIP regexp condition will evaluate to true if the parameter value
                        does NOT match the regular expression.
                    </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="subscription-type">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">
						The id is a unique key for the subscription group, currently limited to 1.
 						It must be present at the creation of a subscription element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="imsi" type="imsi-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">
						The IMSI that is connected to this subscriber. The IMSI is a maximum 15-digit number as defined in 3GPP TS 23.003. 
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required">
			<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="idAttr"/>
					</xs:appinfo>
				</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="imsi-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,15}"/>
			<!-- An IMSI can have 5 to 15 digits. -->
		</xs:restriction>
	</xs:simpleType>	
	<xs:simpleType name="external-query-type-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="calling-name"/>
			<xs:enumeration value="company-number"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="media-policy-type">
		<xs:sequence>
			<xs:element name="mp-operator-configuration" type="mp-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the media policy service that are available to the operator
						rather than the user. This must be present on the creation of the media-policy service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>	
	<xs:complexType name="mp-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the user is provisioned with
						the media policy service. This must be present on the creation of the media-policy service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- ruleset is a structured parameter that is optional and nillable -->
			<xs:element name="mp-ruleset" type="mp-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Grouping element for a set of zero or more user rules
					</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="mp-rule-key">
					<xs:selector xpath="./mmtel-profile-serv:mp-rule" />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="mp-ruleset-type">
		<xs:sequence>
			<xs:element name="mp-rule" type="mp-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						An individual rule controlling media policy behaviour. The mp-rule element is a sub-MO allowing
						multiple instances with "id" as the unique key.
					</xs:documentation>
				</xs:annotation>
				<!-- sub MOs need a key. The key for rule is id. -->
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="mp-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
						A unique identifier for an individual rule. This must be unique within the scope of the complete
						document. This must be present on the creation of an mp-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mp-conditions" type="mp-conditions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The mp-conditions element is a grouping element for conditions for a rule. All conditions must 
						be satisfied for the rule to take effect.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mp-actions" type="mp-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The mp-actions element is a grouping element for the actions for a rule. This must be present
						on the creation of a mp-rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required"/>
	</xs:complexType>
	<xs:complexType name="mp-conditions-type">
		<xs:sequence>
			<xs:element name="media" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The media element contains a media type that the session must include for the condition to be matched.
						Possible values are "audio", "video", "text", "application" and "message". This parameter can appear
						once in a rule and must be present on the creation of an mp-rule element.
					</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="audio" />
						<xs:enumeration value="video" />
						<xs:enumeration value="text" />
						<xs:enumeration value="application" />
						<xs:enumeration value="message" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="mp-actions-type">
		<xs:sequence>
			<xs:element name="allow" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The allow element has values "true" or "false". If set to "false" then any media line matching the
						condition will be blocked. If set to "true" then the media line will not be affected. This must be
						present on the creation of an mp-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
