<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Body>
        <soapenv:Fault>
            <faultcode>soapenv:Server</faultcode>
            <faultstring>[LISTENER_SOAP_SERVICE_AUAI.INTERNAL_ERROR]</faultstring>
            <detail>
                <om-v1-0:createAndStartRequestByValueException xmlns:om-v1-0="http://ossj.org/xml/OrderManagement/v1-0" xmlns:dox-resource="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:dox-co="http://amdocs/core/ossj-Common/dat/3" xmlns:dox-om="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:cbebi-v1-5="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:ns1="http://xmlns.aua.oss.amdocs.com/meta" xmlns:ns4="http://xmlns.aua.oss.amdocs.com/client-context" xmlns:dox-service="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:ns3="http://xmlns.aua.oss.amdocs.com/requestengine" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <om-v1-0:duplicateKeyException xsi:type="dox-co:DuplicateKeyException">
                        <co-v1-5:message/>
                        <dox-co:exceptionsDetails>
                            <dox-co:item>
                                <dox-co:errorCode>some_error_code</dox-co:errorCode>
                                <dox-co:retryable>false</dox-co:retryable>
                                <dox-co:errorMessage>some errorMessage indicating the OSS/J request failed the initial validation checks</dox-co:errorMessage>
                                <dox-co:errorClass>General</dox-co:errorClass>
                                <dox-co:component>com.amdocs.oss.aua.requestcontroller</dox-co:component>
                                <dox-co:severity>Medium</dox-co:severity>
                                <dox-co:reason>com.amdocs.oss.aua.requestcontroller.exception.RequestExternalIDInUseException: [REQUESTCONTROLLER.REQUEST_EXTERNAL_ID_IN_USE] Constraint violation saving order [HbRequest[id=00282202,class=TRACKING_REQUEST,type=auai-order-service,state=open.not_running.suspended,previousIDs=[],nextIDs=[],parentID=null,externalID=ALA-9283-1247381286217700320,version=0,created=Fri Aug 06 19:27:12 BST 2021,createdBy=super,deferUntil=null,lastUpdated=Fri Aug 06 19:27:12 BST 2021,lastRequeued=null,completed=null,priority=5,worker=0,bounced=0,undoable=false,deleteAt=null,activities=[]]]:org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update</dox-co:reason>
                                <dox-co:managedEntityKey xsi:type="dox-om:ServiceOrderKey">
                                    <co-v1-5:type>ServiceOrder</co-v1-5:type>
                                    <co-v1-5:primaryKey>
                                        <dox-co:primaryKey>some primaryKey</dox-co:primaryKey>
                                    </co-v1-5:primaryKey>
                                </dox-co:managedEntityKey>
                            </dox-co:item>
                        </dox-co:exceptionsDetails>
                        <dox-co:requestItemKeyResults>
                            <cbebi-v1-5:item xsi:type="dox-om:ServiceOrderItemKeyResult">
                                <co-v1-5:success>false</co-v1-5:success>
                                <cbecore-v1-5:entityKey xsi:type="dox-om:ServiceOrderItemKey">
                                    <co-v1-5:type>ServiceOrderItem</co-v1-5:type>
                                    <co-v1-5:primaryKey>
                                        <dox-co:primaryKey>some primaryKey</dox-co:primaryKey>
                                    </co-v1-5:primaryKey>
                                </cbecore-v1-5:entityKey>
                            </cbebi-v1-5:item>
                        </dox-co:requestItemKeyResults>
                    </om-v1-0:duplicateKeyException>
                </om-v1-0:createAndStartRequestByValueException>
            </detail>
        </soapenv:Fault>
    </soapenv:Body>
</soapenv:Envelope>

