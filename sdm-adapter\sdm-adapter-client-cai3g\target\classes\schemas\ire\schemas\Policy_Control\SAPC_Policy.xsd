<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/"
	xmlns:x="http://schemas.ericsson.com/ma/SAPC/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	targetNamespace="http://schemas.ericsson.com/ma/SAPC/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="types/sapcla_types.xsd" />
	<xs:element name="pcPolicyName" type="pcPolicyNameType" />
	<xs:element name="CreatePolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcPolicyName" type="pcPolicyNameType" />
				<xs:element name="pcRuleCombiningAlgorithm" type="pcRuleCombiningAlgorithmType" minOccurs="0"/>
				<xs:element name="pcRules" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcRule" type="pcRuleType" />
						</xs:sequence>
						<xs:attribute name="pcRule" type="pcRuleType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcRuleAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcRule">
						<xs:selector xpath="." />
						<xs:field xpath="@pcRule" />
					</xs:key>
					<xs:keyref name="keyref_create_pcRule" refer="key_create_pcRule">
						<xs:selector xpath="./x:pcRule" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcPolicyName" type="pcPolicyNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_pcPolicyName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcPolicyName" />
		</xs:key>
		<xs:keyref name="keyref_create_pcPolicyName" refer="key_create_pcPolicyName">
			<xs:selector xpath="./x:pcPolicyName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

	<xs:element name="GetResponsePolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcPolicyName" type="pcPolicyNameType" />
				<xs:element name="pcRuleCombiningAlgorithm" type="pcRuleCombiningAlgorithmType"
					minOccurs="0" />
				<xs:element name="pcRules" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcRule" type="pcRuleType" />
						</xs:sequence>
						<xs:attribute name="pcRule" type="pcRuleType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcRuleAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcPolicyName" type="pcPolicyNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<xs:element name="SetPolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcPolicyName" type="pcPolicyNameType" />
				<xs:element name="pcRuleCombiningAlgorithm" type="pcRuleCombiningAlgorithmType" minOccurs="0"/>
				<xs:element name="pcRules" nillable="true" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcRule" type="pcRuleType" />
						</xs:sequence>
						<xs:attribute name="pcRule" type="pcRuleType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcRuleAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcRule">
						<xs:selector xpath="." />
						<xs:field xpath="@pcRule" />
					</xs:key>
					<xs:keyref name="keyref_set_pcRule" refer="key_set_pcRule">
						<xs:selector xpath="./x:pcRule" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcPolicyName" type="pcPolicyNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_pcPolicyName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcPolicyName" />
		</xs:key>
		<xs:keyref name="keyref_set_pcPolicyName" refer="key_set_pcPolicyName">
			<xs:selector xpath="./x:pcPolicyName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

</xs:schema>
