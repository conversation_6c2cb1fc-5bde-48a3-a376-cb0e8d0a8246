<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:m2m="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:x="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/m2mhlr/">
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="createMultipleSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element maxOccurs="10" name="imsi" type="imsiType"/>
<xs:element name="mch" type="mchType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="setMultipleSubscription">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="10" minOccurs="0" name="Subscription" nillable="true" type="MSubscriptionType"/>
<xs:element minOccurs="0" name="mch" type="mchType"/>
<xs:element minOccurs="0" name="acimsi" type="imsiType"/>
<xs:element minOccurs="0" name="mmsisdn" type="msisdnType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:complexType name="MSubscriptionType">
<xs:sequence>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:element name="getMultipleSubscription">
<xs:complexType>
<xs:attribute name="msisdn" type="msisdnType" use="required"/>
</xs:complexType>
</xs:element>
<xs:element name="getResponseMultipleSubscription" type="GetMultipleSubscriptionType">
<xs:annotation>
<xs:documentation>
				The attributes for get M2M HLR multiple subscription response.
			</xs:documentation>
</xs:annotation>
</xs:element>
<xs:complexType name="GetMultipleSubscriptionType">
<xs:sequence>
<xs:element name="mch" type="mchType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element maxOccurs="10" name="SubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="active" type="activeType"/>
<xs:element name="master" type="masterType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
<xs:element name="deleteMultipleSubscription">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
<xs:simpleType name="msisdnType">
<xs:annotation>
<xs:documentation>
				the type definition for MSISDN
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiType">
<xs:annotation>
<xs:documentation>
				the type definition for IMSI
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mchType">
<xs:restriction base="xs:string">
<xs:enumeration value="LOC"/>
<xs:enumeration value="USSD"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="zoneidType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="activeType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="masterType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="BinaryType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
