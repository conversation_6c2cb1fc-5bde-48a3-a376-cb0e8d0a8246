<!-- Home Location Register, Subscription -->

<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" 
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" jaxb:extensionBindingPrefixes="xjc"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="../2G_3G/Layered_HLR_Components/types/hlrla_types.xsd" />

	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="PrimaryHLRId" type="primaryhlridType" />


	<xs:element name="hecdm2i">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2mpid" type="m2mpidType" />
				<xs:element name="dmi" type="dmiType" />
				<xs:element name="nname" type="nnameType" />
				<xs:element name="ntype" type="ntypeType" />
				<xs:element name="url" type="urlType" minOccurs="0" />
				<xs:group ref="ipDef" minOccurs="0" />
				<xs:element name="nmsisdn" type="nmsisdnType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:group name="ipDef">
		<xs:sequence>
			<xs:element name="iptype" type="iptypeType" />
			<xs:element name="ip" type="ipChoiceType" />
			<xs:element name="m2mipport" type="m2mipportType" minOccurs="0" />
		</xs:sequence>
	</xs:group>


	<xs:element name="hecdm2c">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2mpid" type="m2mpidType" />
				<xs:element name="dmi" type="dmiType" minOccurs="0" />
				<xs:element name="nname" type="nnameType" minOccurs="0" />
				<xs:element name="ntype" type="ntypeType" minOccurs="0" />
				<xs:element name="url" type="urlEraseType" minOccurs="0" />
				<xs:group ref="changeIpGroup" minOccurs="0" />
				<xs:element name="nmsisdn" type="nmsisdnEraseType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:group name="changeIpGroup">
		<xs:sequence>
			<xs:element name="ip" type="ipChoiceEraseType" />
			<xs:group ref="optionalChangeIpGroup" minOccurs="0" />
		</xs:sequence>
	</xs:group>

	<xs:group name="optionalChangeIpGroup">
		<xs:sequence>
			<xs:element name="iptype" type="iptypeType" />
			<xs:element name="m2mipport" type="m2mipportType" minOccurs="0" />
		</xs:sequence>
	</xs:group>


	<xs:element name="hecdm2e">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2mpid" type="m2mpidType" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>


	<xs:element name="hecdm2p">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2mpid" type="mpidpType" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>


	<xs:element name="Result">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="M2MProfileData">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="M2MProfile" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="m2mpid" type="m2mpidType" />
										<xs:element name="dmi" type="dmiType" />
										<xs:element name="nname" type="nnameType" />
										<xs:element name="ntype" type="ntypeType" />
										<xs:element name="url" type="urlType" minOccurs="0" />
										<xs:element name="ip" type="ipChoiceType" minOccurs="0" />
										<xs:element name="m2mipport" type="m2mipportType" minOccurs="0" />
										<xs:element name="nmsisdn" type="nmsisdnType" minOccurs="0" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

</xs:schema>