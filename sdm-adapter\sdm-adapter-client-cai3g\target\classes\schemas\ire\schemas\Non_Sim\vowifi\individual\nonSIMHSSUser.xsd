<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 sp1 (http://www.altova.com) by <PERSON><PERSON> (<PERSON> (China) Communications Company Ltd) -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/nonSIM/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:nonSIM="http://schemas.ericsson.com/ma/nonSIM/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://schemas.ericsson.com/ma/nonSIM/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="./types/dataTypes.xsd"/>
	<xs:element name="transactionLogId" type="transactionLogIdType"/>
	<xs:element name="impi" type="impiType"/>
	<xs:element name="CreateNonSIMHSSUser">
		<xs:annotation>
			<xs:documentation>This is for creating the shared IMPI for non-SIM device</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="impi" type="impiType"/>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="password" type="passwdType"/>
			</xs:sequence>
			<xs:attribute name="impi" type="impiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="impiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_identity">
			<xs:selector xpath="."/>
			<xs:field xpath="@impi"/>
		</xs:key>
		<xs:keyref name="keyref_create_identity" refer="key_create_identity">
			<xs:selector xpath="./nonSIM:impi"/>
			<xs:field xpath="."/>
		</xs:keyref>
	</xs:element>
	<xs:element name="SetNonSIMHSSUser">
		<xs:annotation>
			<xs:documentation>This is only for update non-SIM device HSS IMPI user password</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="password" type="passwdType"/>
			</xs:sequence>
			<xs:attribute name="impi" type="impiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="impiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeleteNonSIMHSSUser">
		<xs:annotation>
			<xs:documentation>This is for deleting the shared IMPI of non-SIM device</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
			</xs:sequence>
			<xs:attribute name="impi" type="impiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="impiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
