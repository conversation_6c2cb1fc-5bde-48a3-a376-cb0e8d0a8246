<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/IPWORKS/">
<xs:include schemaLocation="types/aaala_types.xsd"/>
<xs:element name="aaaUserName" type="aaaUserNameType"/>
<xs:element name="CreateAAAUser">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaUserName" type="aaaUserNameType"/>
<xs:element name="aaaUserPassword" type="aaaUserPasswordType"/>
<xs:element minOccurs="0" name="aaaAuthenticationMethod" type="aaaAuthenticationMethodType"/>
<xs:element minOccurs="0" name="aaaIPAllocationType" type="aaaIPAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPAllocationValue" type="aaaIPAllocationValueType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationType" type="aaaIPv6PrefixAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationValue" type="aaaIPv6PrefixAllocationValueType"/>
<xs:element minOccurs="0" name="aaaAssociatedImsi" type="aaaAssociatedImsiType"/>
<xs:element maxOccurs="10" minOccurs="0" name="aaaGroupName">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaGroupName" type="aaaGroupNameType"/>
</xs:sequence>
<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaGroupNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_aaaGroupName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaGroupName"/>
</xs:key>
<xs:keyref name="keyref_create_aaaGroupName" refer="key_create_aaaGroupName">
<xs:selector xpath="./x:aaaGroupName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="aaaPolicy">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="10" minOccurs="0" name="aaaIndividualPolicy">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaIndividualPolicyName" type="aaaPolicyNameType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyChecklist" type="aaaPolicyChecklistType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyReplylist" type="aaaPolicyReplylistType"/>
</xs:sequence>
<xs:attribute name="aaaIndividualPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaIndividualPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_aaaIndividualPolicyName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaIndividualPolicyName"/>
</xs:key>
<xs:keyref name="keyref_create_aaaIndividualPolicyName" refer="key_create_aaaIndividualPolicyName">
<xs:selector xpath="./x:aaaIndividualPolicyName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="10" minOccurs="0" name="aaaSharedPolicyName">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType"/>
</xs:sequence>
<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaSharedPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_aaaSharedPolicyName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaSharedPolicyName"/>
</xs:key>
<xs:keyref name="keyref_create_aaaSharedPolicyName" refer="key_create_aaaSharedPolicyName">
<xs:selector xpath="./x:aaaSharedPolicyName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="aaaUserName" type="aaaUserNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaUserNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_aaaUserName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaUserName"/>
</xs:key>
<xs:keyref name="keyref_create_aaaUserName" refer="key_create_aaaUserName">
<xs:selector xpath="./x:aaaUserName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element name="GetResponseAAAUser">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaUserName" type="aaaUserNameType"/>
<xs:element minOccurs="0" name="aaaAuthenticationMethod" type="aaaAuthenticationMethodType"/>
<xs:element minOccurs="0" name="aaaIPAllocationType" type="aaaIPAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPAllocationValue" type="aaaIPAllocationValueType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationType" type="aaaIPv6PrefixAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationValue" type="aaaIPv6PrefixAllocationValueType"/>
<xs:element minOccurs="0" name="aaaAssociatedImsi" type="aaaAssociatedImsiType"/>
<xs:element maxOccurs="10" minOccurs="0" name="aaaGroupName">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaGroupName" type="aaaGroupNameType"/>
</xs:sequence>
<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaGroupNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="aaaPolicy">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="10" minOccurs="0" name="aaaIndividualPolicy">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaIndividualPolicyName" type="aaaPolicyNameType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyChecklist" type="aaaPolicyChecklistType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyReplylist" type="aaaPolicyReplylistType"/>
</xs:sequence>
<xs:attribute name="aaaIndividualPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaIndividualPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="10" minOccurs="0" name="aaaSharedPolicyName">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType"/>
</xs:sequence>
<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaSharedPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="aaaUserName" type="aaaUserNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaUserNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="SetAAAUser">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="aaaUserPassword" type="aaaUserPasswordType"/>
<xs:element minOccurs="0" name="aaaAuthenticationMethod" nillable="true" type="aaaAuthenticationMethodType"/>
<xs:element minOccurs="0" name="aaaIPAllocationType" nillable="true" type="aaaIPAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPAllocationValue" nillable="true" type="aaaIPAllocationValueType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationType" type="aaaIPv6PrefixAllocationTypeType"/>
<xs:element minOccurs="0" name="aaaIPv6PrefixAllocationValue" type="aaaIPv6PrefixAllocationValueType"/>
<xs:element minOccurs="0" name="aaaAssociatedImsi" type="aaaAssociatedImsiType"/>
<xs:element maxOccurs="10" minOccurs="0" name="aaaGroupName" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaGroupName" type="aaaGroupNameType"/>
</xs:sequence>
<xs:attribute name="aaaGroupName" type="aaaGroupNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaGroupNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_aaaGroupName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaGroupName"/>
</xs:key>
<xs:keyref name="keyref_set_aaaGroupName" refer="key_set_aaaGroupName">
<xs:selector xpath="./x:aaaGroupName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="aaaPolicy">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="10" minOccurs="0" name="aaaIndividualPolicy" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="aaaIndividualPolicyName" type="aaaPolicyNameType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyChecklist" type="aaaPolicyChecklistType"/>
<xs:element minOccurs="0" name="aaaIndividualPolicyReplylist" type="aaaPolicyReplylistType"/>
</xs:sequence>
<xs:attribute name="aaaIndividualPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaIndividualPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_aaaIndividualPolicyName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaIndividualPolicyName"/>
</xs:key>
<xs:keyref name="keyref_set_aaaIndividualPolicyName" refer="key_set_aaaIndividualPolicyName">
<xs:selector xpath="./x:aaaIndividualPolicyName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="10" minOccurs="0" name="aaaSharedPolicyName" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="aaaSharedPolicyName" type="aaaPolicyNameType"/>
</xs:sequence>
<xs:attribute name="aaaSharedPolicyName" type="aaaPolicyNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaSharedPolicyNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_aaaSharedPolicyName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaSharedPolicyName"/>
</xs:key>
<xs:keyref name="keyref_set_aaaSharedPolicyName" refer="key_set_aaaSharedPolicyName">
<xs:selector xpath="./x:aaaSharedPolicyName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="aaaUserName" type="aaaUserNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aaaUserNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_aaaUserName">
<xs:selector xpath="."/>
<xs:field xpath="@aaaUserName"/>
</xs:key>
</xs:element>
<xs:element name="DeleteAAAUser">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
