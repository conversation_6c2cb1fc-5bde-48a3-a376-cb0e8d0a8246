<!-- Home Location Register, Subscription -->
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" jaxb:extensionBindingPrefixes="xjc"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/hlrla_types.xsd" />
	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="PrimaryHLRId" type="primaryhlridType" />

	<!-- CreateMultipleSubscriptionCopySUD MOId: msisdn MOType: MultipleSubscriptionCopySUD@http://schemas.ericsson.com/pg/hlr/13.5/ -->
	<xs:element name="CreateMultipleSubscriptionCopySUD">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" />
				<xs:element name="imsi" type="imsiType" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_msisdn">
			<xs:selector xpath="./x:msisdn" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_msisdn" refer="key_msisdn">
			<xs:selector xpath="." />
			<xs:field xpath="@msisdn" />
		</xs:keyref>
	</xs:element>
	<!-- SetMultipleSubscription MOId: msisdn MOType: MultipleSubscriptionCopySUD@http://schemas.ericsson.com/pg/hlr/13.5/ -->
	<xs:element name="SetMultipleSubscriptionCopySUD">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" />
				<xs:element name="imsi" type="imsiType" minOccurs="0"/>
				<xs:element name="erase" type="eraseType" fixed="true" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

</xs:schema>
