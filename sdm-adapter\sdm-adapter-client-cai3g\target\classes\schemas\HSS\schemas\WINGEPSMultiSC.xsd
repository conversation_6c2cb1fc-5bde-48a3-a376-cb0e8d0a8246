<?xml version="1.0" encoding="UTF-8" standalone="no"?><!-- Home Subscriber Server, EPS -->
	<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ma/HSS/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/HSS/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/HSS/">
    <xs:include schemaLocation="./hssla_types.xsd"/>
    <xs:element name="imsi" type="imsiType"/>
    <xs:element name="CreateEPSMultiSC">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="imsi" type="imsiType"/>
                <xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
                <xs:element minOccurs="0" name="associationId" type="associationIdType"/>
                <xs:element name="epsProfileId" type="epsProfileIdType"/>
                <xs:element minOccurs="0" name="epsOdb" type="epsOdbType"/>
                <xs:element minOccurs="0" name="epsRoamingAllowed" type="epsRoamingAllowedType"/>
                <xs:element minOccurs="0" name="epsIndividualApnOperatorIdentifierReplacement" type="epsIndividualApnOperatorIdentifierReplacementType"/>
                <xs:element minOccurs="0" name="epsIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualContextId" type="epsIndividualContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualSubscribedChargingCharacteristic" type="epsIndividualSubscribedChargingCharacteristicType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpFlow" type="epsIndividualAmbrMaximalUplinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpFlow" type="epsIndividualAmbrMaximalDownlinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualRatFrequencyPriorityId" type="epsIndividualRatFrequencyPriorityIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUeUsageType" type="epsIndividualUeUsageTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualRauTauTimer" type="epsIndividualRauTauTimerType"/>
                <xs:element minOccurs="0" name="epsIndividualAdditionalDefaultContextId" type="epsIndividualAdditionalDefaultContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUserType" type="epsIndividualUserTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpExtended" type="epsIndividualAmbrMaximalUplinkIpExtendedType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpExtended" type="epsIndividualAmbrMaximalDownlinkIpExtendedType"/>
                <xs:element minOccurs="0" name="epsRoamingRestriction" type="epsRoamingRestrictionType"/>
                <xs:element minOccurs="0" name="epsRegionalRoamingServiceAreaId" type="epsRegionalRoamingServiceAreaIdType"/>
                <xs:choice minOccurs="0">
                    <xs:element name="epsAccessRestriction" type="epsAccessRestrictionType"/>
                    <xs:element name="epsExtendedAccessRestriction" type="epsExtendedAccessRestrictionType"/>
                </xs:choice>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV4Address" type="epsUserIpV4AddressType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV6Address" type="epsUserIpV6AddressType"/>
                <xs:element minOccurs="0" name="epsTenantId" type="epsTenantIdType"/>
                <xs:element minOccurs="0" name="epsSessionTransferNumber" type="epsSessionTransferNumberType"/>
                <xs:element minOccurs="0" name="epsMultimediaPriorityService" type="epsMultimediaPriorityServiceType"/>
                <xs:element minOccurs="0" name="epsZoneCodeSetId" type="epsZoneCodeSetIdType"/>
                <xs:choice minOccurs="0">
                    <xs:element name="epsCommonMsisdn" type="epsCommonMsisdnType"/>
                    <xs:element name="commonMsisdn" type="epsCommonMsisdnType"/>
                </xs:choice>
                <xs:element minOccurs="0" name="epsAaaOdb" type="epsAaaOdbType"/>
                <xs:element minOccurs="0" name="epsAaaIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualContextId" type="epsIndividualContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
                <xs:element minOccurs="0" name="epsAdminDisable" type="xs:boolean"/>
                <xs:element minOccurs="0" name="epsNam" type="epsNamType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaMIP6FeatureVector" type="epsAaaMIP6FeatureVectorType"/>
                <xs:element minOccurs="0" name="epsMdtUserConsent" type="epsMdtUserConsentType"/>
                
            </xs:sequence>
            <xs:attribute name="imsi" type="imsiType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="imsiAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="key_imsi">
            <xs:selector xpath="./x:imsi"/>
            <xs:field xpath="."/>
        </xs:key>
        <xs:keyref name="keyref_imsi" refer="key_imsi">
            <xs:selector xpath="."/>
            <xs:field xpath="@imsi"/>
        </xs:keyref>
    </xs:element>
    <!-- DeleteEPSMultiSC MOId: imsi MOType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/ -->
    <xs:element name="DeleteEPSMultiSC">
        <xs:complexType>
            
        </xs:complexType>
    </xs:element>
    <!-- SetEPSMultiSC MOId: imsi MOType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/ -->
    <xs:element name="SetEPSMultiSC">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
                <xs:element minOccurs="0" name="epsProfileId" type="epsProfileIdType"/>
                <xs:element minOccurs="0" name="epsOdb" type="epsOdbType"/>
                <xs:element minOccurs="0" name="epsRoamingAllowed" type="epsRoamingAllowedType"/>
                <xs:element minOccurs="0" name="epsLocationState" type="epsLocationStateType"/>
                <xs:element minOccurs="0" name="epsIndividualApnOperatorIdentifierReplacement" nillable="true" type="epsIndividualApnOperatorIdentifierReplacementType"/>
                <xs:element minOccurs="0" name="epsIndividualDefaultContextId" nillable="true" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualContextId" nillable="true" type="epsIndividualContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualSubscribedChargingCharacteristic" nillable="true" type="epsIndividualSubscribedChargingCharacteristicType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpFlow" nillable="true" type="epsIndividualAmbrMaximalUplinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpFlow" nillable="true" type="epsIndividualAmbrMaximalDownlinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualRatFrequencyPriorityId" nillable="true" type="epsIndividualRatFrequencyPriorityIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualMappingContextId" nillable="true" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUeUsageType" nillable="true" type="epsIndividualUeUsageTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualRauTauTimer" nillable="true" type="epsIndividualRauTauTimerType"/>
                <xs:element minOccurs="0" name="epsIndividualAdditionalDefaultContextId" nillable="true" type="epsIndividualAdditionalDefaultContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUserType" type="epsIndividualUserTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpExtended" nillable="true" type="epsIndividualAmbrMaximalUplinkIpExtendedType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpExtended" nillable="true" type="epsIndividualAmbrMaximalDownlinkIpExtendedType"/>
                <xs:element minOccurs="0" name="epsRoamingRestriction" type="epsRoamingRestrictionType"/>
                <xs:element minOccurs="0" name="epsRegionalRoamingServiceAreaId" nillable="true" type="epsRegionalRoamingServiceAreaIdType"/>
                <xs:choice minOccurs="0">
                    <xs:element name="epsAccessRestriction" type="epsAccessRestrictionType"/>
                    <xs:element name="epsExtendedAccessRestriction" type="epsExtendedAccessRestrictionType"/>
                </xs:choice>
                <xs:element minOccurs="0" name="epsAaaRegistrationState" type="setReqEpsAaaRegistrationStateType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV4Address" nillable="true" type="epsUserIpV4AddressType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV6Address" nillable="true" type="epsUserIpV6AddressType"/>
                <xs:element minOccurs="0" name="epsTenantId" nillable="true" type="epsTenantIdType"/>
                <xs:element minOccurs="0" name="epsSessionTransferNumber" nillable="true" type="epsSessionTransferNumberType"/>
                <xs:element minOccurs="0" name="epsMultimediaPriorityService" type="epsMultimediaPriorityServiceType"/>
                <xs:element minOccurs="0" name="epsZoneCodeSetId" nillable="true" type="epsZoneCodeSetIdType"/>
                <xs:choice minOccurs="0">
                    <xs:element name="epsCommonMsisdn" nillable="true" type="epsCommonMsisdnType"/>
                    <xs:element name="commonMsisdn" nillable="true" type="epsCommonMsisdnType"/>
                </xs:choice>
                <xs:element minOccurs="0" name="epsAaaOdb" nillable="true" type="epsAaaOdbType"/>
                <xs:element minOccurs="0" name="epsAaaIndividualDefaultContextId" nillable="true" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualContextId" nillable="true" type="epsIndividualContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualMappingContextId" nillable="true" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="epsAdminDisable" type="xs:boolean"/>
                <xs:element minOccurs="0" name="epsNam" nillable="true" type="epsNamType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaMIP6FeatureVector" nillable="true" type="epsAaaMIP6FeatureVectorType"/>
                <xs:element minOccurs="0" name="epsMdtUserConsent" type="epsMdtUserConsentType"/>
                
                <xs:element minOccurs="0" name="epsReattachRequired" type="epsReattachRequiredType"/>
                <xs:element minOccurs="0" name="epsSgsnLocationState" type="epsSetSgsnLocationStateType"/>

            </xs:sequence>
            <xs:attribute name="imsi" type="imsiType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="imsiAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <!-- GetEPSMultiSC MOId: imsi MOType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/ -->
    <xs:element name="msisdn" type="msisdnType"/>

    <!-- GetResponseEPSMultiSC MOId: imsi MOType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/ -->
    <xs:element name="GetResponseEPSMultiSC">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="imsi" type="imsiType"/>
                <xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
                <xs:element minOccurs="0" name="associationId" type="associationIdType"/>
                <xs:element name="epsProfileId" type="epsProfileIdType"/>
                <xs:element name="epsOdb" type="epsOdbType"/>
                <xs:element name="epsRoamingAllowed" type="epsRoamingAllowedType"/>
                <xs:element minOccurs="0" name="epsIndividualApnOperatorIdentifierReplacement" type="epsIndividualApnOperatorIdentifierReplacementType"/>
                <xs:element minOccurs="0" name="epsIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualContextId" type="epsIndividualContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualSubscribedChargingCharacteristic" type="epsIndividualSubscribedChargingCharacteristicType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpFlow" type="epsIndividualAmbrMaximalUplinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpFlow" type="epsIndividualAmbrMaximalDownlinkIpFlowType"/>
                <xs:element minOccurs="0" name="epsIndividualRatFrequencyPriorityId" type="epsIndividualRatFrequencyPriorityIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUeUsageType" type="epsIndividualUeUsageTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualRauTauTimer" type="epsIndividualRauTauTimerType"/>
                <xs:element minOccurs="0" name="epsIndividualAdditionalDefaultContextId" type="epsIndividualAdditionalDefaultContextIdType"/>
                <xs:element minOccurs="0" name="epsIndividualUserType" type="epsIndividualUserTypeType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpExtended" type="epsIndividualAmbrMaximalUplinkIpExtendedType"/>
                <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpExtended" type="epsIndividualAmbrMaximalDownlinkIpExtendedType"/>
                <xs:element minOccurs="0" name="mmeAddress" type="mmeAddressType"/>
                <xs:element name="epsLocationState" type="epsLocationStateType"/>
                <xs:element minOccurs="0" name="epsRoamingRestriction" type="epsRoamingRestrictionType"/>
                <xs:element minOccurs="0" name="epsRegionalRoamingServiceAreaId" type="epsRegionalRoamingServiceAreaIdType"/>
                <xs:choice minOccurs="0">
                    <xs:element name="epsAccessRestriction" type="epsAccessRestrictionType"/>
                    <xs:element name="epsExtendedAccessRestriction" type="epsExtendedAccessRestrictionType"/>
                </xs:choice>
                <xs:element minOccurs="0" name="epsAaaRegistrationState" type="epsAaaRegistrationStateType"/>
                <xs:element minOccurs="0" name="epsAaaAddress" type="epsAaaAddressType"/>
                <xs:element minOccurs="0" name="epsAaaRealm" type="epsAaaRealmType"/>
                <xs:element minOccurs="0" name="epsDynamicPdnInformation" type="epsDynamicPdnInformationType"/>
                <xs:element minOccurs="0" name="epsUeSrVccCap" type="epsUeSrVccCapType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV4Address" type="epsUserIpV4AddressType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV6Address" type="epsUserIpV6AddressType"/>
                <xs:element minOccurs="0" name="epsTenantId" type="epsTenantIdType"/>
                <xs:element minOccurs="0" name="epsSessionTransferNumber" type="epsSessionTransferNumberType"/>
                <xs:element minOccurs="0" name="epsMultimediaPriorityService" type="epsMultimediaPriorityServiceType"/>
                <xs:element minOccurs="0" name="epsZoneCodeSetId" type="epsZoneCodeSetIdType"/>
                <xs:element minOccurs="0" name="epsAutomaticProvisioned" type="epsAutomaticProvisionedType"/>
                <xs:element minOccurs="0" name="epsImeiSv" type="epsImeiSvType"/>
                <xs:element minOccurs="0" name="epsLastUpdateLocationDate" type="epsLastUpdateLocationDateType"/>
                <xs:element minOccurs="0" name="commonMsisdn" type="epsCommonMsisdnType"/>
                <xs:element minOccurs="0" name="epsAaaOdb" type="epsAaaOdbType"/>
                <xs:element minOccurs="0" name="epsAaaIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualContextId" type="epsIndividualContextIdType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                <xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
                <xs:element minOccurs="0" name="epsAdminDisable" type="xs:boolean"/>
                <xs:element minOccurs="0" name="epsNam" type="epsNamType"/>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaMIP6FeatureVector" type="epsAaaMIP6FeatureVectorType"/>
                <xs:element minOccurs="0" name="epsMdtUserConsent" type="epsMdtUserConsentType"/>
                <xs:element minOccurs="0" name="epsMmeRealm" type="epsMmeRealmType"/>
                <xs:element minOccurs="0" name="epsSgsnAddress" type="epsSgsnAddressType"/>
                <xs:element minOccurs="0" name="epsSgsnRealm" type="epsSgsnRealmType"/>
                <xs:element minOccurs="0" name="epsSgsnLocationState" type="epsGetSgsnLocationStateType"/>
                <xs:element minOccurs="0" name="epsSgsnLastUpdateLocationDate" type="epsSgsnLastUpdateLocationDateType"/>
                <xs:element minOccurs="0" name="epsSubs">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="epsSub">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="imsi" type="imsiType"/>
                                        <xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
                                        <xs:element minOccurs="0" name="associationId" type="associationIdType"/>
                                        <xs:element name="epsProfileId" type="epsProfileIdType"/>
                                        <xs:element name="epsOdb" type="epsOdbType"/>
                                        <xs:element name="epsRoamingAllowed" type="epsRoamingAllowedType"/>
                                        <xs:element minOccurs="0" name="epsIndividualApnOperatorIdentifierReplacement" type="epsIndividualApnOperatorIdentifierReplacementType"/>
                                        <xs:element minOccurs="0" name="epsIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualContextId" type="epsIndividualContextIdType"/>
                                        <xs:element minOccurs="0" name="epsIndividualSubscribedChargingCharacteristic" type="epsIndividualSubscribedChargingCharacteristicType"/>
                                        <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpFlow" type="epsIndividualAmbrMaximalUplinkIpFlowType"/>
                                        <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpFlow" type="epsIndividualAmbrMaximalDownlinkIpFlowType"/>
                                        <xs:element minOccurs="0" name="epsIndividualRatFrequencyPriorityId" type="epsIndividualRatFrequencyPriorityIdType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                                        <xs:element minOccurs="0" name="epsIndividualUeUsageType" type="epsIndividualUeUsageTypeType"/>
                                        <xs:element minOccurs="0" name="epsIndividualRauTauTimer" type="epsIndividualRauTauTimerType"/>
                                        <xs:element minOccurs="0" name="epsIndividualAdditionalDefaultContextId" type="epsIndividualAdditionalDefaultContextIdType"/>
                                        <xs:element minOccurs="0" name="epsIndividualUserType" type="epsIndividualUserTypeType"/>
                                        <xs:element minOccurs="0" name="epsIndividualAmbrMaximalUplinkIpExtended" type="epsIndividualAmbrMaximalUplinkIpExtendedType"/>
                                        <xs:element minOccurs="0" name="epsIndividualAmbrMaximalDownlinkIpExtended" type="epsIndividualAmbrMaximalDownlinkIpExtendedType"/>
                                        <xs:element minOccurs="0" name="mmeAddress" type="mmeAddressType"/>
                                        <xs:element name="epsLocationState" type="epsLocationStateType"/>
                                        <xs:element minOccurs="0" name="epsRoamingRestriction" type="epsRoamingRestrictionType"/>
                                        <xs:element minOccurs="0" name="epsRegionalRoamingServiceAreaId" type="epsRegionalRoamingServiceAreaIdType"/>
                                        <xs:choice minOccurs="0">
                                            <xs:element name="epsAccessRestriction" type="epsAccessRestrictionType"/>
                                            <xs:element name="epsExtendedAccessRestriction" type="epsExtendedAccessRestrictionType"/>
                                        </xs:choice>
                                        <xs:element minOccurs="0" name="epsAaaRegistrationState" type="epsAaaRegistrationStateType"/>
                                        <xs:element minOccurs="0" name="epsAaaAddress" type="epsAaaAddressType"/>
                                        <xs:element minOccurs="0" name="epsAaaRealm" type="epsAaaRealmType"/>
                                        <xs:element minOccurs="0" name="epsDynamicPdnInformation" type="epsDynamicPdnInformationType"/>
                                        <xs:element minOccurs="0" name="epsUeSrVccCap" type="epsUeSrVccCapType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV4Address" type="epsUserIpV4AddressType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsUserIpV6Address" type="epsUserIpV6AddressType"/>
                                        <xs:element minOccurs="0" name="epsTenantId" type="epsTenantIdType"/>
                                        <xs:element minOccurs="0" name="epsSessionTransferNumber" type="epsSessionTransferNumberType"/>
                                        <xs:element minOccurs="0" name="epsMultimediaPriorityService" type="epsMultimediaPriorityServiceType"/>
                                        <xs:element minOccurs="0" name="epsZoneCodeSetId" type="epsZoneCodeSetIdType"/>
                                        <xs:element minOccurs="0" name="epsAutomaticProvisioned" type="epsAutomaticProvisionedType"/>
                                        <xs:element minOccurs="0" name="epsImeiSv" type="epsImeiSvType"/>
                                        <xs:element minOccurs="0" name="epsLastUpdateLocationDate" type="epsLastUpdateLocationDateType"/>
                                        <xs:element minOccurs="0" name="commonMsisdn" type="epsCommonMsisdnType"/>
                                        <xs:element minOccurs="0" name="epsAaaOdb" type="epsAaaOdbType"/>
                                        <xs:element minOccurs="0" name="epsAaaIndividualDefaultContextId" type="epsIndividualDefaultContextIdType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualContextId" type="epsIndividualContextIdType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaIndividualMappingContextId" type="epsIndividualMappingContextIdType"/>
                                        <xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
                                        <xs:element minOccurs="0" name="epsAdminDisable" type="xs:boolean"/>
                                        <xs:element minOccurs="0" name="epsNam" type="epsNamType"/>
                                        <xs:element maxOccurs="unbounded" minOccurs="0" name="epsAaaMIP6FeatureVector" type="epsAaaMIP6FeatureVectorType"/>
                                        <xs:element minOccurs="0" name="epsMdtUserConsent" type="epsMdtUserConsentType"/>
                                        <xs:element minOccurs="0" name="epsMmeRealm" type="epsMmeRealmType"/>
                                        <xs:element minOccurs="0" name="epsSgsnAddress" type="epsSgsnAddressType"/>
                                        <xs:element minOccurs="0" name="epsSgsnRealm" type="epsSgsnRealmType"/>
                                        <xs:element minOccurs="0" name="epsSgsnLocationState" type="epsGetSgsnLocationStateType"/>
                                        <xs:element minOccurs="0" name="epsSgsnLastUpdateLocationDate" type="epsSgsnLastUpdateLocationDateType"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="imsi" type="imsiType" use="optional">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="imsiAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="msisdn" type="msisdnType" use="optional">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="msisdnAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>
</xs:schema>