<xs:schema xmlns="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/IPWORKS/" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:simpleType name="aaaUserNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="253" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaUserPasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="256" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaAuthenticationMethodType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NONE" />
			<xs:enumeration value="EAP-MD5" />
			<xs:enumeration value="EAP-SIM" />
			<xs:enumeration value="EAP-AKA" />
			<xs:enumeration value="EAP-TLS" />
			<xs:enumeration value="EAP-TTLS" />
			<xs:enumeration value="LEAP" />
			<xs:enumeration value="PEAP" />
			<xs:enumeration value="EAP-MSCHAP2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaIPAllocationTypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaIPAllocationValueType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="128" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaGroupNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="64" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaPolicyNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="64" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaPolicyChecklistType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="1024" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="aaaPolicyReplylistType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="1024" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="aaaIPv6PrefixAllocationTypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="3" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="aaaIPv6PrefixAllocationValueType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="128" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="aaaAssociatedImsiType">
        <xs:restriction base="xs:string">
            <xs:length value="15" />
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
