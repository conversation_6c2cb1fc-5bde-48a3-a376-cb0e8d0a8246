<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions 
	name="PROV_GW_Callback_Service" targetNamespace="urn:siemens:names:prov:gw:HLR_SUBSCRIBER:4:5:3:wsdl"
	xmlns="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:soap-env="http://schemas.xmlsoap.org/soap/envelope/"
	xmlns:soap-enc="http://schemas.xmlsoap.org/soap/encoding/"
	xmlns:tns="urn:siemens:names:prov:gw:HLR_SUBSCRIBER:4:5:3:wsdl"
	xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0"
	xmlns:subscriber="urn:siemens:names:prov:gw:HLR_SUBSCRIBER:4:5:3">

	<!-- TYPE SECTION: BEGIN -->
	<wsdl:types>
		<xsd:schema 
				targetNamespace="urn:siemens:names:prov:gw:HLR_SUBSCRIBER:4:5:3:wsdl"
  				xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
  				xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"
  				xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  				xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  				xmlns="http://www.w3.org/2001/XMLSchema"
  				elementFormDefault="unqualified"
  				attributeFormDefault="unqualified">
            	<xsd:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
            	<xsd:import namespace="urn:siemens:names:prov:gw:HLR_SUBSCRIBER:4:5:3" schemaLocation="hlr_subscriber-4.5.3.xsd"/>
        </xsd:schema>        
	</wsdl:types>
	<!-- TYPE SECTION: END -->
	
	<!-- MESSAGE SECTION: BEGIN -->

	<wsdl:message name="SPMLAddResponseMessage">
		<wsdl:part name="body" element="spml:addResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLModifyResponseMessage">
		<wsdl:part name="body" element="spml:modifyResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLDeleteResponseMessage">
		<wsdl:part name="body" element="spml:deleteResponse" />
	</wsdl:message>
	
	<wsdl:message name="SPMLBatchResponseMessage"> 
		<wsdl:part name="body" element="spml:batchResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLSearchResponseMessage">
		<wsdl:part name="body" element="spml:searchResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLExtendedResponseMessage">
		<wsdl:part name="body" element="spml:extendedResponse" />
	</wsdl:message>
	
	<wsdl:message name="SPMLChangeIdResponseMessage">
		<wsdl:part name="body" element="spml:changeIdResponse" />
	</wsdl:message>
	
	<wsdl:message name="SPMLResponseConfirmationMessage">
		<wsdl:part name="body" element="spml:responseConfirmation" />
	</wsdl:message>

	<!-- MESSAGE SECTION: END -->

	<!-- PORT TYPE SECTION: BEGIN -->
	<wsdl:portType name="SPMLHlrSubscriber453CallbackPortType">
       		<wsdl:operation name="batchResponse">
		    <wsdl:input name="SPMLBatchResponseInput" message="tns:SPMLBatchResponseMessage"/>
		    <wsdl:output name="SPMLBatchResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
	 	</wsdl:operation>

        	<wsdl:operation name="addResponse">
		    <wsdl:input name="SPMLAddResponseInput" message="tns:SPMLAddResponseMessage"/>
		    <wsdl:output name="SPMLAddResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
        	</wsdl:operation>

		<wsdl:operation name="modifyResponse">
		    <wsdl:input name="SPMLModifyResponseInput" message="tns:SPMLModifyResponseMessage"/>
		    <wsdl:output name="SPMLModifyResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
		</wsdl:operation>

		<wsdl:operation name="deleteResponse">
		    <wsdl:input name="SPMLDeleteResponseInput" message="tns:SPMLDeleteResponseMessage"/> 
		    <wsdl:output name="SPMLDeleteResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
		</wsdl:operation> 

		<wsdl:operation name="searchResponse"> 
		    <wsdl:input name="SPMLSearchResponseInput" message="tns:SPMLSearchResponseMessage"/> 
		    <wsdl:output name="SPMLSearchResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
		</wsdl:operation> 

		<wsdl:operation name="extendedResponse"> 
			<wsdl:input name="SPMLExtendedResponseInput" message="tns:SPMLExtendedResponseMessage"/> 
			<wsdl:output name="SPMLExtendedResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
		</wsdl:operation> 
		
        	<wsdl:operation name="changeIdResponse">
		    <wsdl:input name="SPMLChangeIdResponseInput" message="tns:SPMLChangeIdResponseMessage"/>
		    <wsdl:output name="SPMLChangeIdResponseOutput" message="tns:SPMLResponseConfirmationMessage"/>
        	</wsdl:operation>
	</wsdl:portType> 
	<!-- PORT TYPE SECTION: END -->

	<!-- BINDING SECTION: BEGIN -->
	<wsdl:binding name="SPMLHlrSubscriber453CallbackBinding" type="tns:SPMLHlrSubscriber453CallbackPortType">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/> 
		<wsdl:operation name="batchResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/batchResponse" /> 
			<wsdl:input name="SPMLBatchResponseInput" >
				<soap:body use="literal" />
			</wsdl:input> 
            		<wsdl:output name="SPMLBatchResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="addResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/addResponse" />
			<wsdl:input name="SPMLAddResponseInput" >
				<soap:body use="literal" />
			</wsdl:input>
            		<wsdl:output name="SPMLAddResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="modifyResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/modifyResponse" /> 
			<wsdl:input name="SPMLModifyResponseInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
            		<wsdl:output name="SPMLModifyResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="deleteResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/deleteResponse" /> 
			<wsdl:input name="SPMLDeleteResponseInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
            		<wsdl:output name="SPMLDeleteResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="searchResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/searchResponse" /> 
			<wsdl:input name="SPMLSearchResponseInput" >
				<soap:body use="literal" />
			</wsdl:input>
		            <wsdl:output name="SPMLSearchResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="extendedResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/extendedResponse"/>
			<wsdl:input name="SPMLExtendedResponseInput" > 
				<soap:body use="literal" /> 
			</wsdl:input> 
			<wsdl:output name="SPMLExtendedResponseOutput" > 
				<soap:body use="literal" /> 
			</wsdl:output> 
		</wsdl:operation>
		
		<wsdl:operation name="changeIdResponse"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/changeIdResponse" /> 
			<wsdl:input name="SPMLChangeIdResponseInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
            		<wsdl:output name="SPMLChangeIdResponseOutput" >
                		<soap:body use="literal" />
            		</wsdl:output>
		</wsdl:operation>		
	</wsdl:binding> 
	<!-- BINDING SECTION: END -->

	<wsdl:service name="SPMLHlrSubscriber453CallbackService">
	    <port name="SPMLHlrSubscriber453CallbackPortType" binding="tns:SPMLHlrSubscriber453CallbackBinding">
	      <soap:address location="http://localhost:8080/ProvisioningGateway/services/SPMLHlrSubscriber453CallbackService"/>
	    </port>
	</wsdl:service>

</wsdl:definitions>