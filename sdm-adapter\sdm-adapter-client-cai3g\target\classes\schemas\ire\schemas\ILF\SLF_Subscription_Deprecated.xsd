<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/SLF/1.1/"
	xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/SLF/1.1/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:annotation>
		<xs:documentation>This schema is for only provsioning SLF.
		</xs:documentation>
	</xs:annotation>
	<xs:element name="subscriberId" type="subscriberIdType" />
	<xs:element name="setSLFSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="slfName" type="slfnameType" minOccurs="0" />
				<xs:element name="imsiEntry" nillable="true" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imsi" type="imsiType" minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
							<xs:element name="imsiValidForModules" type="imsiValidForModulesType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFImsiKey" id="CAI3GKeySetSLFImsi">
						<xs:selector xpath="." />
						<xs:field xpath="@imsi" />
					</xs:key>
					<xs:keyref name="SetSLFImsiKeyRef" refer="SetSLFImsiKey">
						<xs:selector xpath="." />
						<xs:field xpath="imsi" />
					</xs:keyref>
				</xs:element>
				<xs:element name="msisdnEntry" nillable="true" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdn" type="msisdnType" minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFMsisdnKey" id="CAI3GKeySetSLFMsisdn">
						<xs:selector xpath="." />
						<xs:field xpath="@msisdn" />
					</xs:key>
					<xs:keyref name="SetSLFMsisdnKeyRef" refer="SetSLFMsisdnKey">
						<xs:selector xpath="." />
						<xs:field xpath="msisdn" />
					</xs:keyref>
				</xs:element>
				<xs:element name="publicIdEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="publicId" type="publicIdValueType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="publicId" type="publicIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFPublicIdKey" id="CAI3GKeySetSLFPublicId">
						<xs:selector xpath="." />
						<xs:field xpath="@publicId" />
					</xs:key>
					<xs:keyref name="SetSLFPublicIdKeyRef" refer="SetSLFPublicIdKey">
						<xs:selector xpath="." />
						<xs:field xpath="publicId" />
					</xs:keyref>
				</xs:element>
				<xs:element name="privateIdEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateId" type="privateIdValueType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="privateId" type="privateIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFPrivateIdKey" id="CAI3GKeySetSLFPrivateId">
						<xs:selector xpath="." />
						<xs:field xpath="@privateId" />
					</xs:key>
					<xs:keyref name="SetSLFPrivateIdKeyRef" refer="SetSLFPrivateIdKey">
						<xs:selector xpath="." />
						<xs:field xpath="privateId" />
					</xs:keyref>
				</xs:element>
				<xs:element name="imsiRangeEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imsiRange" type="imsiRangeType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="imsiRange" type="imsiRangeType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFImsiRangeKey" id="CAI3GKeySetSLFImsiRange">
						<xs:selector xpath="." />
						<xs:field xpath="@imsiRange" />
					</xs:key>
					<xs:keyref name="SetSLFImsiRangeKeyRef" refer="SetSLFImsiRangeKey">
						<xs:selector xpath="." />
						<xs:field xpath="imsiRange" />
					</xs:keyref>
				</xs:element>
				<xs:element name="msisdnRangeEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdnRange" type="msisdnRangeType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="msisdnRange" type="msisdnRangeType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFMsisdnRangeKey" id="CAI3GKeySetSLFMsisdnRange">
						<xs:selector xpath="." />
						<xs:field xpath="@msisdnRange" />
					</xs:key>
					<xs:keyref name="SetSLFMsisdnRangeKeyRef" refer="SetSLFMsisdnRangeKey">
						<xs:selector xpath="." />
						<xs:field xpath="msisdnRange" />
					</xs:keyref>
				</xs:element>
				<xs:element name="publicIdRangeEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="publicIdRange" type="publicIdRangeValueType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="publicIdRange" type="publicIdRangeValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFPublicIdRangeKey" id="CAI3GKeySetSLFPublicIdRange">
						<xs:selector xpath="." />
						<xs:field xpath="@publicIdRange" />
					</xs:key>
					<xs:keyref name="SetSLFPublicIdRangeKeyRef" refer="SetSLFPublicIdRangeKey">
						<xs:selector xpath="." />
						<xs:field xpath="publicIdRange" />
					</xs:keyref>
				</xs:element>
				<xs:element name="privateIdRangeEntry" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateIdRange" type="privateIdRangeValueType"
								minOccurs="0" />
							<xs:element name="diaserver" type="diaserverType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="privateIdRange" type="privateIdRangeValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetSLFPrivateIdRangeKey" id="CAI3GKeySetSLFPrivateIdRange">
						<xs:selector xpath="." />
						<xs:field xpath="@privateIdRange" />
					</xs:key>
					<xs:keyref name="SetSLFPrivateIdRangeKeyRef" refer="SetSLFPrivateIdRangeKey">
						<xs:selector xpath="." />
						<xs:field xpath="privateIdRange" />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="subscriberId" type="subscriberIdType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="subscriberIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="SetSubscriberKey" id="CAI3GKeySetSubscriber">
			<xs:selector xpath="." />
			<xs:field xpath="@subscriberId" />
		</xs:key>
	</xs:element>
	<xs:element name="getSLFSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="slfName" type="slfnameType" minOccurs="0" />
				<xs:element name="imsiEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="msisdnEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="publicIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="publicId" type="publicIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="privateIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="privateId" type="privateIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="imsiRangeEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="imsiRange" type="imsiRangeType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="msisdnRangeEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="msisdnRange" type="msisdnRangeType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="publicIdRangeEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="publicIdRange" type="publicIdRangeValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="privateIdRangeEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="privateIdRange" type="privateIdRangeValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdRangeAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="subscriberId" type="subscriberIdType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="subscriberIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="getResponseSLFSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="subscriberId" type="subscriberIdType" />
				<xs:element name="imsiEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imsi" type="imsiType" />
							<xs:element name="diaserver" type="diaserverType" />
						</xs:sequence>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetSLFImsiKey" id="CAI3GKeyGetSLFImsi">
						<xs:selector xpath="." />
						<xs:field xpath="@imsi" />
					</xs:key>
					<xs:keyref name="GetSLFImsiKeyRef" refer="GetSLFImsiKey">
						<xs:selector xpath="." />
						<xs:field xpath="imsi" />
					</xs:keyref>
				</xs:element>
				<xs:element name="msisdnEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdn" type="msisdnType" />
							<xs:element name="diaserver" type="diaserverType" />
						</xs:sequence>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetSLFMsisdnKey" id="CAI3GKeyGetSLFMsisdn">
						<xs:selector xpath="." />
						<xs:field xpath="@msisdn" />
					</xs:key>
					<xs:keyref name="GetSLFMsisdnKeyRef" refer="GetSLFMsisdnKey">
						<xs:selector xpath="." />
						<xs:field xpath="msisdn" />
					</xs:keyref>
				</xs:element>
				<xs:element name="publicIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="publicId" type="publicIdValueType" />
							<xs:element name="diaserver" type="diaserverType" />
						</xs:sequence>
						<xs:attribute name="publicId" type="publicIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetSLFPublicIdKey" id="CAI3GKeyGetSLFPublicId">
						<xs:selector xpath="." />
						<xs:field xpath="@publicId" />
					</xs:key>
					<xs:keyref name="GetSLFPublicIdKeyRef" refer="GetSLFPublicIdKey">
						<xs:selector xpath="." />
						<xs:field xpath="publicId" />
					</xs:keyref>
				</xs:element>
				<xs:element name="privateIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateId" type="privateIdValueType" />
							<xs:element name="diaserver" type="diaserverType" />
						</xs:sequence>
						<xs:attribute name="privateId" type="privateIdValueType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetSLFPrivateIdKey" id="CAI3GKeyGetSLFPrivateId">
						<xs:selector xpath="." />
						<xs:field xpath="@privateId" />
					</xs:key>
					<xs:keyref name="GetSLFPrivateIdKeyRef" refer="GetSLFPrivateIdKey">
						<xs:selector xpath="." />
						<xs:field xpath="privateId" />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="subscriberId" type="subscriberIdType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="subscriberIdAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="GetSubscriberKey" id="CAI3GKeyGetSubscriber">
			<xs:selector xpath="." />
			<xs:field xpath="@subscriberId" />
		</xs:key>
		<xs:keyref name="GetSubscriberKeyRef" refer="GetSubscriberKey">
			<xs:selector xpath="." />
			<xs:field xpath="subscriberId" />
		</xs:keyref>
	</xs:element>
	<!-- below is type definition -->
	<xs:simpleType name="subscriberIdType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="diaserverType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="slfnameType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{6,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="publicIdValueType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(sip:.{1,256})|(tel:\+[\-.()0-9]{0,256}(!\.\*!)?)" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="privateIdValueType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="256" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiValidForModulesType">
		<xs:restriction base="xs:string">
			<xs:minLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	<!-- below is type definition -->
	<xs:simpleType name="imsiRangeType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{0,15}!\.\*!" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnRangeType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{0,15}!\.\*!" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="publicIdRangeValueType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(sip:.{1,256})|(tel:\+[\-.()0-9]{0,256}!.*!)" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="privateIdRangeValueType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="256" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
