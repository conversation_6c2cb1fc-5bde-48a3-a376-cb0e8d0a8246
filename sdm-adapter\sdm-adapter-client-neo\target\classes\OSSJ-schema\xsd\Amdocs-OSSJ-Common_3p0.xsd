<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:cbebi-v1-5="http://ossj.org/xml/Common-CBEBi/v1-5" targetNamespace="http://amdocs/core/ossj-Common/dat/3" elementFormDefault="qualified">
	<xs:annotation>
		<xs:documentation>This schema was generated at: 07 April 2011</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common/v1-5" schemaLocation="OSSJ-Common-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEBi/v1-5" schemaLocation="OSSJ-Common-CBEBi-v1-5.xsd"/>
	<xs:element name="primaryKey" type="xs:string">
		<xs:annotation>
			<xs:documentation>Uniquely identifies the object in question. The key type describes whether this is a request key (used to identify the request as it is processed by the system), the service key, an account key (for example, the billing account key), or the resource key.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:element name="AssociationRuleViolationException" type="amdocs-co:AssociationRuleViolationException"/>
	<xs:complexType name="AssociationRuleViolationException">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:AssociationRuleViolationException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Container for exception details.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ExceptionDetails" type="amdocs-co:ExceptionDetails">
		<xs:annotation>
			<xs:documentation>Container for all of the exception details returned in the exception response.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="ExceptionDetails">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:EntityValue">
				<xs:sequence>
					<xs:element name="errorCode" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The type of error that has been encountered. Can be customized in the Activation Controller configuration file.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="retryable" type="xs:boolean" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Whether the failed operation can be retried or not.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="errorMessage" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The error in detail. Can be customized in the Activation Controller configuration document.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="errorClass" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Categorization of this type of message.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="component" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Full package name for the component that the exception originated at.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="severity" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The relative severity of the error.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="inputXML" type="xs:string" minOccurs="0"/>
					<xs:element name="reason" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The error message thrown by the originating component. This is usually a technical error, and is used to provide more information.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="managedEntityKey" type="co-v1-5:ManagedEntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Uniquely identifies the activation request related to the exception. The external ID of the request is automatically generated when the request is first created, and can be found in the RC_REQUEST table of the Request Controller database.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="problemDetails" type="cbecore-v1-5:CBEManagedEntityValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayofExceptionDetails" type="amdocs-co:ArrayofExceptionDetails"/>
	<xs:complexType name="ArrayofExceptionDetails">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:ExceptionDetails" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Item blocks are used to contain all details relating to an individual object. For example, there can be more than one object of type exceptionDetails, so the interface element contains at least one item. The item contains details of an individual exception. If there are two exceptions, there are two items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfAssociationRuleViolationException" type="amdocs-co:ArrayOfAssociationRuleViolationException"/>
	<xs:complexType name="ArrayOfAssociationRuleViolationException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:AssociationRuleViolationException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssUnsupportedAttributeException" type="amdocs-co:OssUnsupportedAttributeException"/>
	<xs:complexType name="OssUnsupportedAttributeException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssUnsupportedAttributeException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfOssUnsupportedAttributeException" type="amdocs-co:ArrayOfOssUnsupportedAttributeException"/>
	<xs:complexType name="ArrayOfOssUnsupportedAttributeException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssUnsupportedAttributeException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssIllegalArgumentException" type="amdocs-co:OssIllegalArgumentException"/>
	<xs:complexType name="OssIllegalArgumentException">
		<xs:annotation>
			<xs:documentation> Similar to java.lang.IllegalArgumentException, except this inherits from Exception, not RuntimeException. This new class is required because J2EE containers deal with RuntimeException in a special way. See EJB specification. This exception is thrown if the argument of a remote method is invalid. Note: This exception replaces the deprecated javax.oss.IllegalArgumentException</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssIllegalArgumentException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="OssIllegalAttributeValueException" type="amdocs-co:OssIllegalAttributeValueException"/>
	<xs:complexType name="OssIllegalAttributeValueException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssIllegalAttributeValueException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfOssIllegalAttributeValueException" type="amdocs-co:ArrayOfOssIllegalAttributeValueException"/>
	<xs:complexType name="ArrayOfOssIllegalAttributeValueException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssIllegalAttributeValueException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="RemoteException" type="amdocs-co:RemoteException"/>
	<xs:complexType name="RemoteException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:RemoteException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfRemoteException" type="amdocs-co:ArrayOfRemoteException"/>
	<xs:complexType name="ArrayOfRemoteException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:RemoteException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssSetException" type="amdocs-co:OssSetException"/>
	<xs:complexType name="OssSetException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssSetException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfOssSetException" type="amdocs-co:ArrayOfOssSetException"/>
	<xs:complexType name="ArrayOfOssSetException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssSetException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssUnsupportedOperationException" type="amdocs-co:OssUnsupportedOperationException"/>
	<xs:complexType name="OssUnsupportedOperationException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssUnsupportedOperationException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfOssUnsupportedOperationException" type="amdocs-co:ArrayOfOssUnsupportedOperationException"/>
	<xs:complexType name="ArrayOfOssUnsupportedOperationException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssUnsupportedOperationException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssInvalidXMLRequestException" type="amdocs-co:OssInvalidXMLRequestException"/>
	<xs:complexType name="OssInvalidXMLRequestException">
		<xs:choice>
			<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
			<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
		</xs:choice>
	</xs:complexType>
	<xs:element name="ArrayOfOssInvalidXMLRequestException" type="amdocs-co:ArrayOfOssInvalidXMLRequestException"/>
	<xs:complexType name="ArrayOfOssInvalidXMLRequestException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssInvalidXMLRequestException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="CreateException" type="amdocs-co:CreateException"/>
	<xs:complexType name="CreateException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:CreateException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfCreateException" type="amdocs-co:ArrayOfCreateException"/>
	<xs:complexType name="ArrayOfCreateException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:CreateException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="DuplicateKeyException" type="amdocs-co:DuplicateKeyException"/>
	<xs:complexType name="DuplicateKeyException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:DuplicateKeyException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfDuplicateKeyException" type="amdocs-co:ArrayOfDuplicateKeyException"/>
	<xs:complexType name="ArrayOfDuplicateKeyException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:DuplicateKeyException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="FinderException" type="amdocs-co:FinderException"/>
	<xs:complexType name="FinderException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:FinderException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfFinderException" type="amdocs-co:ArrayOfFinderException"/>
	<xs:complexType name="ArrayOfFinderException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:FinderException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ObjectNotFoundException" type="amdocs-co:ObjectNotFoundException"/>
	<xs:complexType name="ObjectNotFoundException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:ObjectNotFoundException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfObjectNotFoundException" type="amdocs-co:ArrayOfObjectNotFoundException"/>
	<xs:complexType name="ArrayOfObjectNotFoundException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:ObjectNotFoundException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="RemoveException" type="amdocs-co:RemoveException"/>
	<xs:complexType name="RemoveException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:RemoveException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfRemoveException" type="amdocs-co:ArrayOfRemoveException"/>
	<xs:complexType name="ArrayOfRemoveException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:RemoveException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="OssResyncRequiredException" type="amdocs-co:OssResyncRequiredException"/>
	<xs:complexType name="OssResyncRequiredException">
		<xs:complexContent>
			<xs:extension base="co-v1-5:OssResyncRequiredException">
				<xs:sequence>
					<xs:element name="exceptionsDetails" type="amdocs-co:ArrayofExceptionDetails" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfOssResyncRequiredException" type="amdocs-co:ArrayOfOssResyncRequiredException"/>
	<xs:complexType name="ArrayOfOssResyncRequiredException">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:OssResyncRequiredException" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="CharacteristicValue" type="amdocs-co:CharacteristicValue">
		<xs:annotation>
			<xs:documentation>Attribute-value pairs that describe some characteristic of the service or resource in question.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="CharacteristicValue">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:CharacteristicValue">
				<xs:sequence>
					<xs:element name="change" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>This element describes what to do with the contained set of characteristic values. Where the request creates an entity, this element must always take the value "add". In this case it adds the contained characteristic values to the entity. For modify requests, this element can take one of the values: "add", to add a new configuration value, "modify" to update the value of an existing characteristic value, or "delete" to remove a characteristic value.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="indexPolicy" type="xs:string" minOccurs="0"/>
					<xs:element name="index" type="xs:string" minOccurs="0"/>
					<xs:element name="id" type="xs:string" minOccurs="0"/>
					<xs:element name="values" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfCharacteristicValue" type="amdocs-co:ArrayOfCharacteristicValue"/>
	<xs:complexType name="ArrayOfCharacteristicValue">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:ArrayOfCharacteristicValue">
				<xs:sequence>
					<xs:element name="item" type="amdocs-co:CharacteristicValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ActivationTargetValue" type="amdocs-co:ActivationTargetValue"/>
	<xs:complexType name="ActivationTargetValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="isDefault" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A flag to determine whether this a default object of the named type. This attribute is only used in requests to load data into the Activation Target Store. It is ignored if it is included in an activation request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="priority" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The priority of an object. Used when attempting to make a selection. The value is a number between 0 and 10, with zero denoting the highest priority, one the next level, and so on.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="routingRange" type="xs:string" minOccurs="0"/>
			<xs:element name="status" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The status of the object. Can be one of: Active - available for use. Inactive - not available for use.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="softwareVersion" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The type and version of the target software. This may be used by the agent to work out which commands it needs to issue.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="target" type="amdocs-co:ActivationTargetTargetValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contains details of the target, which models the network device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="type" type="amdocs-co:ActivationTargetTypeValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contains a reference to the related metadata object.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="group" type="amdocs-co:ActivationTargetGroupValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contains details of the groups that the Activation Target Store objects belong to. Groups are used to return sets of associated information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="roles" type="amdocs-co:ActivationTargetRolesValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contains details of appropriate roles. This is the "role" that the modeled target may possibly play in the environment. For example, it may be possible that a target plays the role of customer edge or provider edge device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="interfaces" type="amdocs-co:ActivationTargetInterfacesValue" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contains details of the interface by which connections to the network devices are established. For example, interfaces may model ports.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfActivationTargetValue" type="amdocs-co:ArrayOfActivationTargetValue"/>
	<xs:complexType name="ArrayOfActivationTargetValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:ActivationTargetValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfActivationTargetIdValue" type="amdocs-co:ArrayOfActivationTargetIdValue"/>
	<xs:complexType name="ArrayOfActivationTargetIdValue">
		<xs:sequence>
			<xs:element name="item" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetTargetValue" type="amdocs-co:ActivationTargetTargetValue"/>
	<xs:complexType name="ActivationTargetTargetValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="isDefault" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A flag to determine whether this a default object of the named type. This attribute is only used in requests to load data into the Activation Target Store. It is ignored if it is included in an activation request.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="InterfaceTypeValue" type="amdocs-co:InterfaceTypeValue"/>
	<xs:complexType name="InterfaceTypeValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="protocolDriverType" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The type of Protocol Driver that should be used when connecting to devices using this type of interface.  Note: This attribute is only necessary in an environment that uses Protocol Drivers to connect to resources.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="packageName" type="xs:string" minOccurs="0"/>
			<xs:element name="devicePlugIn" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The name of the Device Plug-in that performs lifecycle maintenance for the device.  Note: This attribute is only required in an environment that uses agents to activate services and resources.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lockingScheme" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This attribute describes if this type of interface can be locked or not (interfaces are locked while they are in use). The possible values are: LOCKING - specifies that the interface can be locked. NON-LOCKING - specifies that the interface cannot be locked. Note: If the interface is described as LOCKING, a value must be supplied for the interface's locksSupported attribute.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="locksSupported" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This attribute describes the number of locks that can be maintained on one interface at one time. Its value is a positive integer. For example, if a target network element supports 5 locks (it is loaded with locksSupported=5 and lockingScheme=LOCKING), then 5 requests are received, locks can be made on each interface until all 5 are used. If a sixth request is received, an error is returned.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetTypeValue" type="amdocs-co:ActivationTargetTypeValue"/>
	<xs:complexType name="ActivationTargetTypeValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="managerLevel" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The value of this attribute is a number: 0 (zero) means that this type of target is the actual target device, and activation occurs directly. This is the default. A number equal to or greater than 1 means that this type of target is a manager. That is, it is an EMS (1) or other network manager (&gt;1).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lockingScheme" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This attribute describes if this type of interface can be locked or not (interfaces are locked while they are in use). The possible values are: LOCKING - specifies that the interface can be locked. NON-LOCKING - specifies that the interface cannot be locked. Note: If the interface is described as LOCKING, a value must be supplied for the interface's locksSupported attribute.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="osType" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The target type's operating system.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetGroupValue" type="amdocs-co:ActivationTargetGroupValue"/>
	<xs:complexType name="ActivationTargetGroupValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="isDefault" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A flag to determine whether this a default object of the named type. This attribute is only used in requests to load data into the Activation Target Store. It is ignored if it is included in an activation request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="status" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The status of the object. Can be one of: Active - available for use. Inactive - not available for use.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="emsRedundancyPolicy" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>It is possible to use a standby EMS for activation when the primary EMS fails. Whether this "failover" process happens automatically or not depends on the emsRedundancyPolicy setting. The value of this attribute is a number: 0 (zero) - automatic - failover occurs automatically whenever multiple EMS are assigned to the same Target Group. This is the default. 1 (one) - manual - failover does not occur automatically. In this mode the priority of an EMS must be changed in order to effect failover. </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetRolesValue" type="amdocs-co:ActivationTargetRolesValue"/>
	<xs:complexType name="ActivationTargetRolesValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:ActivationTargetRoleValue" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Item blocks are used to contain all details relating to an individual object. For example, there can be more than one object of type role, so the interface element contains at least one item. The item contains details of an individual role. If there are two roles, there are two items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetRoleValue" type="amdocs-co:ActivationTargetRoleValue"/>
	<xs:complexType name="ActivationTargetRoleValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="startIndex" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The start index of the target role. 0 is the default.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="roleFunction" type="xs:string" minOccurs="0"/>
			<xs:element name="length" type="xs:string" minOccurs="0"/>
			<xs:element name="selectionAttribute" type="xs:string" minOccurs="0"/>
			<xs:element name="selectionPolicy" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetInterfacesValue" type="amdocs-co:ActivationTargetInterfacesValue"/>
	<xs:complexType name="ActivationTargetInterfacesValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:ActivationTargetInterfaceValue" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Item blocks are used to contain all details relating to an individual object. For example, there can be more than one object of type interface, so the interface element contains at least one item. The item contains details of an individual interface. If there are two interfaces, there are two items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationTargetInterfaceValue" type="amdocs-co:ActivationTargetInterfaceValue"/>
	<xs:complexType name="ActivationTargetInterfaceValue">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:EntityValue">
				<xs:sequence>
					<xs:element name="id" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the object. This value is usually used to select an activation target with the same ID from the Activation Target Model. In this case, the details from the Activation Target Model will be included in the request before it is run.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="isDefault" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A flag to determine whether this a default object of the named type. This attribute is only used in requests to load data into the Activation Target Store. It is ignored if it is included in an activation request.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="priority" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The priority is used when selecting an interface. The value is a number, with zero denoting the highest priority, one the next level, and so on. 0 is the default.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="status" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The status of the object. Can be one of: Active - available for use. Inactive - not available for use.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="attributes" type="cbecore-v1-5:CBEManagedEntityValue" minOccurs="0"/>
					<xs:element name="interfaceType" type="amdocs-co:InterfaceTypeValue" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A type of interface. Describes the properties that are common to all interfaces of a given type. Interface types are associated with one or more parameter types and one or more credential types.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="credentials" type="amdocs-co:CredentialsValue" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Authentication credentials for target. Each credential item contains a name and a value. The value can be a principal, password, or certificate. Values are stored in their encrypted form by default. To pass in a value in clear text, it should be prefixed "clear:". In this case, the value will be automatically encrypted/decrypted by the Universal Activator.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="CredentialsValue" type="amdocs-co:CredentialsValue"/>
	<xs:complexType name="CredentialsValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-co:CredentialValue" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Item blocks are used to contain all details relating to an individual object. For example, there can be more than one object of type credential, so the interface element contains at least one item. The item contains details of an individual credential. If there are two credentials, there are two items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="CredentialValue" type="amdocs-co:CredentialValue"/>
	<xs:complexType name="CredentialValue">
		<xs:sequence>
			<xs:element name="name" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The name of the credential, for example, "username" or "password". Must never be encrypted.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="principal" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The principal user name. This value should either be encrypted, or, if it is clear text, it should be prefixed "clear:". Clear text values that are prefixed "clear:" will be encrypted by the Universal Activator. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="password" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The password value.  This value should either be encrypted, or, if it is clear text, it should be prefixed "clear:". Clear text values that are prefixed "clear:" will be encrypted by the Universal Activator. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="certificate" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Where authentication occurs using a certificate, this column can contain a reference to the certificate. This value should either be encrypted, or, if it is clear text, it should be prefixed "clear:". Clear text values that are prefixed "clear:" will be encrypted by the Universal Activator. </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ActivationGroupValue" type="amdocs-co:ActivationGroupValue"/>
	<xs:complexType name="ActivationGroupValue">
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0"/>
			<xs:element name="status" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ConfigurationRecordsValue" type="amdocs-co:ConfigurationRecordsValue">
		<xs:annotation>
			<xs:documentation>Container for the configuration documents being loaded, modified, or deleted using this request.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="ConfigurationRecordsValue">
		<xs:sequence>
			<xs:element name="item" type="cbecore-v1-5:EntityValue" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Item blocks are used to contain all details relating to an individual object. For example, there can be more than one object of type configuration record, so the interface element contains at least one item. The item contains details of an individual configuration record. If there are two configuration records, there are two items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ConfigurationRecord" type="amdocs-co:ConfigurationRecord"/>
	<xs:complexType name="ConfigurationRecord">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:EntityValue"/>
		</xs:complexContent>
	</xs:complexType>
</xs:schema>
