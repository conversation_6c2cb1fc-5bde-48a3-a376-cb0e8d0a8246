<!-- 
Build_Label: @@@Build_Label@@@
ClearQuest_MR#: @@@ClearQuest_MR#@@@ 
Build_Date: @@@Build_Date@@@
-->
<xsd:schema xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v2_0">
	<xsd:annotation>
		<xsd:documentation>Common data types across all the enterprise services.
                         This schema definition should contain the common types
                         and elements organized in an alphabetical oder.
                         Version: 2.0</xsd:documentation>
	</xsd:annotation>
	<!-- Types            -->
	<!-- start getBill PDF types       -->
	<xsd:simpleType name="billIdType">
		<xsd:annotation>
			<xsd:documentation>Biller ID</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:simpleType name="pdfByptesType">
		<xsd:annotation>
			<xsd:documentation>base 64 pdf string </xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:base64Binary">
		</xsd:restriction>
	</xsd:simpleType>
	
	<!-- End getBill PDF types       -->
	
	<xsd:simpleType name="ipAddressType">
		<xsd:annotation>
			<xsd:documentation>ipAddress value with validation.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="(([2][0-5][0-5])|([0-1][0-9][0-9])|\d{2}|\d{1})\.(([2][0-5][0-5])|([0-1][0-9][0-9])|\d{2}|\d{1})\.(([2][0-5][0-5])|([0-1][0-9][0-9])|\d{2}|\d{1})\.(([2][0-5][0-5])|([0-1][0-9][0-9])|\d{2}|\d{1})"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	
	
	<xsd:simpleType name="fromDateType">
				<xsd:annotation>
					<xsd:documentation>start Date</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:date"/>
		</xsd:simpleType>
	<xsd:simpleType name="toDateType">
				<xsd:annotation>
					<xsd:documentation>end Date</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:date"/>
			
	</xsd:simpleType>	
	
	<xsd:simpleType name="AccountNumberType">
		<xsd:annotation>
			<xsd:documentation>Generic 9 digit account number base type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{9}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AddressIdType">
		<xsd:annotation>
			<xsd:documentation>Address ID for each Component ID.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AddressLineType">
		<xsd:annotation>
			<xsd:documentation>Street address line.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AmountType">
		<xsd:annotation>
			<xsd:documentation>US dollar value.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AttnLineType">
		<xsd:annotation>
			<xsd:documentation>Attention line on address/pkg.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BanType">
		<xsd:annotation>
			<xsd:documentation>DEPRICATED. The CARES account number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
	</xsd:simpleType>
	<xsd:simpleType name="BarType">
		<xsd:annotation>
			<xsd:documentation>Billing arangement</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
	</xsd:simpleType>
	<xsd:simpleType name="CityType">
		<xsd:annotation>
			<xsd:documentation>City</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CompensationIdType">
		<xsd:annotation>
			<xsd:documentation>Compensation Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="CompIdType">
		<xsd:annotation>
			<xsd:documentation>Component Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="CorrelationIdType">
		<xsd:annotation>
			<xsd:documentation>Trackable correlation or transaction ID from processing system.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CtnType">
		<xsd:annotation>
			<xsd:documentation>Customer telephone number</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{10}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustContactNoType">
		<xsd:annotation>
			<xsd:documentation>Customer Telephone Number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:CtnType"/>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerIdType">
		<xsd:annotation>
			<xsd:documentation>Customer Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
	</xsd:simpleType>
	<xsd:simpleType name="CustNoType">
		<xsd:annotation>
			<xsd:documentation>Customer Number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerSubTypeType">
		<xsd:annotation>
			<xsd:documentation>Customer sub-type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerTypeType">
		<xsd:annotation>
			<xsd:documentation>Customer type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="255"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EmailAddressType">
		<xsd:annotation>
			<xsd:documentation>Email address of the customer.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,6}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EquipmentTypeType">
		<xsd:annotation>
			<xsd:documentation>Service types of equipment.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ErrorCodeType">
		<xsd:annotation>
			<xsd:documentation>General error code.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ErrorDescriptionType">
		<xsd:annotation>
			<xsd:documentation>Description of an error code. There may be error specific info in the string, but the initial text should describe the error and indicate the severity.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EsnType">
		<xsd:annotation>
			<xsd:documentation>ESN or MEID of the device.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{11}|\d{18}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FaIdType">
		<xsd:annotation>
			<xsd:documentation>Financial Account Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
	</xsd:simpleType>
	<xsd:simpleType name="FirstNameType">
		<xsd:annotation>
			<xsd:documentation>First or given name</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GeoCodeType">
		<xsd:annotation>
			<xsd:documentation>Geo code for customer.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="9"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GUIDType">
		<xsd:annotation>
			<xsd:documentation>GUID in custom LDAP format: 8-4-4-4-20-4-4-4-4 .</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{20}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IETFGUIDType">
		<xsd:annotation>
			<xsd:documentation>GUID in ITEF GUID format: {8-4-4-4-12} ( braces optional ).  See http://www.ietf.org/rfc/rfc4122.txt for details.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}?"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IndicatorBooleanType">
		<xsd:annotation>
			<xsd:documentation>Indicator Type. Possible values are true and false.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:boolean"/>
	</xsd:simpleType>
	<xsd:simpleType name="IndicatorStringType">
		<xsd:annotation>
			<xsd:documentation>Indicator Type. Possible values are Y, N or NA (can be used for not available/not applicable scenarios).</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IssueTypeType">
		<xsd:annotation>
			<xsd:documentation>Issue Type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="32"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ItemDescriptionType">
		<xsd:annotation>
			<xsd:documentation>Item Description from catalog.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LastNameType">
		<xsd:annotation>
			<xsd:documentation>Last name </xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LocationCodeType">
		<xsd:annotation>
			<xsd:documentation>Location Code</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="LocationIdType">
		<xsd:annotation>
			<xsd:documentation>Location Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="MakeType">
		<xsd:annotation>
			<xsd:documentation>Device Make.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MdnType">
		<xsd:annotation>
			<xsd:documentation>Member Directory Number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{10}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MdnStatusType">
		<xsd:annotation>
			<xsd:documentation>Member Directory Number status. The possible values are 2 character abbreviations, as follows:
				AC - 	Active
				CE - 	Ceased
				IN   - 	Initialized
				NA - 	Not Active
				PA - 	Pending Activation
				SU - 	Suspended
				AS - 	AS
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="MessageNumberType">
		<xsd:annotation>
			<xsd:documentation>Message number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MessageTextType">
		<xsd:annotation>
			<xsd:documentation>Message text.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="220"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MinType">
		<xsd:annotation>
			<xsd:documentation>Mobile Identification Number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{10}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ModelType">
		<xsd:annotation>
			<xsd:documentation>Device Model.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MSIdType">
		<xsd:annotation>
			<xsd:documentation>Customer MSId.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:MinType"/>
	</xsd:simpleType>
	<xsd:simpleType name="NameType">
		<xsd:annotation>
			<xsd:documentation>General name field.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NumberOfMonthsType">
		<xsd:annotation>
			<xsd:documentation>The term in months, generally used to refer the contract term of a subscriber.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:positiveInteger"/>
	</xsd:simpleType>
	<xsd:simpleType name="OperatorIdType">
		<xsd:annotation>
			<xsd:documentation>Operator Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="PhoneNumberType">
		<xsd:annotation>
			<xsd:documentation>General phone number.  The number could be international and may have &apos;+ - ( )&apos;, e.g.  +1(800)555-1212.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PostalCodeType">
		<xsd:annotation>
			<xsd:documentation>Postal code for the shipping address.
                        The postal code must consist of 5 to 9 digits.
                        If the billing country is the U.S., the 9-digit postal code must follow this format:
                          [5 digits]  or  [5 digits][dash][4 digits] ( optional dash )
                            Example: 12345-6789 or 123456789 
                        If the billing country is Canada, the 6-digit postal code must follow this format:
                          [alpha][numeric][alpha][space][numeric][alpha][numeric]
                            Example: A1B 2C3</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="10"/>
			<xsd:pattern value="((\d{5}-?\d{4})|(\d{5})|([A-CEGHJ-NPR-TVXZ]\d[A-CEGHJ-NPR-TV-Z]\s\d[A-CEGHJ-NPR-TV-Z]\d))"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RequestTransactionIdType">
		<xsd:annotation>
			<xsd:documentation>Request ( incomming ) transaction id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:TransactionIdType"/>
	</xsd:simpleType>
	<xsd:simpleType name="ResponseTransactionIdType">
		<xsd:annotation>
			<xsd:documentation>Response ( outgoing ) transaction id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:TransactionIdType"/>
	</xsd:simpleType>
	<xsd:simpleType name="ResponseCodeType">
		<xsd:annotation>
			<xsd:documentation>General response code.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ResponseDescriptionType">
		<xsd:annotation>
			<xsd:documentation>Description of the response code. There may be error specific info in the string.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RoutingNumberType">
		<xsd:annotation>
			<xsd:documentation>Routing number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SimType">
		<xsd:annotation>
			<xsd:documentation>Subscriber identity module of the device.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{20}|\d{22}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SocType">
		<xsd:annotation>
			<xsd:documentation>SOC Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="SubscriberIdType">
		<xsd:annotation>
			<xsd:documentation>Subscriber Id</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
	</xsd:simpleType>
	<xsd:simpleType name="SSNType">
		<xsd:annotation>
			<xsd:documentation>9 digit SSN type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{9}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SSNLastFourType">
		<xsd:annotation>
			<xsd:documentation>SSN type for last 4 digits..</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{4}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TransactionIdType">
		<xsd:annotation>
			<xsd:documentation>Transaction Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="UICCIDType">
		<xsd:annotation>
			<xsd:documentation>Each SIM is internationally identified by its integrated circuit card identifier (ICCID). ICCIDs are stored in the SIM cards and are also engraved or printed on the SIM card body during a process called personalization. The ICCID is defined by the ITU-T recommendation E.118 as the Primary Account Number. Its layout is based on ISO/IEC 7812. According to E.118, the number is up to 19 digits long, including a single check digit calculated using the Luhn algorithm. However, the GSM Phase 1 defined the ICCID length as 10 octets with operator-specific structure. On GSM SIM cards there are 20-digit (19+1) and 19-digit (18+1) ICCIDs in use, depending upon the issuer. However, a single issuer always uses the same size for its ICCIDs.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
			<xsd:pattern value="\d{19,20}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ZipCodeType">
		<xsd:annotation>
			<xsd:documentation>Specific 5 or 9 digit US postal zip code, no dash allowed.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="9"/>
			<xsd:pattern value="[0-9]{5}([0-9]{4})?"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Structures       -->
	<xsd:complexType name="AddressType">
		<xsd:annotation>
			<xsd:documentation>US postal address.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="addressType" type="enterprise_common_xsd:AddressTypeType">
				<xsd:annotation>
					<xsd:documentation>Address Type.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="firstName" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>First name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="lastName" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Last Name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="addressLine1" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Street address or PO Box.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="addressLine2" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Additional street address line 2.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="city" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>City - Required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="state" type="enterprise_common_xsd:StateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Two letter US state code.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="zipcode" type="enterprise_common_xsd:ZipCodeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>5 or 9 digit zip code - 5 digit US zip code required at a minimum.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DeviceAttributeType">
		<xsd:annotation>
			<xsd:documentation>Structure holding device attribute information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="componentID" type="enterprise_common_xsd:CompIdType">
				<xsd:annotation>
					<xsd:documentation>Attribute's component ID.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="defaultValue" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Attribute's default value.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DeviceAttributesType">
		<xsd:annotation>
			<xsd:documentation>Structure holding device attributes.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="deviceAttribute" type="enterprise_common_xsd:DeviceAttributeType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Device attribute information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DeviceInfoType">
		<xsd:annotation>
			<xsd:documentation>Structure holding name the device information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="type" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The type of the device.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="esn" type="enterprise_common_xsd:EsnType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Equipment Serial Number or MEID of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="sim" type="enterprise_common_xsd:SimType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Subscriber identity module card number of the device.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ErrorInformationType">
		<xsd:annotation>
			<xsd:documentation>Structure to hode general error information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="errorCode" type="enterprise_common_xsd:ErrorCodeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The error code assigned to the error.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="errorDescription" type="enterprise_common_xsd:ErrorDescriptionType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The error description indicating the type and severity, and any corrective action if known.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ResponseType">
		<xsd:annotation>
			<xsd:documentation>Structure to hold response information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="responseCode" type="enterprise_common_xsd:ResponseCodeType">
				<xsd:annotation>
					<xsd:documentation>The response code. 
This value will be USC0000000 to indicate a successful response.
Based on the different error scenarios this value will be different. Refer to the specific service integration guide for more details.
</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="responseDescription" type="enterprise_common_xsd:ResponseDescriptionType">
				<xsd:annotation>
					<xsd:documentation>Description of the response code. There may be error specific info in the string.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentInfoType">
		<xsd:annotation>
			<xsd:documentation>Container holding Payment details.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="paymentMethod" type="enterprise_common_xsd:PaymentMethodType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Payment method.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="depositAmount" type="enterprise_common_xsd:AmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Deposit amount.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="depositAccountNumber" type="enterprise_common_xsd:AccountNumberType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Deposit account number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PersonType">
		<xsd:annotation>
			<xsd:documentation>Container holding Person Details.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="customerId" type="enterprise_common_xsd:CustomerIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Customer id of the subscriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="status" type="enterprise_common_xsd:SubscriptionStatusType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Subscriber status.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="birthDate" type="xsd:date" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Date of birth.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="birthPlace" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Location of birth.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="deathDate" type="xsd:date" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Date of death.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="familyName" type="enterprise_common_xsd:LastNameType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Family or last name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="formattedName" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Preformatted concatenation of other components of the name. If empty, first, middle and last names are concatenated.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="gender" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Gender. See Gender. choice list. This object contains reference id of the gender CRM choice list.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="firstName" type="enterprise_common_xsd:FirstNameType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Give or first name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="id" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Unique identifier that may be optionally assigned by the service consumer on create, or otherwise allocated by the system using a numbering scheme.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="middleName" type="enterprise_common_xsd:NameType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Middle name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="nameChangeDate" type="xsd:date" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Date of the last name change.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="salutation" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Formal salutation, for example Mr., Mrs., Ms. See Salutation choice list This object contains reference id of the salutation CRM choice list.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="pin" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The pin code used for authenticating the contact person.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="faId" type="enterprise_common_xsd:FaIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Financial Account Id of the subscriber's account.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- Enumerations     -->
	<xsd:simpleType name="AddressTypeType">
		<xsd:annotation>
			<xsd:documentation>The type of Address.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Billing">
				<xsd:annotation>
					<xsd:documentation>The billing address of the account.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Primary">
				<xsd:annotation>
					<xsd:documentation>The physical premise address of the account.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CountryType">
		<xsd:annotation>
			<xsd:documentation>Country information of the address.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="2"/>
			<xsd:enumeration value="US">
				<xsd:annotation>
					<xsd:documentation>United States of America</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>Canada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PaymentMethodType">
		<xsd:annotation>
			<xsd:documentation>Payment method.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Bank">
				<xsd:annotation>
					<xsd:documentation>Bank payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Cash">
				<xsd:annotation>
					<xsd:documentation>Cash payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Check">
				<xsd:annotation>
					<xsd:documentation>Check payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CreditCard">
				<xsd:annotation>
					<xsd:documentation>Credit card payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SubscriptionStatusType">
		<xsd:annotation>
			<xsd:documentation>Subscription Status.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ACTIVE">
				<xsd:annotation>
					<xsd:documentation>Subscriber status is active.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CANCELLED">
				<xsd:annotation>
					<xsd:documentation>Subscriber status is canceled.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SUSPENDED">
				<xsd:annotation>
					<xsd:documentation>Subscriber status is suspended.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="StateType">
		<xsd:annotation>
			<xsd:documentation>State information of the address.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="2"/>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Alabama</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MT">
				<xsd:annotation>
					<xsd:documentation>Montana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AK">
				<xsd:annotation>
					<xsd:documentation>Alaska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Nebraska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Arizona </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NV">
				<xsd:annotation>
					<xsd:documentation>Nevada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Arkansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>New Hampshire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>California </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NJ">
				<xsd:annotation>
					<xsd:documentation>New Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>Colorado </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NM">
				<xsd:annotation>
					<xsd:documentation>New Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Connecticut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NY">
				<xsd:annotation>
					<xsd:documentation>New York</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Delaware</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>North Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Florida</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>North Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OH">
				<xsd:annotation>
					<xsd:documentation>Ohio</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HI">
				<xsd:annotation>
					<xsd:documentation>Hawaii</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OK">
				<xsd:annotation>
					<xsd:documentation>Oklahoma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Idaho</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OR">
				<xsd:annotation>
					<xsd:documentation>Oregon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IL">
				<xsd:annotation>
					<xsd:documentation>Illinois</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Pennsylvania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IN">
				<xsd:annotation>
					<xsd:documentation>Indiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RI">
				<xsd:annotation>
					<xsd:documentation>Rhode Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IA">
				<xsd:annotation>
					<xsd:documentation>Iowa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>South Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KS">
				<xsd:annotation>
					<xsd:documentation>Kansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SD">
				<xsd:annotation>
					<xsd:documentation>South Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KY">
				<xsd:annotation>
					<xsd:documentation>Kentucky</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Tennessee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LA">
				<xsd:annotation>
					<xsd:documentation>Louisiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TX">
				<xsd:annotation>
					<xsd:documentation>Texas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ME">
				<xsd:annotation>
					<xsd:documentation>Maine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UT">
				<xsd:annotation>
					<xsd:documentation>Utah</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Maryland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Vermont</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Massachusetts</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VA">
				<xsd:annotation>
					<xsd:documentation>Virginia </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MI">
				<xsd:annotation>
					<xsd:documentation>Michigan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WA">
				<xsd:annotation>
					<xsd:documentation>Washington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Minnesota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WV">
				<xsd:annotation>
					<xsd:documentation>West Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Mississippi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WI">
				<xsd:annotation>
					<xsd:documentation>Wisconsin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Missouri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WY">
				<xsd:annotation>
					<xsd:documentation>Wyoming</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>District of Columbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Americas (except Canada)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Canada/Europe/Middle East/Africa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Pacific</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>American Somoa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Federated States of Micronesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GU">
				<xsd:annotation>
					<xsd:documentation>Guam</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MH">
				<xsd:annotation>
					<xsd:documentation>Marshall Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MP">
				<xsd:annotation>
					<xsd:documentation>Northern Mariana Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Puerto Rico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PW">
				<xsd:annotation>
					<xsd:documentation>Palau (Balau)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>U.S. Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Alberta</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>British Columbia </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MB">
				<xsd:annotation>
					<xsd:documentation>Manitoba</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NB">
				<xsd:annotation>
					<xsd:documentation>New Brunswick</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NL">
				<xsd:annotation>
					<xsd:documentation>Newfoundland and Labrador</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NT">
				<xsd:annotation>
					<xsd:documentation>Northwest Territories</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NS">
				<xsd:annotation>
					<xsd:documentation>Nova Scotia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NU">
				<xsd:annotation>
					<xsd:documentation>Nunavut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ON">
				<xsd:annotation>
					<xsd:documentation>Ontario</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PE">
				<xsd:annotation>
					<xsd:documentation>Prince Edward Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QC">
				<xsd:annotation>
					<xsd:documentation>Quebec</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SK">
				<xsd:annotation>
					<xsd:documentation>Saskatchewan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="YT">
				<xsd:annotation>
					<xsd:documentation>Yukon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="KeyValuePairType">
		<xsd:annotation>
			<xsd:documentation>Defines KeyValuePair structure.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="key" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Contains attribute name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="value" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Contains attribute value of the respective attribute name. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- Exceptions       -->
	<xsd:element name="ServiceException" type="enterprise_common_xsd:ServiceExceptionType">
		<xsd:annotation>
			<xsd:documentation>Base exception definition used in ESB enterprise services.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="ServiceExceptionType">
		<xsd:annotation>
			<xsd:documentation>Base exception definition used in ESB enterprise services.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="errorInformation" type="enterprise_common_xsd:ErrorInformationType">
				<xsd:annotation>
					<xsd:documentation>Error code and description.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="faultDetails" type="xsd:anyType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Fault details represent soap fault details received by OSB.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	
	
	
	
</xsd:schema>
