<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/HSS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/HSS/">
<xs:include schemaLocation="../IMS_Core/types/hssla_types.xsd"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="impi" type="impiType"/>
<xs:element name="CreateAVGMultiSC">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="impi" type="impiType"/>
</xs:choice>
<xs:element name="avgEncryptedK" type="avgEncryptedKType"/>
<xs:element name="avgA4KeyInd" type="avgA4KeyIndType"/>
<xs:element name="avgFSetInd" type="avgFSetIndType"/>
<xs:element minOccurs="0" name="avgAmf" type="avgAmfType"/>
<xs:element minOccurs="0" name="avgEncryptedOPc" type="avgEncryptedOPcType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="impi" type="impiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="impiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
<xs:key name="key_impi">
<xs:selector xpath="./x:impi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_impi" refer="key_impi">
<xs:selector xpath="."/>
<xs:field xpath="@impi"/>
</xs:keyref>
</xs:element>
<xs:element name="DeleteAVGMultiSC">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
<xs:element name="SetAVGMultiSC">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="avgEncryptedK" type="avgEncryptedKType"/>
<xs:element minOccurs="0" name="avgA4KeyInd" type="avgA4KeyIndType"/>
<xs:element minOccurs="0" name="avgAmf" type="avgAmfType"/>
<xs:element minOccurs="0" name="avgEncryptedOPc" type="avgEncryptedOPcType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="impi" type="impiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="impiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetResponseAVGMultiSC">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="impi" type="impiType"/>
</xs:choice>
<xs:element name="avgEncryptedK" type="avgEncryptedKType"/>
<xs:element name="avgA4KeyInd" type="avgA4KeyIndType"/>
<xs:element name="avgFSetInd" type="avgFSetIndType"/>
<xs:element name="avgAmf" type="avgAmfType"/>
<xs:element minOccurs="0" name="avgEncryptedOPc" type="avgEncryptedOPcType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="impi" type="impiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="impiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
