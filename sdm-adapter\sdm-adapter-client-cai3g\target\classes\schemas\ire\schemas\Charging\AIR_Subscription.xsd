<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:include schemaLocation="air_common_types.xsd"/>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="createSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="serviceClassNew" type="serviceClassNewType"/>
<xs:element default="0" minOccurs="0" name="temporaryBlockedFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="languageIDNew" type="languageIDNewType"/>
<xs:element minOccurs="0" name="ussdEndOfCallNotificationID" type="ussdEndOfCallNotificationIDType"/>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="serviceOfferingsValue" type="serviceOfferingsValueType"/>
</xs:choice>
<xs:group minOccurs="0" ref="promotionDef"/>
<xs:element minOccurs="0" name="accountHomeRegion" type="accountHomeRegionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData">
<xs:complexType>
<xs:sequence>
<xs:element name="pamServiceID" type="pamServiceIDType"/>
<xs:element name="pamClassID" type="pamClassIDType"/>
<xs:element name="scheduleID" type="scheduleIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
<xs:attribute name="pamServiceID" type="pamServiceIDType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pamServiceIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pamServiceID">
<xs:selector xpath="."/>
<xs:field xpath="@pamServiceID"/>
</xs:key>
<xs:keyref name="keyref_create_pamServiceID" refer="key_create_pamServiceID">
<xs:selector xpath="./air:pamServiceID"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="masterAccountNumber" type="masterAccountNumberType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerUpdateInformation" type="offerUpdateInformationType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key id="CAI3GKeyCreateMsisdn" name="CreateSubscriberNumberKey">
<xs:selector xpath="./air:subscriberNumber"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="CreateSubscriberNumberKeyRef" refer="CreateSubscriberNumberKey">
<xs:selector xpath="."/>
<xs:field xpath="@subscriberNumber"/>
</xs:keyref>
</xs:element>
<xs:element name="getSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="accountDetails">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element minOccurs="0" name="requestedInformationFlags" type="requestedInformationFlagsType"/>
<xs:element minOccurs="0" name="requestPamInformationFlag" type="requestPamInformationFlagType"/>
<xs:element minOccurs="0" name="requestActiveOffersFlag" type="requestActiveOffersFlagType"/>
<xs:element minOccurs="0" name="requestAttributesFlag" type="requestAttributesFlagType"/>
<xs:element minOccurs="0" name="requestTreeParameterSetsFlag" type="requestTreeParameterSetsFlagType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="allowedServiceClasses">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="balanceAndDate">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountSelection" type="dedicatedAccountSelectionType"/>
<xs:element minOccurs="0" name="chargingRequestInformation" type="chargingRequestInformationType"/>
<xs:element minOccurs="0" name="requestSubDedicatedAccountDetailsFlag" type="requestSubDedicatedAccountDetailsFlagType"/>
<xs:element minOccurs="0" name="requestFirstAccessibleAndExpiredBalanceAndDateFlag" type="requestFirstAccessibleAndExpiredBalanceAndDateFlagType"/>
<xs:element minOccurs="0" name="requestActiveOffersFlag" type="requestActiveOffersFlagType"/>
<xs:element minOccurs="0" name="requestAttributesFlag" type="requestAttributesFlagType"/>
<xs:element minOccurs="0" name="requestTreeParameterSetsFlag" type="requestTreeParameterSetsFlagType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="fafList">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="1" name="requestedOwner" type="requestedOwnerType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="promotionCounters">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="promotionPlans">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="refillOptions">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element name="voucherActivationCode" type="voucherActivationCodeType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="usageThresholdsAndCounters">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="accountDetails">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimitAction" type="accountPrepaidEmptyLimitActionType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element minOccurs="0" name="languageIDNew" type="languageIDNewType"/>
<xs:element minOccurs="0" name="firstIVRCallDoneFlag" type="firstIVRCallDoneFlagType"/>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
<xs:element minOccurs="0" name="accountHomeRegion" type="accountHomeRegionType"/>
<xs:element minOccurs="0" name="ussdEndOfCallNotificationID" type="ussdEndOfCallNotificationIDType"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit" type="accountPrepaidEmptyLimitType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
<xs:element minOccurs="0" name="pamClassIDCurrent" type="pamClassIDOldType"/>
<xs:element minOccurs="0" name="scheduleIDCurrent" type="scheduleIDOldType"/>
<xs:element minOccurs="0" name="pamServicePriorityCurrent" type="pamServicePriorityOldType"/>
</xs:sequence>
<xs:attribute name="pamServiceID" type="pamServiceIDType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pamServiceIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_pamServiceID">
<xs:selector xpath="."/>
<xs:field xpath="@pamServiceID"/>
</xs:key>
<xs:keyref name="keyref_pamServiceID" refer="key_pamServiceID">
<xs:selector xpath="./air:pamServiceID"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="serviceOfferingsValue" type="serviceOfferingsValuesType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="promotionPlan" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionPlanIDCurrent" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionOldStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionOldEndDate" type="promotionEndDateType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
</xs:sequence>
<xs:attribute name="promotionPlanID" type="promotionPlanIDType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="promotionPlanIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_promotionPlanID">
<xs:selector xpath="."/>
<xs:field xpath="@promotionPlanID"/>
</xs:key>
<xs:keyref name="keyref_create_promotionPlanID" refer="key_create_promotionPlanID">
<xs:selector xpath="./air:promotionPlanID"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element default="0" minOccurs="0" name="temporaryBlockedFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="serviceClass">
<xs:complexType>
<xs:sequence>
<xs:element name="serviceClassAction" type="serviceClassActionType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="serviceClassNew" type="serviceClassNewType"/>
<xs:element minOccurs="0" name="serviceClassTemporary" nillable="true" type="serviceClassTemporaryType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryNew" type="serviceClassTemporaryType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryNewExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element default="0" minOccurs="0" name="serviceClassValidationFlag" type="serviceClassValidationFlagType"/>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
<xs:element minOccurs="0" name="chargingRequestInformation" type="chargingRequestInformationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="faf" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="fafNumber" type="fafNumberType"/>
<xs:element minOccurs="0" name="owner" type="ownerType"/>
<xs:element minOccurs="0" name="fafIndicator" type="fafIndicatorType"/>
<xs:element default="0" minOccurs="0" name="exactMatch" type="xs:boolean"/>
<xs:group minOccurs="0" ref="Option"/>
<xs:element minOccurs="0" name="enableFafMNPFlag" type="xs:boolean"/>
</xs:sequence>
<xs:attribute name="fafNumber" type="fafNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="fafNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="owner" type="ownerType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="ownerAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_fafNumber">
<xs:selector xpath="."/>
<xs:field xpath="@fafNumber"/>
</xs:key>
<xs:keyref name="keyref_create_fafNumber" refer="key_create_fafNumber">
<xs:selector xpath="./air:fafNumber"/>
<xs:field xpath="."/>
</xs:keyref>
<xs:key name="key_create_owner">
<xs:selector xpath="."/>
<xs:field xpath="@owner"/>
</xs:key>
<xs:keyref name="keyref_create_owner" refer="key_create_owner">
<xs:selector xpath="./air:owner"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="communityInformationCurrent" type="communityIDType"/>
<xs:choice>
<xs:element minOccurs="0" name="communityInformationNew" type="communityIDType"/>
<xs:element minOccurs="0" name="communityInformationUpdate" type="communityInformationUpdateType"/>
</xs:choice>
<xs:element minOccurs="0" name="balanceAndDate">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:choice>
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationForSubscriptionType"/>
<xs:element minOccurs="0" name="allowCropOfCompositeDedicatedAccounts" type="allowCropOfCompositeDedicatedAccountsType"/>
<xs:choice>
<xs:element minOccurs="0" name="supervisionExpiryDateRelative" type="supervisionExpiryDateRelativeType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
</xs:choice>
<xs:choice>
<xs:element minOccurs="0" name="serviceFeeExpiryDateRelative" type="serviceFeeExpiryDateRelativeType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
</xs:choice>
<xs:element minOccurs="0" name="creditClearancePeriod" type="creditClearancePeriodType"/>
<xs:element minOccurs="0" name="serviceRemovalPeriod" type="serviceRemovalPeriodType"/>
<xs:element minOccurs="0" name="transactionType" type="transactionTypeType"/>
<xs:element minOccurs="0" name="transactionCode" type="transactionCodeType"/>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDateCurrent" type="serviceFeeExpiryDateCurrentType"/>
<xs:element minOccurs="0" name="supervisionExpiryDateCurrent" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="cellIdentifier" type="cellIdentifierType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="deleteDedicatedAccounts">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element maxOccurs="unbounded" name="dedicatedAccountIdentifier" type="dedicatedAccountIdentifierType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="promotionCounters">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="promotionRefillAmountRelative" type="promotionRefillAmountRelativeType"/>
<xs:element minOccurs="0" name="promotionRefillCounterStepRelative" type="promotionRefillCounterStepRelativeType"/>
<xs:element minOccurs="0" name="progressionRefillAmountRelative" type="progressionRefillAmountRelativeType"/>
<xs:element minOccurs="0" name="progressionRefillCounterStepRelative" type="progressionRefillCounterStepRelativeType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="refillBarring">
<xs:complexType>
<xs:sequence>
<xs:element name="refillBarAction" type="refillBarActionType"/>
<xs:element minOccurs="0" name="refillUnbarDateTime" type="refillUnbarDateTimeType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccount">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element maxOccurs="unbounded" name="subDedicatedAccountUpdateInformation" type="subDedicatedAccountUpdateInformationType"/>
<xs:element minOccurs="0" name="transactionType" type="transactionTypeType"/>
<xs:element minOccurs="0" name="transactionCode" type="transactionCodeType"/>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="usageThresholdsAndCounters">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element fixed="1" minOccurs="0" name="updateUsageCounterForMultiUser" type="xs:integer"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUpdateInformation" type="usageCounterUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdUpdateInformation" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="usageThresholdID" type="usageThresholdIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="usageThresholdValueNew" type="usageThresholdValueNewType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValueNew" type="usageThresholdMonetaryValueNewType"/>
</xs:choice>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
<xs:attribute name="usageThresholdID" type="usageThresholdIDType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="usageThresholdIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_usageThresholdID">
<xs:selector xpath="."/>
<xs:field xpath="@usageThresholdID"/>
</xs:key>
<xs:keyref name="keyref_create_usageThresholdID" refer="key_create_usageThresholdID">
<xs:selector xpath="./air:usageThresholdID"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="deleteSubscription">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="deleteReasonCode" type="deleteReasonCodeType"/>
<xs:element default="0" minOccurs="0" name="barring" type="xs:boolean"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="createSubscriptionResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData" type="pamInformationInInstallSubscriberResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationTypeForCreateSubscriptionResp"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListTypeForCreateSubscriberResp"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="getSubscriptionResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="accountDetails">
<xs:complexType>
<xs:sequence>
<xs:element fixed="1" minOccurs="0" name="firstIVRCallFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="ussdEndOfCallNotificationID" type="ussdEndOfCallNotificationIDType"/>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="communityInformationCurrent" type="communityIDType"/>
<xs:element default="0" minOccurs="0" name="temporaryBlockedFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="accountActivatedFlag" type="accountActivatedFlagType"/>
<xs:element minOccurs="0" name="activationDate" type="activationDateType"/>
<xs:element minOccurs="0" name="accountFlags" type="accountFlagsType"/>
<xs:element fixed="1" minOccurs="0" name="masterSubscriberFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="masterAccountNumber" type="masterAccountNumberType"/>
<xs:element minOccurs="0" name="refillUnbarDateTime" type="refillUnbarDateTimeType"/>
<xs:element minOccurs="0" name="promotionAnnouncementCode" type="promotionAnnouncementCodeType"/>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="creditClearanceDate" type="creditClearanceDateType"/>
<xs:element minOccurs="0" name="serviceRemovalDate" type="serviceRemovalDateType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassChangeUnbarDate" type="serviceClassChangeUnbarDateType"/>
<xs:element minOccurs="0" name="serviceFeePeriod" type="serviceFeePeriodType"/>
<xs:element minOccurs="0" name="supervisionPeriod" type="supervisionPeriodType"/>
<xs:element minOccurs="0" name="serviceRemovalPeriod" type="serviceRemovalPeriodType"/>
<xs:element minOccurs="0" name="creditClearancePeriod" type="creditClearancePeriodType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
<xs:element minOccurs="0" name="accountHomeRegion" type="accountHomeRegionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData" type="pamInformationInGetAccountDetailsResponseType"/>
<xs:element minOccurs="0" name="maxServiceFeePeriod" type="maxServiceFeePeriodType"/>
<xs:element minOccurs="0" name="maxSupervisionPeriod" type="maxSupervisionPeriodType"/>
<xs:element minOccurs="0" name="negativeBalanceBarringDate" type="negativeBalanceBarringDateType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsBeforeType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationListType"/>
<xs:element minOccurs="0" name="cellIdentifier" type="cellIdentifierType"/>
<xs:element minOccurs="0" name="locationNumber" type="locationNumberType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit1" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit2" type="accountPrepaidEmptyLimitType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="allowedServiceClasses">
<xs:complexType>
<xs:sequence>
<xs:element name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceClass" type="serviceClassType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="promotionCounters">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue1" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue2" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillCounter" type="promotionRefillCounterType"/>
<xs:element minOccurs="0" name="progressionRefillValue1" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillValue2" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillCounter" type="progressionRefillCounterType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="balanceAndDate">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:group minOccurs="0" ref="account1Def"/>
<xs:group minOccurs="0" ref="account2Def"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
<xs:element minOccurs="0" name="creditClearanceDate" type="creditClearanceDateType"/>
<xs:element minOccurs="0" name="serviceRemovalDate" type="serviceRemovalDateType"/>
<xs:element name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="temporaryBlockedFlag" type="temporaryBlockedFlagType"/>
<xs:element minOccurs="0" name="chargingResultInformation" type="chargingResultInformationType"/>
<xs:element minOccurs="0" name="accountFlagsAfter" type="accountFlagsBeforeType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsBeforeType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationListType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit1" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit2" type="accountPrepaidEmptyLimitType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="fafList">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="fafInformation" type="fafInformationType"/>
<xs:element minOccurs="0" name="fafChangeUnbarDate" type="fafChangeUnbarDateType"/>
<xs:element minOccurs="0" name="fafMaxAllowedNumbersReachedFlag" type="fafMaxAllowedNumbersReachedFlagType"/>
<xs:element minOccurs="0" name="fafChargingNotAllowedFlag" type="fafChargingNotAllowedFlagType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="promotionPlans">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="promotionPlanInformation" type="promotionPlanInformationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="refillOptions">
<xs:complexType>
<xs:sequence>
<xs:element name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element name="refillOptions" type="refillOptionsType"/>
<xs:element minOccurs="0" name="accountFlagsAfter" type="accountFlagsType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="usageThresholdsAndCounters">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="setSubscriptionResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="accountFlagsAfter" type="accountFlagsType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit1" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit2" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="chargingResultInformation" type="chargingResultInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData" type="pamInformationInUpdatePeriodicAccountManagementDataResponseType"/>
<xs:element maxOccurs="8" minOccurs="0" name="allowedOptions" type="allowedOptionsType"/>
<xs:element default="0" minOccurs="0" name="fafMaxAllowedNumbersReachedFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="fafChangeUnbarDate" type="fafChangeUnbarDateType"/>
<xs:element minOccurs="0" name="negativeBalanceBarringDate" type="negativeBalanceBarringDateType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue1" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue2" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillCounter" type="promotionRefillCounterType"/>
<xs:element minOccurs="0" name="progressionRefillValue1" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillValue2" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillCounter" type="progressionRefillCounterType"/>
<xs:element minOccurs="0" name="refillUnbarDateTime" type="refillUnbarDateTimeType"/>
<xs:element minOccurs="0" name="refillFraudCount" type="refillFraudCountType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountDeleteInformation" type="dedicatedAccountDeleteInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="deleteSubscriptionResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="balanceCleared1" type="balanceClearedType"/>
<xs:element minOccurs="0" name="balanceCleared2" type="balanceClearedType"/>
<xs:element minOccurs="0" name="communityInformationCurrent" type="communityInformationCurrentType"/>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="ussdEndOfCallNotificationID" type="ussdEndOfCallNotificationIDType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="creditClearancePeriod" type="creditClearancePeriodType"/>
<xs:element minOccurs="0" name="serviceRemovalPeriod" type="serviceRemovalPeriodType"/>
<xs:element minOccurs="0" name="refillUnbarDateTime" type="refillUnbarDateTimeType"/>
<xs:element minOccurs="0" name="accountFlags" type="accountFlagsType"/>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="activationDate" type="activationDateType"/>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
<xs:element minOccurs="0" name="promotionRefillCounter" type="promotionRefillCounterType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue1" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue2" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="progressionRefillValue1" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillValue2" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillCounter" type="progressionRefillCounterType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountDeleteInformation" type="dedicatedAccountDeleteInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationListType"/>
<xs:element minOccurs="0" name="accountHomeRegion" type="accountHomeRegionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="periodicAccountManagementData" type="pamInformationInDeleteSubscriberResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterDeleteInformation" type="usageCounterDeleteInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:complexType name="dedicatedAccountUpdateInformationForSubscriptionType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="dedicatedAccountValueNew" type="dedicatedAccountValueType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentDateRelative" type="adjustmentDateRelativeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType"/>
<xs:element minOccurs="0" name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
</xs:choice>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="messageCapabilityFlagType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="promotionNotificationFlag" type="promotionNotificationFlagType"/>
<xs:element minOccurs="0" name="firstIVRCallSetFlag" type="firstIVRCallSetFlagType"/>
<xs:element default="0" minOccurs="0" name="accountActivationFlag" type="accountActivationFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterUsageThresholdInformationType">
<xs:sequence>
<xs:element name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterNominalValue" type="usageCounterNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryNominalValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterUsageThresholdInfoTypeForSubscriptionResp">
<xs:sequence>
<xs:element name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterNominalValue" type="usageCounterNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryNominalValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="usageCounterResourceConnected" type="usageCounterResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageThresholdInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageThresholdID" type="usageThresholdIDType"/>
<xs:element minOccurs="0" name="usageThresholdValue" type="usageThresholdValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdSource" type="usageThresholdSourceType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountChangeInformation" type="subDedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="changedAmount1" type="changedAmountType"/>
<xs:element minOccurs="0" name="changedAmount2" type="changedAmountType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="changedExpiryDate" type="changedExpiryDateType"/>
<xs:element minOccurs="0" name="newExpiryDate" type="newExpiryDateType"/>
<xs:element minOccurs="0" name="clearedExpiryDate" type="clearedExpiryDateType"/>
<xs:element minOccurs="0" name="changedStartDate" type="changedStartDateType"/>
<xs:element minOccurs="0" name="newStartDate" type="newStartDateType"/>
<xs:element minOccurs="0" name="clearedStartDate" type="clearedStartDateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValue1Type"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValue2Type"/>
<xs:element minOccurs="0" name="dedicatedReservation1" type="dedicatedReservationType"/>
<xs:element minOccurs="0" name="dedicatedReservation2" type="dedicatedReservationType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValue1Type"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValue2Type"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="compositeDedicatedAccountFlag" type="compositeDedicatedAccountFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountInformationTypeForSubscriptionResp">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValue1Type"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValue2Type"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValue1Type"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValue2Type"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="compositeDedicatedAccountFlag" type="compositeDedicatedAccountFlagType"/>
<xs:element minOccurs="0" name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationInUpdatePeriodicAccountManagementDataResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="lastEvaluationDate" type="xs:dateTime"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="chargingRequestInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="chargingType" type="chargingTypeType"/>
<xs:element minOccurs="0" name="chargingIndicator" type="chargingIndicatorType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="requestedInformationFlagsType">
<xs:sequence>
<xs:element minOccurs="0" name="requestMasterAccountBalanceFlag" type="requestMasterAccountBalanceFlagType"/>
<xs:element minOccurs="0" name="allowedServiceClassChangeDateFlag" type="allowedServiceClassChangeDateFlagType"/>
<xs:element minOccurs="0" name="requestLocationInformationFlag" type="requestLocationInformationFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceOfferingsType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceOfferingID" type="serviceOfferingIDType"/>
<xs:element default="0" name="serviceOfferingActiveFlag" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationInInstallSubscriberRequestType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationInInstallSubscriberResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="lastEvaluationDate" type="xs:dateTime"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationInGetAccountDetailsResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="lastEvaluationDate" type="xs:dateTime"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationInDeleteSubscriberResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="lastEvaluationDate" type="xs:dateTime"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="chargingResultInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="chargingResultCode" type="chargingResultCodeType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
<xs:element minOccurs="0" name="chargingResultInformationService" type="chargingResultInformationServiceType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="chargingResultInformationServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="accountFlagsType">
<xs:sequence>
<xs:element default="0" minOccurs="0" name="activationStatusFlag" type="xs:boolean"/>
<xs:element default="0" minOccurs="0" name="negativeBarringStatusFlag" type="xs:boolean"/>
<xs:element default="0" minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="xs:boolean"/>
<xs:element default="0" minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="xs:boolean"/>
<xs:element default="0" minOccurs="0" name="supervisionPeriodExpiryFlag" type="xs:boolean"/>
<xs:element default="0" minOccurs="0" name="serviceFeePeriodExpiryFlag" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountDeleteInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationListType">
<xs:sequence>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDateTime" type="startDateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="expiryDateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationTypeForCreateSubscriptionResp">
<xs:sequence>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDateTime" type="startDateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="expiryDateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInfoTypeForSubscriptionResp"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationTypeForSubscriptionResp"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountInformationType">
<xs:sequence>
<xs:group minOccurs="0" ref="dedicatedAccountValue"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterDeleteInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="accountFlagsBeforeType">
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="pamInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDType"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceOfferingsValuesType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceOfferingsValueAdd" type="serviceOfferingsValueType"/>
<xs:element minOccurs="0" name="serviceOfferingsValueDelete" type="serviceOfferingsValueType"/>
<xs:element minOccurs="0" name="serviceOfferingsValueCurrent" type="serviceOfferingsValueType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="communityInformationUpdateType">
<xs:sequence>
<xs:element minOccurs="0" name="communityInformationAdd" type="communityIDType"/>
<xs:element minOccurs="0" name="communityInformationDelete" type="communityIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterUpdateInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageCounterID" type="usageCounterIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="usageCounterValueNew" type="usageCounterValueNewType"/>
<xs:element minOccurs="0" name="adjustmentUsageCounterValueRelative" type="adjustmentUsageCounterValueRelativeType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValueNew" type="usageCounterMonetaryValueNewType"/>
<xs:element minOccurs="0" name="adjustmentUsageCounterMonetaryValueRelative" type="adjustmentUsageCounterMonetaryValueRelativeType"/>
</xs:choice>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountUpdateInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="subDedicatedAccountIdentifier" type="subDedicatedAccountIdentifierType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="subDedicatedAccountValueAbsolute" type="subDedicatedAccountValueAbsoluteType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentDateRelative" type="adjustmentDateRelativeType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType"/>
<xs:element minOccurs="0" name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
</xs:choice>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="expiryDateCurrentType"/>
<xs:element minOccurs="0" name="startDateCurrent" type="startDateCurrentType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountIdentifierType">
<xs:sequence>
<xs:element minOccurs="0" name="startDateCurrent" type="startDateCurrentType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="expiryDateCurrentType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountIdentifierType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountSelectionType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountIDFirst" type="dedicatedAccountIDFirstType"/>
<xs:element minOccurs="0" name="dedicatedAccountIDLast" type="dedicatedAccountIDLastType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="promotionPlanInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceClassListType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceClass" type="serviceClassType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="fafInformationListType">
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="fafInformation" type="fafInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="fafInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="fafNumber" type="fafNumberType"/>
<xs:element minOccurs="0" name="owner" type="ownerType"/>
<xs:element minOccurs="0" name="fafIndicator" type="fafIndicatorType"/>
<xs:element default="0" minOccurs="0" name="exactMatch" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:group name="promotionDef">
<xs:sequence>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
</xs:sequence>
</xs:group>
<xs:group name="serviceClassType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
</xs:sequence>
</xs:group>
<xs:group name="pamServicePrioritygroup">
<xs:sequence>
<xs:element minOccurs="0" name="pamServicePriorityOld" type="pamServicePriorityOldType"/>
<xs:element minOccurs="0" name="pamServicePriorityNew" type="pamServicePriorityNewType"/>
</xs:sequence>
</xs:group>
<xs:group name="serviceClass">
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
</xs:sequence>
</xs:group>
<xs:group name="account1Def">
<xs:sequence>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
</xs:sequence>
</xs:group>
<xs:group name="usageThreshold">
<xs:sequence>
<xs:choice>
<xs:element minOccurs="0" name="usageThresholdValue" type="usageThresholdValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType"/>
</xs:choice>
</xs:sequence>
</xs:group>
<xs:group name="account2Def">
<xs:sequence>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
</xs:sequence>
</xs:group>
<xs:group name="dedicatedAccountValue">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
</xs:sequence>
</xs:group>
<xs:group name="Option">
<xs:sequence>
<xs:element minOccurs="0" name="selectedOption" type="selectedOptionType"/>
<xs:element minOccurs="0" name="chargingRequestInformation" type="chargingRequestInformationType"/>
</xs:sequence>
</xs:group>
<xs:simpleType name="msisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTimeStampType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="subscriberNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
<xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="languageIDNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ussdEndOfCallNotificationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountGroupIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceOfferingIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="31"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionPlanIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="4"/>
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionEndDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="accountHomeRegionType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduleIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pamClassIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pamClassIDOldType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pamClassIDNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduleIDOldType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduleIDNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currentPamPeriodType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9\-/]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="deferredToDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="pamServicePriorityOldType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pamServicePriorityNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountTimeZoneType">
<xs:restriction base="xs:string">
<xs:pattern value=".{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCurrencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountPrepaidEmptyLimitType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-************"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="responseCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="masterAccountNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="deleteReasonCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="balanceClearedType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-************"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="communityInformationCurrentType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="serviceClassCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="languageIDCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="supervisionExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="creditClearancePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1023"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceRemovalPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1023"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillUnbarDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceClassOriginalType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassTemporaryExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="activationDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionRefillCounterType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionRefillAccumulatedValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillCounterType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="requestPamInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="promotionNotificationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="firstIVRCallSetFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accountActivationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestMasterAccountBalanceFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="allowedServiceClassChangeDateFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestLocationInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestActiveOffersFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestAttributesFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="communityIDType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="promotionAnnouncementCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="creditClearanceDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceRemovalDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceClassChangeUnbarDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9 ]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="maxServiceFeePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="maxSupervisionPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negativeBalanceBarringDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="cellIdentifierType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{0,19}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="locationNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{0,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="activationStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="negativeBarringStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="startDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="expiryDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="offerStateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerProviderIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterNominalValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryNominalValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountRealMoneyFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="closestExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestExpiryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestAccessibleDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestAccessibleValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedAmountType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountValue1Type">
<xs:restriction base="xs:long"/>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountValue2Type">
<xs:restriction base="xs:long"/>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValue1Type">
<xs:restriction base="xs:long"/>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValue2Type">
<xs:restriction base="xs:long"/>
</xs:simpleType>
<xs:simpleType name="changedExpiryDateType">
<xs:restriction base="xs:int">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="clearedExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="changedStartDateType">
<xs:restriction base="xs:int">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="clearedStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="accountPrepaidEmptyLimitActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="SET"/>
<xs:enumeration value="DELETE"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="firstIVRCallDoneFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="externalDataType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeExpiryDateCurrentType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="pamServicePriorityType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionPlanActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="ADD"/>
<xs:enumeration value="SET"/>
<xs:enumeration value="DELETE"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="Set"/>
<xs:enumeration value="SetOriginal"/>
<xs:enumeration value="SetTemporary"/>
<xs:enumeration value="DeleteTemporary"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassTemporaryType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassValidationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="compositeDedicatedAccountFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="chargingTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="reservationCorrelationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="ADD"/>
<xs:enumeration value="SET"/>
<xs:enumeration value="DELETE"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ownerType">
<xs:restriction base="xs:string">
<xs:enumeration value="Subscriber"/>
<xs:enumeration value="Account"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="selectedOptionType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="costType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingResultCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountActivatedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="allowedOptionsType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafChangeUnbarDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceOfferingsValueType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="allowCropOfCompositeDedicatedAccountsType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionExpiryDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeExpiryDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionTypeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentUsageCounterValueRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentUsageCounterMonetaryValueRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-************"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionRefillAmountRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-************"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionRefillCounterStepRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-255"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillAmountRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-************"/>
<xs:maxInclusive value="************"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillCounterStepRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-255"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillBarActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="CLEAR"/>
<xs:enumeration value="STEP"/>
<xs:enumeration value="BAR"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="startDateCurrentType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="expiryDateCurrentType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="subDedicatedAccountValueAbsoluteType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillFraudCountType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountIDFirstType">
<xs:restriction base="xs:long">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountIDLastType">
<xs:restriction base="xs:long">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="requestSubDedicatedAccountDetailsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestFirstAccessibleAndExpiredBalanceAndDateFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestedOwnerType">
<xs:restriction base="xs:integer">
<xs:enumeration value="1"/>
<xs:enumeration value="2"/>
<xs:enumeration value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="voucherActivationCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{8,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="temporaryBlockedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceClassType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafMaxAllowedNumbersReachedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="fafChargingNotAllowedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestTreeParameterSetsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="refillOptionsType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="offerUpdateInformationType">
<xs:sequence>
<xs:choice>
<xs:element name="offerID" type="offerIDType"/>
<xs:element name="bundleID" type="bundleIDType"/>
</xs:choice>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="dateInformation" type="dateInformationType"/>
<xs:element minOccurs="0" name="dateTimeInformation" type="dateTimeInformationType"/>
</xs:choice>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationTypeForSubscription"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeUpdateInformation" type="attributeUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetUpdateInformation" type="treeParameterSetUpdateInformationType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountUpdateInformationTypeForSubscription">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="dedicatedAccountPamPeriodInformation" type="dedicatedAccountPamPeriodInformationTypeForSubscription"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountPamPeriodInformationTypeForSubscription">
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="dedicatedAccountValueNew" type="dedicatedAccountValueType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentDateRelative" type="adjustmentDateRelativeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType"/>
<xs:element minOccurs="0" name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
</xs:choice>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="localProviderTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mainAccountValueNewType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="backdatedToDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="backdatedToStartOfBillCycleInstanceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-1"/>
<xs:maxInclusive value="0"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="requestAggregatedProductOfferInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="pinCodeOriginalType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,8}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pinCodeValidationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="pinCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,8}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="notAllowedReasonType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="10"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="aggregatedBalanceType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountReservationType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="aggregatedReservationType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="serviceOfferingsResultType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceOfferingID" type="serviceOfferingIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceFeeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceFeeID" type="serviceFeeIDType"/>
<xs:element minOccurs="0" name="serviceFeeAmount1" type="AmountType"/>
<xs:element minOccurs="0" name="serviceFeeAmount2" type="AmountType"/>
<xs:element minOccurs="0" name="serviceFeeChargedAmount1" type="AmountType"/>
<xs:element minOccurs="0" name="serviceFeeChargedAmount2" type="AmountType"/>
<xs:element minOccurs="0" name="serviceFeeDeductionDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="serviceFeeDeductionPeriod" type="serviceFeeDeductionPeriodType"/>
<xs:element minOccurs="0" name="serviceFeePeriodUnit" type="serviceFeePeriodUnitType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="bundleInformationListTypeForCreateSubscriberResp">
<xs:sequence>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:element maxOccurs="unbounded" name="offerInformation" type="offerInformationTypeForCreateSubscriptionResp"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="bundleInformationListType">
<xs:sequence>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:element maxOccurs="unbounded" name="offerInformation" type="offerInformationListType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="serviceFeeIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="AmountType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeDeductionPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodUnitType">
<xs:restriction base="xs:string">
<xs:enumeration value="Days"/>
<xs:enumeration value="Months"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedReservationType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="destinationSDPIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="targetSubscriberIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="28"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="manageSubscriberActionType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originSDPIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="destinationErrorCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
