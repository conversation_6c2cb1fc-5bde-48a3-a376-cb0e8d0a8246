package com.nokia.wing.wdh.sdmadapter.client.neo.security.ssl;

import com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.Enumeration;

/**
 * SSL/TLS Configuration Utility for NEO Client
 *
 * Provides SSL configuration based on feature flags.
 * Follows patterns from reference implementations.
 * Includes certificate validation and enhanced error handling.
 */
@Slf4j
public class NeoSslConfigurationUtil {
    
    private final NeoClientProperties properties;
    private final ResourceLoader resourceLoader;
    
    public NeoSslConfigurationUtil(NeoClientProperties properties, ResourceLoader resourceLoader) {
        this.properties = properties;
        this.resourceLoader = resourceLoader;
    }

    /**
     * Validate SSL configuration and certificates on startup
     */
    @PostConstruct
    public void validateSslConfiguration() {
        log.debug("Starting SSL configuration validation...");

        if (properties == null) {
            log.error("NeoClientProperties is null - cannot validate SSL configuration");
            return;
        }

        log.debug("NeoClientProperties loaded successfully");

        NeoClientProperties.Ssl sslConfig = properties.getSsl();
        if (sslConfig == null) {
            log.warn("SSL configuration is null - using default values");
            return;
        }

        log.debug("SSL configuration loaded successfully");

        // Check for testing-only certificate validation bypass
        if (Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation())) {
            log.warn("SSL CERTIFICATE VALIDATION DISABLED - TESTING ONLY!");
            log.warn("This configuration creates a security vulnerability and should NEVER be used in production!");
            log.warn("Certificate validation is bypassed - all SSL certificates will be trusted!");
        }

        if (Boolean.TRUE.equals(sslConfig.getValidateCertificates()) && isSslEnabled() && !Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation())) {
            log.info("Validating SSL certificates on startup...");

            try {
                if (Boolean.TRUE.equals(sslConfig.getMtlsEnabled()) && sslConfig.getKeystorePath() != null && sslConfig.getKeystorePassword() != null) {
                    validateCertificate(sslConfig.getKeystorePath(), sslConfig.getKeystorePassword(), "Client Keystore");
                }

                if ((Boolean.TRUE.equals(sslConfig.getMtlsEnabled()) || Boolean.TRUE.equals(sslConfig.getHttpsEnabled()))
                    && sslConfig.getTruststorePath() != null && sslConfig.getTruststorePassword() != null) {
                    validateCertificate(sslConfig.getTruststorePath(), sslConfig.getTruststorePassword(), "Server Truststore");
                }

                log.info("SSL certificate validation completed successfully");
            } catch (Exception e) {
                log.error("SSL certificate validation failed: {}", e.getMessage());
                if (log.isDebugEnabled()) {
                    log.debug("SSL certificate validation error details", e);
                }
            }
        } else if (Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation())) {
            log.info("SSL certificate validation skipped - certificate validation disabled for testing");
        }

        // Enable SSL debugging if configured
        if (Boolean.TRUE.equals(sslConfig.getEnableSslDebug())) {
            System.setProperty("javax.net.debug", "ssl,handshake");
            log.info("SSL debugging enabled");
        }
    }

    /**
     * Create HttpClient with appropriate SSL configuration based on feature flags
     */
    public HttpClient createHttpClient() throws Exception {
        if (properties == null) {
            log.warn("NeoClientProperties is null - creating basic HTTP client");
            return createHttpHttpClient();
        }

        NeoClientProperties.Ssl sslConfig = properties.getSsl();
        if (sslConfig == null) {
            log.warn("SSL configuration is null - creating basic HTTP client");
            return createHttpHttpClient();
        }

        if (Boolean.TRUE.equals(sslConfig.getMtlsEnabled())) {
            return createMtlsHttpClient();
        } else if (Boolean.TRUE.equals(sslConfig.getHttpsEnabled())) {
            return createHttpsHttpClient();
        } else {
            return createHttpHttpClient();
        }
    }

    
    /**
     * Create HTTP client for mTLS (client certificate + server trust)
     */
    private HttpClient createMtlsHttpClient() throws Exception {
        NeoClientProperties.Ssl sslConfig = properties.getSsl();

        SSLContext sslContext;

        if (Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation())) {
            // TESTING ONLY: Create SSL context that bypasses all certificate validation
            log.warn("SSL CERTIFICATE VALIDATION DISABLED - TESTING ONLY!");
            log.warn("This configuration creates a security vulnerability and should NEVER be used in production!");

            // Load client keystore for authentication (still required for mTLS)
            KeyStore keyStore = loadKeyStore(sslConfig.getKeystorePath(), sslConfig.getKeystorePassword());

            // Use SSLContextBuilder with a trust strategy that accepts all certificates
            sslContext = SSLContextBuilder.create()
                    .loadKeyMaterial(keyStore, sslConfig.getKeyPassword() != null ? sslConfig.getKeyPassword().toCharArray() : sslConfig.getKeystorePassword().toCharArray())
                    .loadTrustMaterial(null, (X509Certificate[] chain, String authType) -> true) // Trust all certificates
                    .build();
        } else {
            // Normal certificate validation
            KeyStore keyStore = loadKeyStore(sslConfig.getKeystorePath(), sslConfig.getKeystorePassword());
            KeyStore trustStore = loadTrustStore(sslConfig.getTruststorePath(), sslConfig.getTruststorePassword());

            sslContext = SSLContextBuilder.create()
                    .loadKeyMaterial(keyStore, sslConfig.getKeyPassword() != null ? sslConfig.getKeyPassword().toCharArray() : sslConfig.getKeystorePassword().toCharArray())
                    .loadTrustMaterial(trustStore, new TrustSelfSignedStrategy())
                    .build();
        }

        return buildHttpClient(sslContext, sslConfig);
    }
    
    /**
     * Create HTTP client for HTTPS only (server trust, no client certificate)
     */
    private HttpClient createHttpsHttpClient() throws Exception {
        NeoClientProperties.Ssl sslConfig = properties.getSsl();

        SSLContext sslContext;

        if (Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation())) {
            // TESTING ONLY: Create SSL context that bypasses all certificate validation
            log.warn("SSL CERTIFICATE VALIDATION DISABLED - TESTING ONLY!");
            log.warn("This configuration creates a security vulnerability and should NEVER be used in production!");

            // Use SSLContextBuilder with a trust strategy that accepts all certificates
            sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, (X509Certificate[] chain, String authType) -> true) // Trust all certificates
                    .build();
        } else {
            // Normal certificate validation
            KeyStore trustStore = loadTrustStore(sslConfig.getTruststorePath(), sslConfig.getTruststorePassword());

            sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(trustStore, new TrustSelfSignedStrategy())
                    .build();
        }

        return buildHttpClient(sslContext, sslConfig);
    }
    
    /**
     * Create HTTP client for HTTP (no SSL)
     */
    private HttpClient createHttpHttpClient() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(properties.getConnectionTimeout())
                .setSocketTimeout(properties.getReadTimeout())
                .setConnectionRequestTimeout(properties.getConnectionTimeout())
                .build();
        
        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .addInterceptorFirst(new ContentLengthHeaderRemover())  // Fix duplicate Content-Length header issue
                .build();
    }

    
    /**
     * Build HttpClient with SSL context and common configuration
     */
    private HttpClient buildHttpClient(SSLContext sslContext, NeoClientProperties.Ssl sslConfig) {
        SSLConnectionSocketFactory sslSocketFactory;

        // If certificate validation is disabled, also disable hostname verification
        if (Boolean.TRUE.equals(sslConfig.getDisableCertificateValidation()) || Boolean.TRUE.equals(sslConfig.getDisableHostnameVerification())) {
            sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER
            );
        } else {
            sslSocketFactory = new SSLConnectionSocketFactory(sslContext);
        }
        
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(properties.getConnectionTimeout())
                .setSocketTimeout(properties.getReadTimeout())
                .setConnectionRequestTimeout(properties.getConnectionTimeout())
                .build();
        
        return HttpClientBuilder.create()
                .setSSLSocketFactory(sslSocketFactory)
                .setDefaultRequestConfig(requestConfig)
                .addInterceptorFirst(new ContentLengthHeaderRemover())  // Fix duplicate Content-Length header issue
                .build();
    }
    
    /**
     * Load keystore from configured path with enhanced error handling
     */
    private KeyStore loadKeyStore(String keystorePath, String password) throws Exception {
        log.debug("Loading client keystore from: {}", keystorePath);

        try {
            KeyStore keyStore = KeyStore.getInstance("JKS");

            // Use resource loading for classpath resources
            if (keystorePath.startsWith("classpath:")) {
                Resource resource = resourceLoader.getResource(keystorePath);
                try (InputStream is = resource.getInputStream()) {
                    keyStore.load(is, password.toCharArray());
                    log.info("Client keystore loaded successfully from classpath: {}", keystorePath);
                }
            } else {
                // Use file system path
                try (FileInputStream fis = new FileInputStream(resolveResourcePath(keystorePath))) {
                    keyStore.load(fis, password.toCharArray());
                    log.info("Client keystore loaded successfully from file: {}", keystorePath);
                }
            }

            return keyStore;

        } catch (Exception e) {
            log.error("Failed to load client keystore from {}: {}", keystorePath, e.getMessage());
            throw new RuntimeException("Client keystore loading failed", e);
        }
    }

    /**
     * Load truststore from configured path with enhanced error handling
     */
    private KeyStore loadTrustStore(String truststorePath, String password) throws Exception {
        log.debug("Loading server truststore from: {}", truststorePath);

        try {
            KeyStore trustStore = KeyStore.getInstance("JKS");

            // Use resource loading for classpath resources
            if (truststorePath.startsWith("classpath:")) {
                Resource resource = resourceLoader.getResource(truststorePath);
                try (InputStream is = resource.getInputStream()) {
                    trustStore.load(is, password.toCharArray());
                    log.info("Server truststore loaded successfully from classpath: {}", truststorePath);
                }
            } else {
                // Use file system path
                try (FileInputStream fis = new FileInputStream(resolveResourcePath(truststorePath))) {
                    trustStore.load(fis, password.toCharArray());
                    log.info("Server truststore loaded successfully from file: {}", truststorePath);
                }
            }

            return trustStore;

        } catch (Exception e) {
            log.error("Failed to load server truststore from {}: {}", truststorePath, e.getMessage());
            throw new RuntimeException("Server truststore loading failed", e);
        }
    }

    /**
     * Validate certificate loading and content
     */
    private void validateCertificate(String certificatePath, String password, String certificateType) throws Exception {
        log.debug("Validating {}: {}", certificateType, certificatePath);

        try {
            KeyStore keyStore = KeyStore.getInstance("JKS");

            // Load the certificate
            if (certificatePath.startsWith("classpath:")) {
                Resource resource = resourceLoader.getResource(certificatePath);
                try (InputStream is = resource.getInputStream()) {
                    keyStore.load(is, password.toCharArray());
                }
            } else {
                try (FileInputStream fis = new FileInputStream(resolveResourcePath(certificatePath))) {
                    keyStore.load(fis, password.toCharArray());
                }
            }

            // Validate certificate content
            Enumeration<String> aliases = keyStore.aliases();
            int certificateCount = 0;

            while (aliases.hasMoreElements()) {
                String alias = aliases.nextElement();
                Certificate cert = keyStore.getCertificate(alias);
                if (cert != null) {
                    certificateCount++;
                    log.debug("Found certificate with alias '{}' in {}", alias, certificateType);
                }
            }

            if (certificateCount == 0) {
                throw new RuntimeException("No certificates found in " + certificateType);
            }

            log.info("{} validation successful - found {} certificate(s)", certificateType, certificateCount);

        } catch (Exception e) {
            log.error("Failed to validate {}: {}", certificateType, e.getMessage());
            throw e;
        }
    }


    /**
     * Resolve resource path to actual file path
     */
    private String resolveResourcePath(String resourcePath) throws Exception {
        if (resourcePath.startsWith("classpath:") || resourcePath.startsWith("file:")) {
            Resource resource = resourceLoader.getResource(resourcePath);
            return resource.getFile().getAbsolutePath();
        }
        return resourcePath;
    }

    /**
     * Check if SSL is enabled (either HTTPS or mTLS)
     */
    public boolean isSslEnabled() {
        if (properties == null) {
            return false;
        }
        NeoClientProperties.Ssl sslConfig = properties.getSsl();
        if (sslConfig == null) {
            return false;
        }
        return Boolean.TRUE.equals(sslConfig.getHttpsEnabled()) || Boolean.TRUE.equals(sslConfig.getMtlsEnabled());
    }

    /**
     * Get the appropriate protocol for URLs (http or https)
     */
    public String getProtocol() {
        return isSslEnabled() ? "https" : "http";
    }

    /**
     * HTTP Request Interceptor to remove duplicate Content-Length headers
     * This fixes the "Content-Length header already present" error
     */
    private static class ContentLengthHeaderRemover implements HttpRequestInterceptor {
        @Override
        public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
            request.removeHeaders(HTTP.CONTENT_LEN);
        }
    }
}
