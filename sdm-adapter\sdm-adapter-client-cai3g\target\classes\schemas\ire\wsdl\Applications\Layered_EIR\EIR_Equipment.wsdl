<!-- <PERSON><PERSON>, Equipment- PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB9 -->
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:pg="http://schemas.ericsson.com/pg/1.0" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:eir="http://schemas.ericsson.com/ma/EIR/" xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
			<xs:import namespace="http://schemas.ericsson.com/ma/EIR/" schemaLocation="../../../schemas/Applications/Layered_EIR/EIR_Equipment.xsd"/>
			<xs:import namespace="http://schemas.ericsson.com/pg/1.0" schemaLocation="../../../schemas/Applications/Layered_EIR/PGFault.xsd"/>
			<xs:element name="Create">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Equipment@http://schemas.ericsson.com/ma/EIR/"/>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:imei"/>
									<xs:element ref="eir:svn" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes" minOccurs="1">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:CreateEquipment"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreateResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:imei"/>
									<xs:element ref="eir:svn" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Delete">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Equipment@http://schemas.ericsson.com/ma/EIR/"/>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:imei"/>
									<xs:element ref="eir:svn" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:DeleteEquipment"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeleteResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:imei"/>
									<xs:element ref="eir:svn" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Set">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Equipment@http://schemas.ericsson.com/ma/EIR/"/>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:sequence>
										<xs:element ref="eir:imei"/>
										<xs:element ref="eir:svn" minOccurs="0"/>
									</xs:sequence>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes" minOccurs="1">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:SetEquipment"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SetResponse">
				<xs:complexType>
					<xs:sequence>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Get">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="Equipment@http://schemas.ericsson.com/ma/EIR/"/>
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:imei"/>
									<xs:element ref="eir:svn" minOccurs="0"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOAttributes" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="eir:GetResponseEquipment"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Cai3gFault">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="faultcode" type="xs:integer"/>
						<xs:element name="faultreason">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="reasonText" type="xs:string" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="faultrole" type="xs:string"/>
						<xs:element name="details" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="pg:PGFault"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SessionId" type="SessionIdType"/>
			<xs:element name="TransactionId" type="xs:unsignedLong"/>
			<xs:element name="SequenceId" type="xs:unsignedLong"/>
			<xs:simpleType name="SessionIdType">
				<xs:restriction base="xs:string">
					<xs:pattern value="[\d\w]{1,}"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:complexType name="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultactor" type="xs:string"/>
					<xs:element name="description" type="xs:string"/>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="SessionIdFault">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="HeaderFaultType">
							<xs:sequence>
								<xs:element name="faultcode">
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Invalid SessionId"/>
											<xs:enumeration value="Session Timeout"/>
											<xs:enumeration value="SessionId Syntax Error"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="SequenceIdFault">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="HeaderFaultType">
							<xs:sequence>
								<xs:element name="faultcode">
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Invalid SequenceId"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="TransactionIdFault">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="HeaderFaultType">
							<xs:sequence>
								<xs:element name="faultcode">
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Invalid TransactionId"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:schema>
	</types>
	<message name="CreateRequest">
		<part name="parameters" element="cai3g:Create"/>
	</message>
	<message name="CreateResponse">
		<part name="parameters" element="cai3g:CreateResponse"/>
	</message>
	<message name="GetRequest">
		<part name="parameters" element="cai3g:Get"/>
	</message>
	<message name="GetResponse">
		<part name="parameters" element="cai3g:GetResponse"/>
	</message>
	<message name="SetRequest">
		<part name="parameters" element="cai3g:Set"/>
	</message>
	<message name="SetResponse">
		<part name="parameters" element="cai3g:SetResponse"/>
	</message>
	<message name="DeleteRequest">
		<part name="parameters" element="cai3g:Delete"/>
	</message>
	<message name="DeleteResponse">
		<part name="parameters" element="cai3g:DeleteResponse"/>
	</message>
	<message name="HeadInfo">
		<part name="sessionId" element="cai3g:SessionId"/>
		<part name="transactionId" element="cai3g:TransactionId"/>
		<part name="sequenceId" element="cai3g:SequenceId"/>
	</message>
	<message name="Cai3gFault">
		<part name="parameters" element="cai3g:Cai3gFault"/>
	</message>
	<message name="Cai3gHeaderFault">
		<part name="sessionIdFault" element="cai3g:SessionIdFault"/>
		<part name="transactionIdFault" element="cai3g:TransactionIdFault"/>
		<part name="sequenceIdFault" element="cai3g:SequenceIdFault"/>
	</message>
	<portType name="Equipment">
		<operation name="Create">
			<input message="cai3g:CreateRequest"/>
			<output message="cai3g:CreateResponse"/>
			<fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
		</operation>
		<operation name="Delete">
			<input message="cai3g:DeleteRequest"/>
			<output message="cai3g:DeleteResponse"/>
			<fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
		</operation>
		<operation name="Get">
			<input message="cai3g:GetRequest"/>
			<output message="cai3g:GetResponse"/>
			<fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
		</operation>
		<operation name="Set">
			<input message="cai3g:SetRequest"/>
			<output message="cai3g:SetResponse"/>
			<fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
		</operation>
	</portType>
	<binding name="Equipment" type="cai3g:Equipment">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<operation name="Create">
			<soap:operation soapAction="CAI3G#Create" style="document"/>
			<input>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal"/>
			</fault>
		</operation>
		<operation name="Delete">
			<soap:operation soapAction="CAI3G#Delete" style="document"/>
			<input>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal"/>
			</fault>
		</operation>
		<operation name="Get">
			<soap:operation soapAction="CAI3G#Get" style="document"/>
			<input>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal"/>
			</fault>
		</operation>
		<operation name="Set">
			<soap:operation soapAction="CAI3G#Set" style="document"/>
			<input>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal"/>
			</fault>
		</operation>
	</binding>
	<service name="Provisioning">
		<port name="Equipment" binding="cai3g:Equipment">
			<soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
		</port>
	</service>
</definitions>
