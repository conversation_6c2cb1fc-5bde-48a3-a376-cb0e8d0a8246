<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/HSS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/HSS/">
<xs:include schemaLocation="../IMS_Core/types/hssla_types.xsd"/>
<xs:include schemaLocation="../IMS_Core/IMSFault.xsd"/>
<xs:element name="associationId" type="associationIdType"/>
<xs:element name="CreateIMSAssociation">
<xs:complexType>
<xs:sequence>
<xs:element name="associationId" type="associationIdType"/>
<xs:element minOccurs="0" name="chargingProfId" type="chargingProfIdType"/>
<xs:element minOccurs="0" name="chargingId" type="e164Type"/>
<xs:element minOccurs="0" name="isPsi" type="isPsiType"/>
<xs:element minOccurs="0" name="privacyIndicator" type="privacyIndicatorType"/>
<xs:element minOccurs="0" name="defaultPrivateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="defaultRemoteReferenceAccessLocation" type="defaultRemoteReferenceAccessLocationType"/>
<xs:element minOccurs="0" name="asHostingPSI" type="asHostingPSIType"/>
<xs:element minOccurs="0" name="esrNumber" type="esrNumberType"/>
<xs:element minOccurs="0" name="tenantId" type="tenantIdType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="privateUser">
<xs:complexType>
<xs:sequence>
<xs:element name="privateUserId" type="privateUserIdType"/>
<xs:choice minOccurs="0">
<xs:element name="userPassword" type="userPasswordType"/>
<xs:sequence>
<xs:element name="userPrimaryHA1Password" type="userPrimaryHA1PasswordType"/>
<xs:element minOccurs="0" name="userSecondaryHA1Password" type="userSecondaryHA1PasswordType"/>
</xs:sequence>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="secondPrivateUserId">
<xs:complexType>
<xs:sequence>
<xs:element name="secondPrivateUserId" type="secondPrivateUserIdType"/>
</xs:sequence>
<xs:attribute name="secondPrivateUserId" type="secondPrivateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="secondPrivateUserIdAttribute"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_secondPrivateUserId">
<xs:selector xpath="."/>
<xs:field xpath="@secondPrivateUserId"/>
</xs:key>
<xs:keyref name="keyref_create_secondPrivateUserId" refer="key_create_secondPrivateUserId">
<xs:selector xpath="./x:secondPrivateUserId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="allowedAuthMechanism" type="allowedAuthMechanismType"/>
<xs:element minOccurs="0" name="userBarringInd" type="userBarringIndType"/>
<xs:element minOccurs="0" name="roamingAllowed" type="roamingAllowedType"/>
<xs:element minOccurs="0" name="referenceAccessLocation" type="referenceAccessLocationType"/>
<xs:element minOccurs="0" name="userImsi" type="imsiType"/>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ssoMsisdn">
<xs:complexType>
<xs:sequence>
<xs:element name="ssoMsisdn" type="msisdnType"/>
</xs:sequence>
<xs:attribute name="ssoMsisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="ssoMsisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_ssoMsisdn">
<xs:selector xpath="."/>
<xs:field xpath="@ssoMsisdn"/>
</xs:key>
<xs:keyref name="keyref_create_ssoMsisdn" refer="key_create_ssoMsisdn">
<xs:selector xpath="./x:ssoMsisdn"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accessIdentifier">
<xs:complexType>
<xs:sequence>
<xs:element name="accessIdentifier" type="accessIdentifierType"/>
</xs:sequence>
<xs:attribute name="accessIdentifier" type="accessIdentifierType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="ssoMsisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_accessIdentifier">
<xs:selector xpath="."/>
<xs:field xpath="@accessIdentifier"/>
</xs:key>
<xs:keyref name="keyref_create_accessIdentifier" refer="key_create_accessIdentifier">
<xs:selector xpath="./x:accessIdentifier"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="userAccessLineIdentifier">
<xs:complexType>
<xs:sequence>
<xs:element name="lineName" type="lineNameType"/>
<xs:element name="value" type="valueType"/>
<xs:element minOccurs="0" name="authMechanism" type="authMechanismType"/>
</xs:sequence>
<xs:attribute name="lineName" type="lineNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="lineNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_lineName">
<xs:selector xpath="."/>
<xs:field xpath="@lineName"/>
</xs:key>
<xs:keyref name="keyref_create_lineName" refer="key_create_lineName">
<xs:selector xpath="./x:lineName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="privateUserId" type="privateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="privateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_privateUserId">
<xs:selector xpath="."/>
<xs:field xpath="@privateUserId"/>
</xs:key>
<xs:keyref name="keyref_create_privateUserId" refer="key_create_privateUserId">
<xs:selector xpath="./x:privateUserId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="publicData">
<xs:complexType>
<xs:sequence>
<xs:element name="publicIdValue" type="publicIdValueType"/>
<xs:element minOccurs="0" name="privateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="xcapAllowed" type="xcapAllowedType"/>
<xs:element minOccurs="0" name="xcapPassword" type="xcapPasswordType"/>
<xs:element name="implicitRegSet" type="implicitRegSetType"/>
<xs:element minOccurs="0" name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element minOccurs="0" name="wirelineAccessAllowed" type="wirelineAccessAllowedType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="authorizedVisitedAccessLine" type="authorizedVisitedAccessLineType"/>
<xs:element minOccurs="0" name="sessionBarringInd" type="sessionBarringIndType"/>
<xs:element minOccurs="0" name="maxNumberOfContacts" type="maxNumberOfContactsType"/>
<xs:element minOccurs="0" name="priorityLevel" type="priorityLevelType"/>
<xs:element minOccurs="0" name="isWildcardExtended" type="isWildcardExtendedType"/>
<xs:element minOccurs="0" name="aliasGroupId" type="aliasGroupIdType"/>
<xs:element minOccurs="0" name="displayName" type="displayNameType"/>
</xs:sequence>
<xs:attribute name="publicIdValue" type="publicIdValueType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="publicIdValueAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_publicIdValue">
<xs:selector xpath="."/>
<xs:field xpath="@publicIdValue"/>
</xs:key>
<xs:keyref name="keyref_create_publicIdValue" refer="key_create_publicIdValue">
<xs:selector xpath="./x:publicIdValue"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subscriberServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="configuredServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="configuredServiceProfileId" type="configuredServiceProfileIdType"/>
</xs:sequence>
<xs:attribute name="configuredServiceProfileId" type="configuredServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="configuredServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_configuredServiceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@configuredServiceProfileId"/>
</xs:key>
<xs:keyref name="keyref_create_configuredServiceProfileId" refer="key_create_configuredServiceProfileId">
<xs:selector xpath="./x:configuredServiceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="subscribedMediaProfile" type="subscribedMediaProfileType"/>
<xs:element minOccurs="0" name="maxNumberSessions" type="maxNumberSessionsType"/>
<xs:element minOccurs="0" name="phoneContext" type="phoneContextType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="individualServiceProfileId" type="individualServiceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualCapability" type="individualCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualOptionalCapability" type="individualOptionalCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualTrigger">
<xs:complexType>
<xs:sequence>
<xs:element name="triggerDescription" type="triggerDescriptionType"/>
<xs:element name="triggerPriority" type="triggerPriorityType"/>
<xs:element minOccurs="0" name="isActive" type="isActiveType"/>
<xs:element minOccurs="0" name="triggerType" type="triggerTypeType"/>
<xs:element minOccurs="0" name="detectionPoint" type="detectionPointType"/>
<xs:element minOccurs="0" name="negatedDetectionPoint" type="negatedDetectionPointType"/>
<xs:element minOccurs="0" name="conditionType" type="conditionTypeType"/>
<xs:element minOccurs="0" name="requestedURI" type="requestedURIType"/>
<xs:element minOccurs="0" name="negatedRequestedURI" type="negatedRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" type="sipHeaderType"/>
<xs:element name="applicationServer" type="applicationServerType"/>
<xs:element maxOccurs="2" minOccurs="0" name="registrationType" type="registrationTypeType"/>
<xs:element minOccurs="0" name="defaultHandling" type="defaultHandlingType"/>
<xs:element minOccurs="0" name="includeRegisterRequest" type="includeRegisterRequestType"/>
<xs:element minOccurs="0" name="includeRegisterResponse" type="includeRegisterResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="triggerCriteriaGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="groupId" type="groupIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionDescription" type="sessionDescriptionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionCase" type="sessionCaseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipMethod" type="sipMethodType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="groupRequestedURI" type="groupRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" type="sipHeaderType"/>
</xs:sequence>
<xs:attribute name="groupId" type="groupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="groupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_groupId">
<xs:selector xpath="."/>
<xs:field xpath="@groupId"/>
</xs:key>
<xs:keyref name="keyref_create_groupId" refer="key_create_groupId">
<xs:selector xpath="./x:groupId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="triggerDescription" type="triggerDescriptionType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="triggerDescriptionAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_triggerDescription">
<xs:selector xpath="."/>
<xs:field xpath="@triggerDescription"/>
</xs:key>
<xs:keyref name="keyref_create_triggerDescription" refer="key_create_triggerDescription">
<xs:selector xpath="./x:triggerDescription"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="individualServiceProfileId" type="individualServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="individualServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_individualServiceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@individualServiceProfileId"/>
</xs:key>
<xs:keyref name="keyref_create_individualServiceProfileId" refer="key_create_individualServiceProfileId">
<xs:selector xpath="./x:individualServiceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="serviceProfileId" type="serviceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="serviceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_serviceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@serviceProfileId"/>
</xs:key>
<xs:keyref name="keyref_create_serviceProfileId" refer="key_create_serviceProfileId">
<xs:selector xpath="./x:serviceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="associationId" type="associationIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="associationIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_associationId">
<xs:selector xpath="."/>
<xs:field xpath="@associationId"/>
</xs:key>
<xs:keyref name="keyref_create_associationId" refer="key_create_associationId">
<xs:selector xpath="./x:associationId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element name="DeleteIMSAssociation">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
<xs:element name="SetIMSAssociation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="chargingProfId" type="chargingProfIdType"/>
<xs:element minOccurs="0" name="chargingId" type="e164Type"/>
<xs:element minOccurs="0" name="isPsi" type="isPsiType"/>
<xs:element minOccurs="0" name="privacyIndicator" type="privacyIndicatorType"/>
<xs:element minOccurs="0" name="defaultPrivateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="defaultRemoteReferenceAccessLocation" nillable="true" type="defaultRemoteReferenceAccessLocationType"/>
<xs:element minOccurs="0" name="asHostingPSI" type="asHostingPSIType"/>
<xs:element minOccurs="0" name="esrNumber" type="esrNumberType"/>
<xs:element minOccurs="0" name="tenantId" nillable="true" type="tenantIdType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="privateUser" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="privateUserId" type="privateUserIdType"/>
<xs:choice minOccurs="0">
<xs:element name="userPassword" type="userPasswordType"/>
<xs:sequence>
<xs:element name="userPrimaryHA1Password" type="userPrimaryHA1PasswordType"/>
<xs:element minOccurs="0" name="userSecondaryHA1Password" type="userSecondaryHA1PasswordType"/>
</xs:sequence>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="secondPrivateUserId" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="secondPrivateUserId" type="secondPrivateUserIdType"/>
</xs:sequence>
<xs:attribute name="secondPrivateUserId" type="secondPrivateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="secondPrivateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_secondPrivateUserId">
<xs:selector xpath="."/>
<xs:field xpath="@secondPrivateUserId"/>
</xs:key>
<xs:keyref name="keyref_set_secondPrivateUserId" refer="key_set_secondPrivateUserId">
<xs:selector xpath="./x:secondPrivateUserId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="allowedAuthMechanism" type="allowedAuthMechanismType"/>
<xs:element minOccurs="0" name="userBarringInd" type="userBarringIndType"/>
<xs:element minOccurs="0" name="roamingAllowed" type="roamingAllowedType"/>
<xs:element minOccurs="0" name="referenceAccessLocation" nillable="true" type="referenceAccessLocationType"/>
<xs:element minOccurs="0" name="userImsi" type="imsiType"/>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ssoMsisdn" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="ssoMsisdn" type="msisdnType"/>
</xs:sequence>
<xs:attribute name="ssoMsisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="ssoMsisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_ssoMsisdn">
<xs:selector xpath="."/>
<xs:field xpath="@ssoMsisdn"/>
</xs:key>
<xs:keyref name="keyref_set_ssoMsisdn" refer="key_set_ssoMsisdn">
<xs:selector xpath="./x:ssoMsisdn"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="numOfFailedSipAuthAttempts" type="numOfFailedSipAuthAttemptsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accessIdentifier" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="accessIdentifier" type="accessIdentifierType"/>
</xs:sequence>
<xs:attribute name="accessIdentifier" type="accessIdentifierType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="accessIdentifierAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_accessIdentifier">
<xs:selector xpath="."/>
<xs:field xpath="@accessIdentifier"/>
</xs:key>
<xs:keyref name="keyref_set_accessIdentifier" refer="key_set_accessIdentifier">
<xs:selector xpath="./x:accessIdentifier"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="userAccessLineIdentifier" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="lineName" type="lineNameType"/>
<xs:element minOccurs="0" name="value" type="valueType"/>
<xs:element minOccurs="0" name="authMechanism" type="authMechanismType"/>
</xs:sequence>
<xs:attribute name="lineName" type="lineNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="lineNameAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_lineName">
<xs:selector xpath="."/>
<xs:field xpath="@lineName"/>
</xs:key>
<xs:keyref name="keyref_set_lineName" refer="key_set_lineName">
<xs:selector xpath="./x:lineName"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="privateUserId" type="privateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="privateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_privateUserId">
<xs:selector xpath="."/>
<xs:field xpath="@privateUserId"/>
</xs:key>
<xs:keyref name="keyref_set_privateUserId" refer="key_set_privateUserId">
<xs:selector xpath="./x:privateUserId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="publicData" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="publicIdValue" type="publicIdValueType"/>
<xs:element minOccurs="0" name="privateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="xcapAllowed" type="xcapAllowedType"/>
<xs:element minOccurs="0" name="xcapPassword" nillable="true" type="xcapPasswordType"/>
<xs:element minOccurs="0" name="implicitRegSet" type="implicitRegSetType"/>
<xs:element minOccurs="0" name="isDefault" type="isDefaultType"/>
<xs:element minOccurs="0" name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element minOccurs="0" name="wirelineAccessAllowed" type="wirelineAccessAllowedType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="authorizedVisitedAccessLine" nillable="true" type="authorizedVisitedAccessLineType"/>
<xs:element minOccurs="0" name="sessionBarringInd" type="sessionBarringIndType"/>
<xs:element minOccurs="0" name="maxNumberOfContacts" type="maxNumberOfContactsType"/>
<xs:element minOccurs="0" name="priorityLevel" nillable="true" type="priorityLevelType"/>
<xs:element minOccurs="0" name="isWildcardExtended" type="isWildcardExtendedType"/>
<xs:element minOccurs="0" name="aliasGroupId" nillable="true" type="aliasGroupIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="privateUserIdState">
<xs:complexType>
<xs:sequence>
<xs:element name="state" type="stateType"/>
</xs:sequence>
<xs:attribute name="privateUserId" type="privateUserIdType" use="required"/>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="numOfFailedXcapAuthAttempts" type="numOfFailedXcapAuthAttemptsType"/>
<xs:element minOccurs="0" name="displayName" type="displayNameType"/>
</xs:sequence>
<xs:attribute name="publicIdValue" type="publicIdValueType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="publicIdValueAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_publicIdValue">
<xs:selector xpath="."/>
<xs:field xpath="@publicIdValue"/>
</xs:key>
<xs:keyref name="keyref_set_publicIdValue" refer="key_set_publicIdValue">
<xs:selector xpath="./x:publicIdValue"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subscriberServiceProfile" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="configuredServiceProfile" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="configuredServiceProfileId" type="configuredServiceProfileIdType"/>
</xs:sequence>
<xs:attribute name="configuredServiceProfileId" type="configuredServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="configuredServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_configuredServiceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@configuredServiceProfileId"/>
</xs:key>
<xs:keyref name="keyref_set_configuredServiceProfileId" refer="key_set_configuredServiceProfileId">
<xs:selector xpath="./x:configuredServiceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="subscribedMediaProfile" type="subscribedMediaProfileType"/>
<xs:element minOccurs="0" name="maxNumberSessions" type="maxNumberSessionsType"/>
<xs:element minOccurs="0" name="phoneContext" type="phoneContextType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualServiceProfile" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="individualServiceProfileId" type="individualServiceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualCapability" nillable="true" type="individualCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualOptionalCapability" nillable="true" type="individualOptionalCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualTrigger" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="triggerDescription" type="triggerDescriptionType"/>
<xs:element minOccurs="0" name="triggerPriority" type="triggerPriorityType"/>
<xs:element minOccurs="0" name="isActive" type="isActiveType"/>
<xs:element minOccurs="0" name="triggerType" type="triggerTypeType"/>
<xs:element minOccurs="0" name="detectionPoint" type="detectionPointType"/>
<xs:element minOccurs="0" name="negatedDetectionPoint" type="negatedDetectionPointType"/>
<xs:element minOccurs="0" name="conditionType" type="conditionTypeType"/>
<xs:element minOccurs="0" name="requestedURI" type="requestedURIType"/>
<xs:element minOccurs="0" name="negatedRequestedURI" type="negatedRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" nillable="true" type="sipHeaderType"/>
<xs:element minOccurs="0" name="applicationServer" type="applicationServerType"/>
<xs:element maxOccurs="2" minOccurs="0" name="registrationType" nillable="true" type="registrationTypeType"/>
<xs:element minOccurs="0" name="defaultHandling" type="defaultHandlingType"/>
<xs:element minOccurs="0" name="includeRegisterRequest" nillable="true" type="includeRegisterRequestType"/>
<xs:element minOccurs="0" name="includeRegisterResponse" nillable="true" type="includeRegisterResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="triggerCriteriaGroup" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="groupId" type="groupIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionDescription" nillable="true" type="sessionDescriptionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionCase" nillable="true" type="sessionCaseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipMethod" nillable="true" type="sipMethodType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="groupRequestedURI" nillable="true" type="groupRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" nillable="true" type="sipHeaderType"/>
</xs:sequence>
<xs:attribute name="groupId" type="groupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="groupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_groupId">
<xs:selector xpath="."/>
<xs:field xpath="@groupId"/>
</xs:key>
<xs:keyref name="keyref_set_groupId" refer="key_set_groupId">
<xs:selector xpath="./x:groupId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="triggerDescription" type="triggerDescriptionType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="triggerDescriptionAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_triggerDescription">
<xs:selector xpath="."/>
<xs:field xpath="@triggerDescription"/>
</xs:key>
<xs:keyref name="keyref_set_triggerDescription" refer="key_set_triggerDescription">
<xs:selector xpath="./x:triggerDescription"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="individualServiceProfileId" type="individualServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="individualServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_individualServiceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@individualServiceProfileId"/>
</xs:key>
<xs:keyref name="keyref_set_individualServiceProfileId" refer="key_set_individualServiceProfileId">
<xs:selector xpath="./x:individualServiceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="serviceProfileId" type="serviceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="serviceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_serviceProfileId">
<xs:selector xpath="."/>
<xs:field xpath="@serviceProfileId"/>
</xs:key>
<xs:keyref name="keyref_set_serviceProfileId" refer="key_set_serviceProfileId">
<xs:selector xpath="./x:serviceProfileId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="associationId" type="associationIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="associationIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="impi" type="privateUserIdType"/>
<xs:element name="impu" type="publicIdValueType"/>
<xs:element name="GetResponseIMSAssociation">
<xs:complexType>
<xs:sequence>
<xs:element name="associationId" type="associationIdType"/>
<xs:element minOccurs="0" name="impi" type="privateUserIdType"/>
<xs:element minOccurs="0" name="impu" type="publicIdValueType"/>
<xs:element minOccurs="0" name="chargingProfId" type="chargingProfIdType"/>
<xs:element minOccurs="0" name="chargingId" type="e164Type"/>
<xs:element minOccurs="0" name="isPsi" type="isPsiType"/>
<xs:element minOccurs="0" name="privacyIndicator" type="privacyIndicatorType"/>
<xs:element minOccurs="0" name="defaultPrivateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="defaultRemoteReferenceAccessLocation" type="defaultRemoteReferenceAccessLocationType"/>
<xs:element minOccurs="0" name="asHostingPSI" type="asHostingPSIType"/>
<xs:element minOccurs="0" name="tenantId" type="tenantIdType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element minOccurs="0" name="esrNumber" type="esrNumberType"/>
<xs:element minOccurs="0" name="originatingCallsS-CSCF" type="originatingCallsS-CSCFType"/>
<xs:element minOccurs="0" name="terminatingCallsS-CSCF" type="terminatingCallsS-CSCFType"/>
<xs:element minOccurs="0" name="diaServId" type="diaServIdType"/>
<xs:element minOccurs="0" name="diaServRealm" type="diaServRealmType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="privateUser">
<xs:complexType>
<xs:sequence>
<xs:element name="privateUserId" type="privateUserIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="secondPrivateUserId">
<xs:complexType>
<xs:sequence>
<xs:element name="secondPrivateUserId" type="secondPrivateUserIdType"/>
</xs:sequence>
<xs:attribute name="secondPrivateUserId" type="secondPrivateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="secondPrivateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="allowedAuthMechanism" type="allowedAuthMechanismType"/>
<xs:element minOccurs="0" name="userBarringInd" type="userBarringIndType"/>
<xs:element minOccurs="0" name="roamingAllowed" type="roamingAllowedType"/>
<xs:element minOccurs="0" name="referenceAccessLocation" type="referenceAccessLocationType"/>
<xs:element minOccurs="0" name="userImsi" type="imsiType"/>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="looseRouteId" type="looseRouteIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ssoMsisdn">
<xs:complexType>
<xs:sequence>
<xs:element name="ssoMsisdn" type="msisdnType"/>
</xs:sequence>
<xs:attribute name="ssoMsisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="accessIdentifierAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="numOfFailedSipAuthAttempts" type="numOfFailedSipAuthAttemptsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accessIdentifier">
<xs:complexType>
<xs:sequence>
<xs:element name="accessIdentifier" type="accessIdentifierType"/>
</xs:sequence>
<xs:attribute name="accessIdentifier" type="accessIdentifierType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="accessIdentifierAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="userAccessLineIdentifier">
<xs:complexType>
<xs:sequence>
<xs:element name="lineName" type="lineNameType"/>
<xs:element name="value" type="valueType"/>
<xs:element minOccurs="0" name="authMechanism" type="authMechanismType"/>
</xs:sequence>
<xs:attribute name="lineName" type="lineNameType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="accessIdentifierAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="privateUserId" type="privateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="privateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="publicData">
<xs:complexType>
<xs:sequence>
<xs:element name="publicIdValue" type="publicIdValueType"/>
<xs:element name="privateUserId" type="privateUserIdType"/>
<xs:element minOccurs="0" name="xcapAllowed" type="xcapAllowedType"/>
<xs:element name="implicitRegSet" type="implicitRegSetType"/>
<xs:element name="isDefault" type="isDefaultType"/>
<xs:element minOccurs="0" name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element minOccurs="0" name="wirelineAccessAllowed" type="wirelineAccessAllowedType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="authorizedVisitedAccessLine" type="authorizedVisitedAccessLineType"/>
<xs:element minOccurs="0" name="sessionBarringInd" type="sessionBarringIndType"/>
<xs:element minOccurs="0" name="maxNumberOfContacts" type="maxNumberOfContactsType"/>
<xs:element minOccurs="0" name="priorityLevel" type="priorityLevelType"/>
<xs:element minOccurs="0" name="isWildcardExtended" type="isWildcardExtendedType"/>
<xs:element minOccurs="0" name="aliasGroupId" type="aliasGroupIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="privateUserIdState">
<xs:complexType>
<xs:sequence>
<xs:element name="privateUserId" type="privateUserIdType"/>
<xs:element name="state" type="stateType"/>
</xs:sequence>
<xs:attribute name="privateUserId" type="privateUserIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="privateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="numOfFailedXcapAuthAttempts" type="numOfFailedXcapAuthAttemptsType"/>
<xs:element minOccurs="0" name="displayName" type="displayNameType"/>
</xs:sequence>
<xs:attribute name="publicIdValue" type="publicIdValueType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="publicIdValueAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subscriberServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="serviceProfileId" type="serviceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="configuredServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="configuredServiceProfileId" type="configuredServiceProfileIdType"/>
</xs:sequence>
<xs:attribute name="configuredServiceProfileId" type="configuredServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="configuredServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="subscribedMediaProfile" type="subscribedMediaProfileType"/>
<xs:element minOccurs="0" name="maxNumberSessions" type="maxNumberSessionsType"/>
<xs:element minOccurs="0" name="phoneContext" type="phoneContextType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualServiceProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="individualServiceProfileId" type="individualServiceProfileIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualCapability" type="individualCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualOptionalCapability" type="individualOptionalCapabilityType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="individualTrigger">
<xs:complexType>
<xs:sequence>
<xs:element name="triggerDescription" type="triggerDescriptionType"/>
<xs:element name="triggerPriority" type="triggerPriorityType"/>
<xs:element minOccurs="0" name="isActive" type="isActiveType"/>
<xs:element minOccurs="0" name="triggerType" type="triggerTypeType"/>
<xs:element minOccurs="0" name="detectionPoint" type="detectionPointType"/>
<xs:element minOccurs="0" name="negatedDetectionPoint" type="negatedDetectionPointType"/>
<xs:element minOccurs="0" name="conditionType" type="conditionTypeType"/>
<xs:element minOccurs="0" name="requestedURI" type="requestedURIType"/>
<xs:element minOccurs="0" name="negatedRequestedURI" type="negatedRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" type="sipHeaderType"/>
<xs:element name="applicationServer" type="applicationServerType"/>
<xs:element maxOccurs="2" minOccurs="0" name="registrationType" type="registrationTypeType"/>
<xs:element minOccurs="0" name="defaultHandling" type="defaultHandlingType"/>
<xs:element minOccurs="0" name="includeRegisterRequest" type="includeRegisterRequestType"/>
<xs:element minOccurs="0" name="includeRegisterResponse" type="includeRegisterResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="triggerCriteriaGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="groupId" type="groupIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionDescription" type="sessionDescriptionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sessionCase" type="sessionCaseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipMethod" type="sipMethodType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="groupRequestedURI" type="groupRequestedURIType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="sipHeader" type="sipHeaderType"/>
</xs:sequence>
<xs:attribute name="groupId" type="groupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="groupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="triggerDescription" type="triggerDescriptionType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="triggerDescriptionAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="individualServiceProfileId" type="individualServiceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="individualServiceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="serviceProfileId" type="serviceProfileIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="serviceProfileIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="aliasGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="aliasGroupId" type="aliasGroupIdType"/>
</xs:sequence>
<xs:attribute name="aliasGroupId" type="aliasGroupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="aliasGroupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="associationId" type="associationIdType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="associationIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="impi" type="privateUserIdType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="privateUserIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="impu" type="publicIdValueType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="publicIdValueAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
