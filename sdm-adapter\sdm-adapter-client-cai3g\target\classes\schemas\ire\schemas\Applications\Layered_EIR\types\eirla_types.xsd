<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/EIR/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!-- EIR types - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
	<xs:simpleType name="actionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="I" />
			<xs:enumeration value="R" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="clarifyReasonType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="commentType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dateType">
		<xs:restriction base="xs:date" />
	</xs:simpleType>
	<xs:simpleType name="imeiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="14" />
			<xs:maxLength value="14" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiPrefixType">
		<xs:restriction base="xs:string">
			<xs:minLength value="2" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*|Default" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="insertReasonType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="10" />
			<xs:enumeration value="11" />
			<xs:enumeration value="16" />
			<xs:enumeration value="23" />
			<xs:enumeration value="25" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="searchOrderListNumberType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="10" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="equipmentListNumberType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="9" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="responseType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Black" />
			<xs:enumeration value="Grey" />
			<xs:enumeration value="White" />
			<xs:enumeration value="Unknown" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="presentInListType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
	<xs:simpleType name="removeReasonType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="14" />
			<xs:enumeration value="18" />
			<xs:enumeration value="20" />
			<xs:enumeration value="22" />
			<xs:enumeration value="24" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sourceOfRequestType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
			<xs:maxLength value="25" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="statusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Black" />
			<xs:enumeration value="Grey" />
			<xs:enumeration value="White" />
			<xs:enumeration value="Unknown" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="svnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{2}|[0][F]" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="timeType">
		<xs:restriction base="xs:time" />
	</xs:simpleType>
</xs:schema>
