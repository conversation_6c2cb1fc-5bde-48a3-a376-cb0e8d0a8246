<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
<types>
<xs:schema xmlns:mnp="http://schemas.ericsson.com/ma/hlr/" targetNamespace="http://schemas.ericsson.com/ma/hlr/">
<xs:include schemaLocation="../../../schemas/2G_3G/Deprecated_Layered_MNP/MNP.xsd"/>
<xs:element name="msisdn" type="mnp:msisdnType"/>
<xs:element name="imsi" type="mnp:imsiType"/>
</xs:schema>
<xs:schema xmlns="http://schemas.ericsson.com/cai3g1.2/" xmlns:mnp="http://schemas.ericsson.com/ma/hlr/" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
<xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd"/>
<xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/types/hlrla_types.xsd"/>
<xs:import namespace="http://schemas.ericsson.com/ma/hlr/" schemaLocation="../../../schemas/2G_3G/Deprecated_Layered_MNP/MNP.xsd"/>
<xs:element name="Delete">
<xs:complexType>
<xs:sequence>
<xs:element fixed="NumberPortability@http://schemas.ericsson.com/ma/hlr/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="mnp:msisdnType"/>
<xs:element name="imsi" type="mnp:imsiType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="Create">
<xs:complexType>
<xs:sequence>
<xs:element fixed="NumberPortability@http://schemas.ericsson.com/ma/hlr/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="mnp:msisdnType"/>
<xs:element name="imsi" type="mnp:imsiType"/>
</xs:choice>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="1" name="MOAttributes">
<xs:complexType>
<xs:sequence>
<xs:element ref="mnp:CreateNumberPortability"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="Set">
<xs:complexType>
<xs:sequence>
<xs:element fixed="NumberPortability@http://schemas.ericsson.com/ma/hlr/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="mnp:msisdnType"/>
<xs:element name="imsi" type="mnp:imsiType"/>
</xs:choice>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="1" name="MOAttributes">
<xs:complexType>
<xs:sequence>
<xs:element ref="mnp:SetNumberPortability"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="Get">
<xs:complexType>
<xs:sequence>
<xs:element fixed="NumberPortability@http://schemas.ericsson.com/ma/hlr/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="mnp:msisdnType"/>
<xs:element name="imsi" type="mnp:imsiType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="GetResponse">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="MOId" type="AnyMOIdType"/>
<xs:element minOccurs="0" name="MOAttributes">
<xs:complexType>
<xs:sequence>
<xs:element ref="mnp:GetResponseNumberPortability"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
</types>
<message name="GetRequest">
<part element="cai3g:Get" name="parameters"/>
</message>
<message name="GetResponse">
<part element="cai3g:GetResponse" name="parameters"/>
</message>
<message name="SetRequest">
<part element="cai3g:Set" name="parameters"/>
</message>
<message name="SetResponse">
<part element="cai3g:SetResponse" name="parameters"/>
</message>
<message name="CreateRequest">
<part element="cai3g:Create" name="parameters"/>
</message>
<message name="CreateResponse">
<part element="cai3g:CreateResponse" name="parameters"/>
</message>
<message name="DeleteRequest">
<part element="cai3g:Delete" name="parameters"/>
</message>
<message name="DeleteResponse">
<part element="cai3g:DeleteResponse" name="parameters"/>
</message>
<message name="HeadInfo">
<part element="cai3g:SessionId" name="sessionId"/>
</message>
<message name="Cai3gFault">
<part element="cai3g:Cai3gFault" name="parameters"/>
</message>
<message name="Cai3gHeaderFault">
<part element="cai3g:SessionIdFault" name="sessionIdFault"/>
<part element="cai3g:TransactionIdFault" name="transactionIdFault"/>
<part element="cai3g:SequenceIdFault" name="sequenceIdFault"/>
</message>
<portType name="MNP_Subscription">
<operation name="Get">
<input message="cai3g:GetRequest"/>
<output message="cai3g:GetResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
<operation name="Set">
<input message="cai3g:SetRequest"/>
<output message="cai3g:SetResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
<operation name="Create">
<input message="cai3g:CreateRequest"/>
<output message="cai3g:CreateResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
<operation name="Delete">
<input message="cai3g:DeleteRequest"/>
<output message="cai3g:DeleteResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
</portType>
<binding name="MNP_Subscription" type="cai3g:MNP_Subscription">
<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
<operation name="Get">
<soap:operation soapAction="CAI3G#Get" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
<operation name="Set">
<soap:operation soapAction="CAI3G#Set" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
<operation name="Create">
<soap:operation soapAction="CAI3G#Create" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
<operation name="Delete">
<soap:operation soapAction="CAI3G#Delete" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
</binding>
<service name="Provisioning">
<port binding="cai3g:MNP_Subscription" name="MNP_Subscription">
<soap:address location="http://anyma.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
</port>
</service>
</definitions>
