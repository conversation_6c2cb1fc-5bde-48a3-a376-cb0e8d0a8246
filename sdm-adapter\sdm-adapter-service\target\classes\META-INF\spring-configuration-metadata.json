{"groups": [{"name": "application", "type": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties"}, {"name": "application.conductor", "type": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties$ConductorProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties.ConductorProperties getConductor() "}], "properties": [{"name": "application.client-type", "type": "com.nokia.wing.wdh.sdmadapter.common.ESdmAdapterType", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties"}, {"name": "application.conductor.mno-prefix", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties$ConductorProperties"}, {"name": "application.conductor.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties$ConductorProperties"}, {"name": "application.conductor.worker-threads", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties$ConductorProperties"}, {"name": "application.conductor.workers", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties$ConductorProperties"}, {"name": "application.rat-types", "type": "java.lang.String[]", "sourceType": "com.nokia.wing.wdh.sdmadapter.service.config.ApplicationProperties"}], "hints": []}