<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:include schemaLocation="air_common_types.xsd"/>
<xs:element name="offerID" type="offerIDType"/>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="deleteOffer">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="dateTimeType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="offerID" type="offerIDType">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="offerIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="bundleID" type="bundleIDType">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="bundleIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="deleteOfferResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationTypeForDelResponse"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountDeleteInformation" type="dedicatedAccountDeleteInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="fafInformation" type="fafInformationType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListTypeForDelResponse"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="setOffer">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="dateTimeType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="currencyType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="offerRequestInformation" type="offerRequestInformationType"/>
<xs:element name="updateAction" type="updateActionType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetUpdateInformation" type="treeParameterSetUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="offerID" type="offerIDType">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="offerIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="bundleID" type="bundleIDType">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="bundleIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setOfferResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationListTypeForSetResponse"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="getOffer">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="dateTimeType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:choice minOccurs="0">
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerSelection" type="offerSelectionType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="productSelection" type="productSelectionType"/>
<xs:element minOccurs="0" name="bundleID" type="bundleIDType"/>
</xs:choice>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="requestInactiveOffersFlag" type="flagType"/>
<xs:element minOccurs="0" name="offerRequestedTypeFlag" type="offerRequestedTypeFlagType"/>
<xs:element minOccurs="0" name="requestDedicatedAccountDetailsFlag" type="flagType"/>
<xs:element minOccurs="0" name="requestSubDedicatedAccountDetailsFlag" type="flagType"/>
<xs:element minOccurs="0" name="requestFirstAccessibleAndExpiredBalanceAndDateFlag" type="flagType"/>
<xs:element minOccurs="0" name="requestUsageCountersAndThresholdsFlag" type="flagType"/>
<xs:element minOccurs="0" name="requestAttributesFlag" type="flagType"/>
<xs:element minOccurs="0" name="requestTreeParameterSetsFlag" type="flagType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="requestOfferResourcesFlag" type="flagType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="getOfferResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationTypeForGetResponse"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bundleInformation" type="bundleInformationType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterInformationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerStateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerProviderIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="28"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="flagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="offerRequestedTypeFlagType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-1]{8}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafNumberType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="30"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ownerType">
<xs:restriction base="xs:string">
<xs:enumeration value="Subscriber"/>
<xs:enumeration value="Account"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fafIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterNominalValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryNominalValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestExpiryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestAccessibleValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedAmountType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedExpiryDateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedStartDateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="bundleInformationType">
<xs:sequence>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:element maxOccurs="unbounded" name="offerInformation" type="offerInformationTypeForGetResponse"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="bundleInformationListTypeForSetResponse">
<xs:sequence>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:element maxOccurs="unbounded" name="offerInformation" type="offerInformationListTypeForSetResponse"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="bundleInformationListTypeForDelResponse">
<xs:sequence>
<xs:element name="bundleID" type="bundleIDType"/>
<xs:element minOccurs="0" name="bundleInstanceID" type="bundleInstanceIDType"/>
<xs:element minOccurs="0" name="externalBundleProductID" type="externalBundleProductIDType"/>
<xs:element maxOccurs="unbounded" name="offerInformation" type="offerInformationTypeForDelResponse"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationListTypeForSetResponse">
<xs:sequence>
<xs:element name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="balanceType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="offerRequestInformationType">
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="dateInformation" type="dateInformationType"/>
<xs:element minOccurs="0" name="dateTimeInformation" type="dateTimeInformationType"/>
</xs:choice>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationTypeForOffer"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeUpdateInformation" type="attributeUpdateInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationTypeForGetResponse">
<xs:sequence>
<xs:element name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationTypeForDelResponse">
<xs:sequence>
<xs:element name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountDeleteInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="compositeDedicatedAccountFlag" type="flagType"/>
<xs:element minOccurs="0" name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="fafInformationType">
<xs:sequence>
<xs:element name="fafNumber" type="fafNumberType"/>
<xs:element name="owner" type="ownerType"/>
<xs:element minOccurs="0" name="fafIndicator" type="fafIndicatorType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterUsageThresholdInformationType">
<xs:sequence>
<xs:element name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterNominalValue" type="usageCounterNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryNominalValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="usageCounterResourceConnected" type="usageCounterResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageThresholdInformationType">
<xs:sequence>
<xs:element name="usageThresholdID" type="usageThresholdIDType"/>
<xs:element minOccurs="0" name="usageThresholdValue" type="usageThresholdValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdSource" type="usageThresholdSourceType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountChangeInformation" type="subDedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="changedAmount1" type="changedAmountType"/>
<xs:element minOccurs="0" name="changedAmount2" type="changedAmountType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="changedExpiryDate" type="changedExpiryDateType"/>
<xs:element minOccurs="0" name="newExpiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="clearedExpiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="changedStartDate" type="changedStartDateType"/>
<xs:element minOccurs="0" name="newStartDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="clearedStartDate" type="dateTimeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="productSelectionType">
<xs:sequence>
<xs:element name="offerID" type="offerIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="aggregatedBalanceInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="totalBalance1" type="balanceType"/>
<xs:element minOccurs="0" name="totalBalance2" type="balanceType"/>
<xs:element minOccurs="0" name="totalActiveBalance1" type="balanceType"/>
<xs:element minOccurs="0" name="totalActiveBalance2" type="balanceType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="closestExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="backdatedToDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="backdatedToStartOfBillCycleInstanceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-1"/>
<xs:maxInclusive value="0"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountRealMoneyFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="dedicatedReservationType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="dedicatedAccountUpdateInformationTypeForOffer">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="dedicatedAccountPamPeriodInformation" type="dedicatedAccountPamPeriodInformationTypeForOffer"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountPamPeriodInformationTypeForOffer">
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="dedicatedAccountValueNew" type="dedicatedAccountValueType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentDateRelative" type="adjustmentDateRelativeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType"/>
<xs:element minOccurs="0" name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
</xs:choice>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="requestFafDetailsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestAggregatedProductOfferInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
</xs:schema>
