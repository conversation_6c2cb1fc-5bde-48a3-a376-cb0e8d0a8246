<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/ma/HSS/" elementFormDefault="qualified">

    <!-- Type definitions -->
    <xs:simpleType name="hlrfeidType">
        <xs:restriction base="xs:string" />
    </xs:simpleType>

    <xs:simpleType name="EsmNetworkDomainIdType">
        <xs:restriction base="xs:string">
            <xs:pattern value="(([a-z0-9]([-a-z0-9]{0,61}[a-z0-9]){0,1}\.)*([a-z0-9]([-a-z0-9]{0,61}[a-z0-9]){0,1})\.([a-z0-9]([-a-z0-9]{0,61}[a-z0-9]){0,1}\.)*([a-z0-9]([-a-z0-9]{0,61}[a-z0-9]){0,1}))"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EsmRaidType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="10" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>

    </xs:simpleType>

    <xs:simpleType name="EsmDefinedAreaRaidType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>

    </xs:simpleType>

    <xs:simpleType name="EsmRoamingServiceAreaIdType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="HssEsmDefinedAreaType">
        <xs:restriction base="xs:string" />
    </xs:simpleType>

    <xs:simpleType name="SRRType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="0" />
            <xs:enumeration value="1" />
            <xs:enumeration value="2" />
            <xs:enumeration value="4" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EsmNonDefinedAreaType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="0" />
            <xs:enumeration value="1" />
            <xs:enumeration value="2" />
            <xs:enumeration value="4" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="eraseType" >
        <xs:restriction base="xs:string">
            <xs:enumeration value="true" />
            <xs:enumeration value="false" />
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
