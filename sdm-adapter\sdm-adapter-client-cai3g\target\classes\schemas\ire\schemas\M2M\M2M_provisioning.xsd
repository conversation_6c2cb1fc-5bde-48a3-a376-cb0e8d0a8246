<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:m2m="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:x="http://schemas.ericsson.com/ma/m2mhlr/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/m2mhlr/">
<xs:element name="imsi" type="IMSIType"/>
<xs:element name="msisdn" type="MSISDNType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="createSubscription" type="CreateSubscriptionType">
<xs:annotation>
<xs:documentation>
				The attributes for creating M2M subscription.
			</xs:documentation>
</xs:annotation>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:unique name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:unique>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:complexType name="CreateSubscriptionType">
<xs:sequence>
<xs:element name="imsi" type="IMSIType"/>
<xs:element minOccurs="0" name="msisdn" type="MSISDNType"/>
<xs:element name="m2msp" type="M2MSPType"/>
<xs:element minOccurs="0" name="rid" type="RIDType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element maxOccurs="15" minOccurs="0" name="amsisdnMo" type="AMSISDNType"/>
<xs:element maxOccurs="2" minOccurs="0" name="mmintMo" type="MobManInTriggeringType"/>
<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" type="CreateClosedUserGroupType"/>
<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionType"/>
<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfu" type="CreateCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfb" type="CreateCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnrc" type="CreateCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnry" type="CreateCfnrySupplementaryServiceType"/>
<xs:element minOccurs="0" name="dcf" type="CreateDcfSupplementaryServiceType"/>
<xs:element minOccurs="0" name="spn" type="CreateSpnSupplementaryServiceType"/>
<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceType"/>
<xs:element default="0000" minOccurs="0" name="pwd" type="pwdType"/>
<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
<xs:element minOccurs="0" name="steMo" type="SteType"/>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="MSISDNType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:element name="getSubscription">
<xs:complexType>
<xs:attribute name="imsi" type="IMSIType" use="optional"/>
<xs:attribute name="msisdn" type="MSISDNType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="getResponseSubscription" type="GetSubscriptionResponseType">
<xs:annotation>
<xs:documentation>
				The attributes for get M2M HLR subscription response.
			</xs:documentation>
</xs:annotation>
<xs:key name="imsiKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:key>
<xs:keyref name="imsiKeyRef_Get" refer="imsiKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="imsi"/>
</xs:keyref>
<xs:key name="msisdnKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:key>
<xs:keyref name="msisdnKeyRef_Get" refer="msisdnKey_Get">
<xs:selector xpath="."/>
<xs:field xpath="msisdn"/>
</xs:keyref>
<xs:key name="amsisdnKey_Get">
<xs:selector xpath="./amsisdnMo"/>
<xs:field xpath="@amsisdn"/>
</xs:key>
<xs:keyref name="amsisdnKeyRef_Get" refer="amsisdnKey_Get">
<xs:selector xpath="./amsisdnMo"/>
<xs:field xpath="amsisdn"/>
</xs:keyref>
</xs:element>
<xs:complexType name="GetSubscriptionResponseType">
<xs:sequence>
<xs:element name="imsi" type="IMSIType"/>
<xs:element name="msisdn" type="MSISDNType"/>
<xs:element name="m2msp" type="M2MSPType"/>
<xs:element minOccurs="0" name="rid" type="RIDType"/>
<xs:element minOccurs="0" name="authd" type="xs:string"/>
<xs:element minOccurs="0" name="state" type="xs:string"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element maxOccurs="15" minOccurs="0" name="amsisdnMo" type="AMSISDNResponseType"/>
<xs:element maxOccurs="2" minOccurs="0" name="mmintMo" type="MobManInTriggeringType"/>
<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" type="ClosedUserGroupResponseType"/>
<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionReponseType"/>
<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="cfu" type="CfuCfbCfnrcSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="cfb" type="CfuCfbCfnrcSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="cfnrc" type="CfuCfbCfnrcSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="cfnry" type="CfnrySupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="dcf" type="DcfSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="spn" type="SpnSupplementaryServiceResponseType"/>
<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceResponseType"/>
<xs:element name="pwd" type="pwdType"/>
<xs:element name="socb" type="ZeroThreeType"/>
<xs:element minOccurs="0" name="passwordBarred" type="xs:boolean"/>
<xs:element minOccurs="0" name="mcfActive" type="xs:boolean"/>
<xs:element minOccurs="0" name="mmint" type="BinaryType"/>
<xs:element minOccurs="0" name="cug" type="BinaryType"/>
<xs:element minOccurs="0" name="ste" type="BinaryType"/>
<xs:element minOccurs="0" name="steMo" type="SteResponseType"/>
<xs:element minOccurs="0" name="msim" type="BinaryType"/>
<xs:element name="messageWaiting" type="MessageWaitingType"/>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="MSISDNType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:element name="setSubscription" type="SetSubscriptionType">
<xs:key name="amsisdnKey_SET">
<xs:selector xpath="./amsisdnMo"/>
<xs:field xpath="@amsisdn"/>
</xs:key>
<xs:keyref name="amsisdnKeyRef_SET" refer="amsisdnKey_SET">
<xs:selector xpath="./amsisdnMo"/>
<xs:field xpath="amsisdn"/>
</xs:keyref>
</xs:element>
<xs:complexType name="SetSubscriptionType">
<xs:sequence>
<xs:element maxOccurs="15" minOccurs="0" name="amsisdnMo" nillable="true" type="AMSISDNType"/>
<xs:element maxOccurs="4" minOccurs="0" name="mmintMo" nillable="true" type="SetMobManInTriggeringType"/>
<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" nillable="true" type="SetClosedUserGroupType"/>
<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionType"/>
<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfu" type="SetCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfb" type="SetCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnrc" type="SetCfuCfbCfnrcSupplementaryServiceType"/>
<xs:element minOccurs="0" name="cfnry" type="SetCfnrySupplementaryServiceType"/>
<xs:element minOccurs="0" name="dcf" type="SetDcfSupplementaryServiceType"/>
<xs:element minOccurs="0" name="spn" type="SetSpnSupplementaryServiceType"/>
<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceType"/>
<xs:element default="0000" minOccurs="0" name="pwd" type="pwdType"/>
<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
<xs:element minOccurs="0" name="steMo" nillable="true" type="SetSteType"/>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="MSISDNType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:element name="deleteSubscription" type="DeleteSubscriptionType">
<xs:annotation>
<xs:documentation>
				The attributes for deleting M2M subscription
				response.
			</xs:documentation>
</xs:annotation>
</xs:element>
<xs:complexType name="DeleteSubscriptionType">
<xs:sequence minOccurs="0"/>
</xs:complexType>
<xs:simpleType name="MSISDNType">
<xs:annotation>
<xs:documentation>
				the type definition for MSISDN
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IMSIType">
<xs:annotation>
<xs:documentation>
				the type definition for IMSI
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="M2MSPType">
<xs:annotation>
<xs:documentation>
				the type definition for M2MSP
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="RIDType">
<xs:annotation>
<xs:documentation>
				the type definition for RID
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="63"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="AMSISDNType">
<xs:sequence>
<xs:element name="amsisdn" type="MSISDNType"/>
<xs:element name="bc" type="bcType"/>
</xs:sequence>
<xs:attribute name="amsisdn" type="MSISDNType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="amsisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="AMSISDNResponseType">
<xs:sequence>
<xs:element name="amsisdn" type="MSISDNType"/>
<xs:element name="bc" type="bcType"/>
<xs:element minOccurs="0" name="bs" type="bsType"/>
</xs:sequence>
<xs:attribute name="amsisdn" type="MSISDNType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="amsisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="MobManInTriggeringType">
<xs:sequence>
<xs:element name="detectionPoint" type="detectionPointType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="serviceKey" type="serviceKeyType"/>
<xs:element minOccurs="0" name="activationState" type="ActivationOneType"/>
</xs:sequence>
<xs:attribute name="detectionPoint" type="detectionPointType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="detectionPointAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="SetMobManInTriggeringType">
<xs:choice>
<xs:sequence>
<xs:element name="detectionPoint" type="detectionPointType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="serviceKey" type="serviceKeyType"/>
<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="activationState" type="ActivationType"/>
</xs:sequence>
<xs:sequence>
<xs:choice>
<xs:sequence>
<xs:element name="serviceKey" type="serviceKeyType"/>
<xs:element minOccurs="0" name="gsa" type="gsaType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="gsa" type="gsaType"/>
</xs:sequence>
</xs:choice>
<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
</xs:sequence>
</xs:choice>
<xs:attribute name="detectionPoint" type="detectionPointType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="detectionPointAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="CreateClosedUserGroupType">
<xs:sequence>
<xs:element name="cugIndex" type="cugIndexType"/>
<xs:element name="interlockCode" type="interlockCodeType"/>
<xs:element minOccurs="0" name="restriction" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="ts10" type="ProvisionType"/>
<xs:element minOccurs="0" name="ts60" type="ProvisionType"/>
<xs:element minOccurs="0" name="tsd0" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs20" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs30" type="ProvisionType"/>
</xs:sequence>
<xs:attribute name="cugIndex" type="cugIndexType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cugIndexAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="ClosedUserGroupResponseType">
<xs:sequence>
<xs:element name="cugIndex" type="cugIndexType"/>
<xs:element name="interlockCode" type="interlockCodeType"/>
<xs:element name="restriction" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="ts10" type="ProvisionType"/>
<xs:element minOccurs="0" name="ts60" type="ProvisionType"/>
<xs:element minOccurs="0" name="tsd0" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs20" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs30" type="ProvisionType"/>
</xs:sequence>
<xs:attribute name="cugIndex" type="cugIndexType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cugIndexAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="SetClosedUserGroupType">
<xs:choice>
<xs:sequence>
<xs:element name="cugIndex" type="cugIndexType"/>
<xs:element name="interlockCode" type="interlockCodeType"/>
<xs:element minOccurs="0" name="restriction" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="ts10" type="ProvisionType"/>
<xs:element minOccurs="0" name="ts60" type="ProvisionType"/>
<xs:element minOccurs="0" name="tsd0" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs20" type="ProvisionType"/>
<xs:element minOccurs="0" name="bs30" type="ProvisionType"/>
</xs:sequence>
<xs:sequence>
<xs:element minOccurs="0" name="restriction" type="ZeroTwoType"/>
<xs:sequence minOccurs="0">
<xs:element name="bsg" type="bsgType"/>
<xs:element minOccurs="0" name="erase" type="eraseType"/>
</xs:sequence>
</xs:sequence>
</xs:choice>
<xs:attribute name="cugIndex" type="cugIndexType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cugIndexAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:simpleType name="eraseType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="bsgType">
<xs:restriction base="xs:string">
<xs:enumeration value="ts10"/>
<xs:enumeration value="ts60"/>
<xs:enumeration value="tsd0"/>
<xs:enumeration value="bs20"/>
<xs:enumeration value="bs30"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CugSingleBsgOptionType">
<xs:sequence>
<xs:element name="accessibility" type="CugAccessibilityType"/>
<xs:element minOccurs="0" name="preferentialCug" type="preferentialCugType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CugSingleBsgOptionResponseType">
<xs:sequence>
<xs:element name="accessibility" type="CugAccessibilityType"/>
<xs:element name="preferentialCug" type="preferentialCugType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CugBsgOptionType">
<xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="accessibility" type="CugAccessibilityType"/>
<xs:element minOccurs="0" name="preferentialCug" type="preferentialCugType"/>
</xs:sequence>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="CugSingleBsgOptionType"/>
<xs:element minOccurs="0" name="ts60" type="CugSingleBsgOptionType"/>
<xs:element minOccurs="0" name="tsd0" type="CugSingleBsgOptionType"/>
<xs:element minOccurs="0" name="bs20" type="CugSingleBsgOptionType"/>
<xs:element minOccurs="0" name="bs30" type="CugSingleBsgOptionType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="CugBsgOptionReponseType">
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="CugSingleBsgOptionResponseType"/>
<xs:element minOccurs="0" name="ts60" type="CugSingleBsgOptionResponseType"/>
<xs:element minOccurs="0" name="tsd0" type="CugSingleBsgOptionResponseType"/>
<xs:element minOccurs="0" name="bs20" type="CugSingleBsgOptionResponseType"/>
<xs:element minOccurs="0" name="bs30" type="CugSingleBsgOptionResponseType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SteType">
<xs:sequence>
<xs:element name="ste" type="SteZeroType"/>
<xs:element name="gmlcid" type="ZeroTwoFiveFiveType"/>
</xs:sequence>
<xs:attribute name="ste" type="ZeroFifteenType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="steAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="SetSteType">
<xs:choice>
<xs:sequence>
<xs:element name="ste" type="SteZeroType"/>
<xs:element name="gmlcid" type="ZeroTwoFiveFiveType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="gmlcid" type="ZeroTwoFiveFiveType"/>
</xs:sequence>
</xs:choice>
<xs:attribute name="ste" type="ZeroFifteenType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="steAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="SteResponseType">
<xs:sequence>
<xs:element name="ste" type="ZeroFifteenType"/>
<xs:element name="gmlcid" type="ZeroTwoFiveFiveType"/>
<xs:element minOccurs="0" name="gmlca">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:minLength value="0"/>
<xs:maxLength value="20"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
</xs:sequence>
<xs:attribute name="ste" type="ZeroFifteenType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="steAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="LinkedImsiType">
<xs:sequence>
<xs:element name="imsi" type="IMSIType"/>
<xs:element minOccurs="0" name="active" type="xs:boolean"/>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:complexType name="CreateCfnrySupplementaryServiceType">
<xs:choice>
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="FnumCfnryDcfActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="CfnrySupplementaryServiceResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="ts60" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="tsd0" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs20" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs30" type="FnumCfnryDcfActivationStateResponseType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SetCfnrySupplementaryServiceType">
<xs:choice>
<xs:choice>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SetFnumCfnryActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SetFnumCfnryActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SetFnumCfnryActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SetFnumCfnryActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SetFnumCfnryActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="CreateDcfSupplementaryServiceType">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:choice>
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="FnumCfnryDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="FnumCfnryDcfActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="DcfSupplementaryServiceResponseType">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="ts10" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="ts60" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="tsd0" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs20" type="FnumCfnryDcfActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs30" type="FnumCfnryDcfActivationStateResponseType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SetDcfSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:choice>
<xs:choice>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SetFnumDcfActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SetFnumDcfActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SetFnumDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SetFnumDcfActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SetFnumDcfActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SetFnumDcfActivationStateType">
<xs:choice>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="FnumCfnryDcfActivationStateType">
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="FnumCfnryDcfActivationStateResponseType">
<xs:sequence>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SetFnumCfnryActivationStateType">
<xs:choice>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="FnumSpnActivationStateResponseType">
<xs:sequence>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CreateSpnSupplementaryServiceType">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:choice>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="ofa" type="x:ofaType"/>
</xs:sequence>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SpnSupplementaryServiceResponseType">
<xs:sequence>
<xs:element name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="ts10" type="FnumSpnActivationStateResponseType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SetSpnSupplementaryServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CawSupplementaryServiceType">
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="CawSupplementaryServiceResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CreateFnumCfuCfbCfnrcActivationStateType">
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="SetFnumCfuCfbCfnrcActivationStateType">
<xs:sequence>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="FnumCfuCfbCfnrcActivationStateResponseType">
<xs:sequence>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CreateCfuCfbCfnrcSupplementaryServiceType">
<xs:choice>
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
</xs:sequence>
</xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="CreateFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="CreateFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="CreateFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="CreateFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="CreateFnumCfuCfbCfnrcActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="SetCfuCfbCfnrcSupplementaryServiceType">
<xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
<xs:element minOccurs="0" name="ofa" type="ofaType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SetFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SetFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SetFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SetFnumCfuCfbCfnrcActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SetFnumCfuCfbCfnrcActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="CfuCfbCfnrcSupplementaryServiceResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="FnumCfuCfbCfnrcActivationStateResponseType"/>
<xs:element minOccurs="0" name="ts60" type="FnumCfuCfbCfnrcActivationStateResponseType"/>
<xs:element minOccurs="0" name="tsd0" type="FnumCfuCfbCfnrcActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs20" type="FnumCfuCfbCfnrcActivationStateResponseType"/>
<xs:element minOccurs="0" name="bs30" type="FnumCfuCfbCfnrcActivationStateResponseType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="BarringSupplementaryServiceType">
<xs:choice>
<xs:element name="activationState" type="ZeroTwoType"/>
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
</xs:sequence>
</xs:choice>
</xs:complexType>
<xs:complexType name="BarringSupplementaryServiceResponseType">
<xs:sequence>
<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SimpleActivationStateType">
<xs:sequence>
<xs:element name="activationState" type="ZeroTwoType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="MessageWaitingType">
<xs:sequence>
<xs:element name="mce" type="EnumType"/>
<xs:element minOccurs="0" name="mnrf" type="EnumType"/>
<xs:element minOccurs="0" name="mnrg" type="EnumType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="scadd" type="ScaddType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="EnumType">
<xs:restriction base="xs:unsignedByte">
<xs:enumeration value="0"/>
<xs:enumeration value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ScaddType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="30"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BinaryType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroTwoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroThreeType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroTwoFiveFiveType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ActivationType">
<xs:restriction base="BinaryType"/>
</xs:simpleType>
<xs:simpleType name="ProvisionType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="CugAccessibilityType">
<xs:restriction base="ZeroThreeType"/>
</xs:simpleType>
<xs:simpleType name="pwdType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ZeroFifteenType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="15"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ofaType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="511"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="gsaType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{3,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="bcType">
<xs:restriction base="xs:string">
<xs:pattern value="(6553[0-4]|655[0-2][0-9]\d|65[0-4](\d){2}|6[0-4](\d){3}|[1-5](\d){4}|[0-9](\d){1,3}|[012389])"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="bsType">
<xs:restriction base="xs:string">
<xs:enumeration value="TS11"/>
<xs:enumeration value="TS21"/>
<xs:enumeration value="TS22"/>
<xs:enumeration value="TS61"/>
<xs:enumeration value="TS62"/>
<xs:enumeration value="TSD1"/>
<xs:enumeration value="BS21"/>
<xs:enumeration value="BS22"/>
<xs:enumeration value="BS23"/>
<xs:enumeration value="BS24"/>
<xs:enumeration value="BS25"/>
<xs:enumeration value="BS26"/>
<xs:enumeration value="BS2G"/>
<xs:enumeration value="BS31"/>
<xs:enumeration value="BS32"/>
<xs:enumeration value="BS33"/>
<xs:enumeration value="BS34"/>
<xs:enumeration value="BS3G"/>
<xs:enumeration value="ALL"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceKeyType">
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="detectionPointType">
<xs:restriction base="xs:unsignedInt">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="interlockCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{4}-(6553[0-5]|655[0-2][0-9]\d|65[0-4](\d){2}|6[0-4](\d){3}|[1-5](\d){4}|[0-9](\d){1,3}|[0-9])"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="cugIndexType">
<xs:restriction base="xs:unsignedShort">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="preferentialCugType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="5"/>
<xs:pattern value="(NONE|3276[0-7]|327[0-5](\d){1}|32[0-6](\d){2}|3[0-1](\d){3}|[1-2](\d){4}|[0-9](\d){1,3}|[0-9])"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="fnumType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="18"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subAddressType">
<xs:restriction base="xs:string">
<xs:pattern value="[012]-([0-9ABCDEF]{2}){1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="noReplyTimeType">
<xs:restriction base="xs:string">
<xs:pattern value="[1-2][05]|30|5"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mchType">
<xs:restriction base="xs:string">
<xs:enumeration value="LOC"/>
<xs:enumeration value="USSD"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="primaryhlridType">
<xs:restriction base="xs:string">
<xs:maxLength value="5"/>
<xs:pattern value="[1-15]-[1-32]"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="zoneidType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ActivationOneType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="SteZeroType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="0"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
