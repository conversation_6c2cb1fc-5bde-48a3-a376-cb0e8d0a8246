<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
    attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
    jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/"
    xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
    xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
    xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/">
    
    <xs:element name="subscriberNumber" type="subscriberNumberType" />
    
    <!-- RunPeriodicAccountManagement -->
    <xs:element name="setRunPeriodicAccountManagement">
    	<xs:complexType>
    		<xs:sequence>
    			<xs:element name="originNodeType" type="originNodeTypeType" minOccurs="0" />
                <xs:element name="originHostName" type="originHostNameType" minOccurs="0" />
                <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0" />
                <xs:element name="originTimeStamp" type="dateTimeType" minOccurs="0" />
                <xs:element name="subscriberNumberNAI" type="subscriberNumberNAIType" minOccurs="0" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="pamServiceID" type="pamServiceIDType" />
                <xs:element name="pamIndicator" type="pamIndicatorType" minOccurs="0" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
    		</xs:sequence>
    		<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="subscriberNumberAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
    	</xs:complexType>
    </xs:element>
    
    <!-- RunPeriodicAccountManagement Response -->
    <xs:element name="setRunPeriodicAccountManagementResponse">
    	<xs:complexType>
    		<xs:sequence>
    			<xs:element name="subscriberNumber" type="subscriberNumberType" minOccurs="0"/>
                <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="pamInformation" type="pamInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
    		</xs:sequence>
    	</xs:complexType>
    </xs:element>
    
    <!-- simple type definition -->
    <xs:simpleType name="originNodeTypeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="EXT" />
            <xs:enumeration value="AIR" />
            <xs:enumeration value="ADM" />
            <xs:enumeration value="UGW" />
            <xs:enumeration value="IVR" />
            <xs:enumeration value="OGW" />
            <xs:enumeration value="SDP" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="originHostNameType">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="originTransactionIDType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,20}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="dateTimeType">
        <xs:restriction base="xs:dateTime" />
    </xs:simpleType>
    <xs:simpleType name="subscriberNumberNAIType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="2" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="originOperatorIDType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Za-z0-9 ]{1,255}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="pamServiceIDType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="99" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="pamIndicatorType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="negotiatedCapabilitiesType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="2147483647" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="availableServerCapabilitiesType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="2147483647" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="subscriberNumberType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,28}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="pamClassIDType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="scheduleIDType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="currentPamPeriodType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9\-/]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="deferredToDateType">
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>
	<xs:simpleType name="pamServicePriorityType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="65535"/>
		</xs:restriction>
	</xs:simpleType>
    <!-- complex type definition -->
    <xs:complexType name="pamInformationType">
        <xs:sequence>
            <xs:element name="pamServiceID" type="pamServiceIDType"/>
			<xs:element name="pamClassID" type="pamClassIDType"/>
			<xs:element name="scheduleID" type="scheduleIDType"/>
			<xs:element name="currentPamPeriod" type="currentPamPeriodType"/>
			<xs:element name="deferredToDate" type="deferredToDateType" minOccurs="0"/>
			<xs:element name="lastEvaluationDate" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="pamServicePriority" type="pamServicePriorityType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>