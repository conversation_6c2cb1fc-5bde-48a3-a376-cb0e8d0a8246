<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEReport/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbereport-v1-5="http://ossj.org/xml/Common-CBEReport/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <!-- Tigerstripe : Datatype definitions for ReportInfo  (basic, ArrayOf) -->
    <complexType name="ReportInfo">
        <annotation>

            <documentation>
Contains information related to a report file.  
- URL where the report is located 
- {@link ReportFormat ReportFormat} information related to the format of the Report 
- expiration date 

            </documentation>
        </annotation>
                <sequence>
                    <element name="URL" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="reportFormat" type="cbereport-v1-5:ReportFormat" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="expirationDate" type="dateTime" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
    </complexType>

    <complexType name="ArrayOfReportInfo">
        <sequence>
            <element name="item" type="cbereport-v1-5:ReportInfo" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReportInfo -->
    <!-- Tigerstripe : End of Datatype definition for ReportInfo -->
    <!-- Tigerstripe : Datatype definitions for CurrentResultReport  (basic, ArrayOf) -->
    <complexType name="CurrentResultReport">

        <annotation>
            <documentation>
This interface represents a complete report. 
 Example: 
 An external system may for some reason be interested in the current values of the measurement job for example tracing the increment of some of the counters. The requested result report of the current values of the measurement job is represented by this interface. The current measurement result can be represented in two ways that the sever decide. The two ways are:  
-  as a location of the current measurement report file or 
-  as data. 

            </documentation>
        </annotation>
                <sequence>
                    <element name="reportInformation" type="cbereport-v1-5:ReportInfo" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="reportData" type="cbereport-v1-5:ReportData" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="URLType" type="boolean" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="dataType" type="boolean" nillable= "true" minOccurs="0" maxOccurs="1" />

                </sequence>
    </complexType>
    <complexType name="ArrayOfCurrentResultReport">
        <sequence>
            <element name="item" type="cbereport-v1-5:CurrentResultReport" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of CurrentResultReport -->
    <!-- Tigerstripe : End of Datatype definition for CurrentResultReport -->

    <!-- Tigerstripe : Datatype definitions for ReportInfoIterator  (basic, ArrayOf) -->
    <complexType name="ReportInfoIterator">
        <annotation>
            <documentation>
This interface manages retrieval of information for multiple reports.  
 Because the query operation could potentially return large amounts of data, the iterator design pattern is used for returning the results. An iterator is an object that is created to contain the results of an operation. The client receives a reference to the iterator as part of the information returned by the query operation. The client then invokes operations on the iterator to receive batches of results in sizes determined by the client. The iterator keeps track of how far through the results the client has progressed.  
 The semantic of the iterator is that it is returning a snapshot of the data matching the query expression at the moment the query was performed. If the underlying data are modified while the iteration is in progress, it will not affect the behavior of the iterator or the data values of the iterator.  
 The report information that is contained in this iterator must be sorted on report data creation date. If the report data is stored in files, the oldest file shall be returned first by the iterator.
            </documentation>
        </annotation>
                <sequence>
                </sequence>
    </complexType>

    <complexType name="ArrayOfReportInfoIterator">
        <sequence>
            <element name="item" type="cbereport-v1-5:ReportInfoIterator" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReportInfoIterator -->
    <!-- Tigerstripe : End of Datatype definition for ReportInfoIterator -->
    <!-- Tigerstripe : Datatype definitions for ReportData  (basic, ArrayOf) -->
    <complexType name="ReportData">

        <annotation>
            <documentation>
This interface represents a report including:  
- {@link ReportFormat ReportFormat} 
- data 

            </documentation>
        </annotation>
                <sequence>
                    <element name="report" type="anyType" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="reportFormat" type="cbereport-v1-5:ReportFormat" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
    </complexType>

    <complexType name="ArrayOfReportData">
        <sequence>
            <element name="item" type="cbereport-v1-5:ReportData" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReportData -->
    <!-- Tigerstripe : End of Datatype definition for ReportData -->
    <!-- Tigerstripe : Datatype definitions for ReportFormat  (basic, ArrayOf) -->
    <complexType name="ReportFormat">

        <annotation>
            <documentation>
This interface represent the format of the report.
            </documentation>
        </annotation>
                <sequence>
                    <element name="type" type="int" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="version" type="cbedatatypes-v1-5:Version" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="owner" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="technology" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="specification" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
    </complexType>
    <complexType name="ArrayOfReportFormat">
        <sequence>
            <element name="item" type="cbereport-v1-5:ReportFormat" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReportFormat -->

    <!-- Tigerstripe : End of Datatype definition for ReportFormat -->
    <!-- Tigerstripe : Enumeration definitions for ReportMode  -->
    <simpleType name="ReportMode">
        <annotation>
            <documentation>
Defines enumeration values for report mode.  
 The report modes (mainly comming from measurement definitions) can be done in different ways. As example, the following report modes can be used or extended:  
- send an event per job. 
- send event for multiple jobs. 
- create report file per job. 
- create file for multiple jobs. 
  
 The report mode can be divided into two major categories: 
- by event
 If the reporting mode is set to event the system will emit an event that carries the result report encapsulated into {@link ReportData ReportData}. 
- by file
 If the reporting mode is set to file, the job will capture the result reports into a data storage and then emit an event to the client about the availability of the datas.
 The frequency of this event is determined by the data storage creation frequency. When a client receives an event of data availability, the client can retrieve the data, by using the URL using {@link ReportInfo ReportInfo} to connect to the system. 

            </documentation>
        </annotation>
        <restriction base="int">
            <!-- name = NO_REPORT_MODE -->

            <enumeration value="0" />
            <!-- name = EVENT_SINGLE -->
            <enumeration value="1" />
            <!-- name = EVENT_MULTIPLE -->
            <enumeration value="2" />
            <!-- name = FILE_SINGLE -->
            <enumeration value="3" />
            <!-- name = FILE_MULTIPLE -->
            <enumeration value="4" />

            <!-- name = STREAM_SINGLE -->
            <enumeration value="5" />
            <!-- name = STREAM_MULTIPLE -->
            <enumeration value="6" />
            <!-- name = ITERATOR_SINGLE -->
            <enumeration value="7" />
            <!-- name = ITERATOR_MULTIPLE -->
            <enumeration value="8" />
        </restriction>

    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ReportMode  -->






</schema>
