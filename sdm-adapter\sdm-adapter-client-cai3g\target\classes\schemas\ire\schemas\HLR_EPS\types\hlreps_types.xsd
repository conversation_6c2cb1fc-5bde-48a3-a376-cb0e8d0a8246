<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/eda/cudb/HlrEps/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/eda/cudb/HlrEps/" elementFormDefault="qualified">

	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsiType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:maxLength value="15" />
					<xs:minLength value="6" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="profileType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="8191" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="63" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="epsProfileIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="epsTenantIdType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="500"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="typeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ALL" />
			<xs:enumeration value="HLR" />
			<xs:enumeration value="EPS" />
		</xs:restriction>
	</xs:simpleType>
	<!-- End of M2M types -->
</xs:schema>
