<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/SAPC/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/SAPC/">
<xs:include schemaLocation="types/sapcla_types.xsd"/>
<xs:element name="pcSubscriberId" type="pcSubscriberIdType"/>
<xs:element name="CreateSAPCSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="pcSubscriberId" type="pcSubscriberIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcTrafficId">
<xs:complexType>
<xs:sequence>
<xs:element name="pcTrafficId" type="pcTrafficIdType"/>
</xs:sequence>
<xs:attribute name="pcTrafficId" type="pcTrafficIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcTrafficIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcTrafficId">
<xs:selector xpath="."/>
<xs:field xpath="@pcTrafficId"/>
</xs:key>
<xs:keyref name="keyref_create_pcTrafficId" refer="key_create_pcTrafficId">
<xs:selector xpath="./x:pcTrafficId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcSubscribedService">
<xs:complexType>
<xs:sequence>
<xs:element name="pcSubscribedService" type="pcSubscribedServiceType"/>
</xs:sequence>
<xs:attribute name="pcSubscribedService" type="pcSubscribedServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscribedServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcSubscribedService">
<xs:selector xpath="."/>
<xs:field xpath="@pcSubscribedService"/>
</xs:key>
<xs:keyref name="keyref_create_pcSubscribedService" refer="key_create_pcSubscribedService">
<xs:selector xpath="./x:pcSubscribedService"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcBlacklistService">
<xs:complexType>
<xs:sequence>
<xs:element name="pcBlacklistService" type="pcBlacklistServiceType"/>
</xs:sequence>
<xs:attribute name="pcBlacklistService" type="pcBlacklistServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcBlacklistServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcBlacklistService">
<xs:selector xpath="."/>
<xs:field xpath="@pcBlacklistService"/>
</xs:key>
<xs:keyref name="keyref_create_pcBlacklistService" refer="key_create_pcBlacklistService">
<xs:selector xpath="./x:pcBlacklistService"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="pcFamilyId" type="pcFamilyIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="pcGroupId" type="pcGroupIdType"/>
<xs:element minOccurs="0" name="pcGroupPriority" type="pcGroupPriorityType"/>
<xs:choice minOccurs="0">
<xs:sequence>
<xs:element minOccurs="0" name="pcGroupStartDate" type="pcGroupStartDateType"/>
<xs:element minOccurs="0" name="pcGroupEndDate" type="pcGroupEndDateType"/>
</xs:sequence>
<xs:element minOccurs="0" name="pcGroupDurations">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="1" name="pcGroupDuration" type="pcGroupDurationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:choice>
<xs:element minOccurs="0" name="pcGroupInstancesContracted" type="pcGroupInstancesContractedType"/>
</xs:sequence>
<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcGroupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcGroupId">
<xs:selector xpath="."/>
<xs:field xpath="@pcGroupId"/>
</xs:key>
<xs:keyref name="keyref_create_pcGroupId" refer="key_create_pcGroupId">
<xs:selector xpath="./x:pcGroupId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="pcNotificationData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationSMS">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationSMS" type="pcNotificationSMSType"/>
</xs:sequence>
<xs:attribute name="pcNotificationSMS" type="pcNotificationSMSType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationSMSAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcNotificationSMS">
<xs:selector xpath="."/>
<xs:field xpath="@pcNotificationSMS"/>
</xs:key>
<xs:keyref name="keyref_create_pcNotificationSMS" refer="key_create_pcNotificationSMS">
<xs:selector xpath="./x:pcNotificationSMS"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationEmail">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationEmail" type="pcNotificationEmailType"/>
</xs:sequence>
<xs:attribute name="pcNotificationEmail" type="pcNotificationEmailType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationEmailAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcNotificationEmail">
<xs:selector xpath="."/>
<xs:field xpath="@pcNotificationEmail"/>
</xs:key>
<xs:keyref name="keyref_create_pcNotificationEmail" refer="key_create_pcNotificationEmail">
<xs:selector xpath="./x:pcNotificationEmail"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcLimitUsage" type="pcLimitUsageType"/>
<xs:element minOccurs="0" name="pcAccumulatedData" type="pcAccumulatedDataType"/>
<xs:element minOccurs="0" name="pcSubcriberQualificationData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcQoSProfileId" type="pcQoSProfileIdType"/>
<xs:element minOccurs="0" name="pcContentFilteringProfileId" type="pcContentFilteringProfileIdType"/>
<xs:element minOccurs="0" name="pcCustomerId" type="pcCustomerIdType"/>
<xs:element minOccurs="0" name="pcRoutingProfileId" type="pcRoutingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfile">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileId" type="pcSubscriberChargingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileName" type="pcSubscriberChargingProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcBearerQoSProfileName">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcMaxBearerQoSProfileName" type="pcMaxBearerQoSProfileNameType"/>
<xs:element minOccurs="0" name="pcMinBearerQoSProfileName" type="pcMinBearerQoSProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcServicestoRedirect" type="pcServicestoRedirectType"/>
<xs:element minOccurs="0" name="pcGeneralProfileName" type="pcGeneralProfileNameType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingSystemName" type="pcSubscriberChargingSystemNameType"/>
<xs:element minOccurs="0" name="pcMpsProfileName" type="pcMpsProfileNameType"/>
<xs:element minOccurs="0" name="pcSpId" type="pcSpIdType"/>
<xs:element minOccurs="0" name="pcPresenceAreaName" type="pcPresenceAreaNameType"/>
<xs:element minOccurs="0" name="pcPdnGwName" type="pcPdnGwNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcEnableMasc" type="pcEnableMascType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcEventTriggers">
<xs:complexType>
<xs:sequence>
<xs:element name="pcEventTriggers" type="pcEventTriggersType"/>
</xs:sequence>
<xs:attribute name="pcEventTriggers" type="pcEventTriggersType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcEventTriggersAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcEventTriggers">
<xs:selector xpath="."/>
<xs:field xpath="@pcEventTriggers"/>
</xs:key>
<xs:keyref name="keyref_create_pcEventTriggers" refer="key_create_pcEventTriggers">
<xs:selector xpath="./x:pcEventTriggers"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcOperatorSpecificInfo">
<xs:complexType>
<xs:sequence>
<xs:element name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType"/>
</xs:sequence>
<xs:attribute name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcOperatorSpecificInfoAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcOperatorSpecificInfo">
<xs:selector xpath="."/>
<xs:field xpath="@pcOperatorSpecificInfo"/>
</xs:key>
<xs:keyref name="keyref_create_pcOperatorSpecificInfo" refer="key_create_pcOperatorSpecificInfo">
<xs:selector xpath="./x:pcOperatorSpecificInfo"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="pcSubscriberId" type="pcSubscriberIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscriberIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_pcSubscriberId">
<xs:selector xpath="."/>
<xs:field xpath="@pcSubscriberId"/>
</xs:key>
<xs:keyref name="keyref_create_pcSubscriberId" refer="key_create_pcSubscriberId">
<xs:selector xpath="./x:pcSubscriberId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element name="GetResponseSAPCSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="pcSubscriberId" type="pcSubscriberIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcTrafficId">
<xs:complexType>
<xs:sequence>
<xs:element name="pcTrafficId" type="pcTrafficIdType"/>
</xs:sequence>
<xs:attribute name="pcTrafficId" type="pcTrafficIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcTrafficIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcSubscribedService">
<xs:complexType>
<xs:sequence>
<xs:element name="pcSubscribedService" type="pcSubscribedServiceType"/>
</xs:sequence>
<xs:attribute name="pcSubscribedService" type="pcSubscribedServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscribedServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcBlacklistService">
<xs:complexType>
<xs:sequence>
<xs:element name="pcBlacklistService" type="pcBlacklistServiceType"/>
</xs:sequence>
<xs:attribute name="pcBlacklistService" type="pcBlacklistServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcBlacklistServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcFamilyId" type="pcFamilyIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="pcGroupId" type="pcGroupIdType"/>
<xs:element minOccurs="0" name="pcGroupPriority" type="pcGroupPriorityType"/>
<xs:choice minOccurs="0">
<xs:sequence>
<xs:element minOccurs="0" name="pcGroupStartDate" type="pcGroupStartDateType"/>
<xs:element minOccurs="0" name="pcGroupEndDate" type="pcGroupEndDateType"/>
</xs:sequence>
<xs:element minOccurs="0" name="pcGroupDurations">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="1" name="pcGroupDuration" type="pcGroupDurationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:choice>
<xs:element minOccurs="0" name="pcGroupInstancesContracted" type="pcGroupInstancesContractedType"/>
</xs:sequence>
<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcGroupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcNotificationData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationSMS">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationSMS" type="pcNotificationSMSType"/>
</xs:sequence>
<xs:attribute name="pcNotificationSMS" type="pcNotificationSMSType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationSMSAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationEmail">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationEmail" type="pcNotificationEmailType"/>
</xs:sequence>
<xs:attribute name="pcNotificationEmail" type="pcNotificationEmailType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationEmailAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcLimitUsage" type="pcLimitUsageType"/>
<xs:element minOccurs="0" name="pcAccumulatedData" type="pcAccumulatedDataType"/>
<xs:element minOccurs="0" name="pcSubcriberQualificationData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcQoSProfileId" type="pcQoSProfileIdType"/>
<xs:element minOccurs="0" name="pcContentFilteringProfileId" type="pcContentFilteringProfileIdType"/>
<xs:element minOccurs="0" name="pcCustomerId" type="pcCustomerIdType"/>
<xs:element minOccurs="0" name="pcRoutingProfileId" type="pcRoutingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfile">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileId" type="pcSubscriberChargingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileName" type="pcSubscriberChargingProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcBearerQoSProfileName">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcMaxBearerQoSProfileName" type="pcMaxBearerQoSProfileNameType"/>
<xs:element minOccurs="0" name="pcMinBearerQoSProfileName" type="pcMinBearerQoSProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcServicestoRedirect" type="pcServicestoRedirectType"/>
<xs:element minOccurs="0" name="pcGeneralProfileName" type="pcGeneralProfileNameType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingSystemName" type="pcSubscriberChargingSystemNameType"/>
<xs:element minOccurs="0" name="pcMpsProfileName" type="pcMpsProfileNameType"/>
<xs:element minOccurs="0" name="pcSpId" type="pcSpIdType"/>
<xs:element minOccurs="0" name="pcPresenceAreaName" type="pcPresenceAreaNameType"/>
<xs:element minOccurs="0" name="pcPdnGwName" type="pcPdnGwNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcEnableMasc" type="pcEnableMascType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcEventTriggers">
<xs:complexType>
<xs:sequence>
<xs:element name="pcEventTriggers" type="pcEventTriggersType"/>
</xs:sequence>
<xs:attribute name="pcEventTriggers" type="pcEventTriggersType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcEventTriggersAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcOperatorSpecificInfo">
<xs:complexType>
<xs:sequence>
<xs:element name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType"/>
</xs:sequence>
<xs:attribute name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcOperatorSpecificInfoAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcOngoingSession" type="pcOngoingSessionType"/>
<xs:element minOccurs="0" name="pcClosedSession" type="pcClosedSessionType"/>
</xs:sequence>
<xs:attribute name="pcSubscriberId" type="pcSubscriberIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscriberIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="SetSAPCSubscription">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcTrafficId" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcTrafficId" type="pcTrafficIdType"/>
</xs:sequence>
<xs:attribute name="pcTrafficId" type="pcTrafficIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcTrafficIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcTrafficId">
<xs:selector xpath="."/>
<xs:field xpath="@pcTrafficId"/>
</xs:key>
<xs:keyref name="keyref_set_pcTrafficId" refer="key_set_pcTrafficId">
<xs:selector xpath="./x:pcTrafficId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcSubscribedService" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcSubscribedService" type="pcSubscribedServiceType"/>
</xs:sequence>
<xs:attribute name="pcSubscribedService" type="pcSubscribedServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscribedServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcSubscribedService">
<xs:selector xpath="."/>
<xs:field xpath="@pcSubscribedService"/>
</xs:key>
<xs:keyref name="keyref_set_pcSubscribedService" refer="key_set_pcSubscribedService">
<xs:selector xpath="./x:pcSubscribedService"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcBlacklistService" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcBlacklistService" type="pcBlacklistServiceType"/>
</xs:sequence>
<xs:attribute name="pcBlacklistService" type="pcBlacklistServiceType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcBlacklistServiceAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcBlacklistService">
<xs:selector xpath="."/>
<xs:field xpath="@pcBlacklistService"/>
</xs:key>
<xs:keyref name="keyref_set_pcBlacklistService" refer="key_set_pcBlacklistService">
<xs:selector xpath="./x:pcBlacklistService"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="pcFamilyId" nillable="true" type="pcFamilyIdType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcGroup" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcGroupId" type="pcGroupIdType"/>
<xs:element minOccurs="0" name="pcGroupPriority" nillable="true" type="pcGroupPriorityType"/>
<xs:choice minOccurs="0">
<xs:sequence>
<xs:element minOccurs="0" name="pcGroupStartDate" nillable="true" type="pcGroupStartDateType"/>
<xs:element minOccurs="0" name="pcGroupEndDate" nillable="true" type="pcGroupEndDateType"/>
</xs:sequence>
<xs:element minOccurs="0" name="pcGroupDurations" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="1" name="pcGroupDuration" type="pcGroupDurationType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:choice>
<xs:element minOccurs="0" name="pcGroupInstancesContracted" nillable="true" type="pcGroupInstancesContractedType"/>
</xs:sequence>
<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcGroupIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcGroupId">
<xs:selector xpath="."/>
<xs:field xpath="@pcGroupId"/>
</xs:key>
<xs:keyref name="keyref_set_pcGroupId" refer="key_set_pcGroupId">
<xs:selector xpath="./x:pcGroupId"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element minOccurs="0" name="pcNotificationData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationSMS" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationSMS" type="pcNotificationSMSType"/>
</xs:sequence>
<xs:attribute name="pcNotificationSMS" type="pcNotificationSMSType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationSMSAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcNotificationSMS">
<xs:selector xpath="."/>
<xs:field xpath="@pcNotificationSMS"/>
</xs:key>
<xs:keyref name="keyref_set_pcNotificationSMS" refer="key_set_pcNotificationSMS">
<xs:selector xpath="./x:pcNotificationSMS"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcNotificationEmail" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcNotificationEmail" type="pcNotificationEmailType"/>
</xs:sequence>
<xs:attribute name="pcNotificationEmail" type="pcNotificationEmailType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcNotificationEmailAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcNotificationEmail">
<xs:selector xpath="."/>
<xs:field xpath="@pcNotificationEmail"/>
</xs:key>
<xs:keyref name="keyref_set_pcNotificationEmail" refer="key_set_pcNotificationEmail">
<xs:selector xpath="./x:pcNotificationEmail"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcLimitUsage" nillable="true" type="pcLimitUsageType"/>
<xs:element minOccurs="0" name="pcAccumulatedData" nillable="true" type="pcAccumulatedDataType"/>
<xs:element minOccurs="0" name="pcSubcriberQualificationData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcQoSProfileId" nillable="true" type="pcQoSProfileIdType"/>
<xs:element minOccurs="0" name="pcContentFilteringProfileId" nillable="true" type="pcContentFilteringProfileIdType"/>
<xs:element minOccurs="0" name="pcCustomerId" nillable="true" type="pcCustomerIdType"/>
<xs:element minOccurs="0" name="pcRoutingProfileId" nillable="true" type="pcRoutingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfile">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileId" nillable="true" type="pcSubscriberChargingProfileIdType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingProfileName" nillable="true" type="pcSubscriberChargingProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcBearerQoSProfileName">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pcMaxBearerQoSProfileName" nillable="true" type="pcMaxBearerQoSProfileNameType"/>
<xs:element minOccurs="0" name="pcMinBearerQoSProfileName" nillable="true" type="pcMinBearerQoSProfileNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcServicestoRedirect" nillable="true" type="pcServicestoRedirectType"/>
<xs:element minOccurs="0" name="pcGeneralProfileName" nillable="true" type="pcGeneralProfileNameType"/>
<xs:element minOccurs="0" name="pcSubscriberChargingSystemName" nillable="true" type="pcSubscriberChargingSystemNameType"/>
<xs:element minOccurs="0" name="pcMpsProfileName" nillable="true" type="pcMpsProfileNameType"/>
<xs:element minOccurs="0" name="pcSpId" nillable="true" type="pcSpIdType"/>
<xs:element minOccurs="0" name="pcPresenceAreaName" nillable="true" type="pcPresenceAreaNameType"/>
<xs:element minOccurs="0" name="pcPdnGwName" nillable="true" type="pcPdnGwNameType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="pcEnableMasc" nillable="true" type="pcEnableMascType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcEventTriggers" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcEventTriggers" type="pcEventTriggersType"/>
</xs:sequence>
<xs:attribute name="pcEventTriggers" type="pcEventTriggersType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcEventTriggersAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcEventTriggers">
<xs:selector xpath="."/>
<xs:field xpath="@pcEventTriggers"/>
</xs:key>
<xs:keyref name="keyref_set_pcEventTriggers" refer="key_set_pcEventTriggers">
<xs:selector xpath="./x:pcEventTriggers"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pcOperatorSpecificInfo" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType"/>
</xs:sequence>
<xs:attribute name="pcOperatorSpecificInfo" type="pcOperatorSpecificInfoDataType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcOperatorSpecificInfoAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="pcSubscriberId" type="pcSubscriberIdType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pcSubscriberIdAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_pcSubscriberId">
<xs:selector xpath="."/>
<xs:field xpath="@pcSubscriberId"/>
</xs:key>
</xs:element>
<xs:element name="DeleteSAPCSubscription">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
