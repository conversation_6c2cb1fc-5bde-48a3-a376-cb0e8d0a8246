<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="pdpid" type="pdpidType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateSubscriberPDPContext">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
<xs:element name="eqosid" type="eqosidType"/>
<xs:choice>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
</xs:choice>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element minOccurs="0" name="vpaa" type="vpaaType"/>
<xs:element minOccurs="0" name="pdpid" type="pdpidType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="SetSubscriberPDPContext">
<xs:complexType>
<xs:sequence>
<xs:element name="pdpid" type="pdpidType"/>
<xs:choice>
<xs:choice>
<xs:sequence>
<xs:element name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
<xs:choice>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
</xs:choice>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element minOccurs="0" name="vpaa" type="vpaaType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="epdpadd" type="epdpaddType"/>
</xs:sequence>
</xs:choice>
<xs:choice>
<xs:sequence>
<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
<xs:choice>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
</xs:choice>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element minOccurs="0" name="vpaa" type="vpaaType"/>
</xs:sequence>
</xs:choice>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetSubscriberPDPContext">
<xs:complexType>
<xs:sequence>
<xs:element name="SubscriberPDPContextData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="pdpcp" type="pdpcpType"/>
<xs:element maxOccurs="unbounded" name="PDPContext">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element name="eqosid" type="eqosidType"/>
<xs:element name="vpaa" type="vpaaType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
<xs:element name="pdpid" type="pdpidType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DeleteSubscriberPDPContext">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
