<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="setCommunicationID">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="msisdnCurrent" type="msisdnCurrentType"/>
<xs:element minOccurs="0" name="msisdnCurrentNAI" type="msisdnCurrentNAIType"/>
<xs:element minOccurs="0" name="msisdnNew" type="msisdnNewType"/>
<xs:element minOccurs="0" name="msisdnNewNAI" type="msisdnNewNAIType"/>
<xs:element minOccurs="0" name="imsiCurrent" type="imsiCurrentType"/>
<xs:element minOccurs="0" name="imsiNew" type="imsiNewType"/>
<xs:element minOccurs="0" name="naiCurrent" type="naiCurrentType"/>
<xs:element minOccurs="0" name="naiNew" type="naiNewType"/>
<xs:element minOccurs="0" name="sipUriCurrent" type="sipUriCurrentType"/>
<xs:element minOccurs="0" name="sipUriNew" type="sipUriNewType"/>
<xs:element minOccurs="0" name="privateCurrent" type="privateCurrentType"/>
<xs:element minOccurs="0" name="privateNew" type="privateNewType"/>
<xs:choice minOccurs="0">
<xs:element name="chargingInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="chargingIndicator" type="chargingIndicatorType"/>
<xs:element minOccurs="0" name="specifiedPrice" type="specifiedPriceType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="suppressDeduction" type="suppressDeductionType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="externalContract" type="externalContractType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setCommunicationIDResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="cleanupDate" type="cleanupDateType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="deductionSuppressed" type="deductionSuppressedType"/>
<xs:element minOccurs="0" name="chargingResultInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="chargingResultCode" type="chargingResultCodeType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="identifierType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="typeType">
<xs:restriction base="xs:string">
<xs:pattern value="imsi"/>
<xs:pattern value="private"/>
<xs:pattern value="nai"/>
<xs:pattern value="sipuri"/>
<xs:pattern value="msisdn"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originHostNameType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTimeStampType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
<xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="msisdnCurrentType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="msisdnCurrentNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="msisdnNewType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="msisdnNewNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiCurrentType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiNewType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="naiCurrentType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="naiNewType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="sipUriCurrentType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="sipUriNewType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="privateCurrentType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="privateNewType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="specifiedPriceType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCurrencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[a-zA-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="suppressDeductionType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="externalContractType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="cleanupDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="deductionSuppressedType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="costType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[a-zA-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingResultCodeType">
<xs:restriction base="xs:integer"/>
</xs:simpleType>
<xs:simpleType name="reservationCorrelationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2147483647"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
