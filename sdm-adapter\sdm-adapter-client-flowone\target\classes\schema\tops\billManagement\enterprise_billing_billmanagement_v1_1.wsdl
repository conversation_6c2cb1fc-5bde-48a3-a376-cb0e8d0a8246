<!-- 
Build_Label: REL000
ClearQuest_MR#: 12121 
Build_Date: 2017-01-26-15:55:27
--><wsdl:definitions name="enterprise_billmanagement" targetNamespace="http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/encoding/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:enterprise_messageheader_xsd="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types" xmlns:enterprise_billing_billmanagement_xsd="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" xmlns:enterprise_billmanagement="http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0" xmlns:enterprise_billmanagement_messages="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/messages">
	<wsdl:documentation>BillManagement service provides :ViewPDFBill and getBillList operations </wsdl:documentation>
	<wsdl:types>
		<xsd:schema elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_1" targetNamespace="http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0">
			<xsd:import namespace="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/messages" schemaLocation="enterprise_billing_billmanagement_messages_v1_1.xsd"/>
		    <xsd:import namespace="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" schemaLocation="enterprise_billing_billmanagement_types_v1_1.xsd"/>
		    <xsd:import namespace="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types" schemaLocation="enterprise_messageheader_types_v1_0.xsd"/>
		</xsd:schema>
	</wsdl:types>
	
	<wsdl:message name="BillManagementExceptionMsg">
      <wsdl:documentation>Bill management exception extension based on USCC fault definition.</wsdl:documentation>
      <wsdl:part name="BillManagementException" element="enterprise_billing_billmanagement_xsd:BillManagementException">
      	<wsdl:documentation>Bill management exception extension based on USCC fault definition.</wsdl:documentation>
      </wsdl:part>
   </wsdl:message>
		
	<wsdl:message name="BillManagement_v1_0_getBillPdf_RequestMsg">
			<wsdl:part name="parameters" element="enterprise_billmanagement_messages:getBillPdf_Request">
			<documentation>Request message for operation getBillPdf().</documentation>
			</wsdl:part>
		<wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			<wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		</wsdl:part>
	</wsdl:message>
	<wsdl:message name="BillManagement_v1_0_getBillPdf_ResponseMsg">
	
		<wsdl:part name="result" element="enterprise_billmanagement_messages:getBillPdf_Response">                                                                                                                                                                                                                                                                                                                                                                                                                                    
		<documentation>Response message for operation getBillPdf().</documentation>
		</wsdl:part>
		<wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			<wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		</wsdl:part>
	</wsdl:message>
	
	<wsdl:message name="BillManagement_v1_0_getBillList_RequestMsg">
	
		<wsdl:part name="parameters" element="enterprise_billmanagement_messages:getBillList_Request">
		<documentation>Request message for operation getBillList().</documentation>
		</wsdl:part>
		<wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			<wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		</wsdl:part>
	</wsdl:message>
	
	<wsdl:message name="BillManagement_v1_0_getBillList_ResponseMsg">
	
		<wsdl:part name="result" element="enterprise_billmanagement_messages:getBillList_Response">
		<documentation>Response message for operation getBillList()</documentation>
		</wsdl:part>
		<wsdl:part name="messageHeader" element="enterprise_messageheader_xsd:MessageHeader">
			<wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		</wsdl:part>
	</wsdl:message>
	
	<wsdl:portType name="BillManagement_v1_0_Port">
		<wsdl:documentation>Defines portType for BillManagement service.</wsdl:documentation>
		<wsdl:operation name="getBillPdf">
			<wsdl:documentation>Operation to request pdf bill details</wsdl:documentation>
			<wsdl:input name="getBillRequest" message="enterprise_billmanagement:BillManagement_v1_0_getBillPdf_RequestMsg"/>
			<wsdl:output name="getBillResponse" message="enterprise_billmanagement:BillManagement_v1_0_getBillPdf_ResponseMsg"/>
			 <wsdl:fault message="enterprise_billmanagement:BillManagementExceptionMsg" name="BillManagementExceptionMsg"/>
		</wsdl:operation>
		
		<wsdl:operation name="getBillList">
			<wsdl:documentation>Operation to request bill Document list details</wsdl:documentation>
			<wsdl:input name="getBillListRequest" message="enterprise_billmanagement:BillManagement_v1_0_getBillList_RequestMsg"/>
		 	<wsdl:output name="getBillListResponse" message="enterprise_billmanagement:BillManagement_v1_0_getBillList_ResponseMsg"/>
		 	 <wsdl:fault message="enterprise_billmanagement:BillManagementExceptionMsg" name="BillManagementExceptionMsg"/> 
		</wsdl:operation>
	</wsdl:portType>
	
	<wsdl:binding name="BillManagement_v1_0_Binding" type="enterprise_billmanagement:BillManagement_v1_0_Port">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="getBillPdf">
			<wsdl:documentation>Operation to request pdf bill details.</wsdl:documentation>
			<soap:operation soapAction="http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillPdf" style="document"/>
			<wsdl:input name="getBillRequest">
				<soap:body parts="parameters" use="literal"/>
				<soap:header message="enterprise_billmanagement:BillManagement_v1_0_getBillPdf_RequestMsg" part="messageHeader" use="literal"/>
			</wsdl:input>
	 	<wsdl:output name="getBillResponse">
				<soap:body parts="result" use="literal"/>
				<soap:header message="enterprise_billmanagement:BillManagement_v1_0_getBillPdf_ResponseMsg" part="messageHeader" use="literal"/>
			</wsdl:output>  
			<wsdl:fault name="BillManagementExceptionMsg">
				<soap:fault name="BillManagementExceptionMsg" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		
		<wsdl:operation name="getBillList">
			<wsdl:documentation>Operation to request Document list details.</wsdl:documentation>
			<soap:operation soapAction="http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillList" style="document"/>
			<wsdl:input name="getBillListRequest">
				<soap:body parts="parameters" use="literal"/>
				<soap:header message="enterprise_billmanagement:BillManagement_v1_0_getBillList_RequestMsg" part="messageHeader" use="literal"/>
			</wsdl:input>
	 		<wsdl:output name="getBillListResponse">
				<soap:body parts="result" use="literal"/>
				<soap:header message="enterprise_billmanagement:BillManagement_v1_0_getBillList_ResponseMsg" part="messageHeader" use="literal"/>
			</wsdl:output> 
			<wsdl:fault name="BillManagementExceptionMsg">
				<soap:fault name="BillManagementExceptionMsg" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		</wsdl:binding>
	
	<wsdl:service name="BillManagement_v1_0">
		<wsdl:documentation>SOAP over HTTP Web Service Binding for Interface BillManagement_v1_1</wsdl:documentation>
		<wsdl:port name="BillManagement_v1_0_Port" binding="enterprise_billmanagement:BillManagement_v1_0_Binding">
			<soap:address location="http://localhost:7001/enterprise/billing/billmanagement/v1_1"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>