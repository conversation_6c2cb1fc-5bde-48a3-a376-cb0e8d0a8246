<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified">

	<!-- Type definitions -->
	<xs:simpleType name="hlrfeidType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:simpleType name="primaryhlridType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[1-15]-[1-32]" />
			<xs:maxLength value="5" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="onsaType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="255"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="nsType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="19" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="charType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="rsaType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="raidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="4096" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="srrType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="0" />
			<xs:enumeration value="1" />
			<xs:enumeration value="2" />
			<xs:enumeration value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="rspType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="rsipType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="4096" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

</xs:schema>
