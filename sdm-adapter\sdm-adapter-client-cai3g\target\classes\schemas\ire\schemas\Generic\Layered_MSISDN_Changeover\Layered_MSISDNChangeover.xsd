<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:cudb="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/">
<xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../2G_3G/Deprecated-Layered_HLR/types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="hlr:msisdnType"/>
<xs:element name="imsi" type="hlr:imsiType"/>
<xs:element name="PrimaryHLRId" type="hlr:primaryhlridType"/>
<xs:element name="CreateMSISDNChangeover">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="hlr:msisdnType"/>
<xs:element name="imsi" type="hlr:imsiType"/>
</xs:choice>
<xs:element name="nmsisdn" type="hlr:msisdnType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="hlr:msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="hlr:imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_imsi">
<xs:selector xpath="./cudb:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:key name="key_msisdn">
<xs:selector xpath="./cudb:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
</xs:schema>
