<!-- <PERSON><PERSON>, SearchOrder - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/EIR/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<!-- CreateSearchOrder MOId: imsiPrefix MOType: CreateSearchOrder@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="imsiPrefix" type="imsiPrefixType" />
	<xs:element name="CreateSearchOrder">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsiPrefix" type="imsiPrefixType" />
				<xs:element name="unknownResponse" type="responseType" />
				<xs:element name="SearchOrderListEntry" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="searchOrderListNumber" type="searchOrderListNumberType" />
							<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
							<xs:element name="response" type="responseType" />
						</xs:sequence>
						<xs:attribute name="searchOrderListNumber" type="searchOrderListNumberType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="searchOrderListNumberAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_searchOrderListNumber">
						<xs:selector xpath="./x:searchOrderListNumber" />
						<xs:field xpath="." />
					</xs:key>
					<xs:keyref name="keyref_create_searchOrderListNumber" refer="key_create_searchOrderListNumber">
						<xs:selector xpath="." />
						<xs:field xpath="@searchOrderListNumber" />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsiPrefix" type="imsiPrefixType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiPrefixAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imsiPrefix">
			<xs:selector xpath="./x:imsiPrefix" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imsiPrefix" refer="key_imsiPrefix">
			<xs:selector xpath="." />
			<xs:field xpath="@imsiPrefix" />
		</xs:keyref>
	</xs:element>
	<!-- SetSearchOrder MOId: imsiPrefix MOType: SetSearchOrder@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="SetSearchOrder">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="unknownResponse" type="responseType" />
				<xs:element name="SearchOrderListEntry" nillable="true" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="searchOrderListNumber" type="searchOrderListNumberType" />
							<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
							<xs:element name="response" type="responseType" />
						</xs:sequence>
						<xs:attribute name="searchOrderListNumber" type="searchOrderListNumberType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="searchOrderListNumberAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_searchOrderListNumber">
						<xs:selector xpath="." />
						<xs:field xpath="@searchOrderListNumber" />
					</xs:key>
					<xs:keyref name="keyref_set_searchOrderListNumber" refer="key_set_searchOrderListNumber">
						<xs:selector xpath="./x:searchOrderListNumber" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsiPrefix" type="imsiPrefixType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiPrefixAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- DeleteSearchOrder MOId: imsiPrefix MOType: SearchOrder@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetSearchOrder MOId: imsiPrefix MOType: SearchOrder@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetResponseSearchOrder MOId: imsiPrefix MOType: SearchOrder@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="GetResponseSearchOrder">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsiPrefix" type="imsiPrefixType" />
				<xs:element name="unknownResponse" type="responseType" />
				<xs:element name="SearchOrderListEntry" minOccurs="0" maxOccurs="10">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="searchOrderListNumber" type="searchOrderListNumberType" />
							<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
							<xs:element name="response" type="responseType" />
						</xs:sequence>
						<xs:attribute name="searchOrderListNumber" type="searchOrderListNumberType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="searchOrderListNumberAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsiPrefix" type="imsiPrefixType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiPrefixAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
