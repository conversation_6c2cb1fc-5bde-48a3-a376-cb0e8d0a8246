<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:cudb="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateMSISDNChangeover">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
<xs:element name="nmsisdn" type="msisdnType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
<xs:key name="key_msisdn">
<xs:selector xpath="./hlr:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="key_imsi">
<xs:selector xpath="./hlr:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
</xs:schema>
