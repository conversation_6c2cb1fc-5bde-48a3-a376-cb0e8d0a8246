package com.nokia.wing.wdh.sdmadapter.client.neo.service.shared.builder;

import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ArrayOfCharacteristicValue;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ServiceOrderValue;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ServiceOrderKey;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ResubmitModeType;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.OperationType;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.PrimaryKeyElementFactory;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.RequestContext;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.NeoConstants;

// Generated OSSJ Standard Classes
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement.CreateAndStartRequestByValueRequest;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement.ArrayOfServiceOrderItemValue;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement.ObjectFactory;

// Use Amdocs ServiceOrderItemValue (extends OSSJ version and has setSubAction method)
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ServiceOrderItemValue;

// Generated Amdocs Core Classes
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.common.ManagedEntityKey;

// Generated Amdocs CBE Service Classes
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeservice.ServiceValue;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeservice.ServiceKey;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.common.ActivationTargetValue;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.common.ArrayOfActivationTargetValue;

// OSSJ Standard Characteristics Classes (for describedBy field)
//import org.ossj.xml.common_cbecore.v1_5.ArrayOfCharacteristicValue;

// Amdocs Characteristics Classes (for individual characteristics)
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.common.CharacteristicValue;

import jakarta.xml.bind.JAXBElement;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.UUID;

/**
 * Abstract base class for request builders
 *
 * Contains common logic for building OSSJ service order requests.
 */
public abstract class AbstractRequestBuilder implements RequestBuilder {

    private static final Logger log = LoggerFactory.getLogger(AbstractRequestBuilder.class);

    private final OperationType supportedOperationType;

    /**
     * Constructor for AbstractRequestBuilder
     *
     * @param supportedOperationType The operation type this builder supports
     */
    protected AbstractRequestBuilder(OperationType supportedOperationType) {
        this.supportedOperationType = supportedOperationType;
    }

    @Override
    public CreateAndStartRequestByValueRequest buildRequest(RequestContext context,
                                                          List<CharacteristicValue> characteristics) {
        log.debug("Building request for operation: {}", context.getOperationType());

        if (!supports(context.getOperationType())) {
            throw new IllegalArgumentException("Builder does not support operation type: " + context.getOperationType());
        }

        return createRequest(context, characteristics);
    }

    @Override
    public boolean supports(OperationType operationType) {
        return supportedOperationType == operationType;
    }

    @Override
    public OperationType getSupportedOperationType() {
        return supportedOperationType;
    }

    /**
     * Create complete OSSJ service order request
     *
     * @param context The request context
     * @param characteristics List of characteristics
     * @return Complete CreateAndStartRequestByValueRequest
     */
    protected CreateAndStartRequestByValueRequest createRequest(RequestContext context,
                                                              List<CharacteristicValue> characteristics) {

        String action = context.getOperationType().getAction();
        int priority = context.getPriority();
        String servicePrimaryKey = context.getServicePrimaryKey();

        log.info("Creating {} service order request with priority {}", action, priority);

        CreateAndStartRequestByValueRequest request = new CreateAndStartRequestByValueRequest();
        ObjectFactory objectFactory = new ObjectFactory();

        // Create Amdocs ServiceOrderValue
        ServiceOrderValue serviceOrder = new ServiceOrderValue();

        // Set Service Order Key with Primary Key
        ServiceOrderKey serviceOrderKey = new ServiceOrderKey();
        serviceOrderKey.setType(getServiceOrderKeyType(context)); // Use template method

        // Generate primary key using template method
        ManagedEntityKey.PrimaryKey primaryKey = generateServiceOrderPrimaryKey(context);
        serviceOrderKey.setPrimaryKey(primaryKey);

        serviceOrder.setKey(serviceOrderKey);
        serviceOrder.setOperator(getOperator(context)); // Use template method

        // Set resubmitMode for service order using template method
        serviceOrder.setResubmitMode(getResubmitMode(context)); // Use template method

        // Set priority using JAXBElement
        com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ObjectFactory amdocsObjectFactory =
            new com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement.ObjectFactory();
        jakarta.xml.bind.JAXBElement<Integer> priorityElement = amdocsObjectFactory.createPriorityRequest(priority);
        serviceOrder.setBasePriorityRequest(priorityElement);

        // Create Service Order Items using OSSJ array (compatible with OSSJ ObjectFactory)
        // but add Amdocs ServiceOrderItemValue (which extends OSSJ version)
        ArrayOfServiceOrderItemValue serviceOrderItems = new ArrayOfServiceOrderItemValue();
        ServiceOrderItemValue serviceOrderItem = createServiceOrderItem(context, characteristics);
        serviceOrderItems.getItem().add(serviceOrderItem);

        // Wrap in JAXBElement
        JAXBElement<ArrayOfServiceOrderItemValue> serviceOrderItemsElement =
            objectFactory.createServiceOrderValueServiceOrderItems(serviceOrderItems);
        serviceOrder.setServiceOrderItems(serviceOrderItemsElement);

        // Allow operation-specific customization of the service order
        customizeServiceOrder(serviceOrder, context);

        request.setRequestValue(serviceOrder);

        log.info("Request created successfully for {} operation with primary key: {}", action,
                PrimaryKeyElementFactory.extractPrimaryKeyValue(primaryKey));
        return request;
    }

    /**
     * Create Service Order Item for any action
     *
     * Extracted from original RequestFactory.createServiceOrderItem() method
     *
     * @param context The request context
     * @param characteristics List of characteristics
     * @return ServiceOrderItemValue
     */
    protected ServiceOrderItemValue createServiceOrderItem(RequestContext context,
                                                          List<CharacteristicValue> characteristics) {

        String action = context.getOperationType().getAction();
        String servicePrimaryKey = context.getServicePrimaryKey();

        log.info("Creating service order item with action: {}", action);

        ServiceOrderItemValue serviceOrderItem = new ServiceOrderItemValue();

        // Set action using correct ObjectFactory
        com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ObjectFactory biObjectFactory =
            new com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ObjectFactory();
        JAXBElement<String> actionElement = biObjectFactory.createBusinessInteractionItemValueAction(action);
        serviceOrderItem.setAction(actionElement);

        // Create Service Value with characteristics
        ServiceValue service = createServiceWithCharacteristics(characteristics, servicePrimaryKey, context);

        // Wrap service in JAXBElement using correct ObjectFactory
        ObjectFactory objectFactory = new ObjectFactory();
        JAXBElement<com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ServiceValue> serviceElement =
            objectFactory.createServiceOrderItemValueService(service);
        serviceOrderItem.setService(serviceElement);

        // Set subAction using template method (directly as String, not JAXBElement)
        serviceOrderItem.setSubAction(getSubAction(context)); // Use template method

        // Allow operation-specific customization of the service order item
        customizeServiceOrderItem(serviceOrderItem, context);

        log.info("Service order item created with action: {}", action);
        return serviceOrderItem;
    }

    // ========================================
    // TEMPLATE METHODS FOR OPERATION-SPECIFIC CUSTOMIZATION
    // ========================================


    /**
     * Get the resubmit mode for service orders
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different resubmit modes for different operations.
     *
     * @param context The request context
     * @return The resubmit mode to use
     */
    protected ResubmitModeType getResubmitMode(RequestContext context) {
        return ResubmitModeType.NONE; // Default for most operations
    }

    /**
     * Get the sub-action for service order items
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different sub-actions for different operations.
     *
     * @param context The request context
     * @return The sub-action to use
     */
    protected String getSubAction(RequestContext context) {
        return NeoConstants.SUB_ACTION_EMPTY; // Default for most operations
    }

    /**
     * Get the service type for services
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different service types for different operations.
     *
     * @param context The request context
     * @return The service type to use
     */
    protected String getServiceType(RequestContext context) {
        return NeoConstants.SERVICE_TYPE_MOBILITY; // Default for most operations
    }

    /**
     * Get the activation target ID for services
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different activation target IDs for different operations.
     *
     * @param context The request context
     * @return The activation target ID to use
     */
    protected String getActivationTargetId(RequestContext context) {
        return NeoConstants.ACTIVATION_TARGET_ID; // Default for most operations
    }

    /**
     * Get the operator for service orders
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different operators for different operations.
     *
     * @param context The request context
     * @return The operator to use
     */
    protected String getOperator(RequestContext context) {
        return NeoConstants.OPERATOR_ACM; // Default for most operations
    }

    /**
     * Get the service order type for service keys
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different service order types for different operations.
     *
     * @param context The request context
     * @return The service order type to use
     */
    protected String getServiceOrderType(RequestContext context) {
        return NeoConstants.SERVICE_ORDER_TYPE; // Default for most operations
    }

    /**
     * Get the service order key type for service order keys
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different service order key types for different operations.
     *
     * @param context The request context
     * @return The service order key type to use
     */
    protected String getServiceOrderKeyType(RequestContext context) {
        return NeoConstants.SERVICE_ORDER_TYPE; // Default for most operations
    }

    /**
     * Generate the primary key for service order keys
     *
     * Template method that can be overridden by operation-specific builders
     * to provide different primary key generation strategies for different operations.
     *
     * The default implementation generates a UUID-based primary key using the format:
     * {ACTION}-{UUID} where ACTION is the operation action (e.g., "PROVIDE-AIA", "cease", etc.)
     *
     * @param context The request context (contains operation type and action)
     * @return The primary key to use for the service order
     */
    protected ManagedEntityKey.PrimaryKey generateServiceOrderPrimaryKey(RequestContext context) {
        // Default implementation: Keep existing UUID-based logic
        String action = context.getOperationType().getAction();
        String uuid = UUID.randomUUID().toString();
        String orderPrimaryKey = action + "-" + uuid;

        log.info("Generated order primary key: {}", orderPrimaryKey);

        return PrimaryKeyElementFactory.createPrimaryKey(orderPrimaryKey);
    }

    // ========================================
    // HOOK METHODS FOR ADDITIONAL CUSTOMIZATION
    // ========================================

    /**
     * Hook method for customizing service orders
     *
     * This method is called after the basic service order is created and configured.
     * Operation-specific builders can override this method to add additional
     * customizations to the service order.
     *
     * @param serviceOrder The service order to customize
     * @param context The request context
     */
    protected void customizeServiceOrder(ServiceOrderValue serviceOrder, RequestContext context) {
        // Default implementation: no additional customization
        // Operation-specific builders can override this method
    }

    /**
     * Hook method for customizing service order items
     *
     * This method is called after the basic service order item is created and configured.
     * Operation-specific builders can override this method to add additional
     * customizations to the service order item.
     *
     * @param serviceOrderItem The service order item to customize
     * @param context The request context
     */
    protected void customizeServiceOrderItem(ServiceOrderItemValue serviceOrderItem, RequestContext context) {
        // Default implementation: no additional customization
        // Operation-specific builders can override this method
    }

    /**
     * Hook method for customizing services
     *
     * This method is called after the basic service is created and configured.
     * Operation-specific builders can override this method to add additional
     * customizations to the service.
     *
     * @param service The service to customize
     * @param context The request context
     */
    protected void customizeService(ServiceValue service, RequestContext context) {
        // Default implementation: no additional customization
        // Operation-specific builders can override this method
    }

    /**
     * Create Service with Characteristics
     *
     * Extracted from original RequestFactory.createServiceWithCharacteristics() method
     *
     * @param characteristics List of characteristics
     * @param servicePrimaryKey The service primary key
     * @param context The request context
     * @return ServiceValue
     */
    protected ServiceValue createServiceWithCharacteristics(List<CharacteristicValue> characteristics,
                                                           String servicePrimaryKey, RequestContext context) {

        log.info("Creating service with {} characteristics", characteristics.size());

        ServiceValue service = new ServiceValue();

        // Set Service Key
        ServiceKey serviceKey = new ServiceKey();
        serviceKey.setType(getServiceOrderType(context)); // Use template method

        log.info("Using service primary key: {}", servicePrimaryKey);

        // For Amdocs ServiceKey, use setServicePrimaryKey method
        serviceKey.setServicePrimaryKey(servicePrimaryKey);

        service.setKey(serviceKey);
        service.setServiceType(getServiceType(context)); // Use template method

        // Create service characteristics array
        ArrayOfCharacteristicValue characteristicsArray = new ArrayOfCharacteristicValue();
        for (CharacteristicValue characteristic : characteristics) {
            characteristicsArray.getItem().add(characteristic);
        }

        // Set characteristics using JAXBElement
        com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ObjectFactory coreObjectFactory =
            new com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ObjectFactory();
        JAXBElement<ArrayOfCharacteristicValue> characteristicsElement =
            coreObjectFactory.createCBEManagedEntityValueDescribedBy(characteristicsArray);
        service.setDescribedBy(characteristicsElement);

        // Set activation targets
        ArrayOfActivationTargetValue activationTargets = new ArrayOfActivationTargetValue();
        ActivationTargetValue activationTarget = new ActivationTargetValue();
        activationTarget.setId(getActivationTargetId(context)); // Use template method
        activationTargets.getItem().add(activationTarget);
        service.setActivationTargets(activationTargets);

        // Allow operation-specific customization of the service
        customizeService(service, context);

        log.info("====>Service created with {} characteristics", characteristics.size());
        return service;
    }
}
