<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="profile" type="profileType"/>
<xs:element minOccurs="0" name="lmu" type="lmuType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="GetSubscription">
<xs:complexType>
<xs:sequence>
<xs:element name="SubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:element name="state" type="stateType"/>
<xs:element minOccurs="0" name="authd" type="authdType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DeleteSubscription">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
