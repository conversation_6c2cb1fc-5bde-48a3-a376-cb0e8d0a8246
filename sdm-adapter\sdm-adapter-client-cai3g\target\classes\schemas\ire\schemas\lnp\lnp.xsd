<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns="http://schemas.ericsson.com/ma/ECE/"
	xmlns:ns="http://schemas.ericsson.com/ma/ECE/" targetNamespace="http://schemas.ericsson.com/ma/ECE/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{5,15}" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="msisdn" type="msisdnType"></xs:element>

	<xs:simpleType name="subTypeType">
		<xs:restriction base="xs:string">
			<!-- <xs:enumeration value="HOME" /> -->
			<xs:enumeration value="EXPORTED" />
			<xs:enumeration value="IMPORTED" />
			<xs:enumeration value="OTHER" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="NPrefixType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,5}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SetLocalNumberPortability">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="NPrefix" type="NPrefixType" minOccurs="1" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="required" />
		</xs:complexType>
	</xs:element>

	<xs:element name="GetLocalNumberPortability">
		<xs:complexType>
			<xs:attribute name="msisdn" type="msisdnType" use="required" />
		</xs:complexType>
	</xs:element>

	<xs:element name="DeleteLocalNumberPortability">
		<xs:complexType>
			<xs:attribute name="msisdn" type="msisdnType" use="required" />
		</xs:complexType>
	</xs:element>

	<xs:element name="GetResponseLocalNumberPortability">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0" />
				<xs:element name="subType" type="subTypeType" minOccurs="0" />
				<xs:element name="NPrefix" type="NPrefixType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

</xs:schema>