<?xml version="1.0" encoding="UTF-8" ?>
<!--
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR <PERSON><PERSON><PERSON><PERSON>D
CONDITIONS, <PERSON><PERSON><PERSON><PERSON><PERSON>ATIONS AND <PERSON><PERSON><PERSON><PERSON>IE<PERSON>, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema xmlns="http://www.w3.org/2001/XMLSchema" 
        xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" 
        version="v1-5" elementFormDefault="qualified" 
        targetNamespace="http://ossj.org/xml/Common/v1-5">
    <annotation>
        <documentation>
            This document provides the definitions of the common set of components
            used as base for any domin specific extensions.
            It also provides the basic Request/Response/Exception definitions to
            get entity types and to query or update entities.
        </documentation>
    </annotation>
    <!--=======================================================-->
    <complexType name="ApplicationContext">

        <annotation>
            <documentation>
                The ApplicationContext interface contains the URL and
                other system properties required to set up an initial
                connection with the JNDI provider into which the
                components in charge of that managed entity are registered.
                
                Security Credentials are not passed.
            </documentation>
        </annotation>
        <sequence>
            <element name="factoryClass" type="string" minOccurs="0" nillable="true"/>
            <element name="URL" type="string" minOccurs="0" nillable="true"/>
            <element name="systemProperties" type="co-v1-5:ArrayOfSystemProperties" minOccurs="0" nillable="true"/>

        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="EventPropertyDescriptor" abstract="true">
        <annotation>
            <documentation>
                The EventPropertyDescriptor is used to document and
                specify the filterable properties of an event.
                
                An Interface extending the base EventPropertyDescriptor
                interface must be defined for each type of application
                specific event emitted by a component.
                
                The name of the specific EventPropertyDescriptor
                interface should be &lt;EventType&gt; EventPropertyDescriptor
                where the &lt;EventType&gt; is the name of the event type
                interface.
                
                Example:
                - NotifyAckStateChangedEvent
                - NotifyAckStateChangedEventEventPropertyDescriptor
            </documentation>

        </annotation>
        <sequence>
            <element name="eventType" type="string" />
            <element name="propertyNames" type="co-v1-5:ArrayOfString" />
            <element name="propertyTypes" type="co-v1-5:ArrayOfString" />
        </sequence>
    </complexType>
    <annotation>
        <documentation>

            Get the names of the optional operations supported by this JVT
            Session Bean. The names of the optional operations are defined
            in the JVT&lt;ApplicationType&gt;SessionOptionalOps interface as
            defined by the API.
        </documentation>
    </annotation>
    <element name="getSupportedOptionalOperationsRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="getSupportedOptionalOperationsResponse">
        <complexType>
            <sequence>
                <element name="strings" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getSupportedOptionalOperationsException">
        <complexType>

            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>
        <documentation>
            Get the Managed Entity types supported by a JVT Session Bean.
        </documentation>

    </annotation>
    <element name="getManagedEntityTypesRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>
    <element name="getManagedEntityTypesResponse">
        <complexType>
            <sequence>

                <element name="strings" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getManagedEntityTypesException">
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>

        </complexType>
    </element>
    <annotation>
        <documentation>
            Get the Event Type names supported by the JVT Session Bean
        </documentation>
    </annotation>
    <element name="getEventTypesRequest">
        <complexType>

            <sequence />
        </complexType>
    </element>
    <element name="getEventTypesResponse">
        <complexType>
            <sequence>
                <element name="strings" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>

    </element>
    <element name="getEventTypesException">
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>

        <documentation>
            Get the EventPropertyDescriptor associated with an event type name.
        </documentation>
    </annotation>
    <element name="getEventDescriptorRequest">
        <complexType>
            <sequence>
                <element name="eventType" type="string" />
            </sequence>

        </complexType>
    </element>
    <element name="getEventDescriptorResponse">
        <complexType>
            <sequence>
                <element name="eventPropertyDescriptor" type="co-v1-5:EventPropertyDescriptor" />
            </sequence>
        </complexType>
    </element>

    <element name="getEventDescriptorException">
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException" />
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>

        <documentation>
            Executes the given update procedure. (null is not a vlid argument for this method)
        </documentation>
    </annotation>
    <element name="updateRequest">
        <complexType>
            <sequence>
                <element name="updateValue" type="co-v1-5:UpdateProcedureValue" minOccurs="0" />
            </sequence>

        </complexType>
    </element>
    <element name="updateResponse">
        <complexType>
            <sequence>
                <element name="updateProcedureResponse" type="co-v1-5:UpdateProcedureResponse" minOccurs="0" nillable="true" />
            </sequence>
        </complexType>
    </element>

    <element name="updateException">
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException" />
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>

        <documentation>
            Get the UpdateProcedure type names supported by a JVT Session Bean
        </documentation>
    </annotation>
    <element name="getUpdateProcedureTypesRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="getUpdateProcedureTypesResponse">
        <complexType>
            <sequence>
                <element name="strings" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getUpdateProcedureTypesException">
        <complexType>

            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>
        <documentation>
            Query multiple Entities using a NamedQueryValue. (null is not a vlid argument for this method)
        </documentation>

    </annotation>
    
    <element name="queryRequest">
        <annotation>
            <documentation>
                This element definition provides an alternative to the 'co-v1-5:queryRequest'
                element. This element definition extends 'co-v1-5:IteratorRequest' and thus allows
                to specifiy the the optional 'howMany' parameter within a query request which is
                missing in the common definition.
                'co-v1-5:queryResponse' and 'co-v1-5:queryException' must be used as the corresponding
                response and exception elements.
                This element will become deprecated in future versions as soon as the common definitions
                support the 'howMany' attribute.
            </documentation>
        </annotation>
        <complexType>
            <complexContent>

                <extension base="co-v1-5:IteratorRequest">
                    <sequence>
                        <element name="namedQuery" type="co-v1-5:NamedQueryValue" minOccurs="0" />
                    </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="queryResponse">

        <complexType>
            <sequence>
                <element name="namedQueryResponse" type="co-v1-5:NamedQueryResponse" minOccurs="0" nillable="true" />
            </sequence>
        </complexType>
    </element>
    <element name="queryException">
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException" />
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>
        </complexType>
    </element>
    <annotation>
        <documentation>
            Get the Named Query type names supported by a JVT Session Bean
        </documentation>

    </annotation>
    <element name="getNamedQueryTypesRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>
    <element name="getNamedQueryTypesResponse">
        <complexType>
            <sequence>

                <element name="strings" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getNamedQueryTypesException">
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException" />
            </choice>

        </complexType>
    </element>
    <!--=======================================================-->
    <element name="ossInvalidXMLRequestException">
        <annotation>
            <documentation>
                This exception container is defined to cover for situations where the server
                receives a 'garbage' request and cannot determine the operation name
                from the request. Because no operation name can be determined it is not
                possible to return the remoteException as part of the [operationName]Exception
                wrapper that is defined for each operation on the XML based integration
                profiles.  This OssInvalidXMLRequestException type is intended to cover that
                gap in the specifications.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>    
    <!--=======================================================-->
    <complexType name="ManagedEntityKey">
        <annotation>

            <documentation>
                A ManagedEntityKey is a unique identifier for a ManagedEntityValue.
                
                The Type, ApplicationDN and ApplicationContext are included in 
                ManagedEntityKey, because a primaryKey is only unique within an
                application instance as given by the ApplicationDN running
                in a given naming system as given by the ApplicationContext URL.
            </documentation>
        </annotation>
        <sequence>
            <element name="applicationContext" minOccurs="0" type="co-v1-5:ApplicationContext" />
            <element name="applicationDN" minOccurs="0" type="string" />
            <element name="type" type="string" />
            <element name="primaryKey" nillable="false">

                <complexType mixed="false">
                    <complexContent mixed="false">
                        <extension base="anyType"/>
                    </complexContent>
                </complexType>
            </element>
        </sequence>
    </complexType>
    <!--=======================================================-->

    <complexType name="ManagedEntityKeyResult" abstract="true">
        <annotation>
            <documentation>
                A base interface for every application specific &lt;ManagedEntity&gt;KeyResult
                interface.
                
                A &lt;ManagedEntity&gt;KeyResult interface must comply with the following template:
                
                public interface &lt;ManagedEntity&gt;KeyResult extends ManagedEntityKeyResult
                {
                public &lt;ManagedEntity&gt;Key get&lt;ManagedEntity&gt;Key();
                }
                
                The managed entity key result encapsulates all the information necessary to
                indicate the result of  a BEST EFFORT operation on a specific managed entity.
                
                The managed entity key result is used in operations involving the update of
                multiple managed entities, in the deletion of multiple managed entities or in
                the creation of multiple managed entities or in bulk business operations.
                
                The base &lt;ManagedEntityKeyResult&gt; interface contains the managed entity key,
                a boolean value indicating if the operation on the targeted managed entity was
                succesful and finally the exception that would have been thrown if this operation
                had been attempted on the individual Managed Entity.
            </documentation>

        </annotation>
        <sequence>
            <element name="success" type="boolean" />
            <element name="exception" minOccurs="0" type="co-v1-5:BaseException" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ManagedEntityValue" abstract="true">
        <annotation>

            <documentation>
                The &lt;ManagedEntityValue&gt; interface is the base interface for all more
                detailed interfaces which represent any kind of object that is to be managed.
                
                Classes which implement &lt;ManagedEntityValue&gt; or a
                sub interface of &lt;ManagedEntityValue&gt; are also called value classes.
                
                All classes implementing a value interface provide several ways to access
                the attributes:
                - Attributes can be accessed through standard JavaBeans get/set (is/set)
                methods.
                - Attributes can be accessed through the generic methods defined
                in Attribute Access:
                -- public Object getAttributeValue(String attributeName)
                -- public void setAttributeValue(String attributeName, Object newValue)
            </documentation>
        </annotation>
        <sequence>

            <element name="lastUpdateVersionNumber" minOccurs="0" type="long" />
            <element name="key" type="co-v1-5:ManagedEntityKey" minOccurs="0"/>
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="NamedQueryResponse">
        <annotation>
            <documentation>
                Object returned as result of a named query execution using a specified NamedQueryValue.
            </documentation>

        </annotation>
        <sequence />
    </complexType>
    <!--=======================================================-->
    <complexType name="NamedQueryValue" abstract="true">
        <annotation>
            <documentation>
                Named query object is used to implement complex query operations. 
                The result of a named query is named a query response.
            </documentation>

        </annotation>
        <sequence />
    </complexType>
    <!--=======================================================-->
    <complexType name="OssIllegalArgumentException">
        <annotation>
            <documentation>
                Similar to java.lang.IllegalArgumentException,
                except this inherits from Exception, not RuntimeException.
                This new class is required because J2EE containers deal with
                RuntimeException in a special way.  See EJB specification.
                
                This exception is thrown if the argument of a remote
                method is invalid.
                
                Note: This exception replaces the deprecated javax.oss.IllegalArgumentException
            </documentation>

        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence />
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssIllegalAttributeValueException">

        <annotation>
            <documentation>
                Similar to javax.oss.OssIllegalArgumentException,
                except it specifically refers to a bad value associated
                with an attribute name within an AttributeAccess object.
                
                This exception is thrown if an AttributeAccess object passed
                to a remote method contains an attribute with a bad value.
                
                Note: This exception replaces the deprecated javax.oss.IllegalAttributeValueException
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:OssIllegalArgumentException">
                <sequence>
                    <element name="illegalAttributeName" type="string" />

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssIllegalStateException">
        <annotation>
            <documentation>
                Similar to java.lang.IllegalStateException,
                except this inherits from Exception, not RuntimeException.
                This new class is required because J2EE containers deal with
                RuntimeException in a special way.  See EJB specification.
                
                The IllegalStateException  exception is thrown by a JVT Session Bean
                to report that the invoked business method could not be completed because
                the operation was invoked at an illegal or inappropriate time.
                
                Note: This exception replaces the deprecated javax.oss.IllegalStateException
            </documentation>

        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence />
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssResyncRequiredException">

        <annotation>
            <documentation>
                This exception is thrown if a an update method (remote) contains
                a stale value object.
                
                Note: This exception replaces the deprecated javax.oss.ResyncRequiredException
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence>
                    <element name="managedEntityKey" type="co-v1-5:ManagedEntityKey" />

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssSetException">
        <annotation>
            <documentation>
                Indicates that there was a problem with a remote call to set the value of a Managed Entity.
                
                Note: This exception replaces the deprecated javax.oss.SetException
            </documentation>

        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence />
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssUnsupportedAttributeException">

        <annotation>
            <documentation>
                This exception is thrown for operations affecting
                unsupported attributes. It is unchecked.
                
                Note: This exception replaces the deprecated javax.oss.UnsupportedAttributeException
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:RuntimeException">
                <sequence>
                    <element name="attributeName" minOccurs="0" nillable="true" type="string" />

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="OssUnsupportedOperationException">
        <annotation>
            <documentation>
                Similar to java.lang.UnsupportedOperationException,
                except this inherits from Exception, not RuntimeException.
                This new class is required because J2EE containers deal with
                RuntimeException in a special way.  See EJB specification.
                
                This exception is thrown if a remote method is not implemented
                (and the throws clause states that this exception may be thrown).
                
                Note: This exception replaces the deprecated javax.oss.UnsupportedOperationException
            </documentation>

        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence />
            </extension>
        </complexContent>
    </complexType>
    <!--=======================================================-->
    <complexType name="UpdateProcedureResponse">

        <annotation>
            <documentation>
                Object returned as result of an update execution using a specified UpdateProcedureValue.
            </documentation>
        </annotation>
        <sequence>
            <element name="status" minOccurs="0" nillable="true" type="int" />
            <element name="successful" minOccurs="0" nillable="true" type="boolean" />
        </sequence>

    </complexType>
    <!--=======================================================-->
    <complexType name="UpdateProcedureValue">
        <annotation>
            <documentation>
                Named Update Procedures are used to implement complex Update operations.
                
                Named update procedures are similar to named queries and allow implementing
                complex atomic update operations. The result of the execution of a named update
                procedure will typically be the creation, removal or update of  a collection
                of managed entity values. As in the case of named queries, the template-based
                JVT operations are not sufficient to implement such complex operations.
            </documentation>
        </annotation>
        <sequence />

    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfApplicationContext">
        <sequence>
            <element name="item" type="co-v1-5:ApplicationContext" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfEventPropertyDescriptor">

        <sequence>
            <element name="item" type="co-v1-5:EventPropertyDescriptor" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfString">
        <sequence>
            <element name="item" type="string" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>

    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfManagedEntityKey">
        <sequence>
            <element name="item" type="co-v1-5:ManagedEntityKey" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfManagedEntityKeyResult">

        <sequence>
            <element name="item" type="co-v1-5:ManagedEntityKeyResult" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfManagedEntityValue">
        <sequence>
            <element name="item" type="co-v1-5:ManagedEntityValue" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>

    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfNamedQueryResponse">
        <sequence>
            <element name="item" type="co-v1-5:NamedQueryResponse" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfNamedQueryValue">

        <sequence>
            <element name="item" type="co-v1-5:NamedQueryValue" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfUpdateProcedureResponse">
        <sequence>
            <element name="item" type="co-v1-5:UpdateProcedureResponse" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>

    </complexType>
    <!--=======================================================-->
    <complexType name="ArrayOfUpdateProcedureValue">
        <sequence>
            <element name="item" type="co-v1-5:UpdateProcedureValue" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>
    <!--============MultiValueList=============-->
    <complexType name="MultiValueList" abstract="true">

        <annotation>
            <documentation>
                The MultiValuedList is used In oder to minimize the amount of 
                data required to add or remove a single attribute value from a 
                multi-valued attribute of a Managed Entity.
            </documentation>
        </annotation>
        <sequence>
            <element name="modifier" type="co-v1-5:Modifier"/>
        </sequence>
    </complexType>

    <simpleType name="Modifier">
        <annotation>
            <documentation>
                This is modifier is used  to indicate the operation on a MultiValueList.
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="NONE"/>
            <enumeration value="SET"/>

            <enumeration value="ADD"/>
            <enumeration value="REMOVE"/>
        </restriction>
    </simpleType>
    <!--================ejb exceptions==================-->
    <complexType name="CreateException">
        <annotation>
            <documentation> 
                The CreateException exception can be
                returned by all create(...) requests defined in the OSS
                through Java XML/JMS interface. The exception is used as a
                standard application-level exception to report a failure to
                create a managed entity or a collection of managed
                entities. This exception is thrown when a particular managed
                entity or group of managed entities cannot be
                created.
            </documentation>

        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="DuplicateKeyException">
        <annotation>

            <documentation> 
                The DuplicateKeyException exception is
                returned if a managed entity cannot be created because an
                object with the same key already exists. This exception is
                only used when a managed entity key is provided in a
                create(...) request and when client controlled naming is
                used. This exception is returned by the create requests
                defined in the OSS through Java XML/JMS
                interface. 
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="FinderException">
        <annotation>
            <documentation> 
                The exception is used as a standard
                application-level exception to report a failure to find the
                requested managed entities. This exception is returned when a
                collection of one or more entity cannot be found. This
                exception should not be returned by requests that return a
                collection of managed entitites using an associative lookup
                approach (they should return a null list
                instead). 
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence/>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ObjectNotFoundException">
        <annotation>
            <documentation> 
                The ObjectNotFoundException exception is
                returned by an OSS through Java request to indicate that the
                specified managed entity does not exist. Only the request that
                are declared to return a single managed entity use this
                exception. This exception should not be returned by methods
                that return a collection of managed entitites. This exception
                is returned when a singular managed entity cannot be
                found. 
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">

                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="RemoveException">
        <annotation>
            <documentation> 
                The RemoveException exception is returned
                at an attempt to remove a collection of one or more managed
                entity when the XML/JMS interface does not allow the managed
                entity to be removed. This exception is returned when a a
                collection of one or more managed entity cannot be
                removed
            </documentation>
        </annotation>

        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <!--================rmi  exceptions==================-->
    <complexType name="RemoteException">
        <annotation>

            <documentation> 
                The RemoteException is returned when an errors occurs during any remote object operation.
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <!--================java exceptions==================-->
    <complexType name="RuntimeException">
        <annotation>
            <documentation>
                RuntimeException is the superclass of those exceptions that can be thrown during the normal operation of the Java Virtual Machine.
            </documentation>
        </annotation>
        <complexContent>
            <extension base="co-v1-5:BaseException">

                <sequence/>
            </extension>
        </complexContent>
    </complexType>	
    
    <complexType name="BaseException" abstract="true">
        <annotation>
            <documentation>
                The BaseException is the parent complexType of the Exceptions.
            </documentation>
        </annotation>

        <sequence>
            <element name="message" type="string">
                <annotation>
                    <documentation>
                        The Message element indicates the error message from the Exception. This is most 
                        likely the results from a Exception.getMessage() call.
                    </documentation>
                </annotation>
            </element>
        </sequence>

    </complexType>
    <!--================Events==================-->
    <complexType name="BaseEventType" abstract="true">
        <annotation>
            <documentation>
                Base Event definition
            </documentation>
        </annotation>
        <sequence>

            <element name="applicationDN" type="string"/>
            <element name="eventTime" type="dateTime"/>
            <element name="managedObjectClass" type="string" minOccurs="0"/>
            <element name="managedObjectInstance" type="string" minOccurs="0"/>
        </sequence>
    </complexType>
    <!--============SystemProperties array=============-->
    <complexType name="SystemProperty">
        <annotation>

            <documentation/>
        </annotation>
        <sequence>
            <element name="name" type="string"/>
            <element name="value" type="string"/>
        </sequence>
    </complexType>
    <complexType name="ArrayOfSystemProperties">
        <annotation>

            <documentation/>
        </annotation>
        <sequence>
            <element name="property" type="co-v1-5:SystemProperty" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============int array=============-->
    <complexType name="ArrayOfInt">
        <annotation>

            <documentation/>
        </annotation>
        <sequence>
            <element name="item" type="int" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============Object array=============-->
    <complexType name="ArrayOfObject">
        <sequence>

            <element name="item" type="anyType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============Serializable array=============-->
    <complexType name="ArrayOfSerializable">
        <sequence>
            <element name="item" type="anyType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

    <!--============boolean array=============-->
    <complexType name="ArrayOfBoolean">
        <sequence>
            <element name="item" type="boolean" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============byte array=============-->
    <complexType name="ArrayOfByte">
        <sequence>

            <element name="item" type="byte" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============short array=============-->
    <complexType name="ArrayOfShort">
        <sequence>
            <element name="item" type="short" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

    <!--============char value=============-->
    <simpleType name="CharValue">
        <restriction base="string">
            <length value="1" fixed="true"/>
        </restriction>
    </simpleType>
    <!--============char array=============-->
    <complexType name="ArrayOfChar">
        <sequence>

            <element name="item" type="co-v1-5:CharValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============long array=============-->
    <complexType name="ArrayOfLong">
        <sequence>
            <element name="item" type="long" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

    <!--============float array=============-->
    <complexType name="ArrayOfFloat">
        <sequence>
            <element name="item" type="float" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============double array=============-->
    <complexType name="ArrayOfDouble">
        <sequence>

            <element name="item" type="double" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!--============date array=============-->
    <complexType name="ArrayOfDateTime">
        <sequence>
            <element name="item" type="dateTime" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

    <!--============response iterator=============-->
    <complexType name="IteratorRequest" abstract="true">
        <annotation>
            <documentation>
                This is a representation of an IteratorRequest.
            </documentation>
        </annotation>
        <sequence>
            <element name="howMany" type="unsignedInt" nillable="true" minOccurs="0"/>

        </sequence>
    </complexType>
    <complexType name="IteratorResponse" abstract="true">
        <annotation>
            <documentation>
                This is a representation of an IteratorResponse.
            </documentation>
        </annotation>
        <sequence>

            <element name="sequence" type="int"/>
            <element name="endOfReply" type="boolean"/>
        </sequence>
    </complexType>
</schema>
