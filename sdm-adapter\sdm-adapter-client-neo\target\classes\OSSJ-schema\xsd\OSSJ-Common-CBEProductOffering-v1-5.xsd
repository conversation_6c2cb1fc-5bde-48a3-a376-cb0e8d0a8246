<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEProductOffering/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbeproductoffering-v1-5="http://ossj.org/xml/Common-CBEProductOffering/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <!-- Tigerstripe : Entity definitions for ProductCatalog  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductCatalogValue" >

        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.product.productoffering.ProductCatalogValue. 
 A list of ProductOfferings for sale, with prices and illustrations, for example in book form or on the web. ProductCatalogs can be used by Customers during a self-care ordering process and may be used across one or more DistributionChannels.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="type" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="id" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductCatalogValue">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="ProductCatalogKey">
        <annotation>
            <documentation>
                This ProductCatalogKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductCatalogValue. The type of the 
                primary key for this ProductCatalogKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductCatalogKey">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductCatalogKeyResult">

        <annotation>
            <documentation>
                The ProductCatalogKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductCatalogValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="productCatalogKey" type="cbeproductoffering-v1-5:ProductCatalogKey" nillable="true" minOccurs="0"/>

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductCatalogKeyResult">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductCatalog -->
    <!-- Tigerstripe : End of Entity definition for ProductCatalog -->
    <!-- Tigerstripe : Entity definitions for ProductOffering  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductOfferingValue" >
        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.product.productoffering.ProductOfferingValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="description" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element ref="cbeproductoffering-v1-5:baseState_ProductOffering" minOccurs="0"/>
                    <element name="timePeriod" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOfferingValue">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductOfferingValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOfferingKey">
        <annotation>

            <documentation>
                This ProductOfferingKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductOfferingValue. The type of the 
                primary key for this ProductOfferingKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductOfferingKey">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductOfferingKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOfferingKeyResult">
        <annotation>
            <documentation>
                The ProductOfferingKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductOfferingValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="productOfferingKey" type="cbeproductoffering-v1-5:ProductOfferingKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductOfferingKeyResult">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductOfferingKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductOffering -->
    <element name="baseState_ProductOffering" type="string" abstract="true"/>
     
    <element name="state_ProductOffering" 
        type="cbedatatypes-v1-5:LifeCycleState" 
        substitutionGroup="cbeproductoffering-v1-5:baseState_ProductOffering" /> 

    <element name="state_ProductOfferingState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="cbeproductoffering-v1-5:baseState_ProductOffering"/>

    <!-- Tigerstripe : End of Entity definition for ProductOffering -->
    <!-- Tigerstripe : Entity definitions for ProductCatalogSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductCatalogSpecificationValue" >
        <annotation>
            <documentation>
A ProductCatalogSpecification defines the invariant characteristics of a ProductCatalog.
            </documentation>
        </annotation>

        <complexContent>

            <extension base = "cbecore-v1-5:EntitySpecificationValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="description" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductCatalogSpecificationValue">
        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductCatalogSpecificationKey">
        <annotation>
            <documentation>
                This ProductCatalogSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductCatalogSpecificationValue. The type of the 
                primary key for this ProductCatalogSpecificationKey definition is: string 
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductCatalogSpecificationKey">
        <sequence>

            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductCatalogSpecificationKeyResult">
        <annotation>
            <documentation>
                The ProductCatalogSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductCatalogSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKeyResult">
                <sequence>
                     <element name="productCatalogSpecificationKey" type="cbeproductoffering-v1-5:ProductCatalogSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductCatalogSpecificationKeyResult">

        <sequence>
            <element name="item" type="cbeproductoffering-v1-5:ProductCatalogSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductCatalogSpecification -->
    <!-- Tigerstripe : End of Entity definition for ProductCatalogSpecification -->






</schema>
