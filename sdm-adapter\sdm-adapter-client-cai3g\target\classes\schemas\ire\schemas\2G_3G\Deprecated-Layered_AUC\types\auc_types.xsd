<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/auc/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/pg/auc/13.5/" elementFormDefault="qualified">


	<xs:simpleType name="imsiType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]*" />
					<xs:maxLength value="15" />
					<xs:minLength value="6" />
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL" />
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="imsisType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*" />
			<xs:maxLength value="15" />
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsiallType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>

	<xs:simpleType name="a3a8indType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="a4indType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="7" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="fsetindType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="31" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ekiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="kindType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="511" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="akatypeType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="amfType">
		<xs:restriction base="xs:string">
			<xs:pattern value="6553[0-5]|655[0-2]\d|65[0-4]\d\d|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}|0" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="akaalgindType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1" />
			<xs:enumeration value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="zoneidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="63" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridLogicalType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(([0-9]|[1-5][0-9]|[6][0-3])([&amp;]{1,2}(([0-9]|[1-5][0-9]|[6][0-3])))*)" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>