<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:ns="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/">
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="createNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
<xs:choice>
<xs:element name="nprefix" type="nprefixType"/>
<xs:element name="address" type="addressType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementMsisdn"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementImsi"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="msisdnKey_NPSUB_Create">
<xs:selector xpath="./ns:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="msisdnKey_NPSUB_Create">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="imsiKey_NPSUB_Create">
<xs:selector xpath="./ns:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="imsiKey_NPSUB_Create">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:element name="setNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="nprefix" type="nprefixType"/>
<xs:element name="address" type="addressType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementMsisdn"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementImsi"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="getResponseNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
<xs:element minOccurs="0" name="nprefix" type="nprefixType"/>
<xs:element minOccurs="0" name="subtype" type="subTypeType"/>
<xs:element minOccurs="0" name="subcond" type="subcondType"/>
<xs:element minOccurs="0" name="address" type="addressType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementMsisdn"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="elementImsi"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="msisdnKey_NPSUB_Get">
<xs:selector xpath="./ns:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="msisdnKeyRef_NPSUB_Get" refer="msisdnKey_NPSUB_Get">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="imsiKey_NPSUB_Get">
<xs:selector xpath="./ns:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="imsiKeyRef_NPSUB_Get" refer="imsiKey_NPSUB_Get">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:simpleType name="msisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="nprefixType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,10}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newmsisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="HOME"/>
<xs:enumeration value="EXPORTED"/>
<xs:enumeration value="IMPORTED"/>
<xs:enumeration value="OTHER"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subcondType">
<xs:restriction base="xs:string">
<xs:enumeration value="GSM/WCDMA"/>
<xs:enumeration value="PSTN/ISDN"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="addressType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){5,28}"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
