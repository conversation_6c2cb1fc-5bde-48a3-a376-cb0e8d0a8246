<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv=http://schemas.xmlsoap.org/soap/envelope/>
<soapenv:Body>
    <om-v1-0:createAndStartRequestByValueResponse xmlns:om-v1-0=http://ossj.org/xml/OrderManagement/v1-0 xmlns:dox-resource=http://amdocs/core/ossj-Common-CBEResource/dat/3 xmlns:dox-co=http://amdocs/core/ossj-Common/dat/3 xmlns:dox-om=http://amdocs/core/ossj-OrderManagement/dat/3 xmlns:co-v1-5=http://ossj.org/xml/Common/v1-5 xmlns:cbecore-v1-5=http://ossj.org/xml/Common-CBECore/v1-5 xmlns:cbebi-v1-5=http://ossj.org/xml/Common-CBEBi/v1-5 xmlns:ns1=http://xmlns.aua.oss.amdocs.com/meta xmlns:ns4=http://xmlns.aua.oss.amdocs.com/client-context xmlns:dox-service=http://amdocs/core/ossj-Common-CBEService/dat/3 xmlns:ns3=http://xmlns.aua.oss.amdocs.com/requestengine xmlns:xsi=http://www.w3.org/2001/XMLSchema-instance>
        <om-v1-0:requestKey xsi:type="dox-om:ServiceOrderKey">
            <co-v1-5:type>ServiceOrderKey</co-v1-5:type>
            <co-v1-5:primaryKey>
                <dox-co:primaryKey>ITR8.4-2351500310058300820</dox-co:primaryKey>
            </co-v1-5:primaryKey>
            <dox-om:currentState>closed.aborted.aborted_byserver</dox-om:currentState>
            <dox-om:requestItemKeyResults>
                <cbebi-v1-5:item xsi:type="dox-om:ServiceOrderItemKeyResult">
                    <co-v1-5:success>false</co-v1-5:success>
                    <co-v1-5:exception xsi:type="dox-co:RemoteException">
                        <co-v1-5:message/>
                        <dox-co:exceptionsDetails>
                            <dox-co:item>
                                <dox-co:errorCode>4001</dox-co:errorCode>
                                <dox-co:retryable>false</dox-co:retryable>
                                <dox-co:errorMessage>Request failure from network.</dox-co:errorMessage>
                                <dox-co:errorClass>General</dox-co:errorClass>
                                <dox-co:component>G2 ACP</dox-co:component>
                                <dox-co:severity>Medium</dox-co:severity>
                                <dox-co:reason>com.amdocs.oss.aua.shared.exception.common.InvalidResponseException: [ATTBSSE_G2_ACP.REQUEST_FAILURE] com.amdocs.oss.aua.activation.adapter.plugin.exception.PluginInvocationFailedException: [ATTBSSE_G2_ACP.REQUEST_FAILURE] [ATTBSSE_G2_ACP.REQUEST_FAILURE] Failure Response from Network for transaction CorrelationIDXXXXXXXXXX ,[ ErrorCode : 403 , ErrorMessage : _Incorrect CMS Data:&lt;message details>_]</dox-co:reason>
                            </dox-co:item>
                        </dox-co:exceptionsDetails>
                    </co-v1-5:exception>
                    <cbecore-v1-5:entityKey xsi:type="dox-om:ServiceOrderItemKey">
                        <co-v1-5:type>ServiceOrderItem</co-v1-5:type>
                        <co-v1-5:primaryKey>
                            <dox-co:primaryKey>ITR8.4-2351500310058300820-00-00</dox-co:primaryKey>
                        </co-v1-5:primaryKey>
                    </cbecore-v1-5:entityKey>
                    <dox-om:currentState>closed.aborted.aborted_byserver</dox-om:currentState>
                    <dox-om:returnData>
                        <cbecore-v1-5:describedBy/>
                    </dox-om:returnData>
                </cbebi-v1-5:item>
            </dox-om:requestItemKeyResults>
        </om-v1-0:requestKey>
    </om-v1-0:createAndStartRequestByValueResponse>
</soapenv:Body>
</soapenv:Envelope>


