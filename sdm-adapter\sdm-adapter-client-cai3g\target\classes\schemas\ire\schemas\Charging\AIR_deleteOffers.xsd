<?xml version="1.0" encoding="UTF-8"?>
<!-- (<PERSON> (China) Communications Company Ltd) -->
<xs:schema elementFormDefault="qualified"
           attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0"
           xmlns="http://schemas.ericsson.com/ma/CS/AIR/"
           xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">

  <xs:include schemaLocation="air_common_types.xsd"/>
  <xs:element name="subscriberNumber" type="subscriberNumberType"/>
  <!-- delete offers -->
  <xs:element name="deleteOffers">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="originNodeType" type="originNodeTypeType" minOccurs="0"/>
        <xs:element name="originHostName" type="originHostNameType" minOccurs="0"/>
        <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0"/>
        <xs:element name="originTimeStamp" type="dateTimeType" minOccurs="0"/>
        <xs:element name="subscriberNumberNAI" type="subscriberNumberNAIType" minOccurs="0"/>
        <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0"/>
        <xs:element name="offerDelete" type="offerDeleteListType" maxOccurs="unbounded"/>
        <xs:choice minOccurs="0">
            <xs:element name="backdatedToDateTime" type="dateTimeType" minOccurs="0"/>
            <xs:element name="backdatedToStartOfBillCycleInstance" type="backdatedToStartOfBillCycleInstanceType" minOccurs="0"/>
        </xs:choice>
        <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="subscriberNumberAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <!-- <xs:attribute name="offerID" type="offerIDType" use="required">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="offerIDAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute> -->
    </xs:complexType>
  </xs:element>

  <!-- delete offers Response -->
  <xs:element name="deleteOffersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="subscriberNumber" type="subscriberNumberType"/>
        <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0"/>
        <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0"/>
        <xs:element name="offerInformation" type="offerInformationType" minOccurs="0" maxOccurs="unbounded"/>
        <xs:element name="currency1" type="currencyType" minOccurs="0"/>
        <xs:element name="currency2" type="currencyType" minOccurs="0"/>
        <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0"
                    maxOccurs="unbounded"/>
        <xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0"
                    maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <!-- simple type definition -->
  <xs:simpleType name="originNodeTypeType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="EXT"/>
      <xs:enumeration value="AIR"/>
      <xs:enumeration value="ADM"/>
      <xs:enumeration value="UGW"/>
      <xs:enumeration value="IVR"/>
      <xs:enumeration value="OGW"/>
      <xs:enumeration value="SDP"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="originTransactionIDType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[A-Za-z0-9 ]{1,20}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="subscriberNumberNAIType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="2"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="subscriberNumberType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9]{1,28}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="originOperatorIDType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
    </xs:restriction>
  </xs:simpleType>


  <xs:simpleType name="negotiatedCapabilitiesType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="currencyType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[A-Z]{3}"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="offerStateType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="99"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="offerProviderIDType">
    <xs:restriction base="xs:string">
      <xs:minLength value="1"/>
      <xs:maxLength value="28"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="availableServerCapabilitiesType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>


  <xs:simpleType name="flagType">
    <xs:restriction base="xs:boolean"/>
  </xs:simpleType>
  <xs:simpleType name="offerRequestedTypeFlagType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-1]{8}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="fafNumberType">
    <xs:restriction base="xs:string">
      <xs:minLength value="1"/>
      <xs:maxLength value="30"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ownerType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Subscriber"/>
      <xs:enumeration value="Account"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="fafIndicatorType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="65535"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageCounterIDType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageCounterValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageCounterNominalValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageCounterMonetaryValueType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9]{1,12}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageCounterMonetaryNominalValueType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9]{1,12}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageThresholdIDType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="**********"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageThresholdValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageThresholdMonetaryValueType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[0-9]{1,12}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="usageThresholdSourceType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="3"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="closestExpiryValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="-9223372036854775807"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="closestAccessibleValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="-9223372036854775807"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="dedicatedAccountActiveValueType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="changedAmountType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="-9223372036854775807"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="changedExpiryDateType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="-65535"/>
      <xs:maxInclusive value="65535"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="changedStartDateType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="-65535"/>
      <xs:maxInclusive value="65535"/>
    </xs:restriction>
  </xs:simpleType>


  <xs:simpleType name="balanceType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="9223372036854775807"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- complex type definition -->
  <xs:complexType name="offerRequestInformationType">
    <xs:sequence>
      <xs:choice minOccurs="0">
        <xs:element name="dateInformation" type="dateInformationType" minOccurs="0"/>
        <xs:element name="dateTimeInformation" type="dateTimeInformationType" minOccurs="0"/>
      </xs:choice>
      <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0"/>
      <xs:element name="offerType" type="offerTypeType" minOccurs="0"/>
      <xs:element name="offerProviderID" type="offerProviderIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationType" minOccurs="0"
                  maxOccurs="unbounded"/>
      <xs:element name="attributeUpdateInformation" type="attributeUpdateInformationType" minOccurs="0"
                  maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="dedicatedAccountDeleteInformationType">
    <xs:sequence>
      <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
      <xs:element name="productID" type="productIDType" minOccurs="0"/>
      <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue1" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue2" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="expiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0"/>
      <xs:element name="offerID" type="offerIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountRealMoneyFlag" type="dedicatedAccountRealMoneyFlagType" minOccurs="0"/>
      <xs:element name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType" minOccurs="0"
                  maxOccurs="unbounded"/>
      <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="dedicatedAccountInformationType">
    <xs:sequence>
      <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
      <xs:element name="dedicatedAccountValue1" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue2" type="dedicatedAccountValueType" minOccurs="0"/>


      <xs:element name="dedicatedReservation1" type="dedicatedReservationType" minOccurs="0"/>
      <xs:element name="dedicatedReservation2" type="dedicatedReservationType" minOccurs="0"/>

      <xs:element name="expiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0"/>
      <xs:element name="offerID" type="offerIDType" minOccurs="0"/>
      <xs:element name="productID" type="productIDType" minOccurs="0"/>

      <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountRealMoneyFlag" type="dedicatedAccountRealMoneyFlagType" minOccurs="0"/>

      <xs:element name="closestExpiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestExpiryValue1" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestExpiryValue2" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue1" type="closestAccessibleValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue2" type="closestAccessibleValueType" minOccurs="0"/>
      <xs:element name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType" minOccurs="0"
                  maxOccurs="unbounded"/>
      <xs:element name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0"/>
      <xs:element name="compositeDedicatedAccountFlag" type="flagType" minOccurs="0"/>
      <xs:element name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="subDedicatedAccountInformationType">
    <xs:sequence>
      <xs:element name="dedicatedAccountValue1" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue2" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="expiryDate" type="dateTimeType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="fafInformationType">
    <xs:sequence>
      <xs:element name="fafNumber" type="fafNumberType"/>
      <xs:element name="owner" type="ownerType"/>
      <xs:element name="expiryDate" type="expiryDateType" minOccurs="0"/>
      <xs:element name="fafIndicator" type="fafIndicatorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="exactMatch" type="xs:boolean" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="usageCounterUsageThresholdInformationType">
    <xs:sequence>
      <xs:element name="usageCounterID" type="usageCounterIDType"/>
      <xs:element name="usageCounterValue" type="usageCounterValueType" minOccurs="0"/>
      <xs:element name="usageCounterNominalValue" type="usageCounterNominalValueType" minOccurs="0"/>
      <xs:element name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType" minOccurs="0"/>
      <xs:element name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryNominalValueType" minOccurs="0"/>
      <xs:element name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType" minOccurs="0"/>
      <xs:element name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryNominalValueType" minOccurs="0"/>
      <xs:element name="usageThresholdInformation" type="usageThresholdInformationType" minOccurs="0"
                  maxOccurs="unbounded"/>
      <xs:element name="associatedPartyID" type="associatedPartyIDType" minOccurs="0"/>
      <xs:element name="productID" type="productIDType" minOccurs="0"/>
      <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
      <xs:element name="usageCounterResourceConnected" type="usageCounterResourceConnectedType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="usageThresholdInformationType">
    <xs:sequence>
      <xs:element name="usageThresholdID" type="usageThresholdIDType"/>
      <xs:element name="usageThresholdValue" type="usageThresholdValueType" minOccurs="0"/>
      <xs:element name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType" minOccurs="0"/>
      <xs:element name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType" minOccurs="0"/>
      <xs:element name="usageThresholdSource" type="usageThresholdSourceType" minOccurs="0"/>
      <xs:element name="associatedPartyID" type="associatedPartyIDType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="dedicatedAccountChangeInformationType">
    <xs:sequence>
      <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
      <xs:element name="productID" type="productIDType" minOccurs="0"/>
      <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue1" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue2" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="expiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0"/>
      <xs:element name="offerID" type="offerIDType" minOccurs="0"/>
      <xs:element name="dedicatedAccountRealMoneyFlag" type="dedicatedAccountRealMoneyFlagType" minOccurs="0"/>
      <xs:element name="closestExpiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestExpiryValue1" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestExpiryValue2" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue1" type="closestAccessibleValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue2" type="closestAccessibleValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType" minOccurs="0"/>
      <xs:element name="subDedicatedAccountChangeInformation" type="subDedicatedAccountChangeInformationType"
                  minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0"/>
      <!--<xs:element name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType" minOccurs="0"/> -->
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="subDedicatedAccountChangeInformationType">
    <xs:sequence>
      <xs:element name="changedAmount1" type="changedAmountType" minOccurs="0"/>
      <xs:element name="changedAmount2" type="changedAmountType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue1" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="dedicatedAccountValue2" type="dedicatedAccountValueType" minOccurs="0"/>
      <xs:element name="changedExpiryDate" type="changedExpiryDateType" minOccurs="0"/>
      <xs:element name="newExpiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="clearedExpiryDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="changedStartDate" type="changedStartDateType" minOccurs="0"/>
      <xs:element name="newStartDate" type="dateTimeType" minOccurs="0"/>
      <xs:element name="clearedStartDate" type="dateTimeType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="productSelectionType">
    <xs:sequence>
      <xs:element name="offerID" type="offerIDType"/>
      <xs:element name="productID" type="productIDType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="aggregatedBalanceInformationType">
    <xs:sequence>
      <xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
      <xs:element name="totalBalance1" type="balanceType" minOccurs="0"/>
      <xs:element name="totalBalance2" type="balanceType" minOccurs="0"/>
      <xs:element name="totalActiveBalance1" type="balanceType" minOccurs="0"/>
      <xs:element name="totalActiveBalance2" type="balanceType" minOccurs="0"/>
      <xs:element name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType" minOccurs="0"/>
      <xs:element name="closestExpiryDateTime" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestExpiryValue1" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestExpiryValue2" type="closestExpiryValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleDateTime" type="dateTimeType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue1" type="closestAccessibleValueType" minOccurs="0"/>
      <xs:element name="closestAccessibleValue2" type="closestAccessibleValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>


    <xs:complexType name="offerDeleteListType">
        <xs:sequence>
            <xs:element name="offerID" type="offerIDType"/>
            <xs:choice minOccurs="0">
                <xs:element name="productID" type="productIDType" minOccurs="0"/>
                <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="offerInformationType">
        <xs:sequence>
            <xs:element name="offerID" type="offerIDType"/>
            <xs:element name="startDate" type="dateTimeType" minOccurs="0"/>
            <xs:element name="expiryDate" type="dateTimeType" minOccurs="0"/>
            <xs:element name="startDateTime" type="dateTimeType" minOccurs="0"/>
            <xs:element name="expiryDateTime" type="dateTimeType" minOccurs="0"/>
            <xs:element name="pamServiceID" type="pamServiceIDType" minOccurs="0"/>
            <xs:element name="offerType" type="offerTypeType" minOccurs="0"/>
            <xs:element name="offerState" type="offerStateType" minOccurs="0"/>
            <xs:element name="offerProviderID" type="offerProviderIDType" minOccurs="0"/>
            <xs:element name="productID" type="productIDType" minOccurs="0"/>
            <xs:element name="externalProductID" type="externalProductIDType" minOccurs="0"/>
            <xs:element name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="dedicatedAccountInformation" type="dedicatedAccountInformationType" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="dedicatedAccountDeleteInformation" type="dedicatedAccountDeleteInformationType" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="attributeInformation" type="attributeInformationType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="fafInformation" type="fafInformationType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="treeParameterSetInformation" type="treeParameterSetInformationListType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="productOfferingName" type="productOfferingNameType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="treeParameterSetInformationListType">
        <xs:sequence>
            <xs:element name="serviceProvider" type="serviceProviderType" minOccurs="0"/>
            <xs:element name="treeParameterInformation" type="treeParameterInformationType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="backdatedToStartOfBillCycleInstanceType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="-1"/>
            <xs:maxInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="responseCodeType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="999"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="dedicatedAccountRealMoneyFlagType">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>

  <xs:simpleType name="dedicatedReservationType">
    <xs:restriction base="xs:long">
      <xs:minInclusive value="0" />
      <xs:maxInclusive value="9223372036854775807" />
    </xs:restriction>
  </xs:simpleType>

</xs:schema>
