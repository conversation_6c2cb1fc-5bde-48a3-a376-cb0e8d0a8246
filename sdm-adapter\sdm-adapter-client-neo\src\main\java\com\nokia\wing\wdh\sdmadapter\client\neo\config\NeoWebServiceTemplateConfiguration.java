package com.nokia.wing.wdh.sdmadapter.client.neo.config;

import com.nokia.wing.wdh.sdmadapter.client.neo.security.soap.NeoSoapSecurityInterceptor;
import com.nokia.wing.wdh.sdmadapter.client.neo.security.ssl.NeoSslConfigurationUtil;
import com.nokia.wing.wdh.sdmadapter.client.neo.security.oauth.AuthenticationStrategy;
import com.nokia.wing.wdh.sdmadapter.client.neo.config.interceptor.NeoSoapClientInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

/**
 * WebServiceTemplate Configuration for NEO Client
 *
 * Configures WebServiceTemplate with SSL/TLS support based on feature flags.
 */
@Slf4j
@Configuration
@AutoConfiguration
@ConditionalOnProperty(value="application.clientType", havingValue="NEO")
public class NeoWebServiceTemplateConfiguration {

    @Autowired
    private NeoClientProperties properties;

    @Autowired
    private NeoSslConfigurationUtil sslConfigurationUtil;

    @Autowired
    private AuthenticationStrategy authenticationStrategy;

    /**
     * JAXB Marshaller for NEO SOAP operations
     *
     * Configured for generated OSSJ classes from WSDL/XSD schemas
     */
    @Bean
    public Jaxb2Marshaller neoJaxb2Marshaller() {
        log.info("Creating NEO JAXB2 Marshaller with generated classes now moved to src/main/java");

        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();

        try {
            // Configure for generated OSSJ classes - now in autogenerated packages
            marshaller.setPackagesToScan(
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.common",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.ordermanagement",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.common",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.inventory",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeservice",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cberesource",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.amdocs.cbeparty",
                "com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated"
            );

            // Set additional JAXB properties for better XML formatting
            marshaller.setMarshallerProperties(java.util.Map.of(
                jakarta.xml.bind.Marshaller.JAXB_FORMATTED_OUTPUT, true,
                jakarta.xml.bind.Marshaller.JAXB_ENCODING, "UTF-8"
            ));

            log.info("NEO JAXB Marshaller configured successfully with generated classes from src/main/java");
        } catch (Exception e) {
            log.error("Failed to configure NEO JAXB marshaller: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to configure NEO JAXB marshaller", e);
        }

        return marshaller;
    }


    /**
     * Main WebServiceTemplate with SSL configuration and security interceptor
     */
    @Bean
    public WebServiceTemplate neoWebServiceTemplateMain() throws Exception {
        log.info("Configuring NEO WebServiceTemplate with SSL support");

        WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
        Jaxb2Marshaller marshaller = neoJaxb2Marshaller();

        webServiceTemplate.setMarshaller(marshaller);
        webServiceTemplate.setUnmarshaller(marshaller);

        // Set service URL with appropriate protocol (http/https)
        String serviceUrl = buildServiceUrl();
        webServiceTemplate.setDefaultUri(serviceUrl);

        // Configure message sender with SSL support
        webServiceTemplate.setMessageSender(createHttpComponentsMessageSender());

        // Add both SOAP security and logging interceptors
        ClientInterceptor[] interceptors = {
            createSoapSecurityInterceptor(),
            new NeoSoapClientInterceptor()
        };
        webServiceTemplate.setInterceptors(interceptors);

        log.info("NEO WebServiceTemplate configured successfully for URL: {} with SOAP security and logging interceptors", serviceUrl);
        return webServiceTemplate;
    }

    /**
     * Create HttpComponentsMessageSender with SSL configuration
     */
    private HttpComponentsMessageSender createHttpComponentsMessageSender() throws Exception {
        HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
        messageSender.setHttpClient(sslConfigurationUtil.createHttpClient());

        log.info("NEO HttpComponentsMessageSender configured with SSL support");
        return messageSender;
    }

    /**
     * Build service URL with appropriate protocol based on SSL configuration
     */
    private String buildServiceUrl() {
        String protocol = sslConfigurationUtil.getProtocol();
        String baseUrl = properties.getServiceUrl();

        // Replace protocol if needed
        if (baseUrl.startsWith("http://") && protocol.equals("https")) {
            baseUrl = baseUrl.replace("http://", "https://");
            log.info("Updated service URL protocol to HTTPS");
        } else if (baseUrl.startsWith("https://") && protocol.equals("http")) {
            baseUrl = baseUrl.replace("https://", "http://");
            log.info("Updated service URL protocol to HTTP");
        }

        return baseUrl;
    }

    /**
     * Create SOAP Security Interceptor for adding OAuth tokens and WS-Security headers
     */
    private ClientInterceptor createSoapSecurityInterceptor() {
        log.info("Creating NEO SOAP Security Interceptor with authentication strategy: {}",
                authenticationStrategy.getAuthenticationMethod());
        return new NeoSoapSecurityInterceptor(authenticationStrategy, properties);
    }
}
