<?xml version="1.0" encoding="UTF-8"?>
<!-- Home Location Register, Subscription -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
	targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">

	<xs:include schemaLocation="types/hlrla_types.xsd" />
	<xs:element name="m2msp" type="m2mspType" />

	<!-- create M2M Service Profile -->
	<!-- MOId: m2msp -->
	<!-- MOType: M2MServiceProfile@http://schemas.ericsson.com/pg/hlr/13.5/ -->
	<!--Either MSISDN or IMSI of the dummy subscription must be provided, MPID optional -->
	<xs:element name="CreateM2MServiceProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2msp" type="m2mspType" />
				<xs:choice>
					<xs:element name="msisdn" type="msisdnType" />
					<xs:element name="imsi" type="imsiType" />
				</xs:choice>
				<xs:element name="m2mpid" type="m2mpidType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="m2msp" type="m2mspType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="m2mspAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_m2msp">
			<xs:selector xpath="./x:m2msp" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_m2msp" refer="key_m2msp">
			<xs:selector xpath="." />
			<xs:field xpath="@m2msp" />
		</xs:keyref>
	</xs:element>

	<!-- get M2M Service Profile response is defined below M2MServiceProfileData section is not completed CAI3G interface supports get one profile, to get all profiles is supported via CLI -->
	<xs:element name="GetM2MServiceProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="M2MServiceProfileData">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="m2msp" type="m2mspType" />
							<xs:element name="m2mspv" type="m2mspvType" />
							<xs:element name="nam" type="namType" />
							<xs:sequence>
								<xs:element name="sud" type="sudType" maxOccurs="unbounded" />
							</xs:sequence>
							<xs:element name="gprsobp" type="gprsobpType" minOccurs="0" />
							<xs:element name="gsmobcinv" type="gsmobcinvType" minOccurs="0" />
							<xs:element name="csp" type="cspType" minOccurs="0" />
							<xs:element name="pdpcp" type="pdpcpType" minOccurs="0" />
							<xs:element name="gsap" type="gsapType" minOccurs="0" />
							<xs:element name="m2mpid" type="m2mpidType" minOccurs="0" />
							<xs:element name="SubscriberLocationServicesData" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="PrivacyLCSClassData" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="UniversalLocationServices" type="univType" minOccurs="0" />
													<xs:element name="CallRelatedLocationServices" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="notf" type="notfType" />
																<xs:element name="ExternalAddress" minOccurs="0" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="eadd" type="eaddType" />
																			<xs:element name="gres" type="gresType" minOccurs="0" />
																			<xs:element name="notf" type="notfType" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="CallUnrelatedLocationServices" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="notf" type="notfType" />
																<xs:element name="ExternalAddress" minOccurs="0" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="eadd" type="eaddType" />
																			<xs:element name="gres" type="gresType" minOccurs="0" />
																			<xs:element name="notf" type="notfType" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="PLMNOperatorLocationServices" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="intid" type="intidType" minOccurs="0" maxOccurs="unbounded" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="PrivacyServiceTypeData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="PrivacyServiceType" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="servt" type="servtType" />
																			<xs:element name="gres" type="gresType" minOccurs="0" />
																			<xs:element name="notf" type="notfType" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="MobileOriginatingLCSClass" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="mocl" type="moclType" maxOccurs="unbounded" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="SubscriberLocationServicesAddressData" minOccurs="0">
								<xs:complexType>
									<xs:choice>
										<xs:element name="ResponseData">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="GMLCAddressData" minOccurs="0" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="gmlcid" type="gmlcidType" />
																<xs:element name="gmlcadd" type="gmlcaddType" minOccurs="0" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="HomeGMLCAddressData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="hgmlcid" type="hgmlcidType" />
																<xs:element name="hgmlcadd" type="hgmlcaddType" minOccurs="0" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="PrivacyProfileRegisterAddressData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="pprid" type="ppridType" />
																<xs:element name="ppradd" type="ppraddType" minOccurs="0" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="ErrorResponse">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="ErrorCode" type="xs:integer" />
													<xs:element name="ErrorMessage" type="xs:string" minOccurs="0" />
													<xs:element name="ErrorDetail" type="xs:string" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:choice>
								</xs:complexType>
							</xs:element>
							<xs:element name="SubscriberSpamSMSData">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="smspam" type="smspamType" />
										<xs:element name="scadds" type="scaddsType" minOccurs="0" maxOccurs="unbounded" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="CAMELSubscriptionProfileData" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="csp" type="cspType" />
							<xs:element name="CamelTriggeringDetectionPointData" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="tdptype" type="tdptypeType" />
										<xs:element name="tdp" type="tdpType" />
										<xs:element name="sk" type="skType" />
										<xs:element name="gsa" type="gsaType" />
										<xs:element name="deh" type="dehType" minOccurs="0" />
										<xs:element name="cch" type="cchType" />
										<xs:element name="i" type="iType" minOccurs="0" />
										<xs:element name="dialnum" type="dialnumType" minOccurs="0" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="CAMELSubscriptionOptions" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="gcso" type="gcsoType" />
											<xs:element name="mcso" type="mcsoType" />
											<xs:element name="sslo" type="ssloType" />
											<xs:element name="gc2so" type="gc2soType" />
											<xs:element name="mc2so" type="mc2soType" />
											<xs:element name="tif" type="tifType" minOccurs="0" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="gprsso" type="gprssoType" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="osmsso" type="osmssoType" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="gc3so" type="gc3soType" />
											<xs:element name="mc3so" type="mc3soType" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="gc4so" type="gc4soType" />
											<xs:element name="mc4so" type="mc4soType" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="mmso" type="mmsoType" />
										</xs:sequence>
										<xs:sequence minOccurs="0">
											<xs:element name="tsmsso" type="tsmssoType" />
										</xs:sequence>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="ExtendedCAMELData" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="eoinci" type="eoinciType" />
										<xs:element name="eoick" type="eoickType" />
										<xs:element name="etinci" type="etinciType" />
										<xs:element name="etick" type="etickType" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="CAMELTriggeringCriteriaData" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="OCTDP2TriggeringCriteriaData" minOccurs="0" maxOccurs="unbounded">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="cch" type="cchType" />
													<xs:element name="mty" type="mtyType" minOccurs="0" />
													<xs:element name="dnum" type="dnumType" minOccurs="0" maxOccurs="unbounded" />
													<xs:element name="dlgh" type="dlghType" minOccurs="0" maxOccurs="unbounded" />
													<xs:element name="ftc" type="ftcType" minOccurs="0" />
													<xs:element name="bs" type="bsType" minOccurs="0" maxOccurs="unbounded" />
													<xs:element name="bsg" type="bsgType" minOccurs="0" maxOccurs="unbounded" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="TCTDP12TriggeringCriteriaData" minOccurs="0" maxOccurs="unbounded">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="cch" type="cchType" />
													<xs:element name="bs" type="bsType" minOccurs="0" maxOccurs="unbounded" />
													<xs:element name="bsg" type="bsgType" minOccurs="0" maxOccurs="unbounded" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="PDPContextData" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pdpcp" type="pdpcpType" />
							<xs:element name="PDPContext" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pdpid" type="pdpidType" />
										<xs:element name="apnid" type="apnidType" minOccurs="0" />
										<xs:element name="eqosid" type="eqosidType" />
										<xs:element name="vpaa" type="vpaaType" />
										<xs:element name="pdpch" type="pdpchType" minOccurs="0" />
										<xs:element name="pdpty" type="pdptyType" />
										<xs:element name="epdpind" type="epdpindType" minOccurs="0" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="GSMSCFProfileData" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="gsap" type="xs:integer" />
							<xs:element name="gsa" type="xs:string" maxOccurs="unbounded" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="M2MProfileData" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="m2mpid" type="xs:integer" />
							<xs:element name="dmi" type="xs:string" />
							<xs:element name="nname" type="xs:string" />
							<xs:element name="ntype" type="xs:integer" />
							<xs:element name="url" type="xs:string" minOccurs="0" />
							<xs:element name="ip" type="xs:string" minOccurs="0" />
							<xs:element name="m2mipport" type="xs:integer" minOccurs="0" />
							<xs:element name="nmsisdn" type="xs:string" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<!-- CLI command HECDMPE deletes the M2MServie Profile with the m2msp. -->
	<xs:element name="hecdmpe">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2msp" type="m2mspType" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<!-- CLI commands HECDMPP prints all M2MService Profiles in the database. -->
	<xs:element name="hecdmpp">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="m2msp" type="m2msppType" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>


	<!-- The response schema for HECDMPP. -->
	<xs:element name="M2MServiceProfilesData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="M2MServiceProfile" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
						<xs:choice>
							<xs:element name="ResponseData">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="M2MServiceProfileData">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="m2msp" type="m2mspType" />
													<xs:element name="m2mspv" type="m2mspvType" />
													<xs:element name="nam" type="namType" />
													<xs:sequence>
														<xs:element name="sud" type="sudType" maxOccurs="unbounded" />
													</xs:sequence>
													<xs:element name="gprsobp" type="gprsobpType" minOccurs="0" />
													<xs:element name="gsmobcinv" type="gsmobcinvType" minOccurs="0" />
													<xs:element name="csp" type="cspType" minOccurs="0" />
													<xs:element name="pdpcp" type="pdpcpType" minOccurs="0" />
													<xs:element name="gsap" type="gsapType" minOccurs="0" />
													<xs:element name="m2mpid" type="m2mpidType" minOccurs="0" />
													<xs:element name="SubscriberLocationServicesData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="PrivacyLCSClassData" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="UniversalLocationServices" type="univType" minOccurs="0" />
																			<xs:element name="CallRelatedLocationServices" minOccurs="0">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="notf" type="notfType" />
																						<xs:element name="ExternalAddress" minOccurs="0" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="eadd" type="eaddType" />
																									<xs:element name="gres" type="gresType" minOccurs="0" />
																									<xs:element name="notf" type="notfType" />
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="CallUnrelatedLocationServices" minOccurs="0">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="notf" type="notfType" />
																						<xs:element name="ExternalAddress" minOccurs="0" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="eadd" type="eaddType" />
																									<xs:element name="gres" type="gresType" minOccurs="0" />
																									<xs:element name="notf" type="notfType" />
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="PLMNOperatorLocationServices" minOccurs="0">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="intid" type="intidType" minOccurs="0" maxOccurs="unbounded" />
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="PrivacyServiceTypeData" minOccurs="0">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="PrivacyServiceType" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="servt" type="servtType" />
																									<xs:element name="gres" type="gresType" minOccurs="0" />
																									<xs:element name="notf" type="notfType" />
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="MobileOriginatingLCSClass" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="mocl" type="moclType" maxOccurs="unbounded" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="SubscriberLocationServicesAddressData" minOccurs="0">
														<xs:complexType>
															<xs:choice>
																<xs:element name="ResponseData" type="ResponseData">
																</xs:element>
																<xs:element name="ErrorResponse">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="ErrorCode" type="xs:integer" />
																			<xs:element name="ErrorMessage" type="xs:string" minOccurs="0" />
																			<xs:element name="ErrorDetail" type="xs:string" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:choice>
														</xs:complexType>
													</xs:element>
													<xs:element name="SubscriberSpamSMSData">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="smspam" type="smspamType" />
																<xs:element name="scadds" type="scaddsType" minOccurs="0" maxOccurs="unbounded" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="CAMELSubscriptionProfileData" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="csp" type="cspType" />
													<xs:element name="CamelTriggeringDetectionPointData" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tdptype" type="tdptypeType" />
																<xs:element name="tdp" type="tdpType" />
																<xs:element name="sk" type="skType" />
																<xs:element name="gsa" type="gsaType" />
																<xs:element name="deh" type="dehType" minOccurs="0" />
																<xs:element name="cch" type="cchType" />
																<xs:element name="i" type="iType" minOccurs="0" />
																<xs:element name="dialnum" type="dialnumType" minOccurs="0" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="CAMELSubscriptionOptions" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="gcso" type="gcsoType" />
																	<xs:element name="mcso" type="mcsoType" />
																	<xs:element name="sslo" type="ssloType" />
																	<xs:element name="gc2so" type="gc2soType" />
																	<xs:element name="mc2so" type="mc2soType" />
																	<xs:element name="tif" type="tifType" minOccurs="0" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="gprsso" type="gprssoType" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="osmsso" type="osmssoType" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="gc3so" type="gc3soType" />
																	<xs:element name="mc3so" type="mc3soType" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="gc4so" type="gc4soType" />
																	<xs:element name="mc4so" type="mc4soType" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="mmso" type="mmsoType" />
																</xs:sequence>
																<xs:sequence minOccurs="0">
																	<xs:element name="tsmsso" type="tsmssoType" />
																</xs:sequence>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="ExtendedCAMELData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="eoinci" type="eoinciType" />
																<xs:element name="eoick" type="eoickType" />
																<xs:element name="etinci" type="etinciType" />
																<xs:element name="etick" type="etickType" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="CAMELTriggeringCriteriaData" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="OCTDP2TriggeringCriteriaData" minOccurs="0" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="cch" type="cchType" />
																			<xs:element name="mty" type="mtyType" minOccurs="0" />
																			<xs:element name="dnum" type="dnumType" minOccurs="0" maxOccurs="unbounded" />
																			<xs:element name="dlgh" type="dlghType" minOccurs="0" maxOccurs="unbounded" />
																			<xs:element name="ftc" type="ftcType" minOccurs="0" />
																			<xs:element name="bs" type="bsType" minOccurs="0" maxOccurs="unbounded" />
																			<xs:element name="bsg" type="bsgType" minOccurs="0" maxOccurs="unbounded" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="TCTDP12TriggeringCriteriaData" minOccurs="0" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="cch" type="cchType" />
																			<xs:element name="bs" type="bsType" minOccurs="0" maxOccurs="unbounded" />
																			<xs:element name="bsg" type="bsgType" minOccurs="0" maxOccurs="unbounded" />
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="PDPContextData" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="pdpcp" type="pdpcpType" />
													<xs:element name="PDPContext" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="pdpid" type="pdpidType" />
																<xs:element name="apnid" type="apnidType" minOccurs="0" />
																<xs:element name="eqosid" type="eqosidType" />
																<xs:element name="vpaa" type="vpaaType" />
																<xs:element name="pdpch" type="pdpchType" minOccurs="0" />
																<xs:element name="pdpty" type="pdptyType" />
																<xs:element name="epdpind" type="epdpindType" minOccurs="0" />
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="GSMSCFProfileData" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="gsap" type="xs:integer" />
													<xs:element name="gsa" type="xs:string" maxOccurs="unbounded" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="M2MProfileData" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="m2mpid" type="xs:integer" />
													<xs:element name="dmi" type="xs:string" />
													<xs:element name="nname" type="xs:string" />
													<xs:element name="ntype" type="xs:integer" />
													<xs:element name="url" type="xs:string" minOccurs="0" />
													<xs:element name="ip" type="xs:string" minOccurs="0" />
													<xs:element name="m2mipport" type="xs:integer" minOccurs="0" />
													<xs:element name="nmsisdn" type="xs:string" minOccurs="0" />
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="ErrorResponse">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="ErrorCode" type="xs:integer" />
										<xs:element name="ErrorMessage" type="xs:string" minOccurs="0" />
										<xs:element name="ErrorDetail" type="xs:string" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ResponseData">
		<xs:sequence>
			<xs:element name="GMLCAddressData" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="gmlcid" type="gmlcidType" />
						<xs:element name="gmlcadd" type="gmlcaddType" minOccurs="0" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="HomeGMLCAddressData" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="hgmlcid" type="hgmlcidType" />
						<xs:element name="hgmlcadd" type="hgmlcaddType" minOccurs="0" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PrivacyProfileRegisterAddressData" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="pprid" type="ppridType" />
						<xs:element name="ppradd" type="ppraddType" minOccurs="0" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>

