<!-- BCE Call BarringDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd"/>
	<xs:element name="serviceProviderId" type="ServiceProviderId"/>
	<xs:element name="companyId" type="CompanyId"/>
	<xs:element name="barringProfileId" type="BarringProfileId"/>
	<xs:complexType name="BceCallBarringProfileType">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="barringProfileId" type="BarringProfileId"/>
			<!-- Call Barring Profile Attributes -->
			<xs:element name="description" type="DescriptionType" minOccurs="0"/>
			<xs:element name="blackList" type="BarringRuleType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="whiteList" type="BarringRuleType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="name" type="NameType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="serviceProviderId" type="ServiceProviderId" use="required"/>
		<xs:attribute name="companyId" type="CompanyId" use="required"/>
		<xs:attribute name="barringProfileId" type="BarringProfileId"/>
	</xs:complexType>
	<!-- Create Call Barring Profile MOId: serviceProviderId, companyId, barringProfileId  MOType: CallBarringProfile@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createCallBarringProfile" type="BceCallBarringProfileType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Call Barring Profile
			</xs:documentation>
		</xs:annotation>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@serviceProviderId"/>
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="serviceProviderId"/>
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@companyId"/>
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="companyId"/>
		</xs:keyref>
		<xs:key name="barringProfileIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@barringProfileId"/>
		</xs:key>
		<xs:keyref name="barringProfileIdKeyRef_Create" refer="barringProfileIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="barringProfileId"/>
		</xs:keyref>
	</xs:element>
	<!-- Set Call Barring Profile MOId: serviceProviderId, companyId, barringProfileId  MOType: CallBarringProfile@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setCallBarringProfile" type="BceCallBarringProfileType">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Call Barring Profile
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- get Call Barring Profile MOId: serviceProviderId, companyId, barringProfileId  MOType: CallBarringProfile@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get Call Barring Profile response -->
	<xs:element name="getCallBarringProfileResponse" type="BceCallBarringProfileType">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Call Barring Profile
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- delete Call Barring Profile MOId: serviceProviderId, companyId, barringProfileId  MOType: CallBarringProfile@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
