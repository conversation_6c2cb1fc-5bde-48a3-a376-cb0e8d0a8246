<!-- RoamingServiceArea Adaptation, RoamingServiceArea example, RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/ -->
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
             targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
    <jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
        <!-- disable wrapper style generation -->
        <jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
    </jaxws:bindings>
    <types>
        <xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   xmlns:rsa="http://schemas.ericsson.com/pg/hlr/13.5/"
                   xmlns:pg="http://schemas.ericsson.com/pg/1.0"
                   xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified"
                   attributeFormDefault="unqualified">
            <xs:include schemaLocation="../../../schemas/2G_3G/RoamingProfile/cai3g1.2_provisioning_without_cai3gfault.xsd"/>
            <xs:import namespace="http://schemas.ericsson.com/pg/1.0"
                       schemaLocation="../../../schemas/2G_3G/RoamingProfile/PGFault.xsd" />
            <xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/"
                       schemaLocation="../../../schemas/2G_3G/RoamingProfile/RoamingServiceArea.xsd"/>
            <!-- SetRoamingServiceArea
            MOId: Either rsa and raid
            MOType: RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/
            -->
            <xs:element name="Set">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOType" type="xs:string"
                                    fixed="RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/"/>
                        <xs:element name="MOId">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="rsa:rsa"/>
                                    <xs:element ref="rsa:raid"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="MOAttributes" minOccurs="1">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="rsa:SetRoamingServiceArea"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <!-- GetRoamingServiceArea
            MOId: rsa
            MOType: RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/
            -->
            <xs:element name="Get">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOType" type="xs:string"
                                    fixed="RoamingServiceArea@http://schemas.ericsson.com/pg/hlr/13.5/"/>
                        <xs:element name="MOId">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="rsa:rsa"/>
                                    <xs:element ref="rsa:frontendid" minOccurs="0" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <!-- GetRoamingServiceArea
            -->
            <xs:element name="GetResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="MOAttributes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="rsa:HlrRoamingServiceAreaData"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Cai3gFault">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="faultcode" type="xs:integer"/>
                        <xs:element name="faultreason">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="reasonText" type="xs:string" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="faultrole" type="xs:string"/>
                        <xs:element name="details" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element ref="pg:PGFault"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:schema>
    </types>
    <message name="SetRequest">
        <part name="parameters" element="cai3g:Set"/>
    </message>
    <message name="SetResponse">
        <part name="parameters" element="cai3g:SetResponse"/>
    </message>
    <message name="GetRequest">
        <part name="parameters" element="cai3g:Get"/>
    </message>
    <message name="GetResponse">
        <part name="parameters" element="cai3g:GetResponse"/>
    </message>
    <message name="HeadInfo">
        <part name="sessionId" element="cai3g:SessionId"/>
        <part name="transactionId" element="cai3g:TransactionId"/>
        <part name="sequenceId" element="cai3g:SequenceId"/>
    </message>
    <message name="Cai3gFault">
        <part name="parameters" element="cai3g:Cai3gFault"/>
    </message>
    <message name="Cai3gHeaderFault">
        <part name="sessionIdFault" element="cai3g:SessionIdFault"/>
        <part name="transactionIdFault" element="cai3g:TransactionIdFault"/>
        <part name="sequenceIdFault" element="cai3g:SequenceIdFault"/>
    </message>
    <portType name="RoamingServiceArea">
        <operation name="Set">
            <input message="cai3g:SetRequest"/>
            <output message="cai3g:SetResponse"/>
            <fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
        </operation>
        <operation name="Get">
            <input message="cai3g:GetRequest"/>
            <output message="cai3g:GetResponse"/>
            <fault name="Cai3gFault" message="cai3g:Cai3gFault"/>
        </operation>
    </portType>
    <binding name="RoamingServiceArea" type="cai3g:RoamingServiceArea">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="Set">
            <soap:operation soapAction="CAI3G#Set" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
                </soap:header>
            </output>
            <fault name="Cai3gFault">
                <soap:fault name="Cai3gFault" use="literal"/>
            </fault>
        </operation>
        <operation name="Get">
            <soap:operation soapAction="CAI3G#Get" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
                <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="transactionId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
                </soap:header>
                <soap:header message="cai3g:HeadInfo" part="sequenceId" use="literal">
                    <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
                </soap:header>
            </output>
            <fault name="Cai3gFault">
                <soap:fault name="Cai3gFault" use="literal"/>
            </fault>
        </operation>

    </binding>
    <service name="Provisioning">
        <port name="RoamingServiceArea" binding="cai3g:RoamingServiceArea">
            <soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
        </port>
    </service>
</definitions>
