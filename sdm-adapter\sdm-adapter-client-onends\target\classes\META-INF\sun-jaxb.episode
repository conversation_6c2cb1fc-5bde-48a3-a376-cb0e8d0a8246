<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bindings xmlns="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
      
    <!--

This file was generated by the Eclipse Implementation of JAXB, v2.3.6 
See https://eclipse-ee4j.github.io/jaxb-ri 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2024.09.18 at 12:02:29 AM IST 

  -->
      
    <bindings xmlns:tns="urn:siemens:names:prov:gw:SPML:2:0" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="~tns:AddRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AddRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AddResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AddResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ModifyRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ModifyRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ModifyResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ModifyResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DeleteRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.DeleteRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DeleteResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.DeleteResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SearchRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SearchResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:CancelRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.CancelRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:CancelResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.CancelResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:StatusRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.StatusRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:StatusResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.StatusResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BatchRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BatchRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BatchResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BatchResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ChangeIdRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ChangeIdRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ChangeIdResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ChangeIdResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SpmlResponseConfirmation">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SpmlResponseConfirmation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BulkRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BulkRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BulkResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BulkResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BulkResponseFile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BulkResponseFile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedBulkRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedBulkRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedBulkOperationResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedBulkOperationResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedBulkResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedBulkResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedBulkResponseFile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedBulkResponseFile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MigrationRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.MigrationRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MigrationOperationResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.MigrationOperationResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MigrationResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.MigrationResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtendedResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtendedResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Attribute">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.Attribute"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Attributes">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.Attributes"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExtensibleObject">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExtensibleObject"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:FirstClassObject">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.FirstClassObject"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SecondClassObject">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SecondClassObject"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SecondClassObjects">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SecondClassObjects"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SpmlModification">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SpmlModification"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ErrorRecomendation">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ErrorRecomendation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ErrorHintInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ErrorHintInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ValidationErrorHintInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ValidationErrorHintInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ErrorHint">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ErrorHint"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SpmlRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SpmlRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SpmlResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SpmlResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BatchableRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BatchableRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BatchableResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BatchableResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AliasType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AliasType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchBase">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SearchBase"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Filter">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.Filter"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:FilterSet">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.FilterSet"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AttributeValueAssertion">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AttributeValueAssertion"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AttributeDescription">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AttributeDescription"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SubstringFilter">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SubstringFilter"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ID">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ID"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Identifier">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.Identifier"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:IdentifierFileNameType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.IdentifierFileNameType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AbstractMigration">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AbstractMigration"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AbstractOperation">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.AbstractOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:relocationOperation">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.RelocationOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ExecutionType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ExecutionType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResultCode">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ResultCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ProcessingType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ProcessingType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OnErrorType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.OnErrorType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ModifyOperationType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ModifyOperationType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:CancelResultType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.CancelResultType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:StatusReturnsType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.StatusReturnsType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ReturnResultingObjectType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ReturnResultingObjectType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ModificationTypeMappingScope">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ModificationTypeMappingScope"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:LanguageType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.LanguageType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ErrorType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ErrorType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ErrorActionCode">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ErrorActionCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchStatus">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SearchStatus"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BulkOperationType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.BulkOperationType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:IdentifierFilterType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.IdentifierFilterType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResponseFileStatus">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ResponseFileStatus"/>
                
        </bindings>
          
    </bindings>
      
    <bindings xmlns:tns="urn:siemens:names:prov:gw:HSS_UNIFIED_NSR:1:0" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="~tns:PsRoamingArea">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PsRoamingArea"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:VplmnIdAlternateResultCode">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.VplmnIdAlternateResultCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PsRoamAreaMmeAddr">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PsRoamAreaMmeAddr"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:RoamSubscriptionInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.RoamSubscriptionInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssImeisvWildCardList">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssImeisvWildCardList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:RoamPlan">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.RoamPlan"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ODBPlan">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ODBPlan"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EPSPDNPlan">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EPSPDNPlan"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ScsGroup">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ScsGroup"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssSharedProfile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssSharedProfile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssSharedPDNContext">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssSharedPDNContext"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PrivacyProfileRegister">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PrivacyProfileRegister"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPDNToSPCMapping">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPDNToSPCMapping"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsSgsnMccMnc">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsSgsnMccMnc"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsEnterpriseData">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsEnterpriseData"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsIoTIdentityList">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsIoTIdentityList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssAPNProperties">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssAPNProperties"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EPSQos">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EPSQos"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:VplmnIdAlternateResultCodeList">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.VplmnIdAlternateResultCodeList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MMEaddress">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.MMEaddress"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ERoamPlanInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ERoamPlanInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnContextBlocking">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnContextBlocking"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HplmnList">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HplmnList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:LipaAllowedVplmnList">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.LipaAllowedVplmnList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResetTriggerInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ResetTriggerInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DeviceProfile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.DeviceProfile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:QualityOfServiceProfile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.QualityOfServiceProfile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssSharedIoTServiceData">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssSharedIoTServiceData"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SharedImsiGroupId">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SharedImsiGroupId"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssLocalGroupIdBits">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssLocalGroupIdBits"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:RefScsGroupName">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.RefScsGroupName"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssAdditionalServices">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssAdditionalServices"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsAAAData">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsAAAData"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EPSPsRszi">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EPSPsRszi"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EPSCsg">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EPSCsg"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EPSCsgId">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EPSCsgId"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PdnChargingCharacteristics">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PdnChargingCharacteristics"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:GeneralChargingCharacteristics">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.GeneralChargingCharacteristics"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SendResetProfile">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SendResetProfile"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:S6aS6dClientInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.S6AS6DClientInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HssBlockAPN">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.HssBlockAPN"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsAPNBlockProtocol">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsAPNBlockProtocol"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PreEmption">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PreEmption"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsAccessRestriction">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsAccessRestriction"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ArdTreatment">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ArdTreatment"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsCnrTreatment">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsCnrTreatment"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsStrTreatment">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsStrTreatment"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsRfspIndexTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsRfspIndexTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsApnOIReplTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsApnOIReplTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsDefContextIdTreatmentTreat">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsDefContextIdTreatmentTreat"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsUserAmbrTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsUserAmbrTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsTracingTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsTracingTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsSessionTimeoutTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsSessionTimeoutTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsAccessApnTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsAccessApnTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsNotAllowedRATType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsNotAllowedRATType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsNotAllowedRATTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsNotAllowedRATTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsMipFeatureVector">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsMipFeatureVector"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsMip6VectorTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsMip6VectorTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsDeviceProfileTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsDeviceProfileTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsIcsIndicatorTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsIcsIndicatorTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsOdbIdType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsOdbIdType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsOdbTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsOdbTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnAddressTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnAddressTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsQosTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsQosTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsVplmnAddrAllowedTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsVplmnAddrAllowedTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsApnAmbrTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsApnAmbrTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnContextBlockingTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnContextBlockingTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnTypeTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnTypeTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnGwAllocTypeTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnGwAllocTypeTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnGwIdentityTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnGwIdentityTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsLipaPermissionTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsLipaPermissionTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsLipaPermissionType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsLipaPermissionType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResetTriggerInterface">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ResetTriggerInterface"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DelayClass">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.DelayClass"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ReliabilityClass">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ReliabilityClass"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PrecedenceClass">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PrecedenceClass"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:TrafficClass">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.TrafficClass"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DeliveryOfErrorneousDataUnit">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.DeliveryOfErrorneousDataUnit"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:TrafficHandlingPriority">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.TrafficHandlingPriority"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SourceStatisticsDescriptor">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.SourceStatisticsDescriptor"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:PdpTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.PdpTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsMpsPriorityTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsMpsPriorityTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsRestorationPriorityTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsRestorationPriorityTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnOnDemandType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnOnDemandType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsBarringType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsBarringType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsSUPUserAmbrTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsSUPUserAmbrTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MonteTypeUeAllowed">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.MonteTypeUeAllowed"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnContextTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnContextTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsServiceSelectionTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsServiceSelectionTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsPdnContextIdTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsPdnContextIdTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EpsSPCApnAmbrTreatmentType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EpsSPCApnAmbrTreatmentType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:RejectRequestType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.RejectRequestType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OdbBarringRoam">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.OdbBarringRoam"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NetworkAccessMode">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.NetworkAccessMode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OdbPOAccess">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.OdbPOAccess"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ChargingCharacteristics">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.ChargingCharacteristics"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EnterpriseAaaApn">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EnterpriseAaaApn"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:EnterpriseAaaMissingParams">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.EnterpriseAaaMissingParams"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NonIpDataDeliveryMechType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.NonIpDataDeliveryMechType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:GroupType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.onends.soap.hssunifiednsr.GroupType"/>
                
        </bindings>
          
    </bindings>
    
</bindings>
