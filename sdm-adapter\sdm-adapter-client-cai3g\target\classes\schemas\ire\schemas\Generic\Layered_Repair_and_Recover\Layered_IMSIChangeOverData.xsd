<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:x="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:include schemaLocation="../../../schemas/Generic/Layered_Repair_and_Recover/types/cudb_types.xsd" />
	<!-- IMSIChangeOverData MOId: mscId1 MOType: IMSIChangeOverData@http://schemas.ericsson.com/ma/cudb/ -->
	<xs:element name="mscId1" type="mscIdType" />
	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="imsiAux" type="imsiType" />
	<xs:element name="mscId" type="mscIdType" />

	<xs:element name="SetIMSIChangeOverData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="nimsi" type="imsiType" />
				<xs:element name="mscId2" type="mscIdType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="mscId1" type="mscIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="mscId1Attr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
