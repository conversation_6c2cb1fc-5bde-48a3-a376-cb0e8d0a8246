<!-- edited with XMLSpy v2011 sp1 (http://www.altova.com) by <PERSON> (<PERSON><PERSON>) -->
<!-- <PERSON><PERSON>, Equipments - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/EIR/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<!-- CreateEquipments MOId: imei<PERSON>rom, imeiTo, svn (optional) MOType: CreateEquipments@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="imeiFrom" type="imeiType" />
	<xs:element name="imeiTo" type="imeiType" />
	<xs:element name="svn" type="svnType" />
	<xs:element name="CreateEquipments">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imeiFrom" type="imeiType" />
				<xs:element name="imeiTo" type="imeiType" />
				<xs:element name="svn" type="svnType" default="0F" minOccurs="0" />
				<xs:element name="date" type="dateType" minOccurs="0" />
				<xs:element name="time" type="timeType" minOccurs="0" />
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:element name="InsertEquipmentListType">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
							<xs:element name="insertReason" type="insertReasonType" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="clarifyReason" type="clarifyReasonType" minOccurs="0" />
				<xs:element name="sourceOfRequest" type="sourceOfRequestType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imeiFrom" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiFromAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imeiTo" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiToAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imeiFrom">
			<xs:selector xpath="./x:imeiFrom" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imeiFrom" refer="key_imeiFrom">
			<xs:selector xpath="." />
			<xs:field xpath="@imeiFrom" />
		</xs:keyref>
		<xs:key name="key_imeiTo">
			<xs:selector xpath="./x:imeiTo" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imeiTo" refer="key_imeiTo">
			<xs:selector xpath="." />
			<xs:field xpath="@imeiTo" />
		</xs:keyref>
	</xs:element>
	<!-- DeleteEquipments MOId: imeiFrom, imeiTo, svn (optional) MOType: DeleteEquipments@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="DeleteEquipments">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:element name="removeReason" type="removeReasonType" minOccurs="0" />
				<xs:element name="clarifyReason" type="clarifyReasonType" minOccurs="0" />
				<xs:element name="sourceOfRequest" type="sourceOfRequestType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imeiFrom" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiFromAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imeiTo" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiToAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
