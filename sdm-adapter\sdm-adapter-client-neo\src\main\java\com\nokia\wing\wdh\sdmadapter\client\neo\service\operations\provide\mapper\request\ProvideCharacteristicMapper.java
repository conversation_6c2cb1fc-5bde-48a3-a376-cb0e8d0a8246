package com.nokia.wing.wdh.sdmadapter.client.neo.service.operations.provide.mapper.request;

import com.nokia.wing.wdh.sdmadapter.client.neo.service.shared.mapper.AbstractCharacteristicMapper;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.NeoConstants;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.OperationType;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.RequestContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Characteristic mapper for PROVIDE operations
 *
 * DESIGN DECISION: This mapper uses ONLY real data from SDM DTOs, configuration properties,
 * and constants. No hardcoded fallback defaults are used to ensure PROVIDE requests contain
 * only actual subscriber service data.
 *
 * Data Sources (in priority order):
 * 1. SDM DTO data extracted by NeoProvideRequestMapper
 * 2. Configuration properties from NeoClientProperties.Defaults
 * 3. Constants from NeoConstants and NeoMappingConstants classes
 *
 */
public class ProvideCharacteristicMapper extends AbstractCharacteristicMapper {

    private static final Logger log = LoggerFactory.getLogger(ProvideCharacteristicMapper.class);

    public ProvideCharacteristicMapper() {
        super(OperationType.PROVIDE);
    }

    @Override
    protected Map<String, String> getCharacteristicDefinitions(RequestContext context) {
        log.debug("Creating Provide service characteristics directly from parameter map");
        Map<String, String> characteristics = new LinkedHashMap<>();

        // Get all parameters from context and convert them to characteristics
        Map<String, Object> parameters = context.getParameters();

        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // Future...may be???
            // Skip internal/system parameters that shouldn't become characteristics
            // or add something that is missed
            if (isSystemParameter(key)) {
                log.trace("Skipping system parameter: {}", key);
                continue;
            }

            // Convert parameter value to string
            String stringValue = convertToString(value);
            if (stringValue != null && !stringValue.trim().isEmpty()) {
                characteristics.put(key, stringValue);
                log.debug("Added characteristic '{}' with real value: '{}'", key, stringValue);
            } else {
                log.debug("Skipped characteristic '{}' - null or empty value", key);
            }
        }

        // Add missing priority parameter,if any
        characteristics.put(NeoConstants.PRIORITY, String.valueOf(NeoConstants.PRIORITY_PROVIDE));
        log.debug("Added priority characteristic with default value {}", NeoConstants.PRIORITY_PROVIDE);

        log.debug("Created {} Provide characteristics directly from parameter map (efficient approach)", characteristics.size());
        return characteristics;
    }

    /**
     * Check if a parameter is a system/internal parameter that shouldn't become a characteristic
     *
     * @param key The parameter key
     * @return true if this is a system parameter, false if it should become a characteristic
     */
    private boolean isSystemParameter(String key) {
        // System parameters that are used for internal processing but shouldn't become characteristics
        return "operationType".equals(key) ||
               key.startsWith("_") || // Convention for internal parameters
               key.startsWith("system.");
    }

    /**
     * Convert parameter value to string representation
     *
     * @param value The parameter value
     * @return String representation or null if value is null
     */
    private String convertToString(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return (String) value;
        }

        // Handle other types that might be in the parameter map
        return value.toString();
    }
}
