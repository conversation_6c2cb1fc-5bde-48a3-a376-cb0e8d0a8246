<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:ns="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
  <xs:element name="e164" type="e164Type"/>
  <xs:element name="msisdn" type="msisdnType"/>
  <xs:element name="createDNSSubscription">
    <xs:complexType>
      <xs:sequence>
        <xs:choice>
          <xs:element name="msisdn" type="msisdnType"/>
          <xs:element name="e164" type="e164Type"/>
        </xs:choice>
        <xs:element name="dnsname" type="xs:string" minOccurs="0"/>
        <xs:element name="domainName" type="xs:string" minOccurs="0"/>
        <xs:element name="records" nillable="true" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType"/>
              <xs:element name="order" type="naptrOrderType"/>
              <xs:element name="preference" type="naptrPreferenceType"/>
              <xs:element name="service" type="naptrServiceType"/>
              <!--new attribute for 12B -->
              <xs:element name="propBlocking" type="propBlockingType" minOccurs="0"/>
              <xs:element name="txt" type="xs:string" minOccurs="0"/>
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>

              <xs:element name="ttl" type="naptrTtlType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdValueType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="publicIdAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
        <xs:element name="rangeRecords" nillable="true" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType" minOccurs="0"/>
              <xs:element name="order" type="naptrOrderType" minOccurs="0"/>
              <xs:element name="preference" type="naptrPreferenceType" minOccurs="0"/>
              <xs:element name="service" type="naptrServiceType" minOccurs="0"/>
              <!--new attribute for 12B -->
              <xs:element name="viewId" type="xs:string" minOccurs="0"/>
              <xs:element name="scope" type="xs:string" minOccurs="0"/>
              <xs:element name="updateLevel" type="xs:unsignedInt" minOccurs="0"/>
              <xs:element name="destNode" type="xs:string" minOccurs="0"/>
              <xs:element name="callServer" type="xs:string" minOccurs="0"/>
              <xs:element name="oliAlias" type="xs:string" minOccurs="0"/>
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>

            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdValueType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="publicIdAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="msisdn" type="msisdnType" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="msisdnAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="e164" type="e164Type" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="e164Attr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
    <xs:key name="CreateMsisdnKey" id="CAI3GKeyCreateMsisdn">
      <xs:selector xpath="./ns:msisdn"/>
      <xs:field xpath="."/>
    </xs:key>
    <xs:keyref name="CreateMsisdnKeyRef" refer="CreateMsisdnKey">
      <xs:selector xpath="."/>
      <xs:field xpath="@msisdn"/>
    </xs:keyref>
    <xs:key name="Create164Key" id="CAI3GKeyCreate164">
      <xs:selector xpath="./ns:e164"/>
      <xs:field xpath="."/>
    </xs:key>
    <xs:keyref name="Create164KeyRef" refer="Create164Key">
      <xs:selector xpath="."/>
      <xs:field xpath="@e164"/>
    </xs:keyref>
  </xs:element>
  <xs:element name="setDNSSubscription">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="dnsname" type="xs:string" minOccurs="0"/>
        <xs:element name="domainName" type="xs:string" minOccurs="0"/>
        <xs:element name="records" nillable="true" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType" minOccurs="0"/>
              <xs:element name="order" type="naptrOrderType" minOccurs="0"/>
              <xs:element name="preference" type="naptrPreferenceType" minOccurs="0"/>
              <xs:element name="service" type="naptrServiceType" minOccurs="0"/>
              <!--new attribute for 12B -->
              <xs:element name="propBlocking" type="propBlockingType" minOccurs="0"/>
              <xs:element name="txt" type="xs:string" minOccurs="0"/>
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>

              <xs:element name="ttl" type="naptrTtlType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdValueType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="publicIdAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="naptrTxt" type="xs:string" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="naptrTxtAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="service" type="naptrServiceType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="serviceAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="flags" type="naptrFlagsType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="flagsAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="order" type="naptrOrderType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="orderAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="preference" type="naptrPreferenceType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="preferenceAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
        <xs:element name="rangeRecords" nillable="true" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType" minOccurs="0"/>
              <xs:element name="order" type="naptrOrderType" minOccurs="0"/>
              <xs:element name="preference" type="naptrPreferenceType" minOccurs="0"/>
              <xs:element name="service" type="naptrServiceType" minOccurs="0"/>
              <!--new attribute for 12B -->
              <xs:element name="viewId" type="xs:string" minOccurs="0"/>
              <xs:element name="scope" type="xs:string" minOccurs="0"/>
              <xs:element name="updateLevel" type="xs:unsignedInt" minOccurs="0"/>
              <xs:element name="destNode" type="xs:string" minOccurs="0"/>
              <xs:element name="callServer" type="xs:string" minOccurs="0"/>
              <xs:element name="oliAlias" type="xs:string" minOccurs="0"/>
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>

            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdValueType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="publicIdAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="service" type="naptrServiceType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="serviceAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="naptrTxt" type="xs:string" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="naptrTxtAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="flags" type="naptrFlagsType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="flagsAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="order" type="naptrOrderType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="orderAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="preference" type="naptrPreferenceType" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="preferenceAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="viewId" type="xs:string" use="optional">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="viewIdAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="msisdn" type="msisdnType" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="msisdnAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="e164" type="e164Type" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="e164Attr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="getDNSSubscription">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="dnsname" type="xs:string" minOccurs="0"/>
        <xs:element name="domainName" type="xs:string" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="msisdn" type="msisdnType" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="msisdnAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="e164" type="e164Type" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="e164Attr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="getResponseDNSSubscription">
    <xs:complexType>
      <xs:sequence>
        <xs:choice>
          <xs:element name="msisdn" type="msisdnType"/>
          <xs:element name="e164" type="e164Type"/>
        </xs:choice>
        <xs:element name="records" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdRespValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType"/>
              <xs:element name="order" type="naptrOrderType"/>
              <xs:element name="preference" type="naptrPreferenceType"/>
              <xs:element name="service" type="naptrServiceType"/>
              <!--new attribute for 12B -->
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>
              <xs:element name="updateLevel" type="xs:unsignedInt" minOccurs="0"/>
              <xs:element name="updateType" type="xs:string" minOccurs="0"/>
              <xs:element name="propBlocking" type="propBlockingType" minOccurs="0"/>
              <xs:element name="txt" type="xs:string" minOccurs="0"/>
              <xs:element name="ttl" type="naptrTtlType" minOccurs="0"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="rangeRecords" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="publicId" type="publicIdRespValueType" minOccurs="0"/>
              <xs:element name="flags" type="naptrFlagsType" minOccurs="0"/>
              <xs:element name="order" type="naptrOrderType" minOccurs="0"/>
              <xs:element name="preference" type="naptrPreferenceType" minOccurs="0"/>
              <xs:element name="service" type="naptrServiceType" minOccurs="0"/>
              <!--new attribute for 12B -->
              <xs:element name="naptrTxt" type="xs:string" minOccurs="0"/>
              <xs:element name="viewId" type="xs:string" minOccurs="0"/>
              <xs:element name="scope" type="xs:string" minOccurs="0"/>
              <xs:element name="enumZoneId" type="xs:string" minOccurs="0"/>
              <xs:element name="updateLevel" type="xs:unsignedInt" minOccurs="0"/>
              <xs:element name="rrflag" type="xs:string" minOccurs="0"/>
              <xs:element name="minDigits" type="xs:string" minOccurs="0"/>
              <xs:element name="maxDigits" type="xs:string" minOccurs="0"/>
              <xs:element name="propBlocking" type="propBlockingType" minOccurs="0"/>
              <xs:element name="destNode" type="xs:string" minOccurs="0"/>
              <xs:element name="callServer" type="xs:string" minOccurs="0"/>
              <xs:element name="oliAlias" type="xs:string" minOccurs="0"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="msisdn" type="msisdnType" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="msisdnAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="e164" type="e164Type" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="e164Attr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
    <xs:key name="GetMsisdnKey" id="CAI3GKeyGetMsisdn">
      <xs:selector xpath="./ns:msisdn"/>
      <xs:field xpath="."/>
    </xs:key>
    <xs:keyref name="GetMsisdnKeyRef" refer="GetMsisdnKey">
      <xs:selector xpath="."/>
      <xs:field xpath="@msisdn"/>
    </xs:keyref>
    <xs:key name="Get164Key" id="CAI3GKeyGet164">
      <xs:selector xpath="./ns:e164"/>
      <xs:field xpath="."/>
    </xs:key>
    <xs:keyref name="Get164KeyRef" refer="Get164Key">
      <xs:selector xpath="."/>
      <xs:field xpath="@e164"/>
    </xs:keyref>
  </xs:element>
  <xs:element name="deleteDNSSubscription">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="dnsname" type="xs:string" minOccurs="0"/>
        <xs:element name="domainName" type="xs:string" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="msisdn" type="msisdnType" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="msisdnAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="e164" type="e164Type" use="optional">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="e164Attr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>

  <!-- "n" or "nu" = Regular Expression    "r" = Replacement string  -->
  <xs:simpleType name="naptrFlagsType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="n"/>
      <xs:enumeration value="nu"/>
      <xs:enumeration value="r"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="naptrServiceType">
    <xs:restriction base="xs:string">
      <xs:pattern value=".{1,32}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="naptrOrderType">
    <xs:restriction base="xs:unsignedInt">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="65535"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="naptrPreferenceType">
    <xs:restriction base="xs:unsignedInt"/>
  </xs:simpleType>
  <xs:simpleType name="msisdnType">
    <xs:restriction base="xs:string">
      <xs:pattern value="tel:.{1,256}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="e164Type">
    <xs:restriction base="xs:string">
      <xs:pattern value=".{1,256}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="publicIdValueType">
    <xs:restriction base="xs:string">
      <xs:pattern value="sip:.{1,256}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="publicIdRespValueType">
    <xs:restriction base="xs:string">
      <xs:pattern value=".{0,256}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="propBlockingType">
    <xs:restriction base="xs:unsignedInt">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="100"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="naptrTtlType">
    <xs:restriction base="xs:string">
      <xs:minLength value="1"/>
      <xs:maxLength value="11"/>
      <xs:pattern value="[0-9]*" />
    </xs:restriction>
  </xs:simpleType>
</xs:schema>