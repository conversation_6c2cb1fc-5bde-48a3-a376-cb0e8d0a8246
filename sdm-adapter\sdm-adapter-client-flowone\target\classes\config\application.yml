# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================



spring:
  application:
    name: SdmAdapterService
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: #dev

sdmadpterclient:
  flowone:
    includeQosInApn: false
    trustStore: classpath:config/keys/poc_ntt.jks
    keyStore: classpath:config/keys/ntsysopt2-client.keystore
    password: provgw
    url: https://**********/ProvisioningGateway/services/SPMLNWSubscriber10Service
    hlrNsrUrl: https://**********/ProvisioningGateway/services/SPMLHlrNsr21Service 
    hssUnifiedNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
    netType: USCC
    orderNo: CRM_ORDER_NUMBER
    ReqUser: WDH_USER 
    http:
      maxConnections: 25
      maxConnectionsPerRoute: 15 