<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="active" type="xs:boolean"/>
<xs:element name="dp" type="dpType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateMobilityManagementINTriggeringSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="dp" type="dpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="SetMobilityManagementINTriggeringSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="dp" type="dpType"/>
<xs:choice>
<xs:sequence>
<xs:element name="sk" type="skType"/>
<xs:element minOccurs="0" name="gsa" type="gsaType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="gsa" type="gsaType"/>
</xs:sequence>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetMobilityManagementINTriggeringSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="MobilityManagementINTriggeringSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element maxOccurs="unbounded" name="DetectionPointData">
<xs:complexType>
<xs:sequence>
<xs:element name="dp" type="dpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element minOccurs="0" name="dpstatus" type="dpstatusType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DeleteMobilityManagementINTriggeringSubscriptionData">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
