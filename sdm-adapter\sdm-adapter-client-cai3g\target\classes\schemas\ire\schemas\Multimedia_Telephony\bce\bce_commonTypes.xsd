<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ma/bce/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!--
        UpCommonId is the id of the object that is provisioned or the id of the object the provisioned
        object is attached to.
    -->
	<xs:complexType name="UpCommonId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId" minOccurs="0"/>
			<!-- OrganizationUnitId contains the relative path of organization unit ids from the company level -->
			<xs:element name="organizationUnitId" type="OrganizationUnitId" minOccurs="0"/>
			<xs:element name="objectId" type="ObjectId" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganizationUnitId">
		<xs:sequence>
			<xs:element name="organizationUnitIds" type="UpOrganizationIdType" maxOccurs="3"/>
		</xs:sequence>
	</xs:complexType>
	<!--
        UpObject encapsulates objects that are provisioned through the generic CRUD provisioning API. The supported objects
        are listed in UpDataObject defined below.
    -->
	<xs:complexType name="UpObject">
		<xs:sequence>
			<xs:element name="objectType" type="UpObjectType"/>
			<xs:element name="object" type="UpDataObject"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Lists all objects that can be provisioned through the generic CRUD provisioning API. -->
	<xs:complexType name="UpDataObject">
		<xs:choice>
			<xs:element ref="UpNumberBag"/>
		</xs:choice>
	</xs:complexType>
	<!-- Lists the types of all objects that can be provisioned through the generic CRUD provisioning API. -->
	<xs:simpleType name="UpObjectType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UpMobileNumberBag"/>
			<xs:enumeration value="UpFixedNumberBag"/>
			<xs:enumeration value="UpClipNumberBag"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Number bag definition -->
	<xs:element name="UpNumberBag">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="numberRange" type="UpNumberRange" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpProvisioningFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="code" type="xs:string"/>
				<xs:element name="header" type="xs:string"/>
				<xs:element name="message" type="xs:string" minOccurs="0"/>
				<xs:element name="localizedMessage" type="xs:string" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- ConnectionProfile definition -->
	<xs:complexType name="UpConnectionProfileId">
		<xs:sequence>
			<xs:element name="connectionProfileId" type="ConnectionProfileId" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpConnectionProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="UpConnectionProfileId"/>
				<xs:element name="dbSchema" type="NameType" minOccurs="0"/>
				<xs:element name="directoryType" type="DirectoryType" minOccurs="0"/>
				<xs:element name="searchDomain" type="NameType" minOccurs="0"/>
				<xs:element name="url" type="UrlType" minOccurs="0"/>
				<xs:element name="userName" type="UserNameType" minOccurs="0"/>
				<xs:element name="password" type="PasswordType" minOccurs="0"/>
				<xs:element name="vendor" type="NameType" minOccurs="0"/>
				<xs:element name="schema" type="UpDirectorySchema" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- ServiceProvider definition -->
	<xs:complexType name="UpDirectorySchema">
		<xs:sequence>
			<xs:element name="fields" type="UpDirectorySchemaField" maxOccurs="unbounded"/>
			<xs:element name="uniqueId" type="UpFieldType"/>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpDirectorySchemaField">
		<xs:sequence>
			<xs:element name="name" type="NameType"/>
			<xs:element name="type" type="UpFieldType"/>
			<xs:element name="searchable" type="xs:boolean"/>
			<xs:element name="primaryField" type="xs:boolean"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpFieldType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ID"/>
			<xs:enumeration value="FIRSTNAME"/>
			<xs:enumeration value="LASTNAME"/>
			<xs:enumeration value="DISPLAYNAME"/>
			<xs:enumeration value="PHONE"/>
			<xs:enumeration value="MOBILE"/>
			<xs:enumeration value="EMAIL"/>
			<xs:enumeration value="SIPURI"/>
			<xs:enumeration value="EXTENSION"/>
			<xs:enumeration value="TITLE"/>
			<xs:enumeration value="DEPARTMENT"/>
			<xs:enumeration value="LOCATION"/>
			<xs:enumeration value="PICTURE"/>
			<xs:enumeration value="STREETADDRESS"/>
			<xs:enumeration value="ZIPCODE"/>
			<xs:enumeration value="CITY"/>
			<xs:enumeration value="REGION"/>
			<xs:enumeration value="COUNTRY"/>
			<xs:enumeration value="SKILLS"/>
			<xs:enumeration value="ROLE"/>
			<xs:enumeration value="COMPANY"/>
			<xs:enumeration value="WEBURL"/>
			<xs:enumeration value="FAX"/>
			<xs:enumeration value="UNDEFINED"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Directory definition -->
	<xs:element name="UpDirectory">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="NameType"/>
				<!-- Indicates if this directory is the default directory to use when the user makes a search
                and no directory is specified -->
				<xs:element name="default" type="xs:boolean" minOccurs="0"/>
				<xs:element name="minpartialmatch" type="xs:int" minOccurs="0"/>
				<xs:element name="connectionProfileId" type="UpConnectionProfileId" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- ServiceProvider definition -->
	<xs:complexType name="UpServiceProviderId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpServiceProvider">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="UpServiceProviderId"/>
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="country" type="CountryType" minOccurs="0"/>
				<!-- Call type prefixes: Fixed types.  This element allows only for a fixed enum set. -->
				<xs:element name="callPrefix" type="UpServiceProviderPrefix" minOccurs="0" maxOccurs="unbounded"/>
				<!-- Configurable call type prefixes: Free format (String, String) -->
				<xs:element name="configurableCallPrefix" type="UpCallTypePrefixType" minOccurs="0" maxOccurs="unbounded"/>
				<!-- Optional. The attribute is mandatory for installations where charging has been enabled, and
                for charging integration purposes the value configured per service provider must then be
                aligned with the CSCF charging configuration and parameter "CscfChargingInterOpId"
                -->
				<xs:element name="domain" type="NameType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- Company definition -->
	<xs:complexType name="UpCompanyId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpCompany">
		<xs:complexType>
			<xs:sequence>
				<!-- Company provisioning identity -->
				<xs:element name="id" type="UpCompanyId"/>
				<!-- Limitations regarding objects belonging to this company -->
				<xs:element ref="UpLimitations" minOccurs="0"/>
				<!-- Company Attributes -->
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="address" type="AddressType" minOccurs="0"/>
				<xs:element name="searchCountLimit" type="xs:int" minOccurs="0"/>
				<!-- Call type prefixes: Fixed types.  This element allows only for a fixed enum set. -->
				<xs:element name="callPrefix" type="UpCompanyPrefix" minOccurs="0" maxOccurs="unbounded"/>
				<!-- Configurable call type prefixes: Free format (String, String) -->
				<xs:element name="configurableCallPrefix" type="UpCallTypePrefixType" minOccurs="0" maxOccurs="unbounded"/>
				<!-- Company state can be set to PENDING or ACTIVE for SSP to use for restrictions -->
				<xs:element name="activeStatus" type="UpActiveStatusType" default="ACTIVE" minOccurs="0"/>
				<!-- Company child objects -->
				<xs:element ref="UpUser" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds" minOccurs="0"/>
				<xs:element name="domain" type="NameType" minOccurs="0"/>
				<xs:choice>
					<!-- Connection profile -->
					<xs:element name="connectionProfileId" type="UpConnectionProfileId" minOccurs="0"/>
					<!-- Directory -->
					<xs:element ref="UpDirectory" minOccurs="0" maxOccurs="unbounded"/>
				</xs:choice>
				<xs:element ref="UpAutoAttendant" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element ref="UpCdg" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element ref="UpManagerSecretaryGroup" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="addressPlanRule" type="UpAddressPlanType" minOccurs="0"/>
				<xs:element name="musicOnHold" type="UpFeatureState" minOccurs="0"/>
				<xs:element name="numberPresentation" type="UpNumberPresentation" minOccurs="0"/>
				<xs:element name="callMove" type="UpFeatureState" minOccurs="0"/>
				<xs:element name="audioConferencing" type="UpFeatureState" minOccurs="0"/>
				<!-- IWL, version 3.5 and later, site conferencing configuration -->
				<xs:element ref="UpIwlSiteConfiguration" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!--CDG-->
	<xs:complexType name="UpCdgId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="cdgId" type="UpCdgIdType"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Autoattendant definition -->
	<xs:complexType name="UpAutoAttendantId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="autoAttendantId" type="UpAutoAttendantIdType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpBagNumbers">
		<xs:sequence>
			<xs:element name="mobileNumber" type="NumberType" minOccurs="0"/>
			<xs:element name="fixedNumber" type="NumberType" minOccurs="0"/>
			<xs:element name="clipNumber" type="NumberType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpAutoAttendant">
		<xs:complexType>
			<xs:sequence>
				<!-- AutoAttendant provisioning identity -->
				<xs:element name="id" type="UpAutoAttendantId"/>
				<!-- AutoAttendant attributes -->
				<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
				<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
				<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
				<xs:element name="voiceMailAddress" type="GenericAddressType" minOccurs="0"/>
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="aaOpen" type="UpOpenEnum" minOccurs="0"/>
				<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
				<xs:element name="incomingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
				<!-- AutoAttendant embedded objects -->
				<xs:element ref="UpAutoAttendantMenuItemType" minOccurs="0" maxOccurs="10"/>
				<xs:element name="timeOutAction" type="UpAutoAttendantMenuActionType" minOccurs="0"/>
				<xs:element name="wrongSelectionAction" type="UpAutoAttendantMenuActionType" minOccurs="0"/>
				<xs:element name="openingHours" type="UpOpeningHourType" minOccurs="0" maxOccurs="7"/>
				<xs:element name="announcements" type="UpAnnouncementType" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpCdg">
		<xs:complexType>
			<xs:sequence>
				<!-- CDG provisioning identity -->
				<xs:element name="id" type="UpCdgId"/>
				<!-- CDG Attributes -->
				<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
				<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
				<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="maxConcurrentAttempts" type="UpMaxConcurrentAttemptsType" minOccurs="0"/>
				<xs:element name="concurrentAttempts" type="xs:int" minOccurs="0"/>
				<xs:element name="clericalTimeOut" type="UpClericalTimeOutType" minOccurs="0"/>
				<xs:element name="noAnswerTimeOut" type="UpNoAnswerTimeOutType" minOccurs="0"/>
				<xs:element name="maxQueueSize" type="UpMaxQueueSizeType" minOccurs="0"/>
				<xs:element name="maxTimeInQueue" type="UpMaxTimeInQueueType" minOccurs="0"/>
				<xs:element name="cdgOpen" type="UpOpenEnum" minOccurs="0"/>
				<xs:element name="playWelcomeAnnouncement" type="xs:boolean" minOccurs="0"/>
				<xs:element name="playQueueAnnouncement" type="xs:boolean" minOccurs="0"/>
				<xs:element name="overflowWhenBusy" type="UpOverflowSituationType" minOccurs="0"/>
				<xs:element name="overflowWhenNoAgents" type="UpOverflowSituationType" minOccurs="0"/>
				<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
				<xs:element name="voiceMailAddress" type="GenericAddressType" minOccurs="0"/>
				<xs:element name="incomingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
				<xs:element name="outgoingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
				<xs:element name="agents" type="UpAgentType" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="openingHours" type="UpOpeningHourType" minOccurs="0" maxOccurs="7"/>
				<xs:element name="announcements" type="UpAnnouncementType" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="anonymousCallRejection" type="UpFeatureState" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- import user result -->
	<xs:element name="UpImportUserResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="UpUser"/>
				<xs:element ref="UpProvisioningFault"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- CalendarServer -->
	<xs:complexType name="UpCalendarServerId">
		<xs:sequence>
			<xs:element name="calendarServerId" type="CalendarServerId" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpCalendarServer">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="UpCalendarServerId"/>
				<xs:element name="calendarServerName" type="NameType" minOccurs="0"/>
				<xs:element name="pollingUserName" type="NameType" minOccurs="0"/>
				<xs:element name="address" type="UrlType" minOccurs="0"/>
				<xs:element name="pollingUserPassword" type="PasswordType" minOccurs="0"/>
				<xs:element name="servedDomain" type="NameType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- User definition -->
	<xs:complexType name="UpUserId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="userId" type="UserId"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Directory Credential-->
	<xs:complexType name="UpDirectoryCredential">
		<xs:sequence>
			<xs:element name="userName" type="UserNameType"/>
			<xs:element name="password" type="PasswordType" minOccurs="0"/>
			<xs:element name="directoryName" type="NameType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpUser">
		<xs:complexType>
			<xs:sequence>
				<!-- User provisioning identity -->
				<xs:element name="id" type="UpUserId"/>
				<!-- org unit ids -->
				<xs:element name="organizationUnitIds" type="UpOrganizationUnitIds" minOccurs="0"/>
				<!-- User attributes -->
				<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
				<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
				<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
				<xs:element name="firstName" type="FirstNameType" minOccurs="0"/>
				<xs:element name="lastName" type="LastNameType" minOccurs="0"/>
				<xs:element name="password" type="PasswordType" minOccurs="0"/>
				<xs:element name="expirePassword" type="xs:boolean" minOccurs="0"/>
				<xs:element name="email" type="GenericAddressType" minOccurs="0"/>
				<xs:element name="active" type="ActiveType" minOccurs="0"/>
				<xs:element name="nonTelephonyUser" type="xs:boolean" minOccurs="0"/>
				<xs:element name="contactAdministrator" type="AdministratorType" minOccurs="0"/>
				<xs:element name="administrator" type="AdministratorType" minOccurs="0"/>
				<xs:element name="systemAdministrator" type="AdministratorType" minOccurs="0"/>
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds" minOccurs="0"/>
				<xs:element name="callForwardingNoAnswer" type="CallForwardingType" minOccurs="0"/>
				<xs:element name="callForwardingUnconditional" type="CallForwardingType" minOccurs="0"/>
				<xs:element name="callForwardingWhenBusy" type="CallForwardingType" minOccurs="0"/>
				<xs:element name="callForwardingNotReachable" type="CallForwardingType" minOccurs="0"/>
				<xs:element name="callForwardingNotRegistered" type="CallForwardingType" minOccurs="0"/>
				<xs:element name="voiceMailAddress" type="GenericAddressType" minOccurs="0"/>
				<xs:element name="personalDiversionAddresses" type="PersonalDiversionAddressType" minOccurs="0"/>
				<xs:element name="parallelAlerting" type="ParallelAlertingType" minOccurs="0"/>
				<xs:element name="clientEnum" type="ClientTypeEnum" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="userName" type="UserNameType" minOccurs="0"/>
				<xs:element name="location" type="LocationType" minOccurs="0"/>
				<xs:element name="locationAddress" type="AddressType" minOccurs="0"/>
				<xs:element name="skills" type="SkillsType" minOccurs="0"/>
				<xs:element name="role" type="RoleType" minOccurs="0"/>
				<xs:element name="webUrl" type="WebUrlType" minOccurs="0"/>
				<xs:element name="fax" type="FaxType" minOccurs="0"/>
				<xs:element name="title" type="TitleType" minOccurs="0"/>
				<xs:element name="department" type="DepartmentType" minOccurs="0"/>
				<xs:element name="calendarServerId" type="UpCalendarServerId" minOccurs="0"/>
				<xs:element name="directoryCredential" type="UpDirectoryCredential" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="authorizationCode" type="PasswordType" minOccurs="0"/>
				<xs:element name="musicOnHold" type="UpFeatureState" minOccurs="0"/>
				<xs:element name="numberPresentation" type="UpNumberPresentation" minOccurs="0"/>
				<xs:element name="callForwardingReminder" type="UpCallForwardingReminder" minOccurs="0"/>
				<xs:element name="callPickup" type="UpFeatureState" minOccurs="0"/>
				<xs:element name="anonymousCallRejection" type="UpFeatureState" minOccurs="0"/>
				<!--
                The attach code can be set at a different level in the hierarchical company tree.
                In that case, the value here will be "inherited" from a parent.
                Note that this parameter can not be set, it is pure read only value.
                -->
				<xs:element name="inheritedAttachCode" type="AttachCodeType" minOccurs="0"/>
				<xs:element name="callMove" type="UpFeatureState" minOccurs="0"/>
				<xs:element name="externalId" type="UpExternalId" minOccurs="0"/>
				<xs:element name="callForwardingDoNotDisturb" type="CallForwardingType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="CallBarringProfileIds">
		<xs:sequence>
			<xs:element name="ids" type="BarringProfileId" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ManagerSecretaryGroup definition -->
	<xs:complexType name="UpManagerSecretaryGroupId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="userId" type="UserId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpManagerSecretaryGroup">
		<xs:complexType>
			<xs:sequence>
				<!-- MSGG provisioning identity -->
				<xs:element name="id" type="UpManagerSecretaryGroupId"/>
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="clericalTimeOut" type="UpClericalTimeOutType" minOccurs="0"/>
				<xs:element name="noAnswerTimeOut" type="UpNoAnswerTimeOutType" minOccurs="0"/>
				<xs:element name="open" type="UpOpenEnum" minOccurs="0"/>
				<xs:element name="overflowWhenBusy" type="UpLimitedOverflowType" minOccurs="0"/>
				<!-- Embedded objects -->
				<xs:element name="agents" type="UpAgentType" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="conditionalForwarding" type="xs:boolean" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- AudioConference definition -->
	<xs:element name="UpAudioConference">
		<xs:complexType>
			<xs:sequence>
				<!-- UpAudioConference provisioning identity -->
				<xs:element name="id" type="UpObjectId"/>
				<!-- UpAudioConference attributes -->
				<xs:element name="serviceBase" type="UpServiceBase"/>
				<xs:element name="open" type="UpOpenEnum" minOccurs="0"/>
				<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
				<xs:element name="pinCodeLength" type="xs:int" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- Multi party call definition -->
	<xs:element name="UpMultiPartyCall">
		<xs:complexType>
			<xs:sequence>
				<!-- UpMultiPartyCall provisioning identity -->
				<xs:element name="id" type="UpObjectIdOptionalCompanyId"/>
				<xs:element name="serviceBase" type="UpServiceBase"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- Call park call definition -->
	<xs:element name="UpCallPark">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="UpObjectIdOptionalCompanyId"/>
				<xs:element name="serviceBase" type="UpServiceBase"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="UpOrganizationUnitId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="organizationUnitId" type="UpOrganizationIdType" minOccurs="0" maxOccurs="3"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="UpOrganizationUnit">
		<xs:complexType>
			<xs:sequence>
				<!-- Org unit provisioning identity -->
				<xs:element name="id" type="UpOrganizationUnitId"/>
				<!-- Limitations regarding objects belonging to this organization unit -->
				<xs:element ref="UpLimitations" minOccurs="0"/>
				<!-- Company Attributes -->
				<xs:element name="name" type="NameType" minOccurs="0"/>
				<xs:element name="description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="address" type="AddressType" minOccurs="0"/>
				<xs:element name="type" type="UpOrganizationUnitType" minOccurs="0"/>
				<xs:element name="attachCode" type="AttachCodeType" minOccurs="0"/>
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds" minOccurs="0"/>
				<!--
                The attach code can be set at a different level in the hierarchical company tree.
                In that case, the value here will be "inherited" from a parent.
                Note that this parameter can not be set, it is pure read only value.
                -->
				<xs:element name="inheritedAttachCode" type="AttachCodeType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!--  EMBEDDED ELEMENT DEFINITIONS  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->
	<!-- UpCallBarringTemplate definition -->
	<xs:simpleType name="UpBarringRuleOperationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ADD"/>
			<xs:enumeration value="DELETE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="BarringRuleType">
		<xs:sequence>
			<xs:element name="operation" type="UpBarringRuleOperationType" minOccurs="0"/>
			<xs:choice>
				<xs:element name="callTypeStatement" type="CallTypeBarring" minOccurs="0"/>
				<xs:element name="numRangeStatement" type="NumberRangeBarring" minOccurs="0"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CallTypeBarring">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PERSONAL"/>
			<xs:enumeration value="BUSINESS"/>
			<xs:enumeration value="ONNET"/>
			<xs:enumeration value="OFFNET"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="NumberRangeBarring">
		<xs:sequence>
			<xs:element name="value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Number Range definition -->
	<xs:complexType name="UpNumberRange">
		<xs:sequence>
			<xs:element name="startNumber" type="NumberType"/>
			<xs:element name="rangeSize" type="NumberRangeSizeType"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Number Range size definition -->
	<xs:simpleType name="NumberRangeSizeType">
		<xs:restriction base="xs:int"/>
	</xs:simpleType>
	<!-- Number type definition -->
	<xs:simpleType name="NumberType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="[1-9]([0-9])*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="UpBarringProfileId">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId"/>
				<xs:element name="companyId" type="CompanyId"/>
				<xs:element name="barringProfileId" type="BarringProfileId"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpCallBarringProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="UpBarringProfileId"/>
				<xs:element name="Description" type="DescriptionType" minOccurs="0"/>
				<xs:element name="BlackList" type="BarringRuleType" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="WhiteList" type="BarringRuleType" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="Name" type="NameType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpInheritCallBarringProfile">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="UpCallBarringProfile"/>
				<xs:element name="inheritPointName" type="NameType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpAllBarringProfiles">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="UpCallBarringProfile" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element ref="UpInheritCallBarringProfile" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- IWL Conference Site definition -->
	<xs:element name="UpIwlSiteConfiguration">
		<xs:complexType>
			<xs:sequence>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="maxAudio" type="maxAudio" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="maxVideo" type="maxVideo" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="videoResolution" type="videoResolution" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="ToSV" type="ToSV" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="ToSA" type="ToSA" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="ToSD" type="ToSD" minOccurs="0"/>
				<!-- Mandatory for operation "create company" but optional for "set company" -->
				<xs:element name="status" type="status" minOccurs="0"/>
				<!-- Optional for both "create company" and "set company" -->
				<!-- String format [yyyy-MM-dd] -->
				<xs:element name="effectiveTime" type="xs:string" minOccurs="0"/>
				<!-- Optional for both "create company" and "set company" -->
				<!-- String format [yyyy-MM-dd] -->
				<xs:element name="ineffectiveTime" type="xs:string" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- IWL Conference Site children elements definitions -->
	<!-- Max number of participants in an audio conference -->
	<xs:simpleType name="maxAudio">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Max number of participants in an video conference -->
	<xs:simpleType name="maxVideo">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="16"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Video resolution: 1=720x576, 2=640x480, 3=352x288, 4=320x240, 5=176x144 -->
	<xs:simpleType name="videoResolution">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- ToSV: 1=high, 2=medium, 3=low -->
	<xs:simpleType name="ToSV">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- ToSA: 1=high, 2=medium, 3=low -->
	<xs:simpleType name="ToSA">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- ToSD: 1=high, 2=medium, 3=low -->
	<xs:simpleType name="ToSD">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- The status of the IWL site configuration: 1=active, 0=not active -->
	<xs:simpleType name="status">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- IDENTITY TYPE DEFINITIONS xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->
	<xs:simpleType name="ServiceProviderId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CompanyId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UserId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ObjectId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BarringProfileId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- UpAutoAttendant MenuItem definition -->
	<xs:element name="UpAutoAttendantMenuItemType">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
				<xs:element name="key" type="AutoAttendantKeyType"/>
				<xs:element name="keyPressedAction" type="UpAutoAttendantMenuActionType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- ATTRIBUTE TYPE DEFINITIONS xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->
	<xs:simpleType name="GenericIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="NameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DescriptionType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CountryType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="GenericAddressType">
		<xs:restriction base="xs:string">
			<!-- xs:pattern value="[0-9]{2,5}|[0-9]{7,28}"/-->
			<!-- [a-zA-Z0-9]+(-[a-zA-Z0-9]+)*(\.[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*)*(:[0-9]+)? -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ShortNumberType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{0,5}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FirstNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="LastNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActiveType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="AdministratorType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="PasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="SipUriType">
		<xs:sequence>
			<xs:element name="SipUri" type="GenericAddressType"/>
			<xs:element name="Type" type="PublicIdentityType"/>
			<xs:element name="Default" type="xs:boolean"/>
		</xs:sequence>
		<xs:attribute name="readOnly" type="xs:boolean" default="false"/>
	</xs:complexType>
	<xs:simpleType name="PublicIdentityType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FIXED"/>
			<xs:enumeration value="MOBILE"/>
			<xs:enumeration value="CLIP"/>
			<xs:enumeration value="PUBLIC_ID"/>
			<xs:enumeration value="PRIVATE_ID"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpCallTypePrefixType">
		<xs:sequence>
			<xs:element name="type" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ServiceProviderPrefixType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IAP"/>
			<xs:enumeration value="NTP"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpServiceProviderPrefix">
		<xs:sequence>
			<xs:element name="type" type="ServiceProviderPrefixType"/>
			<xs:element name="value" type="xs:string"/>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CompanyPrefixType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PERSONAL"/>
			<xs:enumeration value="BUSINESS"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpCompanyPrefix">
		<xs:sequence>
			<xs:element name="type" type="CompanyPrefixType"/>
			<xs:element name="value" type="xs:string"/>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CallForwardingType">
		<xs:sequence>
			<xs:element name="ForwardingTargetType" type="ForwardingTargetTypeEnum"/>
			<xs:element name="ForwardingTarget" type="ForwardingTargetType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ForwardingTargetTypeEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NO_FORWARDING"/>
			<xs:enumeration value="FORWARD_TO_ADDRESS"/>
			<xs:enumeration value="REJECT_WITH_ANNOUNCEMENT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ForwardingTargetType">
		<xs:choice>
			<xs:element name="TargetAddress" type="SingleDiversionAddressType"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="ParallelAlertingType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<xs:simpleType name="ClientTypeEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MOBILE"/>
			<xs:enumeration value="PC"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UserNameType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="LocationType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AddressType">
		<xs:sequence>
			<xs:element name="StreetAddress" type="AddressStreetAddressType" minOccurs="0"/>
			<xs:element name="ZipCode" type="AddressZipCodeType" minOccurs="0"/>
			<xs:element name="City" type="AddressCityType" minOccurs="0"/>
			<xs:element name="Region" type="AddressRegionType" minOccurs="0"/>
			<xs:element name="Country" type="AddressCountryType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="AddressStreetAddressType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressZipCodeType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressCityType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressRegionType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AddressCountryType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SkillsType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="RoleType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CompanyType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="WebUrlType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FaxType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DepartmentType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TitleType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ConnectionProfileId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CalendarServerId">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpCdgIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpAutoAttendantIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DirectoryType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DUMMY"/>
			<xs:enumeration value="SRD"/>
			<xs:enumeration value="SQL"/>
			<xs:enumeration value="VOICE"/>
			<xs:enumeration value="BCSDIR"/>
			<xs:enumeration value="LDAP"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UrlType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpMaxConcurrentAttemptsType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpClericalTimeOutType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="300"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpNoAnswerTimeOutValueType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="300"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpNoAnswerTimeOutType">
		<xs:choice>
			<xs:element name="TimeOut" type="UpNoAnswerTimeOutValueType" minOccurs="0"/>
			<xs:element name="CompanyDefault" type="xs:boolean" minOccurs="0"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="UpMaxQueueSizeType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpMaxTimeInQueueType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="60"/>
			<xs:maxInclusive value="1800"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpOpenEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OPEN"/>
			<xs:enumeration value="CLOSED"/>
			<xs:enumeration value="SCHEDULE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpOverflowOptionEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FORWARD_TO_VOICEMAIL"/>
			<xs:enumeration value="HANGUP"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpOverflowActionType">
		<xs:choice>
			<xs:element name="forwardToAddress" type="xs:string" minOccurs="0"/>
			<xs:element name="overflowOption" type="UpOverflowOptionEnum" minOccurs="0"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="UpOverflowSituationType">
		<xs:sequence>
			<xs:element name="playAnnouncement" type="xs:boolean"/>
			<xs:element name="overflowAction" type="UpOverflowActionType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpNumberPresentationEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CALLER"/>
			<xs:enumeration value="SERVICE_ADDRESS"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpAgentType">
		<xs:sequence>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
			<xs:element name="userId" type="UserId"/>
			<xs:element name="loggedIn" type="xs:boolean" minOccurs="0"/>
			<xs:element name="prio" type="UpAgentPrioType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpOperationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DELETE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpOrganizationUnitOperationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UPDATE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpAgentPrioType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpDayEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MONDAY"/>
			<xs:enumeration value="TUESDAY"/>
			<xs:enumeration value="WEDNESDAY"/>
			<xs:enumeration value="THURSDAY"/>
			<xs:enumeration value="FRIDAY"/>
			<xs:enumeration value="SATURDAY"/>
			<xs:enumeration value="SUNDAY"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpTimeType">
		<xs:restriction base="xs:string">
			<!--	<xs:pattern value="\d{2}\:\d{2}"/> -->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpOpeningHourType">
		<xs:sequence minOccurs="0">
			<xs:element name="day" type="UpDayEnum"/>
			<xs:element name="open" type="UpOpenEnum"/>
			<xs:element name="close" type="UpTimeType"/>
		</xs:sequence>
	</xs:complexType>
	
    <xs:complexType name="UpAnnouncementType">
        <xs:sequence>
            <xs:element name="operation" type="UpOperationType" minOccurs="0"/>
            <xs:element name="announcementName" type="xs:string" minOccurs="1"/>
            <xs:element name="companyId" type="UpCompanyId" minOccurs="1"/>
            <xs:element name="fileId" type="xs:long" minOccurs="1"/>
        </xs:sequence>
    </xs:complexType>
	
	<xs:complexType name="UpAnnouncementUpTimeTypeType">
		<xs:sequence>
			<xs:element name="operation" type="UpOperationType" minOccurs="0"/>
			<xs:element name="announcementName" type="xs:string"/>
			<xs:element name="companyId" type="UpCompanyId"/>
			<xs:element name="fileId" type="xs:long"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="AutoAttendantKeyType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:pattern value="[0-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpAutoAttendantMenuActionType">
		<xs:choice>
			<xs:element name="SipAddress" type="GenericAddressType"/>
			<xs:element name="CdgId" type="UpCdgIdType"/>
			<xs:element name="MenuAction" type="UpAutoAttendantActionEnum"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="UpAutoAttendantActionEnum">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FORWARD_TO_VOICE_MAIL"/>
			<xs:enumeration value="REPEAT_ANNOUNCEMENT"/>
			<xs:enumeration value="DISCONNECT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpLimitedOverflowType">
		<xs:sequence>
			<xs:element name="playAnnouncement" type="xs:boolean" minOccurs="0"/>
			<xs:element name="overflowOption" type="UpOverflowOptionEnum" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpOrganizationUnitType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpOrganizationIdType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpOrganizationUnitIds">
		<xs:sequence>
			<xs:element name="operationType" type="UpOrganizationUnitOperationType" minOccurs="0"/>
			<xs:element name="organizationUnitId" type="UpOrganizationIdType" minOccurs="0" maxOccurs="6"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpLevel">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="6"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpPageNumber">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpPageSize">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AttachCodeType">
		<xs:restriction base="xs:string">
			<!--xs:minLength value="2"/-->
			<!--xs:maxLength value="5"/-->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpAddressPlanType">
		<xs:sequence>
			<xs:element name="planPrefix" type="UpPlanPrefix"/>
			<xs:element name="planValue" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpPlanPrefix">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PUBLIC_PREFIX"/>
			<xs:enumeration value="PRIVATE_PREFIX"/>
			<xs:enumeration value="PRIVATE_LENGTH"/>
			<xs:enumeration value="PUBLIC_STARTS_WITH"/>
			<xs:enumeration value="NO_PLAN"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpCallForwardingReminder">
		<xs:sequence>
			<xs:element name="ringBurst" type="UpFeatureState" minOccurs="0"/>
			<xs:element name="ringTone" type="UpFeatureState" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpNumberPresentation">
		<xs:sequence>
			<xs:element name="callingLineIdRestriction" type="UpFeatureState" minOccurs="0"/>
			<xs:element name="shortNumberPresentation" type="UpFeatureState" minOccurs="0"/>
			<xs:element name="callingNameDisplay" type="UpFeatureState" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpFeatureState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ON"/>
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="UNSET"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpServiceBase">
		<xs:sequence>
			<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
			<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
			<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
			<xs:element name="name" type="NameType" minOccurs="0"/>
			<xs:element name="description" type="DescriptionType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpObjectId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="objectId" type="ObjectId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpObjectIdOptionalCompanyId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId" minOccurs="0"/>
			<xs:element name="objectId" type="ObjectId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="UpExternalId">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="UpOptionalCompanyId">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonalDiversionAddressType">
		<xs:sequence minOccurs="0" maxOccurs="3">
			<xs:element name="personalDiversionAddress" type="SingleDiversionAddressType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SingleDiversionAddressType">
		<xs:choice>
			<xs:element name="diversionAddress" type="GenericAddressType"/>
			<xs:element name="knownAddress" type="KnownAddressType"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="KnownAddressType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FIXED1"/>
			<xs:enumeration value="MOBILE1"/>
			<xs:enumeration value="VOICEMAIL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="UpLimitations">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="UpLimitation" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UpLimitation">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="type" type="UpLimitationType"/>
				<xs:element name="max" type="xs:long" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="UpLimitationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UpOrganizationUnit"/>
			<xs:enumeration value="UpUser"/>
			<xs:enumeration value="UpCdg"/>
			<xs:enumeration value="UpAutoAttendant"/>
			<xs:enumeration value="UpManagerSecretaryGroup"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UpActiveStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE"/>
			<xs:enumeration value="PENDING"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- For Files, Company File -->
	<xs:simpleType name="mediaTypeVo">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PICTURE"/>
			<xs:enumeration value="AUDIO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="metaData">
		<xs:sequence>
			<xs:element name="name" type="xs:string" minOccurs="0"/>
			<xs:element name="extension" type="xs:string" minOccurs="0"/>
			<xs:element name="description" type="xs:string" minOccurs="0"/>
			<xs:element name="language" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="upFileDescriptor">
		<xs:sequence>
			<xs:element name="externalUrl" type="xs:string" minOccurs="0"/>
			<xs:element name="fileSize" type="xs:long"/>
			<xs:element name="id" type="xs:long" minOccurs="0"/>
			<xs:element name="internalUrl" type="xs:string" minOccurs="0"/>
			<xs:element name="metaData" type="metaData" minOccurs="0"/>
			<xs:element name="mimeType" type="xs:string" minOccurs="0"/>
			<xs:element name="uniqueHash" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
