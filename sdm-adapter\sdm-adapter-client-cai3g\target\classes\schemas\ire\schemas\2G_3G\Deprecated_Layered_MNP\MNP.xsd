<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/hlr/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:ns="http://schemas.ericsson.com/ma/hlr/" xmlns:x="http://schemas.ericsson.com/ma/hlr/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/hlr/">
<xs:simpleType name="msisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="imsiType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="HOME"/>
<xs:enumeration value="EXPORTED"/>
<xs:enumeration value="IMPORTED"/>
<xs:enumeration value="OTHER"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subcondType">
<xs:restriction base="xs:string">
<xs:enumeration value="GSM/WCDMA"/>
<xs:enumeration value="PSTN/ISDN"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="NPrefixType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,10}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="addressType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){5,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newmsisdnType">
<xs:restriction base="xs:string">
<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:element name="CreateNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:choice>
<xs:element name="NPrefix" type="NPrefixType"/>
<xs:element name="address" type="addressType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:element name="SetNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:choice>
<xs:element name="NPrefix" type="NPrefixType"/>
<xs:element name="address" type="addressType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="DeleteNumberPortability">
<xs:complexType>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetNumberPortability">
<xs:complexType>
<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
<xs:attribute name="imsi" type="imsiType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetResponseNumberPortability">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:element minOccurs="0" name="subType" type="subTypeType"/>
<xs:element minOccurs="0" name="NPrefix" type="NPrefixType"/>
<xs:element minOccurs="0" name="subcond" type="subcondType"/>
<xs:element minOccurs="0" name="address" type="addressType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
