<!-- BCE Call BarringDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/bce/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd" />
	<xs:element name="serviceProviderId" type="ServiceProviderId" />
	<xs:element name="companyId" type="CompanyId" />
	<xs:element name="mediaType" type="mediaTypeVo" />
	<!-- Create Company File MOId: serviceProviderId, companyId, mediaType MOType: 
		CompanyFile@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createCompanyFile">
		<xs:annotation>
			<xs:documentation>
				The attributes for adding a file to a BCE Company
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="mediaType" type="mediaTypeVo" />
				<xs:element name="mimeType" type="xs:string" minOccurs="0" />
				<xs:element name="metaData" type="metaData" minOccurs="0" />
				<xs:element name="data" type="xs:string" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required" />
			<xs:attribute name="companyId" type="CompanyId" use="required" />
			<xs:attribute name="mediaType" type="mediaTypeVo" use="required" />
		</xs:complexType>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@serviceProviderId" />
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="serviceProviderId" />
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@companyId" />
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="companyId" />
		</xs:keyref>
		<xs:key name="mediaTypeKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@mediaType" />
		</xs:key>
		<xs:keyref name="mediaTypeKeyRef_Create" refer="mediaTypeKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="mediaType" />
		</xs:keyref>
	</xs:element>
	<xs:element name="createCompanyFileResponse">
		<xs:annotation>
			<xs:documentation>
				The file ID in the response when adding a file to a
				BCE Company
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="mediaType" type="mediaTypeVo" />
				<xs:element name="fileId" type="xs:long" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId" />
			<xs:attribute name="companyId" type="CompanyId" />
			<xs:attribute name="mediaType" type="mediaTypeVo" />
		</xs:complexType>
	</xs:element>
	<!-- Set Company File MOId: serviceProviderId, companyId, mediaType MOType: 
		CompanyFile@http://schemas.ericsson.com/ma/bce/ -->
	<!-- No SET operation -->
	<!-- get Company File MOId: serviceProviderId, companyId, mediaType MOType: 
		CompanyFile@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get Company File response -->
	<xs:element name="getCompanyFileResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Company File
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="mediaType" type="mediaTypeVo" />
				<xs:element name="fileDescriptors" type="upFileDescriptor"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId" />
			<xs:attribute name="companyId" type="CompanyId" />
			<xs:attribute name="mediaType" type="mediaTypeVo" />
		</xs:complexType>
	</xs:element>
	<!-- delete Company File MOId: serviceProviderId, companyId, mediaType MOType: 
		CompanyFile@http://schemas.ericsson.com/ma/bce/ -->
	<!-- To remove a kind of files, identified by mediaType, from a BCE Company -->
</xs:schema>
