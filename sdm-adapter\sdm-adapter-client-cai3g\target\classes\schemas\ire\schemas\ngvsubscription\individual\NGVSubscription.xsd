<!-- Customer Adaptation, Subscriber View, ngvsubscription example, 
	 NGVSubscription -->
<!-- For a Customer Adaptation with Subscriber View data model
     the subscriber identities can be selected as same as the internal
	 sub JDVs' ones, that can be present in the interface schema and WSDL 
	 file. -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/CA/NGV/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" 
       xmlns:x="http://schemas.ericsson.com/ma/CA/NGV/" xmlns:xs="http://www.w3.org/2001/XMLSchema" 
       xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:ns1="http://schemas.ericsson.com/ma/CA/NGV/" 
       targetNamespace="http://schemas.ericsson.com/ma/CA/NGV/" elementFormDefault="qualified" 
       attributeFormDefault="unqualified" jaxb:version="2.0">

	<!-- CAI3G MOId type definitions. -->
	<xs:element name="imsi" type="imsiType"/>
	<xs:element name="msisdn" type="msisdnType"/>
	<!-- Replace moId with the ones that internal sub JDVs support.
		 The corresponding changes must be done in both schema and wsdl file. -->
	<!-- CreateNGVSubscription MOId: 
								imsi
								msisdn
	-->
	<!-- CreateNGVSubscription MOType: NGVSubscription@http://schemas.ericsson.com/ma/CA/ngvsubscription/ -->
	<xs:element name="CreateNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<!-- Replace element with the ones that internal sub JDVs support. -->
				<xs:element name="csp" type="xs:string" maxOccurs="1" minOccurs="1"/>
				<xs:element name="privateUserId" type="xs:string" maxOccurs="1" minOccurs="1"/>
				<xs:element name="publicIdValue" type="xs:string" maxOccurs="1" minOccurs="1"/>
				<xs:element name="serviceProfileId" type="xs:string" maxOccurs="1" minOccurs="0"/>
			</xs:sequence>
			<!-- Replace attribute with the ones that internal sub JDVs support. -->
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		
	</xs:element>	

	<!-- SetNGVSubscription MOId: 
							imsi 
							msisdn 
	-->
	<!-- SetNGVSubscription MOType: NGVSubscription@http://schemas.ericsson.com/ma/CA/ngvsubscription/ -->
	<xs:element name="SetNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<!-- Replace element with the ones that internal sub JDVs support. -->
				<xs:element name="newImsi" type="xs:string" maxOccurs="1" minOccurs="0"/>
				<xs:element name="newMsisdn" type="xs:string" maxOccurs="1" minOccurs="0"/>
			</xs:sequence>
			<!-- Replace attribute with the ones that internal sub JDVs support. -->
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	
	<!-- DeleteNGVSubscription MOId: 
								imsi 
								msisdn 
	-->
	<!-- DeleteNGVSubscription MOType: NGVSubscription@http://schemas.ericsson.com/ma/CA/ngvsubscription/ -->
	<xs:element name="DeleteNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<!-- Replace element with the ones that internal sub JDVs support. -->
				<xs:element name="csp" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
				<xs:element name="rsa" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
				<xs:element name="bs20" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
				<xs:element name="bs30" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
				<xs:element name="ts60" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
				<xs:element name="amsisdn" type="xs:string" maxOccurs="unbounded" minOccurs="0"></xs:element>
				<xs:element name="bc" type="xs:string" maxOccurs="1" minOccurs="0"></xs:element>
			</xs:sequence>
			<!-- Replace attribute with the ones that internal sub JDVs support. -->
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="CreateResponseNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" name="responsecode" type="xs:string"/>
				<xs:element minOccurs="0" name="responsemessage" type="xs:string"/>
			</xs:sequence>				
		</xs:complexType>
	</xs:element>
	
	<xs:element name="DeleteResponseNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" name="responsecode" type="xs:string"/>
				<xs:element minOccurs="0" name="responsemessage" type="xs:string"/>
			</xs:sequence>				
		</xs:complexType>
	</xs:element>
	
	<xs:element name="SetResponseNGVSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" name="responsecode" type="xs:string"/>
				<xs:element minOccurs="0" name="responsemessage" type="xs:string"/>
			</xs:sequence>				
		</xs:complexType>
	</xs:element>
	
	
	<!-- Type definitions -->
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<!--
	<xs:simpleType name="impiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="70" />
		</xs:restriction>
	</xs:simpleType>
	-->

</xs:schema>
