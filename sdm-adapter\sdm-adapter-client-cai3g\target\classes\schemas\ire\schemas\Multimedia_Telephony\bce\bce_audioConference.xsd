<!-- BCE Call BarringDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd"/>
	<xs:element name="serviceProviderId" type="ServiceProviderId"/>
	<xs:element name="companyId" type="CompanyId"/>
	<xs:element name="objectId" type="ObjectId"/>
	<xs:complexType name="BceAudioConferenceType">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="objectId" type="ObjectId"/>
			<!-- Audio Conference Attributes -->
			<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
			<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
			<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
			<xs:element name="name" type="NameType" minOccurs="0"/>
			<xs:element name="description" type="DescriptionType" minOccurs="0"/>
			<xs:element name="open" type="UpOpenEnum" minOccurs="0"/>
			<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
			<xs:element name="pinCodeLength" type="xs:int" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="serviceProviderId" type="ServiceProviderId" use="required"/>
		<xs:attribute name="companyId" type="CompanyId" use="required"/>
		<xs:attribute name="objectId" type="ObjectId"/>
	</xs:complexType>
	<!-- Create Audio Conference MOId: serviceProviderId, companyId, objectId  MOType: AudioConference@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createAudioConference" type="BceAudioConferenceType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Audio Conference
			</xs:documentation>
		</xs:annotation>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@serviceProviderId"/>
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="serviceProviderId"/>
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@companyId"/>
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="companyId"/>
		</xs:keyref>
		<xs:key name="objectIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@objectId"/>
		</xs:key>
		<xs:keyref name="objectIdKeyRef_Create" refer="objectIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="objectId"/>
		</xs:keyref>
	</xs:element>
	<!-- Set Audio Conference MOId: serviceProviderId, companyId, objectId  MOType: AudioConference@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setAudioConference" type="BceAudioConferenceType">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Audio Conference
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- get Audio Conference MOId: serviceProviderId, companyId, objectId  MOType: AudioConference@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get Audio Conference response -->
	<xs:element name="getAudioConferenceResponse" type="BceAudioConferenceType">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Audio Conference
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- delete Audio Conference MOId: serviceProviderId, companyId, objectId  MOType: AudioConference@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
