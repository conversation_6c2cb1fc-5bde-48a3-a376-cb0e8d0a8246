<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="SetClosedUserGroupBasicServiceGroupOptions">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
<xs:element minOccurs="0" name="access" type="accessType"/>
<xs:element minOccurs="0" name="pcug" type="pcugType"/>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetClosedUserGroupBasicServiceGroupOptions">
<xs:complexType>
<xs:sequence>
<xs:element name="ClosedUserGroupBasicServiceGroupOptionsData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element maxOccurs="unbounded" name="ClosedUserGroupBasicServiceGroupOption">
<xs:complexType>
<xs:sequence>
<xs:element name="bsg" type="bsgType"/>
<xs:element name="access" type="accessType"/>
<xs:element name="pcug" type="pcugType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
