<!-- 
Build_Label: REL000
ClearQuest_MR#: 12121 
Build_Date: 2017-01-26-15:55:27
-->
<xsd:schema targetNamespace="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_0" xmlns:enterprise_billing_billmanagement_xsd="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<xsd:annotation>
		<xsd:documentation>Types defined for BillManagement service.</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<!-- Types -->
	

	
			
	<!-- Exceptions -->
	<xsd:element name="BillManagementException" type="enterprise_billing_billmanagement_xsd:BillManagementExceptionType"/>
	<xsd:complexType name="BillManagementExceptionType">
		<xsd:complexContent>
			<xsd:extension base="enterprise_common_xsd:ServiceExceptionType">
				<xsd:annotation>
					<xsd:documentation>Bill management exception extension based on
						USCC fault definition.</xsd:documentation>
				</xsd:annotation>
				<xsd:sequence/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
</xsd:schema>