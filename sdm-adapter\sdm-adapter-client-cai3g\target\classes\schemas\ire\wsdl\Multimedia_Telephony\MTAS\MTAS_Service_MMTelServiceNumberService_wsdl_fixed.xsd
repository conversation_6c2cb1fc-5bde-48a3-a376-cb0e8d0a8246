<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelServiceNumberService/" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelServiceNumberService/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:element name="publicId" type="publicIdentityType"/>
	<xs:element name="createService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Creating MMTel ServiceNumber Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType"/>
				<xs:element name="activated" type="xs:boolean"/>
				<xs:element name="announcement-welcome-id" type="announcement-type"/>
				<xs:element name="announcement-retry-id" type="announcement-type"/>
				<xs:element name="announcement-hangup-id" type="announcement-type"/>
				<xs:element name="announcement-wait-for-moderator-id" type="announcement-type"/>
				<xs:element name="pin-code-length" type="conference-pin-code-type"/>
				<xs:element name="pin-code-attempts" type="conference-pin-attempts-type"/>
                <xs:element name="activating-attendant-assistance" type="activating-attendant-assistance-type" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyCreate">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="setService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Setting MMTel ServiceNumber Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="concurrency-control" type="xs:integer" minOccurs="0"/>
				<xs:element name="activated" type="xs:boolean" minOccurs="0"/>
				<xs:element name="announcement-welcome-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-retry-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-hangup-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-wait-for-moderator-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="pin-code-length" type="conference-pin-code-type" minOccurs="0"/>
				<xs:element name="pin-code-attempts" type="conference-pin-attempts-type" minOccurs="0"/>
                <xs:element name="activating-attendant-assistance" type="activating-attendant-assistance-type" nillable="true" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeySet">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="getResponseService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Getting MMTel ServiceNumber Service</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType"/>
				<xs:element name="concurrency-control" type="xs:integer" minOccurs="0"/>
				<xs:element name="activated" type="xs:boolean" minOccurs="0"/>
				<xs:element name="announcement-welcome-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-retry-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-hangup-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="announcement-wait-for-moderator-id" type="announcement-type" minOccurs="0"/>
				<xs:element name="pin-code-length" type="conference-pin-code-type" minOccurs="0"/>
				<xs:element name="pin-code-attempts" type="conference-pin-attempts-type" minOccurs="0"/>
                <xs:element name="activating-attendant-assistance" type="activating-attendant-assistance-type" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyGetResp">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:simpleType name="publicIdentityType">
		<xs:restriction base="xs:anyURI">
			<xs:pattern value="tel:.*|sip:.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="announcement-type">
		<xs:annotation>
			<xs:documentation>
                Announcement identity range
            </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="65535"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="conference-pin-code-type">
		<xs:annotation>
			<xs:documentation>
                Defines the PIN length. Valid range is 4-10 digits
            </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="4"/>
			<xs:maxInclusive value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="conference-pin-attempts-type">
		<xs:annotation>
			<xs:documentation>
                Pin code attempts
            </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="6"/>
		</xs:restriction>
	</xs:simpleType>
    <xs:complexType name="activating-attendant-assistance-type">
        <xs:annotation>
            <xs:documentation>
                This element defines whether Attendant Assistance is invoked when the limit of number of PIN attempts has been exceeded.
            </xs:documentation>
        </xs:annotation>
            <xs:sequence>
                <xs:element name="announcement-attendant-assistance-id" type="announcement-type" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            Defines the Attendant Assistance announcement when the limit of number of attempts has been exceeded.
                            This value must be aligned with the number configured for the announcement in the MRF/P.
                            The Attendant Assistance announcement is played continuously.
                            This must be present on the creation when activating-attendant-assistance is present.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="attendant-uri" type="stored-number-type" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The SIP or tel URI of the Attendant.
                            This must be present on the creation when activating-attendant-assistance is present.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    <xs:simpleType name="stored-number-type">
        <xs:annotation>
            <xs:documentation>
                The stored-number-type is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to
                section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized after removing a
                dynamic ad-hoc presentation supplementary service code and/or a carrier select code.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:anyURI"/>
    </xs:simpleType>
</xs:schema>
