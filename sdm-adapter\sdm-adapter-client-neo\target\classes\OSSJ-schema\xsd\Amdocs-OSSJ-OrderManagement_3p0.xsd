<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2006 sp2 U (http://www.altova.com) by <PERSON> Forth (Jacobs Rimell Limited) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-om="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:amdocs-customer="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:cbecustomer-v1-5="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:inventory-v1-2="http://ossj.org/xml/Inventory/v1-2" xmlns:amdocs-inv="http://amdocs/core/ossj-Inventory/dat/3" xmlns:cbelocation-v1-5="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:om-v1-0="http://ossj.org/xml/OrderManagement/v1-0" xmlns:cbeproductoffering-v1-5="http://ossj.org/xml/Common-CBEProductOffering/v1-5" xmlns:cbebi-v1-5="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" targetNamespace="http://amdocs/core/ossj-OrderManagement/dat/3" elementFormDefault="qualified">
	<xs:import namespace="http://amdocs/core/ossj-Inventory/dat/3" schemaLocation="Amdocs-OSSJ-Inventory_3p0.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common/dat/3" schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common-CBECustomer/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBECustomer_3p0.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Inventory/v1-2" schemaLocation="OSSJ-Inventory-v1-2.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5" schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5" schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5" schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common/v1-5" schemaLocation="OSSJ-Common-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/OrderManagement/v1-0" schemaLocation="OSSJ-OrderManagement-v1-0.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEBi/v1-5" schemaLocation="OSSJ-Common-CBEBi-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECustomer/v1-5" schemaLocation="OSSJ-Common-CBECustomer-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEProductOffering/v1-5" schemaLocation="OSSJ-Common-CBEProductOffering-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBELocation/v1-5" schemaLocation="OSSJ-Common-CBELocation-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:annotation>
		<xs:documentation>This schema was updated: 26 October 2011</xs:documentation>
	</xs:annotation>
	<!-- Generic elements -->
	<xs:element name="priority_Request" type="xs:int" substitutionGroup="om-v1-0:basePriority_Request"/>
	<xs:element name="AlternativeIdentifier" type="amdocs-om:AlternativeIdentifier"/>
	<xs:complexType name="AlternativeIdentifier">
		<xs:sequence>
			<xs:element name="type" type="xs:string" minOccurs="0"/>
			<xs:element name="value" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ResourceOrderItem Entities -->
	<xs:element name="ResourceOrderItemValue" type="amdocs-om:ResourceOrderItemValue"/>
	<xs:complexType name="ResourceOrderItemValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ResourceOrderItemValue">
				<xs:sequence>
					<xs:element name="alternativeIdentifier" type="amdocs-om:AlternativeIdentifier" minOccurs="0"/>
					<xs:element name="requestedCompletionDate" type="xs:dateTime" nillable="true" minOccurs="0"/>
					<xs:element name="priority" type="xs:string" minOccurs="0"/>
					<xs:element name="subAction" type="xs:string" minOccurs="0"/>
					<xs:element name="change" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="sub-items" type="amdocs-om:ArrayOfResourceOrderItemValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceOrderItemValue" type="amdocs-om:ArrayOfResourceOrderItemValue"/>
	<xs:complexType name="ArrayOfResourceOrderItemValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ResourceOrderItemValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ResourceOrderItemKey Entities -->
	<xs:element name="ResourceOrderItemKey" type="amdocs-om:ResourceOrderItemKey"/>
	<xs:complexType name="ResourceOrderItemKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ResourceOrderItemKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceOrderItemKey" type="amdocs-om:ArrayOfResourceOrderItemKey"/>
	<xs:complexType name="ArrayOfResourceOrderItemKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ResourceOrderItemKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ResourceOrderItemKeyResult" type="amdocs-om:ResourceOrderItemKeyResult"/>
	<xs:complexType name="ResourceOrderItemKeyResult">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKeyResult">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="returnData" type="cbecore-v1-5:EntityValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceOrderItemKeyResult" type="amdocs-om:ArrayOfResourceOrderItemKeyResult"/>
	<xs:complexType name="ArrayOfResourceOrderItemKeyResult">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ResourceOrderItemKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ResourceOrder Entities -->
	<xs:element name="ResourceOrderValue" type="amdocs-om:ResourceOrderValue"/>
	<xs:complexType name="ResourceOrderValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ResourceOrderValue">
				<xs:sequence>
					<xs:element name="associatedOrderKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related parent order.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedOrderItemKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related order item.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="currentState" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Indicates the current state of the service. This may be active, inactive, and so on.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="operator" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes the custom topic that event notifications associated with this request will be written to. If no operator is included in the request, or the request operator is not present in configuration, this value is ignored and events are sent to the default topic.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requesterId" type="xs:string" minOccurs="0"/>
					<xs:element name="keepFor" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A comma-separated list of retention rules, each of which comprises the closed state that the rule applies to, an equals sign, and the length of time that a record of the request should be retained for. For example: completed=2. Once a record has exceeded the specified time period (2 seconds), it is marked as "eligible for delete". Note:  Where the value is 0, the record is deleted immediately. The record is not marked as eligible for delete, and there is no delay before the  maintenance process occurs. </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="maxResponseWaitTime" type="xs:int" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Specifies that the request should be handled according to the client blocking business exchange pattern. Possible values: 0 - The request is handled according to the client blocking business exchange pattern.  The response will only be sent once the request has completed, regardless of how long that takes. A number greater than 0 - The request is handled according to the client blocking business exchange pattern, but the value specifies the number of seconds to wait for a response. If the maximum wait time is exceeded a response is sent, requests from the client are unblocked, and the request continues to be processed in the background. If this element is not supplied, the request is handled according to the non-client-blocking business exchange pattern. The response will be sent as soon as the request is received. This is the default behavior.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="market" type="xs:string" minOccurs="0"/>
                                        <xs:element name="resubmitMode" type="amdocs-om:resubmitModeType" minOccurs="0"/>
					<!-- Check -->
					<xs:element name="failureDetails" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceOrderValue" type="amdocs-om:ArrayOfResourceOrderValue"/>
	<xs:complexType name="ArrayOfResourceOrderValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ResourceOrderValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ResourceOrderKey Entities -->
	<xs:element name="ResourceOrderKey" type="amdocs-om:ResourceOrderKey"/>
	<xs:complexType name="ResourceOrderKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ResourceOrderKey">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceOrderKey" type="amdocs-om:ArrayOfResourceOrderKey"/>
	<xs:complexType name="ArrayOfResourceOrderKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ResourceOrderKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ServiceOrderItem Entities -->
	<xs:element name="ServiceOrderItemValue" type="amdocs-om:ServiceOrderItemValue"/>
	<xs:complexType name="ServiceOrderItemValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ServiceOrderItemValue">
				<xs:sequence>
					<xs:element name="alternativeIdentifier" type="amdocs-om:AlternativeIdentifier" minOccurs="0"/>
					<xs:element name="requestedCompletionDate" type="xs:dateTime" nillable="true" minOccurs="0"/>
					<xs:element name="priority" type="xs:string" minOccurs="0"/>
					<xs:element name="subAction" type="xs:string" minOccurs="0"/>
					<xs:element name="change" type="xs:string" minOccurs="0"/>
					<xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="sub-items" type="amdocs-om:ArrayOfServiceOrderItemValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceOrderItemValue" type="amdocs-om:ArrayOfServiceOrderItemValue"/>
	<xs:complexType name="ArrayOfServiceOrderItemValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ServiceOrderItemValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ServiceOrderItemKey Entities -->
	<xs:element name="ServiceOrderItemKey" type="amdocs-om:ServiceOrderItemKey"/>
	<xs:complexType name="ServiceOrderItemKey">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceOrderItemKey" type="amdocs-om:ArrayOfServiceOrderItemKey"/>
	<xs:complexType name="ArrayOfServiceOrderItemKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ServiceOrderItemKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ServiceOrderItemKeyResult" type="amdocs-om:ServiceOrderItemKeyResult"/>
	<xs:complexType name="ServiceOrderItemKeyResult">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKeyResult">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="returnData" type="cbecore-v1-5:EntityValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceOrderItemKeyResult" type="amdocs-om:ArrayOfServiceOrderItemKeyResult"/>
	<xs:complexType name="ArrayOfServiceOrderItemKeyResult">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ServiceOrderItemKeyResult" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ServiceOrder Entities -->
	<xs:element name="ServiceOrderValue" type="amdocs-om:ServiceOrderValue"/>
	<xs:complexType name="ServiceOrderValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ServiceOrderValue">
				<xs:sequence>
					<xs:element name="associatedOrderKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related parent order.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedOrderItemKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related order item.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="currentState" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Indicates the current state of the service. This may be active, inactive, and so on.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="operator" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes the custom topic that event notifications associated with this request will be written to. If no operator is included in the request, or the request operator is not present in configuration, this value is ignored and events are sent to the default topic.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requesterId" type="xs:string" minOccurs="0"/>
					<xs:element name="keepFor" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A comma-separated list of retention rules, each of which comprises the closed state that the rule applies to, an equals sign, and the length of time that a record of the request should be retained for. For example: completed=2. Once a record has exceeded the specified time period (2 seconds), it is marked as "eligible for delete". Note:  Where the value is 0, the record is deleted immediately. The record is not marked as eligible for delete, and there is no delay before the  maintenance process occurs. </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="maxResponseWaitTime" type="xs:int" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Specifies that the request should be handled according to the client blocking business exchange pattern. Possible values: 0 - The request is handled according to the client blocking business exchange pattern.  The response will only be sent once the request has completed, regardless of how long that takes. A number greater than 0 - The request is handled according to the client blocking business exchange pattern, but the value specifies the number of seconds to wait for a response. If the maximum wait time is exceeded a response is sent, requests from the client are unblocked, and the request continues to be processed in the background. If this element is not supplied, the request is handled according to the non-client-blocking business exchange pattern. The response will be sent as soon as the request is received. This is the default behavior.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="market" type="xs:string" minOccurs="0"/>
                                        <xs:element name="resubmitMode" type="amdocs-om:resubmitModeType" minOccurs="0"/>
					<!-- Check -->
					<xs:element name="failureDetails" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ServiceOrderKey Entities -->
	<xs:element name="ServiceOrderKey" type="amdocs-om:ServiceOrderKey"/>
	<xs:complexType name="ServiceOrderKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ServiceOrderKey">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- Check -->
	<xs:element name="ServiceOrderKeyResult" type="amdocs-om:ServiceOrderKeyResult"/>
	<xs:complexType name="ServiceOrderKeyResult">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ServiceOrderKeyResult">
				<xs:sequence>
					<xs:element name="serviceOrderKey" type="amdocs-om:ServiceOrderKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ProductOrderItem Entities -->
	<xs:element name="ProductOrderItemValue" type="amdocs-om:ProductOrderItemValue"/>
	<xs:complexType name="ProductOrderItemValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ProductOrderItemValue">
				<xs:sequence>
					<xs:element name="alternativeIdentifier" type="amdocs-om:AlternativeIdentifier" minOccurs="0"/>
					<xs:element name="requestedCompletionDate" type="xs:dateTime" nillable="true" minOccurs="0"/>
					<xs:element name="priority" type="xs:string" minOccurs="0"/>
					<xs:element name="subAction" type="xs:string" minOccurs="0"/>
					<xs:element name="change" type="xs:string" minOccurs="0"/>
					<xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="deleteUnused" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="sub-items" type="amdocs-om:ArrayOfProductOrderItemValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductOrderItemValue" type="amdocs-om:ArrayOfProductOrderItemValue"/>
	<xs:complexType name="ArrayOfProductOrderItemValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ProductOrderItemValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ProductOrderItemKey Entities -->
	<xs:element name="ProductOrderItemKey" type="amdocs-om:ProductOrderItemKey"/>
	<xs:complexType name="ProductOrderItemKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ProductOrderItemKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductOrderItemKey" type="amdocs-om:ArrayOfProductOrderItemKey"/>
	<xs:complexType name="ArrayOfProductOrderItemKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ProductOrderItemKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ProductOrderItemKeyResult" type="amdocs-om:ProductOrderItemKeyResult"/>
	<xs:complexType name="ProductOrderItemKeyResult">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKeyResult">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="returnData" type="cbecore-v1-5:EntityValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductOrderItemKeyResult" type="amdocs-om:ArrayOfProductOrderItemKeyResult"/>
	<xs:complexType name="ArrayOfProductOrderItemKeyResult">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ProductOrderItemKeyResult" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ProductOrder Entities -->
	<xs:element name="ProductOrderValue" type="amdocs-om:ProductOrderValue"/>
	<xs:complexType name="ProductOrderValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ProductOrderValue">
				<xs:sequence>
					<xs:element name="associatedOrderKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related parent order.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedOrderItemKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related order item.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="currentState" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Indicates the current state of the service. This may be active, inactive, and so on.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="operator" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes the custom topic that event notifications associated with this request will be written to. If no operator is included in the request, or the request operator is not present in configuration, this value is ignored and events are sent to the default topic.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requesterId" type="xs:string" minOccurs="0"/>
					<xs:element name="keepFor" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A comma-separated list of retention rules, each of which comprises the closed state that the rule applies to, an equals sign, and the length of time that a record of the request should be retained for. For example: completed=2. Once a record has exceeded the specified time period (2 seconds), it is marked as "eligible for delete". Note:  Where the value is 0, the record is deleted immediately. The record is not marked as eligible for delete, and there is no delay before the  maintenance process occurs. </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="maxResponseWaitTime" type="xs:int" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Specifies that the request should be handled according to the client blocking business exchange pattern. Possible values: 0 - The request is handled according to the client blocking business exchange pattern.  The response will only be sent once the request has completed, regardless of how long that takes. A number greater than 0 - The request is handled according to the client blocking business exchange pattern, but the value specifies the number of seconds to wait for a response. If the maximum wait time is exceeded a response is sent, requests from the client are unblocked, and the request continues to be processed in the background. If this element is not supplied, the request is handled according to the non-client-blocking business exchange pattern. The response will be sent as soon as the request is received. This is the default behavior.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="market" type="xs:string" minOccurs="0"/>
                                        <xs:element name="resubmitMode" type="amdocs-om:resubmitModeType" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductOrderValue" type="amdocs-om:ArrayOfProductOrderValue"/>
	<xs:complexType name="ArrayOfProductOrderValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ProductOrderValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ProductOrderKey Entities -->
	<xs:element name="ProductOrderKey" type="amdocs-om:ProductOrderKey"/>
	<xs:complexType name="ProductOrderKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:ProductOrderKey">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfProductOrderKey" type="amdocs-om:ArrayOfProductOrderKey"/>
	<xs:complexType name="ArrayOfProductOrderKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:ProductOrderKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- SubscriberOrderItem Entities -->
	<xs:element name="SubscriberOrderItemValue" type="amdocs-om:SubscriberOrderItemValue"/>
	<xs:complexType name="SubscriberOrderItemValue">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemValue">
				<xs:sequence>
					<xs:element name="subscriber" type="amdocs-customer:SubscriberValue" minOccurs="0"/>
					<xs:element name="alternativeIdentifier" type="amdocs-om:AlternativeIdentifier" minOccurs="0"/>
					<xs:element name="requestedCompletionDate" type="xs:dateTime" nillable="true" minOccurs="0"/>
					<xs:element name="priority" type="xs:string" minOccurs="0"/>
					<xs:element name="subAction" type="xs:string" minOccurs="0"/>
					<xs:element name="change" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="sub-items" type="amdocs-om:ArrayOfSubscriberOrderItemValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberOrderItemValue" type="amdocs-om:ArrayOfSubscriberOrderItemValue"/>
	<xs:complexType name="ArrayOfSubscriberOrderItemValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:SubscriberOrderItemValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- SubscriberOrderItemKey Entities -->
	<xs:element name="SubscriberOrderItemKey" type="amdocs-om:SubscriberOrderItemKey"/>
	<xs:complexType name="SubscriberOrderItemKey">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberOrderItemKey" type="amdocs-om:ArrayOfSubscriberOrderItemKey"/>
	<xs:complexType name="ArrayOfSubscriberOrderItemKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:SubscriberOrderItemKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SubscriberOrderItemKeyResult" type="amdocs-om:SubscriberOrderItemKeyResult"/>
	<xs:complexType name="SubscriberOrderItemKeyResult">
		<xs:complexContent>
			<xs:extension base="cbebi-v1-5:BusinessInteractionItemKeyResult">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="returnData" type="cbecore-v1-5:EntityValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberOrderItemKeyResult" type="amdocs-om:ArrayOfSubscriberOrderItemKeyResult"/>
	<xs:complexType name="ArrayOfSubscriberOrderItemKeyResult">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:SubscriberOrderItemKeyResult" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- SubscriberOrder Entities -->
	<xs:element name="SubscriberOrderValue" type="amdocs-om:SubscriberOrderValue"/>
	<xs:complexType name="SubscriberOrderValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestValue">
				<xs:sequence>
					<xs:element name="subscriberOrderItems" type="amdocs-om:ArrayOfSubscriberOrderItemValue" minOccurs="0"/>
					<xs:element name="associatedOrderKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related parent order.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedOrderItemKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related order item.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="currentState" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Indicates the current state of the service. This may be active, inactive, and so on.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="operator" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes the custom topic that event notifications associated with this request will be written to. If no operator is included in the request, or the request operator is not present in configuration, this value is ignored and events are sent to the default topic.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requesterId" type="xs:string" minOccurs="0"/>
					<xs:element name="keepFor" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A comma-separated list of retention rules, each of which comprises the closed state that the rule applies to, an equals sign, and the length of time that a record of the request should be retained for. For example: completed=2. Once a record has exceeded the specified time period (2 seconds), it is marked as "eligible for delete". Note:  Where the value is 0, the record is deleted immediately. The record is not marked as eligible for delete, and there is no delay before the  maintenance process occurs. </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="maxResponseWaitTime" type="xs:int" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Specifies that the request should be handled according to the client blocking business exchange pattern. Possible values: 0 - The request is handled according to the client blocking business exchange pattern.  The response will only be sent once the request has completed, regardless of how long that takes. A number greater than 0 - The request is handled according to the client blocking business exchange pattern, but the value specifies the number of seconds to wait for a response. If the maximum wait time is exceeded a response is sent, requests from the client are unblocked, and the request continues to be processed in the background. If this element is not supplied, the request is handled according to the non-client-blocking business exchange pattern. The response will be sent as soon as the request is received. This is the default behavior.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="market" type="xs:string" minOccurs="0"/>
                                        <xs:element name="resubmitMode" type="amdocs-om:resubmitModeType" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberOrderValue" type="amdocs-om:ArrayOfSubscriberOrderValue"/>
	<xs:complexType name="ArrayOfSubscriberOrderValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:SubscriberOrderValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- SubscriberOrderKey Entities -->
	<xs:element name="SubscriberOrderKey" type="amdocs-om:SubscriberOrderKey"/>
	<xs:complexType name="SubscriberOrderKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestKey">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberOrderKey" type="amdocs-om:ArrayOfSubscriberOrderKey"/>
	<xs:complexType name="ArrayOfSubscriberOrderKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:SubscriberOrderKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Request Entities -->
	<xs:element name="RequestValue" type="amdocs-om:RequestValue"/>
	<xs:complexType name="RequestValue">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestValue">
				<xs:sequence>
					<xs:element name="associatedOrderKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related parent order.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedOrderItemKey" type="cbecore-v1-5:EntityKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Associated decomposition related order item.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="currentState" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Indicates the current state of the service. This may be active, inactive, and so on.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="operator" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes the custom topic that event notifications associated with this request will be written to. If no operator is included in the request, or the request operator is not present in configuration, this value is ignored and events are sent to the default topic.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="requesterId" type="xs:string" minOccurs="0"/>
					<xs:element name="keepFor" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A comma-separated list of retention rules, each of which comprises the closed state that the rule applies to, an equals sign, and the length of time that a record of the request should be retained for. For example: completed=2. Once a record has exceeded the specified time period (2 seconds), it is marked as "eligible for delete". Note:  Where the value is 0, the record is deleted immediately. The record is not marked as eligible for delete, and there is no delay before the  maintenance process occurs. </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="maxResponseWaitTime" type="xs:int" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Specifies that the request should be handled according to the client blocking business exchange pattern. Possible values: 0 - The request is handled according to the client blocking business exchange pattern.  The response will only be sent once the request has completed, regardless of how long that takes. A number greater than 0 - The request is handled according to the client blocking business exchange pattern, but the value specifies the number of seconds to wait for a response. If the maximum wait time is exceeded a response is sent, requests from the client are unblocked, and the request continues to be processed in the background. If this element is not supplied, the request is handled according to the non-client-blocking business exchange pattern. The response will be sent as soon as the request is received. This is the default behavior.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="market" type="xs:string" minOccurs="0"/>
                                        <xs:element name="resubmitMode" type="amdocs-om:resubmitModeType" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfRequestValue" type="amdocs-om:ArrayOfRequestValue"/>
	<xs:complexType name="ArrayOfRequestValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:RequestValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- RequestKey Entities -->
	<xs:element name="RequestKey" type="amdocs-om:RequestKey"/>
	<xs:complexType name="RequestKey">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestKey">
				<xs:sequence>
					<!-- Check -->
					<xs:element name="serviceConfiguration" type="inventory-v1-2:InventoryQueryResponse" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfRequestKey" type="amdocs-om:ArrayOfRequestKey"/>
	<xs:complexType name="ArrayOfRequestKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:RequestKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--Event Entities -->
	<xs:element name="RequestCreateEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:RequestCreateEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="RequestCreateEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestCreateEventType">
				<xs:sequence>
					<xs:element name="parent" type="om-v1-0:RequestValue" minOccurs="0"/>
					<xs:element name="children" type="om-v1-0:ArrayOfRequestValue" minOccurs="0"/>
					<xs:element name="relationshipType" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="RequestAttributeValueChangeEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:RequestAttributeValueChangeEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="RequestAttributeValueChangeEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestAttributeValueChangeEventType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="RequestStateChangeEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:RequestStateChangeEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="RequestStateChangeEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestStateChangeEventType">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="requestItemKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionItemKeyResult" nillable="true" minOccurs="0"/>
					<xs:element name="childRequestStateChangeEvents" type="amdocs-om:ArrayOfRequestStateChangeEventType" nillable="true" minOccurs="0"/>
					<xs:element name="exception" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="RequestItemStateChangeEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:RequestItemStateChangeEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="RequestItemStateChangeEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestStateChangeEventType">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="childRequestItemStateChangeEvents" type="amdocs-om:ArrayOfRequestItemStateChangeEventType" nillable="true" minOccurs="0"/>
					<xs:element name="exception" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfRequestStateChangeEventType" type="amdocs-om:ArrayOfRequestStateChangeEventType"/>
	<xs:complexType name="ArrayOfRequestStateChangeEventType">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:RequestStateChangeEventType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfRequestItemStateChangeEventType" type="amdocs-om:ArrayOfRequestItemStateChangeEventType"/>
	<xs:complexType name="ArrayOfRequestItemStateChangeEventType">
		<xs:sequence>
			<xs:element name="item" type="amdocs-om:RequestItemStateChangeEventType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="RequestRemoveEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:RequestRemoveEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="RequestRemoveEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:RequestRemoveEventType">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="exception" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="AwaitingClientValidationEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:AwaitingClientValidationEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="AwaitingClientValidationEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:AwaitingClientValidationEventType">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="exception" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="AwaitingClientInputEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="amdocs-om:AwaitingClientInputEventType"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="AwaitingClientInputEventType">
		<xs:complexContent>
			<xs:extension base="om-v1-0:AwaitingClientInputEventType">
				<xs:sequence>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="exception" type="co-v1-5:BaseException" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

    <xs:simpleType name="resubmitModeType">
        <xs:annotation>
            <xs:documentation>
                Valid values for what to do on resubmit.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="none">
                <xs:annotation>
                    <xs:documentation>
                        Indicates this is not a resubmit - so the request should fail if the request identifier already exists.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="all">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that all requests/request-items should be re-submitted regardless of the state they are in.
                        This allows re-flowing of a completed (successfully or partially successfully) request.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="failed">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that only failed requests/request-items should be re-submitted.
                        In this mode, successful requests/request-items will not be re-submitted.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
