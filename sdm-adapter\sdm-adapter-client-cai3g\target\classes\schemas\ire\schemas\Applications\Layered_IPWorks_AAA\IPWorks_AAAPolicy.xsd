<xs:schema xmlns="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:x="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	targetNamespace="http://schemas.ericsson.com/ma/IPWORKS/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/aaala_types.xsd" />
	<xs:element name="aaaPolicyName" type="aaaPolicyNameType" />

	<xs:element name="CreateAAAPolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaPolicyName" type="aaaPolicyNameType" />
				<xs:element name="aaaPolicyChecklist" type="aaaPolicyChecklistType" minOccurs="0" />
				<xs:element name="aaaPolicyReplylist" type="aaaPolicyReplylistType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="aaaPolicyName" type="aaaPolicyNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_aaaPolicyName">
			<xs:selector xpath="." />
			<xs:field xpath="@aaaPolicyName" />
		</xs:key>
		<xs:keyref name="keyref_create_aaaPolicyName" refer="key_create_aaaPolicyName">
			<xs:selector xpath="./x:aaaPolicyName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

	<xs:element name="GetResponseAAAPolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaPolicyName" type="aaaPolicyNameType" />
				<xs:element name="aaaPolicyChecklist" type="aaaPolicyChecklistType" minOccurs="0" />
				<xs:element name="aaaPolicyReplylist" type="aaaPolicyReplylistType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="aaaPolicyName" type="aaaPolicyNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<xs:element name="SetAAAPolicy">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="aaaPolicyChecklist" type="aaaPolicyChecklistType" minOccurs="0" />
				<xs:element name="aaaPolicyReplylist" type="aaaPolicyReplylistType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="aaaPolicyName" type="aaaPolicyNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="aaaPolicyNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_aaaPolicyName">
			<xs:selector xpath="." />
			<xs:field xpath="@aaaPolicyName" />
		</xs:key>
	</xs:element>
</xs:schema>
