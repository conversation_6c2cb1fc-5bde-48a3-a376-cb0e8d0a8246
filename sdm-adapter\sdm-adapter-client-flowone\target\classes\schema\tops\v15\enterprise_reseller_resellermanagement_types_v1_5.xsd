<!-- 
Jira_Tracking_ID: PR002564-17
Build_Label: JDeveloperBuild
Build_Date: 2023-05-04T15:23:20.605-0500
-->
<xsd:schema xmlns:enterprise_reseller_resellermanagement_xsd="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_1">
	<xsd:annotation>
		<xsd:documentation>Reseller Management types.</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<!-- Types            -->
	<xsd:simpleType name="ZipcodeType">
		<xsd:annotation>
			<xsd:documentation>5 or 9 digit zip code.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{5}(-[0-9]{4})?"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="APNType">
		<xsd:annotation>
			<xsd:documentation>OrgName to be added to Internet address.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
			<xsd:pattern value="([a-zA-Z0-9]){9}\.([a-zA-Z0-9]){11}\.usc-cdp"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ipProfileType">
		<xsd:annotation>
			<xsd:documentation>Type of option which subscriber is assigned for IP allocation for data services.
					Option 0-Private IP
					Option 1- CustomerProvided IP
					Option 2- Public IP
					Option 3- Static IP					
					</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Structures -->      
	<xsd:complexType name="AddressType">
		<xsd:annotation>
			<xsd:documentation>Subscriber's billing address - required.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="line1" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Address line 1 - required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="line2" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 2 - optional.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="city" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>City - required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="state" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>State - required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="zipcode" type="enterprise_reseller_resellermanagement_xsd:ZipcodeType">
				<xsd:annotation>
					<xsd:documentation>5 or 9 digit zip code - 5 digit US zipcode required at a minimum.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
		
	</xsd:complexType>
	
	<xsd:complexType name="AttributeType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'attributes'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="name" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Name in the name-value pair.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="value" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Value in the name-value pair.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="FeatureType">
		<xsd:annotation>
			<xsd:documentation>Feature name and attributes.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="componentName" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Name of the feature.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="attributes" type="enterprise_reseller_resellermanagement_xsd:AttributeType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Reserved for future use. List of provisioning information for this feature.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="SubscriptionType">
		<xsd:sequence>
			<xsd:element name="componentCode" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Component name for  subscription change.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="NameType">
		<xsd:annotation>
			<xsd:documentation>Subscriber name (not Reseller Name) to be activated in the backend system.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="lastname" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Last name of the subscriber - required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="firstname" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>First name of the subscriber - required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="middlename" type="xsd:string" nillable="true">
				<xsd:annotation>
					<xsd:documentation>Middle name or initial of the subscriber - Optional.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="suffix" type="xsd:string" nillable="true">
				<xsd:annotation>
					<xsd:documentation>Suffix such as Jr, Sr, I, II, etc. - Optional.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="OfferType">
		<xsd:annotation>
			<xsd:documentation>Price plan associated to the reseller.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="offerName" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Name of the offer.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="features" type="enterprise_reseller_resellermanagement_xsd:FeatureType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>List of features/SOCs associated with the offer.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="OrderConfirmationType">
		<xsd:annotation>
			<xsd:documentation>Response returned from new backend.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="orderId" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Order Id returned by the Ordering System.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="orderActionId" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Order action Id returned by the Ordering system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="productsType">
		<xsd:annotation>
			<xsd:documentation>Type of Customer assigned products list.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="product" type="enterprise_reseller_resellermanagement_xsd:productType" minOccurs="1" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Customer assigned product.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="productType">
		<xsd:annotation>
			<xsd:documentation>Definition of the Customer assigned Product List</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="mdn" type="enterprise_common_xsd:MdnType">
				<xsd:annotation>
					<xsd:documentation>Mobile Directory Number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="productType" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>
						The service type of an assigned product. Posible values are
							CD - modem/tablet
							TM  - traditional mobile
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="productState" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>
						The state of the assigned product. Valid values are 
						- CA - Cancelled;
						- FI  - Ficitious;
						- OA - OA;
						- AS - Assigned;
						- OR - Ordered
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="productStatus" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>
						The status of the assigned product. Valid values are 
						- AC - Active;
						- AS - AS;
						- CE - Ceased;
						- IN - Initialized;
						- NA - Not Active;
						- PA - Pending Activation;
						- SU - Suspended;
						- PS - Pending Suspend;
						- PC - Pending Cease;
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="hasPendingOrdersInd" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Indicates if the product has pending orders on it. 
						Valid values are 
						- yes
						- no
						</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="orderId" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The structure identifier of the order.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="orderStatus" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Status of the order. Valid values are :
						- Cancelled
						- Discounted
						- Done
						- Open
						- Submitted
						- Future
						- Initial
						- Reject
						- To Be Cancelled
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="orderActionId" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A unique identifier structure of the order action (order action reference number).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="orderActionStatus" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						The status of the order action. Valid values are 
						- Amend
						- Being Amend
						- Cancelled
						- Discontinued
						- Submitted
						- Done
						- Ficitious
						- Future
						- Initial
						- Negotiation
						- Completion
						- On Hold
						- Reject
						- To Be Cancelled
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- Enumerations     -->
	<xsd:simpleType name="CtnStatusType">
		<xsd:annotation>
			<xsd:documentation>State of a given CTN/Subscriber.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Active">
				<xsd:annotation>
					<xsd:documentation>Subscriber is in 'Active' state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Suspended">
				<xsd:annotation>
					<xsd:documentation>Subscriber is in 'Suspended' state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Canceled">
				<xsd:annotation>
					<xsd:documentation>Subscriber is in 'Canceled' state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Exceptions       -->
	<xsd:element name="ResellerManagementException" type="enterprise_reseller_resellermanagement_xsd:ResellerManagementExceptionType">
		<xsd:annotation>
			<xsd:documentation>Service Exception used for ResellerMangement</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="ResellerManagementExceptionType">
		<xsd:annotation>
			<xsd:documentation>Definition of element 'ResellerMangementException'.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="enterprise_common_xsd:ServiceExceptionType">
				<xsd:annotation>
					<xsd:documentation>Reseller management exception extension based on USCC fault definition.</xsd:documentation>
				</xsd:annotation>
				<xsd:sequence/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
</xsd:schema>
