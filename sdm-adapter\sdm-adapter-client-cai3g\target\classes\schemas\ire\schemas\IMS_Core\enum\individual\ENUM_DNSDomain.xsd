<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 (http://www.altova.com) by <PERSON><PERSON><PERSON> (<PERSON> (China) Communications Company Ltd) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:ns="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
  <xs:element name="domainsId" type="xs:string"/>
  <xs:element name="setDNSDomain">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="domainsId" type="xs:string"/>
        <xs:element name="dnsname" type="xs:string"/>
        <xs:element name="domainData" nillable="true" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="domain" type="domainType"/>
            </xs:sequence>
            <xs:attribute name="domain" type="domainType" use="required">
              <xs:annotation>
                <xs:appinfo>
                  <jaxb:property name="domainAttr"/>
                </xs:appinfo>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="domainsId" type="xs:string" use="required">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="domainsIdAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="domainsType">
    <xs:sequence>
      <xs:element name="domain" type="domainType"/>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="domainType">
    <xs:annotation>
      <xs:documentation>
	      domain type.
	  </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:token">
      <xs:minLength value="2"/>
      <xs:maxLength value="80"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>