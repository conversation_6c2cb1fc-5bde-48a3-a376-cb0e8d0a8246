<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:x="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:include schemaLocation="../../../schemas/Generic/Layered_Repair_and_Recover/types/cudb_types.xsd" />

	<!-- DeleteMultipleSubscriptionData MOId: msisdn MOType: MultipleSubscriptionData@http://schemas.ericsson.com/ma/cudb/ -->
	<xs:element name="msisdn" type="msisdnType" />
</xs:schema>
