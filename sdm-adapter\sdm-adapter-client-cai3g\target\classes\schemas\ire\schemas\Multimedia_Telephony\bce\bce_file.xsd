<!-- BCE Call BarringDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/bce/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd" />
	<xs:element name="fileId" type="xs:long" />
	<!-- Create File MOId: fileId MOType: File@http://schemas.ericsson.com/ma/bce/ -->
	<!-- No CREATE operation -->
	<!-- Set File MOId: fileId MOType: File@http://schemas.ericsson.com/ma/bce/ -->
	<!-- No SET operation -->
	<xs:element name="setFile">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE File
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fileId" type="xs:long" minOccurs="0" />
				<xs:element name="fileData" type="xs:string" minOccurs="0" />
				<xs:element name="metaData" type="metaData" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="fileId" type="xs:long" use="required" />
		</xs:complexType>
	</xs:element>
	<!-- get File MOId: fileId MOType: File@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get File response -->
	<xs:element name="getFileResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE File
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fileId" type="xs:long" minOccurs="0" />
				<xs:element name="fileDescriptors" type="upFileDescriptor"
					minOccurs="0" />
				<xs:element name="fileData" type="xs:string" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="fileId" type="xs:long" use="required" />
		</xs:complexType>
	</xs:element>
	<!-- delete File MOId: fileId MOType: File@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
