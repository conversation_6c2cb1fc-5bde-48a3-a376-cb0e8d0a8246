{"groups": [{"name": "cai3g", "type": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.camel", "type": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$CamelProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties.CamelProperties getCamel() "}, {"name": "cai3g.http", "type": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$HttpProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties.HttpProperties getHttp() "}], "properties": [{"name": "cai3g.auc-mo-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.camel.tdps", "type": "java.util.List<com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$CamelProperties$CamelTdpProperties>", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$CamelProperties"}, {"name": "cai3g.hlr-mo-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.hlr-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.hss-mo-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.hss-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.http.connection-time-out", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$HttpProperties"}, {"name": "cai3g.http.max-connections", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$HttpProperties"}, {"name": "cai3g.http.max-connections-per-route", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties$HttpProperties"}, {"name": "cai3g.include-qos-in-apn", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.location", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.session-pool-size", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.trust-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.tspassword", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}, {"name": "cai3g.user", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.cai3g.config.Cai3gClientProperties"}], "hints": []}