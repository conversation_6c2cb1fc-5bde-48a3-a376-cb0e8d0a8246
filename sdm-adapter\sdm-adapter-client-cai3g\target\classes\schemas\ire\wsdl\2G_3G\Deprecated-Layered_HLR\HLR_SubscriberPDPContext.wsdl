<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
	xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/"
			xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/"
			xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified"
			attributeFormDefault="unqualified">
			<xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd" />
			<xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/"
				schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/HLR_SubscriberPDPContext.xsd" />
			<xs:element name="Create">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string"
							fixed="SubscriberPDPContext@http://schemas.ericsson.com/pg/hlr/13.5/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="hlr:msisdn" />
										<xs:element ref="hlr:imsi" />
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes" minOccurs="1">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="hlr:CreateSubscriberPDPContext" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="extension" minOccurs="0">
                           <xs:complexType>
                               <xs:sequence>
                                 <xs:element ref="hlr:PrimaryHLRId" minOccurs="0"/>
                               </xs:sequence>
                           </xs:complexType>
                       </xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Get">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string"
							fixed="SubscriberPDPContext@http://schemas.ericsson.com/pg/hlr/13.5/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="hlr:msisdn" />
										<xs:element ref="hlr:imsi" />
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
                        <xs:element name="extension" minOccurs="0">
                           <xs:complexType>
                               <xs:sequence>
                                 <xs:element ref="hlr:PrimaryHLRId" minOccurs="0"/>
                               </xs:sequence>
                           </xs:complexType>
                       </xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOId" type="AnyMOIdType" minOccurs="0"
							maxOccurs="unbounded" />
						<xs:element name="MOAttributes" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="hlr:GetSubscriberPDPContext" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Set">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string"
							fixed="SubscriberPDPContext@http://schemas.ericsson.com/pg/hlr/13.5/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="hlr:msisdn" />
										<xs:element ref="hlr:imsi" />
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="hlr:SetSubscriberPDPContext" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="extension" minOccurs="0">
                           <xs:complexType>
                               <xs:sequence>
                                 <xs:element ref="hlr:PrimaryHLRId" minOccurs="0"/>
                               </xs:sequence>
                           </xs:complexType>
                       </xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Delete">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string"
							fixed="SubscriberPDPContext@http://schemas.ericsson.com/pg/hlr/13.5/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="hlr:msisdn" />
										<xs:element ref="hlr:imsi" />
									</xs:choice>
									<xs:element ref="hlr:pdpid" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes" minOccurs="0">
                           <xs:complexType>
                               <xs:sequence>
                                 <xs:element ref="hlr:DeleteSubscriberPDPContext" minOccurs="0"/>
                               </xs:sequence>
                           </xs:complexType>
                       </xs:element>
						<xs:element name="extension" minOccurs="0">
                           <xs:complexType>
                               <xs:sequence>
                                 <xs:element ref="hlr:PrimaryHLRId" minOccurs="0"/>
                               </xs:sequence>
                           </xs:complexType>
                       </xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:schema>
	</types>
	<message name="CreateRequest">
		<part name="parameters" element="cai3g:Create" />
	</message>
	<message name="CreateResponse">
		<part name="parameters" element="cai3g:CreateResponse" />
	</message>
	<message name="GetRequest">
		<part name="parameters" element="cai3g:Get" />
	</message>
	<message name="GetResponse">
		<part name="parameters" element="cai3g:GetResponse" />
	</message>
	<message name="SetRequest">
		<part name="parameters" element="cai3g:Set" />
	</message>
	<message name="SetResponse">
		<part name="parameters" element="cai3g:SetResponse" />
	</message>
	<message name="DeleteRequest">
		<part name="parameters" element="cai3g:Delete" />
	</message>
	<message name="DeleteResponse">
		<part name="parameters" element="cai3g:DeleteResponse" />
	</message>
	<message name="HeadInfo">
		<part name="sessionId" element="cai3g:SessionId" />
	</message>
	<message name="Cai3gFault">
		<part name="parameters" element="cai3g:Cai3gFault" />
	</message>
	<message name="Cai3gHeaderFault">
		<part name="sessionIdFault" element="cai3g:SessionIdFault" />
		<part name="transactionIdFault" element="cai3g:TransactionIdFault" />
		<part name="sequenceIdFault" element="cai3g:SequenceIdFault" />
	</message>
	<portType name="SubscriberPDPContext">
		<operation name="Create">
			<input message="cai3g:CreateRequest" />
			<output message="cai3g:CreateResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
		<operation name="Delete">
			<input message="cai3g:DeleteRequest" />
			<output message="cai3g:DeleteResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
		<operation name="Get">
			<input message="cai3g:GetRequest" />
			<output message="cai3g:GetResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
		<operation name="Set">
			<input message="cai3g:SetRequest" />
			<output message="cai3g:SetResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
	</portType>
	<binding name="SubscriberPDPContext" type="cai3g:SubscriberPDPContext">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<operation name="Create">
			<soap:operation soapAction="CAI3G#Create" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault"
						part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
		<operation name="Delete">
			<soap:operation soapAction="CAI3G#Delete" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault"
						part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
		<operation name="Get">
			<soap:operation soapAction="CAI3G#Get" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault"
						part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
		<operation name="Set">
			<soap:operation soapAction="CAI3G#Set" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId"
					use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault"
						part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
	</binding>
	<service name="Provisioning">
		<port name="SubscriberPDPContext" binding="cai3g:SubscriberPDPContext">
			<soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2" />
		</port>
	</service>
</definitions>