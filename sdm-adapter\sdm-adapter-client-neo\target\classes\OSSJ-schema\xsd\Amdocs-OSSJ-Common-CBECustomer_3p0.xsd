<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-customer="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:amdocs-resource="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:cbecustomer-v1-5="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" xmlns:cbelocation-v1-5="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:cbeparty-v1-5="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:amdocs-party="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:amdocs-product="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:ns1="http://ossj.org/xml/Common-CBEDatatypes/v1-5" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:cbebi-v1-5="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5" targetNamespace="http://amdocs/core/ossj-Common-CBECustomer/dat/3" elementFormDefault="qualified">
	<xs:import namespace="http://amdocs/core/ossj-Common-CBEParty/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBEParty_3p0.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common-CBEResource/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBEResource_3p0.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common/dat/3" schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBELocation/v1-5" schemaLocation="OSSJ-Common-CBELocation-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5" schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEParty/v1-5" schemaLocation="OSSJ-Common-CBEParty-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5" schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5" schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common/v1-5" schemaLocation="OSSJ-Common-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEBi/v1-5" schemaLocation="OSSJ-Common-CBEBi-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5" schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECustomer/v1-5" schemaLocation="OSSJ-Common-CBECustomer-v1-5.xsd"/>
	<xs:annotation>
		<xs:documentation>This schema was updated: 27 October 2011</xs:documentation>
	</xs:annotation>
	<xs:element name="CustomerState" type="xs:string" substitutionGroup="cbecustomer-v1-5:baseCustomerState_Customer">
		<xs:annotation>
			<xs:documentation>The state that the customer account is in. If the customer is in any inactive or suspended state they will not be able to access any of their services.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:element name="CustomerKey" type="amdocs-customer:CustomerKey"/>
	<xs:complexType name="CustomerKey">
		<xs:annotation>
			<xs:documentation>A key identifying a Customer. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="cbecustomer-v1-5:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="CustomerValue" type="amdocs-customer:CustomerValue"/>
	<xs:complexType name="CustomerValue">
		<xs:complexContent>
			<xs:extension base="cbecustomer-v1-5:CustomerValue">
				<xs:sequence>
					<xs:element name="operator" type="xs:string" minOccurs="0"/>
					<xs:element name="contactMediums" type="amdocs-party:ArrayOfContactMediumValue" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Contains a customer's address and other contact details.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="party" type="amdocs-party:PartyValue" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Describes a customer's location independent attributes. Includes naming characteristics, date of birth, and gender.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="customerType" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The type of customer.</xs:documentation>
						</xs:annotation>
					</xs:element>
				
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfCustomerKey" type="amdocs-customer:ArrayOfCustomerKey"/>
	<xs:complexType name="ArrayOfCustomerKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:CustomerKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfCustomerValue" type="amdocs-customer:ArrayOfCustomerValue"/>
	<xs:complexType name="ArrayOfCustomerValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:CustomerValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Subscriber Entities -->
	<xs:element name="SubscriberRelationship" type="amdocs-customer:SubscriberRelationship"/>
	<xs:complexType name="SubscriberRelationship">
		<xs:sequence>
			<xs:element name="type" type="xs:string"/>
			<xs:element name="subscriberKey" type="amdocs-customer:SubscriberKey"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberRelationship" type="amdocs-customer:ArrayOfSubscriberRelationship"/>
	<xs:complexType name="ArrayOfSubscriberRelationship">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:SubscriberRelationship" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SubscriberKey" type="amdocs-customer:SubscriberKey"/>
	<xs:complexType name="SubscriberKey">
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberKey" type="amdocs-customer:ArrayOfSubscriberKey"/>
	<xs:complexType name="ArrayOfSubscriberKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:SubscriberKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SubscriberValue" type="amdocs-customer:SubscriberValue"/>
	<xs:complexType name="SubscriberValue">
		<xs:annotation>
			<xs:documentation>An entity representing a Subscriber</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="subscriberType" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="associatedSubscribers" type="amdocs-customer:ArrayOfSubscriberRelationship" minOccurs="0"/>
					<xs:element name="associatedProducts" type="cbeproduct-v1-5:ArrayOfProductValue" minOccurs="0"/>
					<xs:element name="associatedResources" type="cberesource-v1-5:ArrayOfResourceValue" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfSubscriberValue" type="amdocs-customer:ArrayOfSubscriberValue"/>
	<xs:complexType name="ArrayOfSubscriberValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:SubscriberValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Check -->
	<xs:element name="BillingAccountKey" type="amdocs-customer:BillingAccountKey">
		<xs:annotation>
			<xs:documentation>Contains the unique identifier for the billing account. This key is usually the same across BSS and OSS systems. </xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="BillingAccountKey">
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="BillingAccountValue" type="amdocs-customer:BillingAccountValue"/>
	<xs:complexType name="BillingAccountValue">
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="smsBillingAccountIdentifier" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the Billing Account entity. This value is usually the same for both the BSS system (identified using the value of subscriberManagementSystem in the OSS system), and the OSS system.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="relatedConsumers" type="amdocs-customer:ArrayOfConsumerKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="SubAccountKey" type="amdocs-customer:SubAccountKey"/>
	<xs:complexType name="SubAccountKey">
		<xs:annotation>
			<xs:documentation>A key identifying a SubAccount. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="SubAccountValue" type="amdocs-customer:SubAccountValue"/>
	<xs:complexType name="SubAccountValue">
		<xs:annotation>
			<xs:documentation>An entity representing a SubAccount. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="smsSubAccountIdentifier" type="xs:string" minOccurs="0"/>
					<xs:element name="associatedConsumer" type="amdocs-customer:ConsumerKey" minOccurs="0"/>
					<xs:element name="subAccountType" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ConsumerKey" type="amdocs-customer:ConsumerKey"/>
	<xs:complexType name="ConsumerKey">
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ConsumerValue" type="amdocs-customer:ConsumerValue"/>
	<xs:complexType name="ConsumerValue">
		<xs:annotation>
			<xs:documentation>An entity representing a Consumer. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="smsConsumerIdentifier" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the Consumer. This value is usually shared by both BSS and OSS systems.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedBillingAccount" type="amdocs-customer:BillingAccountKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the associated (parent) Billing Account.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="relatedSubAccounts" type="amdocs-customer:ArrayOfSubAccountKey" minOccurs="0"/>
					<xs:element name="consumerType" type="xs:string" minOccurs="0"/>
					<xs:element name="subscribedProducts" type="cbeproduct-v1-5:ArrayOfProductKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="BusinessKey" type="amdocs-customer:BusinessKey"/>
	<xs:complexType name="BusinessKey">
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="BusinessValue" type="amdocs-customer:BusinessValue"/>
	<xs:complexType name="BusinessValue">
		<xs:annotation>
			<xs:documentation>An entity representing a Business. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="smsConsumerIdentifier" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the Consumer. This value is usually shared by both BSS and OSS systems.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedBillingAccount" type="amdocs-customer:BillingAccountKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the associated (parent) Billing Account.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="relatedSubAccounts" type="amdocs-customer:ArrayOfSubAccountKey" minOccurs="0"/>
					<xs:element name="consumerType" type="xs:string" minOccurs="0"/>
					<xs:element name="subscribedProducts" type="cbeproduct-v1-5:ArrayOfProductKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="EmployeeKey" type="amdocs-customer:EmployeeKey"/>
	<xs:complexType name="EmployeeKey">
		<xs:annotation>
			<xs:documentation>A key identifying a Employee. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="EmployeeValue" type="amdocs-customer:EmployeeValue"/>
	<xs:complexType name="EmployeeValue">
		<xs:annotation>
			<xs:documentation>An entity representing a Employee. This is an auto-generated file: do not modify!</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="amdocs-customer:CustomerValue">
				<xs:sequence>
					<xs:element name="smsSubAccountIdentifier" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The identifier for any sub-accounts associated with this employee.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="associatedBusiness" type="amdocs-customer:BusinessKey" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The identifier for the business that the employee is associated with.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="businessEmployeeType" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The type of employee this is.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfBillingAccountKey" type="amdocs-customer:ArrayOfBillingAccountKey"/>
	<xs:complexType name="ArrayOfBillingAccountKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:BillingAccountKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfBillingAccountValue" type="amdocs-customer:ArrayOfBillingAccountValue"/>
	<xs:complexType name="ArrayOfBillingAccountValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:BillingAccountValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfSubAccountKey" type="amdocs-customer:ArrayOfSubAccountKey"/>
	<xs:complexType name="ArrayOfSubAccountKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:SubAccountKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfSubAccountValue" type="amdocs-customer:ArrayOfSubAccountValue"/>
	<xs:complexType name="ArrayOfSubAccountValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:SubAccountValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfConsumerKey" type="amdocs-customer:ArrayOfConsumerKey"/>
	<xs:complexType name="ArrayOfConsumerKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:ConsumerKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfConsumerValue" type="amdocs-customer:ArrayOfConsumerValue"/>
	<xs:complexType name="ArrayOfConsumerValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:ConsumerValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfBusinessKey" type="amdocs-customer:ArrayOfBusinessKey"/>
	<xs:complexType name="ArrayOfBusinessKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:BusinessKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfBusinessValue" type="amdocs-customer:ArrayOfBusinessValue"/>
	<xs:complexType name="ArrayOfBusinessValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:BusinessValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfEmployeeKey" type="amdocs-customer:ArrayOfEmployeeKey"/>
	<xs:complexType name="ArrayOfEmployeeKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:EmployeeKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfEmployeeValue" type="amdocs-customer:ArrayOfEmployeeValue"/>
	<xs:complexType name="ArrayOfEmployeeValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-customer:EmployeeValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>