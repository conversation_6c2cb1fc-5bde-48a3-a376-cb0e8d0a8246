<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/auc/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/auc/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/auc/13.5/">
<xs:include schemaLocation="types/auc_types.xsd"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="GetAuthenticationandKeyAgreementAlgorithm">
<xs:complexType>
<xs:sequence>
<xs:element ref="AkaAlgorithmSubscriptionData"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="AkaAlgorithmSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="eki" type="ekiType"/>
<xs:element name="kind" type="kindType"/>
<xs:element name="fsetind" type="fsetindType"/>
<xs:element name="a4ind" type="a4indType"/>
<xs:element name="akaalgind" type="akaalgindType"/>
<xs:element name="amf" type="amfType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="SetAuthenticationandKeyAgreementAlgorithm">
<xs:complexType>
<xs:sequence>
<xs:element name="akaalgind" type="akaalgindType"/>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
