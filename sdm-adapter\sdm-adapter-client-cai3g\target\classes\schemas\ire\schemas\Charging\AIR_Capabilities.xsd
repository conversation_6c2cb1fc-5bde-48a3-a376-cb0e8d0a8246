<xs:schema elementFormDefault="qualified"
    attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
    jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/"
    xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
    xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
    xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/">
    
    <xs:element name="subscriberNumber" type="subscriberNumberType" />
    
    <!-- Capabilities -->
    <xs:element name="getCapabilities">
    	<xs:complexType>
    		<xs:sequence>
    		</xs:sequence>
    		<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="subscriberNumberAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
    	</xs:complexType>
    </xs:element>
    
    <!-- Capabilities Response -->
    <xs:element name="getCapabilitiesResponse">
    	<xs:complexType>
    		<xs:sequence>
    			<xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0" maxOccurs="unbounded"/>
    		</xs:sequence>
    	</xs:complexType>
    </xs:element>
    
    <xs:simpleType name="subscriberNumberType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,28}" />
        </xs:restriction>
    </xs:simpleType>
    
    <xs:simpleType name="availableServerCapabilitiesType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="2147483647" />
        </xs:restriction>
    </xs:simpleType>
    
</xs:schema>