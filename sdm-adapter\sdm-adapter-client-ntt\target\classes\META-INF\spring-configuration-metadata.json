{"groups": [{"name": "ntt", "type": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.charging", "type": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$ChargingPropetries", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties.ChargingPropetries getCharging() "}, {"name": "ntt.eps", "type": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$EpsProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties.EpsProperties getEps() "}, {"name": "ntt.http", "type": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$HttpProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties.HttpProperties getHttp() "}], "properties": [{"name": "ntt.charging.charging-character-allowed", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$ChargingPropetries"}, {"name": "ntt.charging.charging-character-behavior", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$ChargingPropetries"}, {"name": "ntt.charging.charging-character-profile", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$ChargingPropetries"}, {"name": "ntt.csd", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.eps.max-bandwidth-down", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$EpsProperties"}, {"name": "ntt.eps.max-bandwidth-up", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$EpsProperties"}, {"name": "ntt.hlr-nsr-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.hss-unified-nsr-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.http.connection-time-out", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$HttpProperties"}, {"name": "ntt.http.max-connections", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$HttpProperties"}, {"name": "ntt.http.max-connections-per-route", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties$HttpProperties"}, {"name": "ntt.include-qos-in-apn", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.key-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.trust-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}, {"name": "ntt.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.ntt.config.NttClientProperties"}], "hints": []}