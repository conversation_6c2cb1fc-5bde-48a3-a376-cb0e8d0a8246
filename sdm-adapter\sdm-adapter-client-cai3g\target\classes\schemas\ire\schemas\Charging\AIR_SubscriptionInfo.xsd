<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="getSubscriptionInfo">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element default="0" minOccurs="0" name="requestOfferInformationFlag " type="requestOfferInformationFlagType"/>
<xs:element default="0" minOccurs="0" name="requestSubscriberInformationFlag" type="requestSubscriberInformationFlagType"/>
<xs:element default="0" minOccurs="0" name="getUsageThresholdsAndCountersFlag" type="getUsageThresholdsAndCountersFlagType"/>
<xs:element default="0" minOccurs="0" name="requestDedicatedAccountInformationFlag" type="requestDedicatedAccountInformationFlagType"/>
<xs:element default="0" minOccurs="0" name="requestAccumulatorsFlag" type="requestAccumulatorsFlagType"/>
<xs:element minOccurs="0" name="requestedInformationFlags" type="requestedInformationFlagsType"/>
<xs:element minOccurs="0" name="chargingRequestInformation" type="chargingRequestInformationType"/>
<xs:element fixed="1" minOccurs="0" name="requestPamInformationFlag" type="requestPamInformationFlagType"/>
<xs:element default="0" minOccurs="0" name="requestActiveOffersFlag" type="requestActiveOffersFlagType"/>
<xs:element default="0" minOccurs="0" name="requestAttributesFlag" type="requestAttributesFlagType"/>
<xs:element fixed="1" minOccurs="0" name="requestTreeParameterSetsFlag" type="requestTreeParameterSetsFlagType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accumulatorSelection">
<xs:complexType>
<xs:sequence>
<xs:element name="accumulatorIDFirst" type="accumulatorIDFirstType"/>
<xs:element minOccurs="0" name="accumulatorIDLast" type="accumulatorIDLastType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="getSubscriptionInfoResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="firstIVRCallFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="ussdEndOfCallNotificationID" type="ussdEndOfCallNotificationIDType"/>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="communityInformationCurrent" type="communityIDType"/>
<xs:element minOccurs="0" name="temporaryBlockedFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="accountActivatedFlag" type="accountActivatedFlagType"/>
<xs:element minOccurs="0" name="activationDate" type="activationDateType"/>
<xs:element minOccurs="0" name="masterSubscriberFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="masterAccountNumber" type="masterAccountNumberType"/>
<xs:element minOccurs="0" name="refillUnbarDateTime" type="refillUnbarDateTimeType"/>
<xs:element minOccurs="0" name="promotionAnnouncementCode" type="promotionAnnouncementCodeType"/>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="promotionStartDate" type="promotionStartDateType"/>
<xs:element minOccurs="0" name="promotionEndDate" type="promotionEndDateType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="creditClearanceDate" type="creditClearanceDateType"/>
<xs:element minOccurs="0" name="serviceRemovalDate" type="serviceRemovalDateType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassChangeUnbarDate" type="serviceClassChangeUnbarDateType"/>
<xs:element minOccurs="0" name="serviceFeePeriod" type="serviceFeePeriodType"/>
<xs:element minOccurs="0" name="supervisionPeriod" type="supervisionPeriodType"/>
<xs:element minOccurs="0" name="serviceRemovalPeriod" type="serviceRemovalPeriodType"/>
<xs:element minOccurs="0" name="creditClearancePeriod" type="creditClearancePeriodType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
<xs:element minOccurs="0" name="aggregatedBalance1" type="aggregatedBalanceType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
<xs:element minOccurs="0" name="accountHomeRegion" type="accountHomeRegionType"/>
<xs:element minOccurs="0" name="pinCode" type="pinCodeType"/>
<xs:element minOccurs="0" name="aggregatedBalance2" type="aggregatedBalanceType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="pamInformation" type="pamInformationType"/>
<xs:element minOccurs="0" name="maxServiceFeePeriod" type="maxServiceFeePeriodType"/>
<xs:element minOccurs="0" name="maxSupervisionPeriod" type="maxSupervisionPeriodType"/>
<xs:element minOccurs="0" name="negativeBalanceBarringDate" type="negativeBalanceBarringDateType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsBeforeType"/>
<xs:element minOccurs="0" name="accountFlags" type="accountFlagsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationType"/>
<xs:element minOccurs="0" name="accountTimeZone" type="xs:string"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accumulatorInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="accumulatorID" type="accumulatorIDType"/>
<xs:element minOccurs="0" name="accumulatorValue" type="accumulatorValueType"/>
<xs:element minOccurs="0" name="accumulatorStartDate" type="accumulatorStartDateType"/>
<xs:element minOccurs="0" name="accumulatorEndDate" type="accumulatorEndDateType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="chargingResultInformation" type="chargingResultInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
<xs:element minOccurs="0" name="cellIdentifier" type="cellIdentifierType"/>
<xs:element minOccurs="0" name="locationNumber" type="locationNumberType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit1" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit2" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="refillFraudCount" type="refillFraudCountType"/>
<xs:element minOccurs="0" name="localProviderType" type="localProviderTypeType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="setSubscriptionInfo">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="mainAccountValueNew " type="mainAccountValueNewType"/>
</xs:choice>
<xs:element minOccurs="0" name="transactionType" type="transactionTypeType"/>
<xs:element minOccurs="0" name="transactionCode" type="transactionCodeType"/>
<xs:element minOccurs="0" name="serviceClassAction" type="serviceClassActionType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="serviceClassNew" type="serviceClassNewType"/>
<xs:element minOccurs="0" name="serviceClassTemporary" type="serviceClassTemporaryType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryNew" type="serviceClassTemporaryType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryNewExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="accountGroupID" type="accountGroupIDType"/>
<xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element minOccurs="0" name="serviceOfferingsValue" type="serviceOfferingsValuesType"/>
</xs:choice>
<xs:element minOccurs="0" name="communityInformationCurrent" type="communityIDType"/>
<xs:choice>
<xs:element minOccurs="0" name="communityInformationNew" type="communityIDType"/>
<xs:element minOccurs="0" name="communityInformationUpdate" type="communityInformationUpdateType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerUpdateInformation" type="offerUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="accumulatorUpdateInformation" type="accumulatorUpdateInformationType"/>
<xs:element minOccurs="0" name="backdatedToDateTime " type="dateTimeType"/>
<xs:element minOccurs="0" name="backdatedToStartOfBillCycleInstance" type="backdatedToStartOfBillCycleInstanceType"/>
<xs:element fixed="1" minOccurs="0" name="updateUsageCounterForMultiUser" type="xs:integer"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUpdateInformation" type="usageCounterUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdUpdateInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="usageThresholdID" type="usageThresholdIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="usageThresholdValueNew" type="usageThresholdValueNewType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValueNew" type="usageThresholdMonetaryValueNewType"/>
</xs:choice>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
<xs:attribute name="usageThresholdID" type="usageThresholdIDType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="usageThresholdIDAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="timeRestrictionInformation" type="timeRestrictionInformationType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="supervisionExpiryDateRelative" type="supervisionExpiryDateRelativeType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="serviceFeeExpiryDateRelative" type="serviceFeeExpiryDateRelativeType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
</xs:choice>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData3" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData4" type="externalDataType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setSubscriptionInfoResponse">
<xs:complexType>
<xs:sequence>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="currency1" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="currency2" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="negativeBalanceBarringDate" type="negativeBalanceBarringDateType"/>
<xs:element minOccurs="0" name="accountFlagsAfter" type="accountFlagsAfterType"/>
<xs:element minOccurs="0" name="accountFlagsBefore" type="accountFlagsBeforeType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="timeRestrictionInformation" type="timeRestrictionInformationTypeForResponse"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originHostNameType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTimeStampType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="subscriberNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="messageCapabilityFlagType">
<xs:sequence>
<xs:element minOccurs="0" name="promotionNotificationFlag" type="promotionNotificationFlagType"/>
<xs:element minOccurs="0" name="firstIVRCallSetFlag" type="firstIVRCallSetFlagType"/>
<xs:element minOccurs="0" name="accountActivationFlag" type="accountActivationFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="promotionNotificationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="firstIVRCallSetFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accountActivationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestOfferInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestSubscriberInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="getUsageThresholdsAndCountersFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestDedicatedAccountInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestAccumulatorsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="requestedInformationFlagsType">
<xs:sequence>
<xs:element minOccurs="0" name="requestMasterAccountBalanceFlag" type="requestMasterAccountBalanceFlagType"/>
<xs:element minOccurs="0" name="allowedServiceClassChangeDateFlag" type="allowedServiceClassChangeDateFlagType"/>
<xs:element minOccurs="0" name="requestLocationInformationFlag" type="requestLocationInformationFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="requestMasterAccountBalanceFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="allowedServiceClassChangeDateFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestLocationInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="chargingRequestInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="chargingType" type="chargingTypeType"/>
<xs:element minOccurs="0" name="chargingIndicator" type="chargingIndicatorType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="chargingTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="reservationCorrelationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="requestPamInformationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestActiveOffersFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestAttributesFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestTreeParameterSetsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="255"/>
<xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDFirstType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDLastType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="languageIDCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassTemporaryExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="ussdEndOfCallNotificationIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountGroupIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="serviceOfferingsType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceOfferingID" type="serviceOfferingIDType"/>
<xs:element minOccurs="0" name="serviceOfferingActiveFlag" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="serviceOfferingIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="31"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="communityIDType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="temporaryBlockedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accountActivatedFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="activationDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="masterAccountNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillUnbarDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionAnnouncementCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionPlanIDType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="4"/>
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionEndDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="supervisionExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="creditClearanceDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceRemovalDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceFeeExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceClassChangeUnbarDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceRemovalPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1023"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="creditClearancePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1023"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9 ]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountReservationType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="aggregatedBalanceType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9 ]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="aggregatedReservationType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountHomeRegionType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="pinCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,8}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="pamInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="pamClassID" type="pamClassIDTpye"/>
<xs:element minOccurs="0" name="scheduleID" type="scheduleIDType"/>
<xs:element minOccurs="0" name="currentPamPeriod" type="currentPamPeriodType"/>
<xs:element minOccurs="0" name="deferredToDate" type="deferredToDateType"/>
<xs:element minOccurs="0" name="lastEvaluationDate" type="lastEvaluationDateType"/>
<xs:element minOccurs="0" name="pamServicePriority" type="pamServicePriorityType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="pamClassIDTpye">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduleIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currentPamPeriodType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9\-/]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="lastEvaluationDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="pamServicePriorityType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="maxServiceFeePeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="maxSupervisionPeriodType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negativeBalanceBarringDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:complexType name="accountFlagsBeforeType">
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="activationStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="negativeBarringStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="accountFlagsType">
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="twoStepActivationFlag" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDateTime" type="startDateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="expiryDateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="offerIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="startDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="expiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="startDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="expiryDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="pamServiceIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerStateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerProviderIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="productIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="externalProductIDType">
<xs:restriction base="xs:string">
<xs:pattern value="(EXT.{1,52})"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="usageCounterUsageThresholdInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterNominalValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
<xs:element minOccurs="0" name="usageCounterResourceConnected" type="usageCounterResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="usageCounterIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="usageThresholdInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageThresholdID" type="usageThresholdIDType"/>
<xs:element minOccurs="0" name="usageThresholdValue" type="usageThresholdValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdSource" type="usageThresholdSourceType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="usageThresholdIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="associatedPartyIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterResourceConnectedType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="dedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountRealMoneyFlag" type="dedicatedAccountRealMoneyFlagType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountChangeInformation" type="subDedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="dedicatedAccountIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountRealMoneyFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="closestExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestExpiryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestAccessibleDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestAccessibleValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="subDedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="changedAmount1" type="changedAmountType"/>
<xs:element minOccurs="0" name="changedAmount2" type="changedAmountType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="changedExpiryDate" type="changedExpiryDateType"/>
<xs:element minOccurs="0" name="newExpiryDate" type="newExpiryDateType"/>
<xs:element minOccurs="0" name="clearedExpiryDate" type="clearedExpiryDateType"/>
<xs:element minOccurs="0" name="changedStartDate" type="changedStartDateType"/>
<xs:element minOccurs="0" name="newStartDate" type="newStartDateType"/>
<xs:element minOccurs="0" name="clearedStartDate" type="clearedStartDateType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="changedAmountType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedExpiryDateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="clearedExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="changedStartDateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="newStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="clearedStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountUnitTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="dedicatedAccountInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedReservation1" type="dedicatedReservationType"/>
<xs:element minOccurs="0" name="dedicatedReservation2" type="dedicatedReservationType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountRealMoneyFlag" type="dedicatedAccountRealMoneyFlagType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="compositeDedicatedAccountFlag" type="compositeDedicatedAccountFlagType"/>
<xs:element minOccurs="0" name="dedicatedAccountResourceConnected" type="dedicatedAccountResourceConnectedType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="dedicatedReservationType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="subDedicatedAccountInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="compositeDedicatedAccountFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountResourceConnectedType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="attributeInformationType">
<xs:sequence>
<xs:element name="attributeName" type="attributeNameType"/>
<xs:element minOccurs="0" name="attributeValueString" type="attributeValueStringType"/>
<xs:element minOccurs="0" name="attributeValueDate" type="attributeValueDateType"/>
<xs:element minOccurs="0" name="attributeValueDecimal" type="attributeValueDecimalType"/>
<xs:element minOccurs="0" name="attributeValueInteger" type="attributeValueIntegerType"/>
<xs:element minOccurs="0" name="attributeSource" type="attributeSourceType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="attributeValueStringType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="attributeValueDate">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:complexType name="attributeValueDecimalType">
<xs:sequence>
<xs:element minOccurs="0" name="attributeValueNumber" type="attributeValueNumberType"/>
<xs:element minOccurs="0" name="numberOfDecimals" type="numberOfDecimalsType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="attributeValueNumberType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-********9999999"/>
<xs:maxInclusive value="********9999999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="numberOfDecimalsType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="15"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="attributeValueIntegerType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-**********"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="attributeSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="treeParameterSetInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceProvider" type="serviceProviderType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterInformation" type="treeParameterInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="serviceProviderType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="6"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="treeParameterInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="treeParameterName" type="treeParameterNameType"/>
<xs:element minOccurs="0" name="treeParameterValueString" type="treeParameterValueStringType"/>
<xs:element minOccurs="0" name="treeParameterValueDate" type="treeParameterValueDateType"/>
<xs:element minOccurs="0" name="treeParameterValueDecimal" type="treeParameterValueDecimalType"/>
<xs:element minOccurs="0" name="treeParameterValueInteger" type="treeParameterValueIntegerType"/>
<xs:element minOccurs="0" name="treeParameterValueSchedule" type="treeParameterValueScheduleType"/>
<xs:element minOccurs="0" name="treeParameterValueDayOfWeek" type="treeParameterValueDayOfWeekType"/>
<xs:element minOccurs="0" name="treeParameterValueDateRange" type="treeParameterValueDateRangeType"/>
<xs:element minOccurs="0" name="treeParameterValueTime" type="treeParameterValueTimeType"/>
<xs:element minOccurs="0" name="treeParameterSource" type="treeParameterSourceType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="treeParameterNameType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9\-_]{1,128}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeParameterValueStringType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeParameterValueDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:complexType name="treeParameterValueDecimalType">
<xs:sequence>
<xs:element minOccurs="0" name="treeParameterValueNumber" type="treeParameterValueNumberType"/>
<xs:element minOccurs="0" name="numberOfDecimals" type="numberOfDecimalsType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="treeParameterValueNumberType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-********9999999"/>
<xs:maxInclusive value="********9999999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeParameterValueIntegerType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="treeParameterValueScheduleType">
<xs:sequence>
<xs:element minOccurs="0" name="scheduledFrequency" type="scheduledFrequencyType"/>
<xs:element minOccurs="0" name="scheduledInterval" type="scheduledIntervalType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="scheduledMonth" type="scheduledMonthType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="scheduledDayOfMonth" type="scheduledDayOfMonthType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="scheduledDayOfWeek" type="scheduledDayOfWeekType"/>
<xs:element minOccurs="0" name="scheduledHour" type="scheduledHourType"/>
<xs:element minOccurs="0" name="scheduledMinute" type="scheduledMinuteType"/>
<xs:element minOccurs="0" name="scheduledSecond" type="scheduledSecondType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="scheduledFrequencyType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledIntervalType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="99999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledMonthType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="12"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledDayOfMonthType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="31"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledDayOfWeekType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledHourType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="23"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledMinuteType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="59"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="scheduledSecondType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="59"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeParameterValueDayOfWeekType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="7"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="treeParameterValueDateRangeType">
<xs:sequence>
<xs:element minOccurs="0" name="treeParameterStartDate" type="treeParameterStartDateType"/>
<xs:element minOccurs="0" name="treeParameterEndDate" type="treeParameterEndDateType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="treeParameterStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="treeParameterEndDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="treeParameterValueTimeType">
<xs:restriction base="xs:string">
<xs:pattern value="(0\d{1}|1\d{1}|2[0-3]):[0-5]\d{1}:([0-5]\d{1})"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeParameterSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="productOfferingNameType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9\-_+ ]{1,128}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorValueType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-2147483648 "/>
<xs:maxInclusive value="2147483648 "/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="accumulatorEndDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:complexType name="chargingResultInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="chargingResultCode" type="chargingResultCodeType"/>
<xs:element minOccurs="0" name="reservationCorrelationID" type="reservationCorrelationIDType"/>
<xs:element minOccurs="0" name="chargingResultInformationService" type="chargingResultInformationServiceType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="costType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-********9999999"/>
<xs:maxInclusive value="********9999999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="chargingResultCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="chargingResultInformationServiceType">
<xs:sequence>
<xs:element minOccurs="0" name="cost1" type="costType"/>
<xs:element minOccurs="0" name="cost2" type="costType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="cellIdentifierType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,19}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="locationNumber">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{0,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountPrepaidEmptyLimitType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-********9999999"/>
<xs:maxInclusive value="********9999999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillFraudCountType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="localProviderTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassOriginalType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="deferredToDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="attributeNameType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9\-_]{1,128}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="attributeValueDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="locationNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCurrencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentAmountRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="mainAccountValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionTypeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="Set"/>
<xs:enumeration value="SetOriginal"/>
<xs:enumeration value="SetTemporary"/>
<xs:enumeration value="DeleteTemporary"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassNewType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassTemporaryType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="expiryPamPeriodIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentStartDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-999"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="startPamPeriodIndicatorType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="updateActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXPIRE"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="offerUpdateInformationType">
<xs:sequence>
<xs:element minOccurs="1" name="offerID" type="offerIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="dateInformation" type="dateInformationType"/>
<xs:element minOccurs="0" name="dateTimeInformation" type="dateTimeInformationType"/>
</xs:choice>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountUpdateInformation" type="dedicatedAccountUpdateInformationType"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
</xs:choice>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeUpdateInformation" type="attributeUpdateInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetUpdateInformation" type="treeParameterSetUpdateInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="dateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-999"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dateTimeRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-********"/>
<xs:maxInclusive value="********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="attributeUpdateActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="ADD"/>
<xs:enumeration value="DELETE"/>
<xs:enumeration value="CLEAR"/>
<xs:enumeration value="SET"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="accumulatorUpdateInformationType">
<xs:sequence>
<xs:element name="accumulatorID" type="accumulatorIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="accumulatorValueRelative" type="accumulatorValueType"/>
<xs:element minOccurs="0" name="accumulatorValueAbsolute" type="accumulatorValueType"/>
</xs:choice>
<xs:element minOccurs="0" name="accumulatorStartDate" type="accumulatorStartDateType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="backdatedToStartOfBillCycleInstanceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-1"/>
<xs:maxInclusive value="0"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="usageCounterUpdateInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="usageCounterID" type="usageCounterIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="usageCounterValueNew" type="usageCounterValueNewType"/>
<xs:element minOccurs="0" name="adjustmentUsageCounterValueRelative" type="adjustmentUsageCounterValueRelativeType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValueNew" type="usageCounterMonetaryValueNewType"/>
<xs:element minOccurs="0" name="adjustmentUsageCounterMonetaryValueRelative" type="adjustmentUsageCounterMonetaryValueRelativeType"/>
</xs:choice>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="usageCounterValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentUsageCounterValueRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="adjustmentUsageCounterMonetaryValueRelativeType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-********9999"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueNewType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="********9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="supervisionExpiryDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="timeRestrictionInformationType">
<xs:sequence>
<xs:element name="timeRestrictionID" type="timeRestrictionIDType"/>
<xs:element minOccurs="0" name="timeRestrictionFlags" type="timeRestrictionFlagsType"/>
<xs:element name="timeRestrictionStartTime" type="timeRestrictionTime"/>
<xs:element name="timeRestrictionEndTime" type="timeRestrictionTime"/>
<xs:element name="offerID" type="offerIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="timeRestrictionInformationTypeForResponse">
<xs:sequence>
<xs:element minOccurs="0" name="timeRestrictionID" type="timeRestrictionIDType"/>
<xs:element minOccurs="0" name="timeRestrictionFlags" type="timeRestrictionFlagsType"/>
<xs:element minOccurs="0" name="timeRestrictionStartTime" type="timeRestrictionTime"/>
<xs:element minOccurs="0" name="timeRestrictionEndTime" type="timeRestrictionTime"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="timeRestrictionIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="timeRestrictionTime">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="86399"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="timeRestrictionFlagsType">
<xs:sequence>
<xs:element minOccurs="0" name="mondayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="tuesdayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="wednesdayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="thursdayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="fridayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="saturdayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="sundayFlag" type="xs:boolean"/>
<xs:element minOccurs="0" name="timeRestrictionSuspendedFlag" type="xs:boolean"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="serviceFeeExpiryDateRelativeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="-32767"/>
<xs:maxInclusive value="32767"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="externalDataType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="dateInformationType">
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateRelative" type="dateRelativeType"/>
<xs:element name="startPamPeriod" type="startPamPeriodType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateRelative" type="dateRelativeType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="startPamPeriodType">
<xs:sequence>
<xs:element name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
<xs:element minOccurs="0" name="currentTimeOffset" type="currentTimeOffsetType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="currentTimeOffsetType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:complexType name="dateTimeInformationType">
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateTimeRelative" type="dateTimeRelativeType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="expiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTimeRelative" type="dateTimeRelativeType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="attributeUpdateInformationType">
<xs:sequence>
<xs:element name="attributeName" type="attributeNameType"/>
<xs:element name="attributeUpdateAction" type="attributeUpdateActionType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="attributeValueString" type="attributeValueStringType"/>
<xs:element minOccurs="0" name="attributeValueDate" type="attributeValueDateType"/>
<xs:element minOccurs="0" name="attributeValueDecimal" type="attributeValueDecimalType"/>
<xs:element minOccurs="0" name="attributeValueInteger" type="attributeValueIntegerType"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="treeParameterSetUpdateInformationType">
<xs:sequence>
<xs:element name="serviceProvider" type="serviceProviderType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterUpdateInformation" type="treeParameterUpdateInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="treeParameterUpdateInformationType">
<xs:sequence>
<xs:element name="treeParameterName" type="treeParameterNameType"/>
<xs:element name="treeParameterUpdateAction" type="treeParameterUpdateActionType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="treeParameterValueString" type="treeParameterValueStringType"/>
<xs:element minOccurs="0" name="treeParameterValueDate" type="treeParameterValueDateType"/>
<xs:element minOccurs="0" name="treeParameterValueDecimal" type="treeParameterValueDecimalType"/>
<xs:element minOccurs="0" name="treeParameterValueInteger" type="treeParameterValueIntegerType"/>
<xs:element minOccurs="0" name="treeParameterValueDayOfWeek" type="treeParameterValueDayOfWeekType"/>
<xs:element minOccurs="0" name="treeParameterValueDateRange" type="treeParameterValueDateRangeType"/>
<xs:element minOccurs="0" name="treeParameterValueTime" type="treeParameterValueTimeType"/>
<xs:element minOccurs="0" name="treeParameterValueSchedule" type="treeParameterValueScheduleType"/>
</xs:choice>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="treeParameterUpdateActionType">
<xs:restriction base="xs:string">
<xs:enumeration value="ADD"/>
<xs:enumeration value="DELETE"/>
<xs:enumeration value="CLEAR"/>
<xs:enumeration value="SET"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="dedicatedAccountUpdateInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="externalProductID" type="externalProductIDType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentAmountRelative" type="adjustmentAmountRelativeType"/>
<xs:element minOccurs="0" name="dedicatedAccountValueNew" type="dedicatedAccountValueType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="adjustmentDateRelative" type="adjustmentDateRelativeType"/>
<xs:element minOccurs="0" name="expiryDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="expiryPamPeriodIndicator" type="expiryPamPeriodIndicatorType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element minOccurs="0" name="startDate" type="dateTimeType"/>
<xs:element minOccurs="0" name="adjustmentStartDateRelative" type="adjustmentStartDateRelativeType"/>
<xs:element minOccurs="0" name="startPamPeriodIndicator" type="startPamPeriodIndicatorType"/>
</xs:choice>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="expiryDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="startDateCurrent" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="updateAction" type="updateActionType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="accountFlagsAfterType">
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceOfferingsValuesType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceOfferingsValueAdd" type="serviceOfferingsValueType"/>
<xs:element minOccurs="0" name="serviceOfferingsValueDelete" type="serviceOfferingsValueType"/>
<xs:element minOccurs="0" name="serviceOfferingsValueCurrent" type="serviceOfferingsValueType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="serviceOfferingsValueType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:complexType name="communityInformationUpdateType">
<xs:sequence>
<xs:element minOccurs="0" name="communityInformationAdd" type="communityIDType"/>
<xs:element minOccurs="0" name="communityInformationDelete" type="communityIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="counterInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="counterID" type="counterIDType"/>
<xs:element minOccurs="0" name="totalCounterValue" type="CounterValueType"/>
<xs:element minOccurs="0" name="periodCounterValue" type="CounterValueType"/>
<xs:element minOccurs="0" name="counterClearingDate" type="xs:dateTime"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="promotionCounterInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue1" type="PriceType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue2" type="PriceType"/>
<xs:element minOccurs="0" name="promotionRefillCounter" type="CounterType"/>
<xs:element minOccurs="0" name="progressionRefillValue1" type="PriceType"/>
<xs:element minOccurs="0" name="progressionRefillValue2" type="PriceType"/>
<xs:element minOccurs="0" name="progressionRefillCounter" type="CounterType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="counterIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="201"/>
<xs:maxInclusive value="203"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="CounterValueType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="127"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="PriceType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="CounterType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
