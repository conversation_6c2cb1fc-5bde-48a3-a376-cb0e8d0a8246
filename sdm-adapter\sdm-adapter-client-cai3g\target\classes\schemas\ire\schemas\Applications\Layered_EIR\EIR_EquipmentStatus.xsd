<!-- <PERSON><PERSON>, EquipmentStatus - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/EIR/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<!-- GetEquipment MOId: imei, svn (optional), imsi (optional) MOType: EquipmentStatus@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetResponseEquipment MOId: imsi, svn (optional) imsi (optional) MOType: EquipmentStatus@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="imei" type="imeiType" />
	<xs:element name="svn" type="svnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="GetResponseEquipmentStatus">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imei" type="imeiType" />
				<xs:element name="svn" type="svnType" minOccurs="0" />
				<xs:element name="imsi" type="imsiType" minOccurs="0" />
				<xs:element name="status" type="statusType" />
				<xs:element name="equipmentListNumber" type="equipmentListNumberType" minOccurs="0" />
				<xs:element name="date" type="dateType" minOccurs="0" />
				<xs:element name="time" type="timeType" minOccurs="0" />
				<xs:element name="comment" type="commentType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsi" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
