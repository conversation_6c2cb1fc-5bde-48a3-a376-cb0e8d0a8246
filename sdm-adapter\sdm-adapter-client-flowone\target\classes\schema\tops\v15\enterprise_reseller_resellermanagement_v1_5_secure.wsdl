<?xml version="1.0" encoding="UTF-8"?>
<!-- 
<PERSON>ra_Tracking_ID: PR002564-17
Build_Label: JDeveloperBuild
Build_Date: 2023-05-04T15:23:20.605-0500
-->
<wsdl:definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:enterprise_reseller_resellermanagement="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0"
                  xmlns:enterprise_reseller_resellermanagement_xsd="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types"
                  xmlns:enterprise_messageheader_xsd="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types"
                  xmlns:enterprise_reseller_resellermanagement_messages="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/messages"
                  xmlns:ns="http://services.uscellular.com/schema/enterprise/common/v1_0/types"
                  name="enterprise_reseller_resellermanagement"
                  targetNamespace="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0">
	  <wsdl:documentation>
   		ResellerManagement Service will provides following functionalities for resellers.
   		- Submits order for Activating Subscriber.
   		- Submits order for Suspending Subscriber
   		- Submits order for Resuming Suspended Subscriber
   		- Submits order for Cancel/Cease Subscriber
   		- Submits order for changing CTN of Subscriber
   		- Submits order for changing ESN of the Subscriber
   		- Retrieving Offers and associated Features of Subscriber
   		- Submits order for Adding and/or removing Features for Subscriber
   		- Search for Subscriber Order status
   </wsdl:documentation>
	  <wsdl:types>
		    <xsd:schema elementFormDefault="qualified"
                  attributeFormDefault="unqualified"
                  version="v1_1"
                  targetNamespace="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0">
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/types"
                     schemaLocation="enterprise_reseller_resellermanagement_types_v1_5.xsd"/>
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/messageheader/v1_0/types"
                     schemaLocation="enterprise_messageheader_types_v1_0.xsd"/>
			      <xsd:import namespace="http://services.uscellular.com/schema/enterprise/reseller/resellermanagement/v1_0/messages"
                     schemaLocation="enterprise_reseller_resellermanagement_messages_v1_5.xsd"/>
		    </xsd:schema>
	  </wsdl:types>
	  <wsdl:message name="ResellerManagementExceptionMsg">
		    <wsdl:documentation>Reseller management exception extension based on USCC fault definition.</wsdl:documentation>
		    <wsdl:part name="ResellerManagementException"
                 element="enterprise_reseller_resellermanagement_xsd:ResellerManagementException"/>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_activate_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:activate_Request">
			      <documentation>Request message for operation activate().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_activate_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:activate_Response">
			      <documentation>Response message(activate_Response) for operation activate().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_suspend_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:suspend_Request">
			      <documentation>Request message for operation suspend()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_suspend_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:suspend_Response">
			      <documentation>Response message(suspend_Response) for operation suspend().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_resume_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:resume_Request">
			      <documentation>Request message for operation resume()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_resume_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:resume_Response">
			      <documentation>Response message(resume_Response) for operation resume().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_cancel_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:cancel_Request">
			      <documentation>Request message for operation cancel()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_cancel_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:cancel_Response">
			      <documentation>Response message(cancel_Response) for operation cancel().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeCTN_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:changeCTN_Request">
			      <documentation>Request message for operation changeCTN()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeCTN_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:changeCTN_Response">
			      <documentation>Response message(changeCTN_Response) for operation changeCTN().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeESN_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:changeESN_Request">
			      <documentation>Request message for operation changeESN()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeESN_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:changeESN_Response">
			      <documentation>Response message(changeESN_Response) for operation changeESN().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeFeatures_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:changeFeatures_Request">
			      <documentation>Request message for operation changeFeatures()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_changeFeatures_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:changeFeatures_Response">
			      <documentation>Response message(changeFeatures_Response) for operation changeFeatures().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_retrieveFeatures_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:retrieveFeatures_Request">
			      <documentation>Request message for operation retrieveFeatures()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_retrieveFeatures_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:retrieveFeatures_Response">
			      <documentation>Response message(retrieveFeatures_Response) for operation retrieveFeatures().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_getInfoByCTN_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:getInfoByCTN_Request">
			      <documentation>Request message for operation getInfoByCTN()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_getInfoByCTN_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:getInfoByCTN_Response">
			      <documentation>Response message(getInfoByCTN_Response) for operation getInfoByCTN().</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_searchOrder_RequestMsg">
		    <wsdl:part name="parameters"
                 element="enterprise_reseller_resellermanagement_messages:searchOrder_Request">
			      <documentation>Request message for operation searchOrder()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:message name="ResellerManagement_v1_0_searchOrder_ResponseMsg">
		    <wsdl:part name="result"
                 element="enterprise_reseller_resellermanagement_messages:searchOrder_Response">
			      <documentation>Response message(searchOrder_Response) for operation searchOrder()</documentation>
		    </wsdl:part>
		    <wsdl:part name="messageHeader"
                 element="enterprise_messageheader_xsd:MessageHeader">
			      <wsdl:documentation>Standard OSB audit message header.</wsdl:documentation>
		    </wsdl:part>
	  </wsdl:message>
	  <wsdl:portType name="ResellerManagement_v1_0_Port">
		    <wsdl:documentation>Defines PortType for ResellerManagement Service.</wsdl:documentation>
		    <wsdl:operation name="activate">
			      <wsdl:documentation>Submits order to Activate CTN on LTE or non-LTE devices for a Reseller.</wsdl:documentation>
			      <wsdl:input name="activateRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_activate_RequestMsg"/>
			      <wsdl:output name="activateResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_activate_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="suspend">
			      <wsdl:documentation>Submits order to Suspend a CTN for Reseller.</wsdl:documentation>
			      <wsdl:input name="suspendRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_suspend_RequestMsg"/>
			      <wsdl:output name="suspendResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_suspend_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="resume">
			      <wsdl:documentation>Submits order to resume a CTN for Reseller.</wsdl:documentation>
			      <wsdl:input name="resumeRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_resume_RequestMsg"/>
			      <wsdl:output name="resumeResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_resume_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="cancel">
			      <wsdl:documentation>Submits order to Cancel a CTN for Reseller.</wsdl:documentation>
			      <wsdl:input name="cancelRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_cancel_RequestMsg"/>
			      <wsdl:output name="cancelResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_cancel_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="changeCTN">
			      <wsdl:documentation>Submits order to Change a CTN for a Reseller subscriber.</wsdl:documentation>
			      <wsdl:input name="changeCTNRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeCTN_RequestMsg"/>
			      <wsdl:output name="changeCTNResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeCTN_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="changeESN">
			      <wsdl:documentation>Submits order to Change a ESN for a Reseller subscriber.</wsdl:documentation>
			      <wsdl:input name="changeESNRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeESN_RequestMsg"/>
			      <wsdl:output name="changeESNResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeESN_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="changeFeatures">
			      <wsdl:documentation>Submits order to Change a features on a CTN for a Reseller subscriber.</wsdl:documentation>
			      <wsdl:input name="changeFeaturesRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeFeatures_RequestMsg"/>
			      <wsdl:output name="changeFeaturesResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeFeatures_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="retrieveFeatures">
			      <wsdl:documentation>Retrieves the list of offers and features assigned to a subscriber. There can be only one offer allowed in the backend.</wsdl:documentation>
			      <wsdl:input name="retrieveFeaturesRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_retrieveFeatures_RequestMsg"/>
			      <wsdl:output name="retrieveFeaturesResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_retrieveFeatures_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="getInfoByCTN">
			      <wsdl:documentation>Retrieves the list of offers and features assigned to a subscriber. There can be only one offer allowed in the backend.</wsdl:documentation>
			      <wsdl:input name="getInfoByCTNRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_getInfoByCTN_RequestMsg"/>
			      <wsdl:output name="getInfoByCTNResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_getInfoByCTN_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
		    <wsdl:operation name="searchOrder">
			      <wsdl:documentation>Searches order status of  a subscriber.</wsdl:documentation>
			      <wsdl:input name="searchOrderRequest"
                     message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_searchOrder_RequestMsg"/>
			      <wsdl:output name="searchOrderResponse"
                      message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_searchOrder_ResponseMsg"/>
			      <wsdl:fault name="ResellerManagementExceptionMsg"
                     message="enterprise_reseller_resellermanagement:ResellerManagementExceptionMsg"/>
		    </wsdl:operation>
	  </wsdl:portType>
	  <wsdl:binding name="ResellerManagement_v1_0_Binding"
                 type="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_Port">
		    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		    <wsdl:operation name="activate">
			      <wsdl:documentation>Submits order to Activate CTN on LTE or non-LTE devices for a Reseller.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.activate"
                         style="document"/>
			      <wsdl:input name="activateRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_activate_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="activateResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_activate_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="suspend">
			      <wsdl:documentation>Submits order to Suspend a CTN for Reseller.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.suspend"
                         style="document"/>
			      <wsdl:input name="suspendRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_suspend_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="suspendResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_suspend_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="resume">
			      <wsdl:documentation>Submits order to Cancel a CTN for Reseller.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.resume"
                         style="document"/>
			      <wsdl:input name="resumeRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_resume_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="resumeResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_resume_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="cancel">
			      <wsdl:documentation>Submits order to Cancel a CTN for Reseller.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.cancel"
                         style="document"/>
			      <wsdl:input name="cancelRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_cancel_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="cancelResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_cancel_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="changeCTN">
			      <wsdl:documentation>Submits order to Change a CTN for a Reseller subscriber.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.changeCTN"
                         style="document"/>
			      <wsdl:input name="changeCTNRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeCTN_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="changeCTNResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeCTN_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="changeESN">
			      <wsdl:documentation>Submits order to Change a CTN for a Reseller subscriber.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.changeESN"
                         style="document"/>
			      <wsdl:input name="changeESNRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeESN_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="changeESNResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeESN_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="changeFeatures">
			      <wsdl:documentation>Submits order to Change a features on a CTN for a Reseller subscriber.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.changeFeatures"
                         style="document"/>
			      <wsdl:input name="changeFeaturesRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeFeatures_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="changeFeaturesResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_changeFeatures_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="retrieveFeatures">
			      <wsdl:documentation>Retrieves the list of offers and features assigned to a subscriber. There can be only one offer allowed in the backend.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.retrieveFeatures"
                         style="document"/>
			      <wsdl:input name="retrieveFeaturesRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_retrieveFeatures_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="retrieveFeaturesResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_retrieveFeatures_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="getInfoByCTN">
			      <wsdl:documentation>Retrieves the list of offers and features assigned to a subscriber. There can be only one offer allowed in the backend.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.getInfoByCTN"
                         style="document"/>
			      <wsdl:input name="getInfoByCTNRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_getInfoByCTN_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="getInfoByCTNResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_getInfoByCTN_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
		    <wsdl:operation name="searchOrder">
			      <wsdl:documentation>Searches order status of  a subscriber.</wsdl:documentation>
			      <soap:operation soapAction="http://services.uscellular.com/wsdl/enterprise/reseller/resellermanagement/v1_0#ResellerManagement_v1_0_Binding.searchOrder"
                         style="document"/>
			      <wsdl:input name="searchOrderRequest">
				        <soap:body parts="parameters" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_searchOrder_RequestMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:input>
			      <wsdl:output name="searchOrderResponse">
				        <soap:body parts="result" use="literal"/>
				        <soap:header message="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_searchOrder_ResponseMsg"
                         part="messageHeader"
                         use="literal"/>
			      </wsdl:output>
			      <wsdl:fault name="ResellerManagementExceptionMsg">
				        <soap:fault name="ResellerManagementExceptionMsg" use="literal"/>
			      </wsdl:fault>
		    </wsdl:operation>
	  </wsdl:binding>
	  <wsdl:service name="ResellerManagement_v1_0">
		    <wsdl:documentation>ResellerManagement service provides operations activate, suspend, cancel, resume, change CTN, change ESN, search Order, retrieving and changing offers and features for Subscriber. ResellerManagement_v1_1 Service is implemented as SOAP over HTTP Web Service.</wsdl:documentation>
		    <wsdl:port name="ResellerManagement_v1_0_Port"
                 binding="enterprise_reseller_resellermanagement:ResellerManagement_v1_0_Binding">
			      <soap:address location="http://localhost:9080/enterprise/reseller/resellermanagement/v1_5"/>
		       <wsp:PolicyReference xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                              xmlns:xs="http://www.w3.org/2001/XMLSchema"
                              xmlns:fn="http://www.w3.org/2005/xpath-functions"
                              URI="ResellerManagement_v1_5_Policy"/>
      </wsdl:port>
	  </wsdl:service>
   <wsp:UsingPolicy xmlns:wssutil="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                    xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                    xmlns:xs="http://www.w3.org/2001/XMLSchema"
                    xmlns:fn="http://www.w3.org/2005/xpath-functions"
                    wssutil:Required="true"/>
   <wsp:Policy xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
               xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
               xmlns:xs="http://www.w3.org/2001/XMLSchema"
               xmlns:fn="http://www.w3.org/2005/xpath-functions"
               xmlns:ns0="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
               ns0:Id="ResellerManagement_v1_5_Policy">
      <wsp:ExactlyOne>
         <wsp:All>
            <sp:SupportingTokens xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
               <sp:X509Token sp:IncludeToken="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient">
                  <wsp:Policy>
                     <sp:WssX509V3Token11/>
                  </wsp:Policy>
               </sp:X509Token>
            </sp:SupportingTokens>
         </wsp:All>
      </wsp:ExactlyOne>
   </wsp:Policy>
</wsdl:definitions>
