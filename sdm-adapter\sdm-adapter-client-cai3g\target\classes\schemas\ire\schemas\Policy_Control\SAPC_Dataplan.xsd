<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/"
	xmlns:x="http://schemas.ericsson.com/ma/SAPC/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	targetNamespace="http://schemas.ericsson.com/ma/SAPC/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="types/sapcla_types.xsd" />
	<xs:element name="pcDataplanName" type="pcDataplanNameType" />
	<xs:element name="CreateDataplan">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcDataplanName" type="pcDataplanNameType" />
				<xs:element name="pcDescription" type="pcDescriptionType"
					minOccurs="0" />
				<xs:element name="pcDefaultPriority" type="pcDefaultPriorityType"
					minOccurs="0" />
				<xs:element name="pcInstancesContracted" type="pcInstancesContractedType"
					minOccurs="0" />
				<xs:element name="pcGlobalScope" type="pcGlobalScopeType"
					minOccurs="0" />
				<xs:element name="pcNotification" type="pcNotificationType"
					minOccurs="0" />
				<xs:element name="pcStaticQualification" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcMaxBearerQosProfileId" type="pcMaxBearerQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcMinBearerQosProfileId" type="pcMinBearerQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcSubscriberChargingProfileId" type="pcSubscriberChargingProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentFiltering" type="pcContentFilteringType"
								minOccurs="0" />
							<xs:element name="pcPresenceReportingAreaNames"
								minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcPresenceReportingAreaName"
											type="pcPresenceReportingAreaNameType" minOccurs="1"
											maxOccurs="1" />
									</xs:sequence>
									<xs:attribute name="pcPresenceReportingAreaName"
										type="pcPresenceReportingAreaNameType" use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcPresenceReportingAreaNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
								<xs:key name="key_create_pcPresenceReportingAreaName">
									<xs:selector xpath="." />
									<xs:field xpath="@pcPresenceReportingAreaName" />
								</xs:key>
								<xs:keyref name="keyref_create_pcPresenceReportingAreaName"
									refer="key_create_pcPresenceReportingAreaName">
									<xs:selector xpath="./x:pcPresenceReportingAreaName" />
									<xs:field xpath="." />
								</xs:keyref>
							</xs:element>
							<xs:element name="pcPdnGwListName" type="pcPdnGwListNameType"
								minOccurs="0" />
							<xs:element name="pcSpId" type="pcSpIdType" minOccurs="0" />
							<xs:element name="pcUePolicyProfileId" type="pcUePolicyProfileIdType"
										minOccurs="0" />
							<xs:element name="pcServiceAreaRestrictionProfileId" type="pcServiceAreaRestrictionProfileIdType"
										minOccurs="0" />
							<xs:element name="pcMaxAllowedTAs" type="pcMaxAllowedTAsType"
										minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcSubscribedContents" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentName" type="pcContentNameType" />
							<xs:element name="pcRedirect" type="pcRedirectType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcContentName" type="pcContentNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcContentNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcContentName">
						<xs:selector xpath="." />
						<xs:field xpath="@pcContentName" />
					</xs:key>
					<xs:keyref name="keyref_create_pcContentName" refer="key_create_pcContentName">
						<xs:selector xpath="./x:pcContentName" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
				<xs:element name="pcDeniedContents" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcDeniedContent" type="pcDeniedContentType" />
						</xs:sequence>
						<xs:attribute name="pcDeniedContent" type="pcDeniedContentType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcDeniedContentAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcDeniedContent">
						<xs:selector xpath="." />
						<xs:field xpath="@pcDeniedContent" />
					</xs:key>
					<xs:keyref name="keyref_create_pcDeniedContent" refer="key_create_pcDeniedContent">
						<xs:selector xpath="./x:pcDeniedContent" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
				<xs:element name="pcUsageLimits" type="pcUsageLimitsType"
					minOccurs="0" />
				<xs:element name="pcLocators" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcResourceName" type="pcResourceNameType" />
							<xs:element name="pcContext" minOccurs="1" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcContextName" type="pcContextNameType" />
										<xs:element name="pcPolicies" minOccurs="1"
											maxOccurs="unbounded">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="pcPolicy" type="pcPolicyType" />
												</xs:sequence>
												<xs:attribute name="pcPolicy" type="pcPolicyType"
													use="required">
													<xs:annotation>
														<xs:appinfo>
															<jaxb:property name="pcPolicyAttr" />
														</xs:appinfo>
													</xs:annotation>
												</xs:attribute>
											</xs:complexType>
											<xs:key name="key_create_pcPolicy">
												<xs:selector xpath="." />
												<xs:field xpath="@pcPolicy" />
											</xs:key>
											<xs:keyref name="keyref_create_pcPolicy" refer="key_create_pcPolicy">
												<xs:selector xpath="./x:pcPolicy" />
												<xs:field xpath="." />
											</xs:keyref>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="pcContextName" type="pcContextNameType"
										use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcContextNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
								<xs:key name="key_create_pcContextName">
									<xs:selector xpath="." />
									<xs:field xpath="@pcContextName" />
								</xs:key>
								<xs:keyref name="keyref_create_pcContextName" refer="key_create_pcContextName">
									<xs:selector xpath="./x:pcContextName" />
									<xs:field xpath="." />
								</xs:keyref>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="pcResourceName" type="pcResourceNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcResourceNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcResourceName">
						<xs:selector xpath="." />
						<xs:field xpath="@pcResourceName" />
					</xs:key>
					<xs:keyref name="keyref_create_pcResourceName" refer="key_create_pcResourceName">
						<xs:selector xpath="./x:pcResourceName" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
				<xs:element name="pcEventTriggers" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcEventTrigger" type="pcEventTriggerType" />
						</xs:sequence>
						<xs:attribute name="pcEventTrigger" type="pcEventTriggerType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcEventTriggerAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcEventTrigger">
						<xs:selector xpath="." />
						<xs:field xpath="@pcEventTrigger" />
					</xs:key>
					<xs:keyref name="keyref_create_pcEventTrigger" refer="key_create_pcEventTrigger">
						<xs:selector xpath="./x:pcEventTrigger" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcDataplanName" type="pcDataplanNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcDataplanNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_pcDataplanName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcDataplanName" />
		</xs:key>
		<xs:keyref name="keyref_create_pcDataplanName" refer="key_create_pcDataplanName">
			<xs:selector xpath="./x:pcDataplanName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

	<xs:element name="GetResponseDataplan">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcDataplanName" type="pcDataplanNameType" />
				<xs:element name="pcDescription" type="pcDescriptionType"
					minOccurs="0" />
				<xs:element name="pcDefaultPriority" type="pcDefaultPriorityType"
					minOccurs="0" />
				<xs:element name="pcInstancesContracted" type="pcInstancesContractedType"
							minOccurs="0" />
				<xs:element name="pcGlobalScope" type="pcGlobalScopeType"
							minOccurs="0" />
				<xs:element name="pcNotification" type="pcNotificationType"
					minOccurs="0" />
				<xs:element name="pcStaticQualification" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcMaxBearerQosProfileId" type="pcMaxBearerQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcMinBearerQosProfileId" type="pcMinBearerQosProfileIdType"
								minOccurs="0" />
							<xs:element name="pcSubscriberChargingProfileId" type="pcSubscriberChargingProfileIdType"
								minOccurs="0" />
							<xs:element name="pcContentFiltering" type="pcContentFilteringType"
								minOccurs="0" />
							<xs:element name="pcPresenceReportingAreaNames"
								minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcPresenceReportingAreaName"
											type="pcPresenceReportingAreaNameType" minOccurs="1"
											maxOccurs="1" />
									</xs:sequence>
									<xs:attribute name="pcPresenceReportingAreaName"
										type="pcPresenceReportingAreaNameType" use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcPresenceReportingAreaNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
							<xs:element name="pcPdnGwListName" type="pcPdnGwListNameType"
								minOccurs="0" />
							<xs:element name="pcSpId" type="pcSpIdType" minOccurs="0" />
							<xs:element name="pcUePolicyProfileId" type="pcUePolicyProfileIdType"
										minOccurs="0" />
							<xs:element name="pcServiceAreaRestrictionProfileId" type="pcServiceAreaRestrictionProfileIdType"
										minOccurs="0" />
							<xs:element name="pcMaxAllowedTAs" type="pcMaxAllowedTAsType"
										minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcSubscribedContents" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentName" type="pcContentNameType" />
							<xs:element name="pcRedirect" type="pcRedirectType"
								minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcContentName" type="pcContentNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcContentNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcDeniedContents" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcDeniedContent" type="pcDeniedContentType" />
						</xs:sequence>
						<xs:attribute name="pcDeniedContent" type="pcDeniedContentType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcDeniedContentAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcUsageLimits" type="pcUsageLimitsType"
					minOccurs="0" />
				<xs:element name="pcLocators" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcResourceName" type="pcResourceNameType" />
							<xs:element name="pcContext" minOccurs="1" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcContextName" type="pcContextNameType" />
										<xs:element name="pcPolicies" minOccurs="1"
											maxOccurs="unbounded">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="pcPolicy" type="pcPolicyType" />
												</xs:sequence>
												<xs:attribute name="pcPolicy" type="pcPolicyType"
													use="required">
													<xs:annotation>
														<xs:appinfo>
															<jaxb:property name="pcPolicyAttr" />
														</xs:appinfo>
													</xs:annotation>
												</xs:attribute>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="pcContextName" type="pcContextNameType"
										use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcContextNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="pcResourceName" type="pcResourceNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcResourceNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcEventTriggers" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcEventTrigger" type="pcEventTriggerType" />
						</xs:sequence>
						<xs:attribute name="pcEventTrigger" type="pcEventTriggerType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcEventTriggerAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcDataplanName" type="pcDataplanNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcDataplanNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<xs:element name="SetDataplan">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcDataplanName" type="pcDataplanNameType" />
				<xs:element name="pcDescription" type="pcDescriptionType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcDefaultPriority" type="pcDefaultPriorityType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcInstancesContracted" type="pcInstancesContractedType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcGlobalScope" type="pcGlobalScopeType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcNotification" type="pcNotificationType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcStaticQualification" nillable="true"
					minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcMaxBearerQosProfileId" type="pcMaxBearerQosProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcMinBearerQosProfileId" type="pcMinBearerQosProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcSubscriberChargingProfileId" type="pcSubscriberChargingProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcContentFiltering" type="pcContentFilteringType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcPresenceReportingAreaNames"
								nillable="true" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcPresenceReportingAreaName"
											type="pcPresenceReportingAreaNameType" minOccurs="1"
											maxOccurs="1" />
									</xs:sequence>
									<xs:attribute name="pcPresenceReportingAreaName"
										type="pcPresenceReportingAreaNameType" use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcPresenceReportingAreaNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
								<xs:key name="key_set_pcPresenceReportingAreaName">
									<xs:selector xpath="." />
									<xs:field xpath="@pcPresenceReportingAreaName" />
								</xs:key>
								<xs:keyref name="keyref_set_pcPresenceReportingAreaName"
									refer="key_set_pcPresenceReportingAreaName">
									<xs:selector xpath="./x:pcPresenceReportingAreaName" />
									<xs:field xpath="." />
								</xs:keyref>
							</xs:element>
							<xs:element name="pcPdnGwListName" type="pcPdnGwListNameType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcSpId" type="pcSpIdType" nillable="true" minOccurs="0" />
							<xs:element name="pcUePolicyProfileId" type="pcUePolicyProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcServiceAreaRestrictionProfileId" type="pcServiceAreaRestrictionProfileIdType"
								nillable="true" minOccurs="0" />
							<xs:element name="pcMaxAllowedTAs" type="pcMaxAllowedTAsType"
								nillable="true" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcSubscribedContents" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcContentName" type="pcContentNameType" />
							<xs:element name="pcRedirect" type="pcRedirectType"
								nillable="true" minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcContentName" type="pcContentNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcContentNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcContentName">
						<xs:selector xpath="." />
						<xs:field xpath="@pcContentName" />
					</xs:key>
					<xs:keyref name="keyref_set_pcContentName" refer="key_set_pcContentName">
						<xs:selector xpath="./x:pcContentName" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
				<xs:element name="pcDeniedContents" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcDeniedContent" type="pcDeniedContentType" />
						</xs:sequence>
						<xs:attribute name="pcDeniedContent" type="pcDeniedContentType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcDeniedContentAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcDeniedContent">
						<xs:selector xpath="." />
						<xs:field xpath="@pcDeniedContent" />
					</xs:key>
					<xs:keyref name="keyref_set_pcDeniedContent" refer="key_set_pcDeniedContent">
						<xs:selector xpath="./x:pcDeniedContent" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
				<xs:element name="pcUsageLimits" type="pcUsageLimitsType"
					nillable="true" minOccurs="0" />
				<xs:element name="pcLocators" nillable="true" minOccurs="0"
					maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcResourceName" type="pcResourceNameType" />
							<xs:element name="pcContext" nillable="true" minOccurs="1" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="pcContextName" type="pcContextNameType" />
										<xs:element name="pcPolicies" minOccurs="1"
											maxOccurs="unbounded" nillable="true">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="pcPolicy" type="pcPolicyType" />
												</xs:sequence>
												<xs:attribute name="pcPolicy" type="pcPolicyType"
													use="required">
													<xs:annotation>
														<xs:appinfo>
															<jaxb:property name="pcPolicyAttr" />
														</xs:appinfo>
													</xs:annotation>
												</xs:attribute>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="pcContextName" type="pcContextNameType"
										use="required">
										<xs:annotation>
											<xs:appinfo>
												<jaxb:property name="pcContextNameAttr" />
											</xs:appinfo>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="pcResourceName" type="pcResourceNameType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcResourceNameAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="pcEventTriggers" nillable="true"
					minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcEventTrigger" type="pcEventTriggerType" />
						</xs:sequence>
						<xs:attribute name="pcEventTrigger" type="pcEventTriggerType"
							use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcEventTriggerAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcEventTrigger">
						<xs:selector xpath="." />
						<xs:field xpath="@pcEventTrigger" />
					</xs:key>
					<xs:keyref name="keyref_set_pcEventTrigger" refer="key_set_pcEventTrigger">
						<xs:selector xpath="./x:pcEventTrigger" />
						<xs:field xpath="." />
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcDataplanName" type="pcDataplanNameType"
				use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcDataplanNameAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_pcDataplanName">
			<xs:selector xpath="." />
			<xs:field xpath="@pcDataplanName" />
		</xs:key>
		<xs:keyref name="keyref_set_pcDataplanName" refer="key_set_pcDataplanName">
			<xs:selector xpath="./x:pcDataplanName" />
			<xs:field xpath="." />
		</xs:keyref>
	</xs:element>

</xs:schema>
