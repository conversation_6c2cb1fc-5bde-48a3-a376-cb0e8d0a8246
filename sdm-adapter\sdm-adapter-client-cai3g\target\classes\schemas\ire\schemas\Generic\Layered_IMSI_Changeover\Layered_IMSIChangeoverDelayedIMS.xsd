<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:x="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
	jaxb:extensionBindingPrefixes="xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import schemaLocation="../../2G_3G/Deprecated-Layered_HLR/types/hlrla_types.xsd" namespace="http://schemas.ericsson.com/pg/hlr/13.5/" />

	<xs:element name="imsi" type="hlr:imsiType" />
	<!-- CreateSuspendedIMSIChangeover MOId: imsi MOType: SuspendedIMSIChangeover@http://schemas.ericsson.com/pg/cudb/1.0/ -->
	<xs:element name="CreateSuspendedIMSIChangeover">
		<xs:complexType>
			<xs:sequence>
					<xs:element name="imsi" type="hlr:imsiType" />
			</xs:sequence>
			<xs:attribute name="imsi" type="hlr:imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>

		<xs:key name="key_imsi">
			<xs:selector xpath="./x:imsi" />
			<xs:field xpath="." />
		</xs:key>

		<xs:keyref name="keyref_imsi" refer="key_imsi">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>

	</xs:element>
</xs:schema>
