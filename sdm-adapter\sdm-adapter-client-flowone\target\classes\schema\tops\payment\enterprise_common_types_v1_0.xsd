<!-- 
Build_Label: REL500
ClearQuest_MR#: 00348796 
Build_Date: 2016-12-07-14:56:28
-->
<xsd:schema xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v1_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://services.uscellular.com/schema/enterprise/common/v1_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_0">
   <xsd:annotation>
      <xsd:documentation>Common data types across all the enterprise services.
                         This schema definition should contain the common types
                         and elements organized in an alphabetical oder.
                         Version: 1.0</xsd:documentation>
   </xsd:annotation>
   <!-- Types            -->
   <xsd:simpleType name="AccountNumberType">
      <xsd:annotation>
         <xsd:documentation>Generic 9 digit account number base type.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\d{9}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="BanType">
      <xsd:annotation>
         <xsd:documentation>DEPRICATED. The CARES account number.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
   </xsd:simpleType>
   <xsd:simpleType name="BarType">
      <xsd:annotation>
         <xsd:documentation>Billing arangement</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
   </xsd:simpleType>
   <xsd:simpleType name="CityType">
		<xsd:annotation>
			<xsd:documentation>City</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="30"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="CompIdType">
		<xsd:annotation>
			<xsd:documentation>Component Id</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="CompensationIdType">
		<xsd:annotation>
			<xsd:documentation>Compensation Id</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="CtnType">
      <xsd:annotation>
         <xsd:documentation>Customer telephone number</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\d{10}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="CustomerIdType">
      <xsd:annotation>
         <xsd:documentation>Customer Id</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
   </xsd:simpleType>
   <xsd:simpleType name="EmailAddressType">
      <xsd:annotation>
         <xsd:documentation>Email address of the customer.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,6}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="ErrorCodeType">
      <xsd:annotation>
         <xsd:documentation>General error code.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="10"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="ErrorDescriptionType">
      <xsd:annotation>
         <xsd:documentation>Description of an error code. There may be error specific info in the string, but the initial text should describe the error and indicate the severity.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="500"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="EsnType">
      <xsd:annotation>
         <xsd:documentation>ESN or MEID of the device.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\d{11}|\d{18}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="FaIdType">
      <xsd:annotation>
         <xsd:documentation>Financial Account Id</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
   </xsd:simpleType>
   <xsd:simpleType name="FirstNameType">
      <xsd:annotation>
         <xsd:documentation>First or given name</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="50"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="GUIDType">
      <xsd:annotation>
         <xsd:documentation>GUID in custom LDAP format: 8-4-4-4-20-4-4-4-4 .</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{20}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}"/>
      </xsd:restriction>
   </xsd:simpleType>

   <xsd:simpleType name="IETFGUIDType">
      <xsd:annotation>
         <xsd:documentation>GUID in ITEF GUID format: {8-4-4-4-12} ( braces optional ).  See http://www.ietf.org/rfc/rfc4122.txt for details.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}?"/>
      </xsd:restriction>
   </xsd:simpleType>
	<xsd:simpleType name="IndicatorStringType">
		<xsd:annotation>
			<xsd:documentation>Indicator Type. Possible values are Y, N or NA (can be used for not available/not applicable scenarios).</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
   <xsd:simpleType name="LastNameType">
		<xsd:annotation>
			<xsd:documentation>Last Name</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="50"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="LocationCodeType">
		<xsd:annotation>
			<xsd:documentation>Location Code</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="LocationIdType">
		<xsd:annotation>
			<xsd:documentation>Location Id</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="MdnType">
      <xsd:annotation>
         <xsd:documentation>Member Directory Number.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\d{10}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="MinType">
      <xsd:annotation>
         <xsd:documentation>Mobile Identification Number.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:pattern value="\d{10}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="OperatorIdType">
		<xsd:annotation>
			<xsd:documentation>Operator Id</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="PhoneNumberType">
      <xsd:annotation>
         <xsd:documentation>General phone number.  The number could be international and may have &apos;+ - ( )&apos;, e.g.  +1(800)555-1212.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="20"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="PostalCodeType">
      <xsd:annotation>
         <xsd:documentation>Postal code for the shipping address.
                        The postal code must consist of 5 to 9 digits.
                        If the billing country is the U.S., the 9-digit postal code must follow this format:
                          [5 digits]  or  [5 digits][dash][4 digits] ( optional dash )
                            Example: 12345-6789 or 123456789 
                        If the billing country is Canada, the 6-digit postal code must follow this format:
                          [alpha][numeric][alpha][space][numeric][alpha][numeric]
                            Example: A1B 2C3</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:minLength value="1"/>
         <xsd:maxLength value="10"/>
			<xsd:pattern value="((\d{5}-?\d{4})|(\d{5})|([A-CEGHJ-NPR-TVXZ]\d[A-CEGHJ-NPR-TV-Z]\s\d[A-CEGHJ-NPR-TV-Z]\d))"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="RequestTransactionIdType">
      <xsd:annotation>
         <xsd:documentation>Request ( incomming ) transaction id.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:TransactionIdType"/>
   </xsd:simpleType>
   <xsd:simpleType name="ResponseTransactionIdType">
      <xsd:annotation>
         <xsd:documentation>Response ( outgoing ) transaction id.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:TransactionIdType"/>
   </xsd:simpleType>
   <xsd:simpleType name="SocType">
		<xsd:annotation>
			<xsd:documentation>SOC Type</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="StreetAddressLine1Type">
		<xsd:annotation>
			<xsd:documentation/>
		</xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="60"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="StreetAddressLine2Type">
		<xsd:annotation>
			<xsd:documentation>Street Address Line2</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="60"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="StreetAddressLine3Type">
		<xsd:annotation>
			<xsd:documentation>Street Address Line3</xsd:documentation>
		</xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="60"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="SubscriberIdType">
      <xsd:annotation>
         <xsd:documentation>Subscriber Id</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="enterprise_common_xsd:AccountNumberType"/>
   </xsd:simpleType>
   <xsd:simpleType name="TransactionIdType">
      <xsd:restriction base="xsd:string"/>
   </xsd:simpleType>
   <xsd:simpleType name="UICCIDType">
      <xsd:annotation>
         <xsd:documentation>Each SIM is internationally identified by its integrated circuit card identifier (ICCID). ICCIDs are stored in the SIM cards and are also engraved or printed on the SIM card body during a process called personalization. The ICCID is defined by the ITU-T recommendation E.118 as the Primary Account Number. Its layout is based on ISO/IEC 7812. According to E.118, the number is up to 19 digits long, including a single check digit calculated using the Luhn algorithm. However, the GSM Phase 1 defined the ICCID length as 10 octets with operator-specific structure. On GSM SIM cards there are 20-digit (19+1) and 19-digit (18+1) ICCIDs in use, depending upon the issuer. However, a single issuer always uses the same size for its ICCIDs.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="20"/>
         <xsd:pattern value="\d{19,20}"/>
      </xsd:restriction>
   </xsd:simpleType>
   <xsd:simpleType name="ZipCodeType">
      <xsd:annotation>
         <xsd:documentation>Specific 5 or 9 digit US postal zip code, no dash allowed.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:maxLength value="9"/>
         <xsd:pattern value="[0-9]{5}([0-9]{4})?"/>
      </xsd:restriction>
   </xsd:simpleType>
   <!-- Structures       -->
   <xsd:complexType name="ErrorInformationType">
      <xsd:annotation>
         <xsd:documentation>Structure to hode genera error information.</xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
         <xsd:element name="errorCode" type="enterprise_common_xsd:ErrorCodeType" minOccurs="0">
            <xsd:annotation>
               <xsd:documentation>The error code assigned to the error.</xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element name="errorDescription" type="enterprise_common_xsd:ErrorDescriptionType" minOccurs="0">
            <xsd:annotation>
               <xsd:documentation>The error description indicating the type and severity, and any corrective action if known.</xsd:documentation>
            </xsd:annotation>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
   <!-- Enumerations     -->
   <xsd:simpleType name="StateType">
      <xsd:annotation>
         <xsd:documentation>State information of the address.</xsd:documentation>
      </xsd:annotation>
      <xsd:restriction base="xsd:string">
         <xsd:minLength value="2"/>
         <xsd:maxLength value="2"/>
         <xsd:enumeration value="AL">
            <xsd:annotation>
               <xsd:documentation>Alabama</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MT">
            <xsd:annotation>
               <xsd:documentation>Montana</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AK">
            <xsd:annotation>
               <xsd:documentation>Alaska</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NE">
            <xsd:annotation>
               <xsd:documentation>Nebraska</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AZ">
            <xsd:annotation>
               <xsd:documentation>Arizona </xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NV">
            <xsd:annotation>
               <xsd:documentation>Nevada</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AR">
            <xsd:annotation>
               <xsd:documentation>Arkansas</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NH">
            <xsd:annotation>
               <xsd:documentation>New Hampshire</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="CA">
            <xsd:annotation>
               <xsd:documentation>California </xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NJ">
            <xsd:annotation>
               <xsd:documentation>New Jersey</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="CO">
            <xsd:annotation>
               <xsd:documentation>Colorado </xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NM">
            <xsd:annotation>
               <xsd:documentation>New Mexico</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="CT">
            <xsd:annotation>
               <xsd:documentation>Connecticut</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NY">
            <xsd:annotation>
               <xsd:documentation>New York</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="DE">
            <xsd:annotation>
               <xsd:documentation>Delaware</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NC">
            <xsd:annotation>
               <xsd:documentation>North Carolina</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="FL">
            <xsd:annotation>
               <xsd:documentation>Florida</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="ND">
            <xsd:annotation>
               <xsd:documentation>North Dakota</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="GA">
            <xsd:annotation>
               <xsd:documentation>Georgia</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="OH">
            <xsd:annotation>
               <xsd:documentation>Ohio</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="HI">
            <xsd:annotation>
               <xsd:documentation>Hawaii</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="OK">
            <xsd:annotation>
               <xsd:documentation>Oklahoma</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="ID">
            <xsd:annotation>
               <xsd:documentation>Idaho</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="OR">
            <xsd:annotation>
               <xsd:documentation>Oregon</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="IL">
            <xsd:annotation>
               <xsd:documentation>Illinois</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="PA">
            <xsd:annotation>
               <xsd:documentation>Pennsylvania</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="IN">
            <xsd:annotation>
               <xsd:documentation>Indiana</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="RI">
            <xsd:annotation>
               <xsd:documentation>Rhode Island</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="IA">
            <xsd:annotation>
               <xsd:documentation>Iowa</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="SC">
            <xsd:annotation>
               <xsd:documentation>South Carolina</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="KS">
            <xsd:annotation>
               <xsd:documentation>Kansas</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="SD">
            <xsd:annotation>
               <xsd:documentation>South Dakota</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="KY">
            <xsd:annotation>
               <xsd:documentation>Kentucky</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="TN">
            <xsd:annotation>
               <xsd:documentation>Tennessee</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="LA">
            <xsd:annotation>
               <xsd:documentation>Louisiana</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="TX">
            <xsd:annotation>
               <xsd:documentation>Texas</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="ME">
            <xsd:annotation>
               <xsd:documentation>Maine</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="UT">
            <xsd:annotation>
               <xsd:documentation>Utah</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MD">
            <xsd:annotation>
               <xsd:documentation>Maryland</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="VT">
            <xsd:annotation>
               <xsd:documentation>Vermont</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MA">
            <xsd:annotation>
               <xsd:documentation>Massachusetts</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="VA">
            <xsd:annotation>
               <xsd:documentation>Virginia </xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MI">
            <xsd:annotation>
               <xsd:documentation>Michigan</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="WA">
            <xsd:annotation>
               <xsd:documentation>Washington</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MN">
            <xsd:annotation>
               <xsd:documentation>Minnesota</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="WV">
            <xsd:annotation>
               <xsd:documentation>West Virginia</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MS">
            <xsd:annotation>
               <xsd:documentation>Mississippi</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="WI">
            <xsd:annotation>
               <xsd:documentation>Wisconsin</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MO">
            <xsd:annotation>
               <xsd:documentation>Missouri</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="WY">
            <xsd:annotation>
               <xsd:documentation>Wyoming</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="DC">
            <xsd:annotation>
               <xsd:documentation>District of Columbia</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AA">
            <xsd:annotation>
               <xsd:documentation>Armed Forces Americas (except Canada)</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AE">
            <xsd:annotation>
               <xsd:documentation>Armed Forces Canada/Europe/Middle East/Africa</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AP">
            <xsd:annotation>
               <xsd:documentation>Armed Forces Pacific</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AS">
            <xsd:annotation>
               <xsd:documentation>American Somoa</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="FM">
            <xsd:annotation>
               <xsd:documentation>Federated States of Micronesia</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="GU">
            <xsd:annotation>
               <xsd:documentation>Guam</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MH">
            <xsd:annotation>
               <xsd:documentation>Marshall Islands</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MP">
            <xsd:annotation>
               <xsd:documentation>Northern Mariana Islands</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="PR">
            <xsd:annotation>
               <xsd:documentation>Puerto Rico</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="PW">
            <xsd:annotation>
               <xsd:documentation>Palau (Balau)</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="VI">
            <xsd:annotation>
               <xsd:documentation>U.S. Virgin Islands</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="AB">
            <xsd:annotation>
               <xsd:documentation>Alberta</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="BC">
            <xsd:annotation>
               <xsd:documentation>British Columbia </xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="MB">
            <xsd:annotation>
               <xsd:documentation>Manitoba</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NB">
            <xsd:annotation>
               <xsd:documentation>New Brunswick</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NL">
            <xsd:annotation>
               <xsd:documentation>Newfoundland and Labrador</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NT">
            <xsd:annotation>
               <xsd:documentation>Northwest Territories</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NS">
            <xsd:annotation>
               <xsd:documentation>Nova Scotia</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="NU">
            <xsd:annotation>
               <xsd:documentation>Nunavut</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="ON">
            <xsd:annotation>
               <xsd:documentation>Ontario</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="PE">
            <xsd:annotation>
               <xsd:documentation>Prince Edward Island</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="QC">
            <xsd:annotation>
               <xsd:documentation>Quebec</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="SK">
            <xsd:annotation>
               <xsd:documentation>Saskatchewan</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
         <xsd:enumeration value="YT">
            <xsd:annotation>
               <xsd:documentation>Yukon</xsd:documentation>
            </xsd:annotation>
         </xsd:enumeration>
      </xsd:restriction>
   </xsd:simpleType>

	<xsd:complexType name="KeyValuePairType">
		<xsd:annotation>
			<xsd:documentation>Defines KeyValuePair structure.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="key" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Contains attribute name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="value" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>Contains attribute value of the respective attribute name. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
   <!-- Exceptions       -->
	<!-- <xsd:element name="ServiceException" type="enterprise_common_xsd:ServiceExceptionType">
		<xsd:annotation>
			<xsd:documentation>Base exception definition used in ESB enterprise services.</xsd:documentation>
		</xsd:annotation>
	</xsd:element> -->
   <xsd:complexType name="ServiceExceptionType">
      <xsd:annotation>
         <xsd:documentation>Base exception definition used in ESB enterprise services.</xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
         <xsd:element name="errorInformation" type="enterprise_common_xsd:ErrorInformationType">
            <xsd:annotation>
               <xsd:documentation>Error code and description.</xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element name="faultDetails" type="xsd:anyType" minOccurs="0">
            <xsd:annotation>
               <xsd:documentation>Fault details represent soap fault details received by OSB.</xsd:documentation>
            </xsd:annotation>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
</xsd:schema>