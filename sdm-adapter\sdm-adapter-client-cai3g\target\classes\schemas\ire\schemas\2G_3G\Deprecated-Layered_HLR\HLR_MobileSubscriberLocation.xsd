<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="SetMobileSubscriberLocation">
<xs:annotation>
<xs:documentation>
				This command sets the location of the mobile subscriber to unknown.
				When the location is reset, the cancellation procedure towards
				the Visitor Location Register (VLR) and the
				Serving General Packet Radio Service (GPRS) Support Node (SGSN)
				where the mobile subscriber is currently located is initiated.
			</xs:documentation>
</xs:annotation>
<xs:complexType>
<xs:sequence>
<xs:choice minOccurs="0">
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
