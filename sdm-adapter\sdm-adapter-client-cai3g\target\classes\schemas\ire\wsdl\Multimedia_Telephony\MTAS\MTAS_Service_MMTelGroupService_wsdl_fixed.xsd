<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelGroupService/" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelGroupService/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:element name="publicId" type="publicIdentityType"/>
	<!-- MO Attributes for top level messages -->
	<xs:element name="createService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to create MMTel group service data</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType">
					<xs:annotation>
						<xs:documentation xml:lang="en">The default public user identity for the group. This identity must already be configured on the HSS.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<!-- the relative order of the existing services must be maintained -->
				<!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
				<xs:element name="group-call-admission-control" type="group-call-admission-control-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">
									The group call admission control service. Use xsi:nil="true" to withdraw the entire service.
								</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyCreate">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
		<xs:keyref name="publicIdKeyRef" refer="publicIdKeyCreate">
			<xs:selector xpath="."/>
			<xs:field xpath="publicId"/>
		</xs:keyref>
	</xs:element>
	<xs:element name="setService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to modify MMTel group service data</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">
								The concurrency-control element is an optional element to control concurrent updates. If present then the set
								request will be accepted only if the service data version is still at the value given in this element i.e. no other
								updates have been performed. It is of type integer.
								</xs:documentation>
					</xs:annotation>
				</xs:element>
				<!-- the relative order of the existing services must be maintained -->
				<!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
				<xs:element name="group-call-admission-control" type="group-call-admission-control-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">
									The group call admission control service. Use xsi:nil="true" to withdraw the entire service.
								</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeySet">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="getResponseService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains the currently configured MMTel group service data</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType">
					<xs:annotation>
						<xs:documentation xml:lang="en">The default public user identity for the group</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="concurrency-control" type="xs:integer">
					<xs:annotation>
						<xs:documentation xml:lang="en">
								The concurrency-control element is an integer value indicating the current version of the MMTel group service data.
								This value can be used in a subsequent setMMTelGroup request to make sure that no changes have been made to
								the service data since the version that was read
								</xs:documentation>
					</xs:annotation>
				</xs:element>
				<!-- the relative order of the existing services must be maintained -->
				<!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
				<xs:element name="group-call-admission-control" type="group-call-admission-control-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">
									The group call admission control service. Use xsi:nil="true" to withdraw the entire service.
								</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyGetResp">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<!--? group-call-admission-control ?-->
	<xs:complexType name="group-call-admission-control-type">
		<xs:sequence>
			<xs:element name="gcac-operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">
						The configuration parameters for the group call admission control service that are available to the operator rather than the user.
						This must be present on the creation of the group-call-admission-control service.
						</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="xs:boolean" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									The activated element has values "true" or "false". When set to "true" the user is provisioned with the group call admission
									control service. If set to "false" this will withdraw the service from the user. This must be present on the creation of the
									group-call-admission-control service.
									</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:choice>
							<xs:element name="group-cac-profile" type="group-profile-type" minOccurs="0">
								<xs:annotation>
									<xs:documentation xml:lang="en">
										Defines the profile used by group call admission control to determine the session limits applicable to this call admission
										control group. This element must not be present if any of the following elements are present:
										orig-active-limit; term-active-limit; total-active-limit; orig-all-limit; term-all-limit; total-all-limit.
 										If this element is not present on creation of the group-call-admission-control service then all of the following elements 
 										must be present: orig-active-limit; term-active-limit; total-active-limit; orig-all-limit; term-all-limit; total-all-limit.
										</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:sequence>
								<xs:element name="orig-active-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of originating, active sessions for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="term-active-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of terminating, active sessions for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="total-active-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of active sessions (i.e. the sum of originating and terminating active sessions) for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="orig-all-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of all originating sessions (i.e. the sum of active and inactive originating sessions) for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="term-all-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of all terminating sessions (i.e. the sum of active and inactive terminating sessions) for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="total-all-limit" type="group-cac-limit-type" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">
									Defines the limit of all sessions (i.e. the sum of all originating and terminating sessions) for this group.
									This must be present on the creation of the group-call-admission-control service.
									</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:choice>
						<xs:element name="member" type="member-type" nillable="true" minOccurs="0" maxOccurs="50">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									The identity of a member of the Call Admission Control Group.  By preference, it should be the default Public User Identity of
									the member, though any alias identity of the member (i.e. any PUI that appears in the members Implicit Registration Set) is
									acceptable. Should be a SIP URI (RFC 3261) or a tel URI (RFC 3966).
									The member element is a sub-MO allowing multiple instances with "id" as the unique key.
									</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="cac-group-member-key">
					<xs:selector xpath="./member"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="member-type">
		<xs:sequence>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">
					A unique identifier for a group member. This must be present on the creation of a member element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:anyURI" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- Common Types -->
	<xs:simpleType name="publicIdentityType">
		<xs:restriction base="xs:anyURI">
			<xs:pattern value="sip:.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="group-cac-limit-type">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="group-profile-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="([a-z]|[A-Z]|[0-9]){1,32}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
