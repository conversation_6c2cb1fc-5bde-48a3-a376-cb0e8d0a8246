<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bindings xmlns="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
      
    <!--

This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2023.01.05 at 12:49:51 PM IST 

  -->
      
    <bindings xmlns:tns="http://soa.comptel.com/2011/02/instantlink" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="com.nokia.wing.wdh.sdmadapter.service.soap.flowone"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="~tns:InstantLinkRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.InstantLinkRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OrderNote">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.OrderNote"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Response">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.Response"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MaxReqTime">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.MaxReqTime"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:InstantLinkAvailabilityInfo">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.InstantLinkAvailabilityInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Notification">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.Notification"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:DeleteRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.DeleteRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:RequestHeader">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.RequestHeader"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:DisplayRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.DisplayRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:CreateRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.CreateRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:ModifyRequest">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.ModifyRequest"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Parameter">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.Parameter"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NoteParameter">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.NoteParameter"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResponseHeader">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.ResponseHeader"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AvailInfoStatus">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.service.soap.flowone.AvailInfoStatus"/>
                
        </bindings>
          
    </bindings>
    
</bindings>
