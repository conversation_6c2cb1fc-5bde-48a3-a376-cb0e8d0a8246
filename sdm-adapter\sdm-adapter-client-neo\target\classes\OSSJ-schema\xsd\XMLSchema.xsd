<?xml version='1.0'?>

<!DOCTYPE xs:schema PUBLIC "-//W3C//DTD XSD 1.1//EN" "XMLSchema.dtd" [

<!-- provide ID type information even for parsers which only read the
     internal subset -->
<!ATTLIST xs:schema          id  ID  #IMPLIED>
<!ATTLIST xs:complexType     id  ID  #IMPLIED>
<!ATTLIST xs:complexContent  id  ID  #IMPLIED>
<!ATTLIST xs:simpleContent   id  ID  #IMPLIED>
<!ATTLIST xs:extension       id  ID  #IMPLIED>
<!ATTLIST xs:element         id  ID  #IMPLIED>
<!ATTLIST xs:group           id  ID  #IMPLIED> 
<!ATTLIST xs:all             id  ID  #IMPLIED>
<!ATTLIST xs:choice          id  ID  #IMPLIED>
<!ATTLIST xs:sequence        id  ID  #IMPLIED>
<!ATTLIST xs:any             id  ID  #IMPLIED>
<!ATTLIST xs:anyAttribute    id  ID  #IMPLIED>
<!ATTLIST xs:attribute       id  ID  #IMPLIED>
<!ATTLIST xs:attributeGroup  id  ID  #IMPLIED>
<!ATTLIST xs:unique          id  ID  #IMPLIED>
<!ATTLIST xs:key             id  ID  #IMPLIED>
<!ATTLIST xs:keyref          id  ID  #IMPLIED>
<!ATTLIST xs:selector        id  ID  #IMPLIED>
<!ATTLIST xs:field           id  ID  #IMPLIED>
<!ATTLIST xs:assert          id  ID  #IMPLIED>
<!ATTLIST xs:include         id  ID  #IMPLIED>
<!ATTLIST xs:import          id  ID  #IMPLIED>
<!ATTLIST xs:redefine        id  ID  #IMPLIED>
<!ATTLIST xs:override        id  ID  #IMPLIED>
<!ATTLIST xs:notation        id  ID  #IMPLIED>
<!--
        Make sure that processors that do not read the external
        subset will know about the various IDs we declare
  -->
        <!ATTLIST xs:simpleType id ID #IMPLIED>
        <!ATTLIST xs:maxExclusive id ID #IMPLIED>
        <!ATTLIST xs:minExclusive id ID #IMPLIED>
        <!ATTLIST xs:maxInclusive id ID #IMPLIED>
        <!ATTLIST xs:minInclusive id ID #IMPLIED>
        <!ATTLIST xs:totalDigits id ID #IMPLIED>
        <!ATTLIST xs:fractionDigits id ID #IMPLIED>
        <!ATTLIST xs:length id ID #IMPLIED>
        <!ATTLIST xs:minLength id ID #IMPLIED>
        <!ATTLIST xs:maxLength id ID #IMPLIED>
        <!ATTLIST xs:enumeration id ID #IMPLIED>
        <!ATTLIST xs:pattern id ID #IMPLIED>
        <!ATTLIST xs:assertion id ID #IMPLIED>
        <!ATTLIST xs:explicitTimezone id ID #IMPLIED>
        <!ATTLIST xs:appinfo id ID #IMPLIED>
        <!ATTLIST xs:documentation id ID #IMPLIED>
        <!ATTLIST xs:list id ID #IMPLIED>
        <!ATTLIST xs:union id ID #IMPLIED>
        ]>

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified" xml:lang="EN"
           targetNamespace="http://www.w3.org/2001/XMLSchema"
           version="1.0">
 <xs:annotation>
  <xs:documentation>
    Part 1 version: structures.xsd (rec-20120405)
    Part 2 version: datatypes.xsd (rec-20120405)
  </xs:documentation>
 </xs:annotation>

  <xs:annotation>
    <xs:documentation  source="../structures/structures.html">
   The schema corresponding to this document is normative,
   with respect to the syntactic constraints it expresses in the
   XML Schema Definition Language.  The documentation (within 'documentation' elements)
   below, is not normative, but rather highlights important aspects of
   the W3C Recommendation of which this is a part.

      See below (at the bottom of this document) for information about
      the revision and namespace-versioning policy governing this
      schema document.

    </xs:documentation>
  </xs:annotation>
  <xs:annotation>
    <xs:documentation>
   The simpleType element and all of its members are defined
   towards the end of this schema document.</xs:documentation>
 </xs:annotation>
  <xs:import namespace="http://www.w3.org/XML/1998/namespace"
             schemaLocation="http://www.w3.org/2001/xml.xsd">
    <xs:annotation>
      <xs:documentation>
       Get access to the xml: attribute groups for xml:lang
       as declared on 'schema' and 'documentation' below
     </xs:documentation>
    </xs:annotation>
  </xs:import>
  <xs:complexType name="openAttrs">
    <xs:annotation>
      <xs:documentation>
       This type is extended by almost all schema types
       to allow attributes from other namespaces to be
       added to user schemas.
     </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:restriction base="xs:anyType">
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="annotated">
    <xs:annotation>
      <xs:documentation>
       This type is extended by all types which allow annotation
       other than &lt;schema> itself
     </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="xs:openAttrs">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="id" type="xs:ID"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:group name="composition">
    <xs:choice>
      <xs:element ref="xs:include"/>
      <xs:element ref="xs:import"/>
      <xs:element ref="xs:redefine"/>
      <xs:element ref="xs:override"/>
      <xs:element ref="xs:annotation"/>
    </xs:choice>
  </xs:group>
  <xs:group name="schemaTop">
    <xs:annotation>
      <xs:documentation>
   This group is for the
   elements which occur freely at the top level of schemas.
   All of their types are based on the "annotated" type by extension.</xs:documentation>
    </xs:annotation>
    <xs:choice>
      <xs:group ref="xs:redefinable"/>
      <xs:element ref="xs:element"/>
      <xs:element ref="xs:attribute"/>
      <xs:element ref="xs:notation"/>
    </xs:choice>
  </xs:group>
  <xs:group name="redefinable">
    <xs:annotation>
      <xs:documentation>
   This group is for the
   elements which can self-redefine (see &lt;redefine> below).</xs:documentation>
    </xs:annotation>
    <xs:choice>
      <xs:element ref="xs:simpleType"/>
      <xs:element ref="xs:complexType"/>
      <xs:element ref="xs:group"/>
      <xs:element ref="xs:attributeGroup"/>
    </xs:choice>
  </xs:group>
  <xs:simpleType name="formChoice">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:NMTOKEN">
      <xs:enumeration value="qualified"/>
      <xs:enumeration value="unqualified"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="reducedDerivationControl">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:derivationControl">
      <xs:enumeration value="extension"/>
      <xs:enumeration value="restriction"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="derivationSet">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
      <xs:documentation>
   #all or (possibly empty) subset of {extension, restriction}</xs:documentation>
    </xs:annotation>
    <xs:union>
      <xs:simpleType>
        <xs:restriction base="xs:token">
          <xs:enumeration value="#all"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType>
        <xs:list itemType="xs:reducedDerivationControl"/>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:simpleType name="typeDerivationControl">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:derivationControl">
      <xs:enumeration value="extension"/>
      <xs:enumeration value="restriction"/>
      <xs:enumeration value="list"/>
      <xs:enumeration value="union"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="fullDerivationSet">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
      <xs:documentation>
   #all or (possibly empty) subset of {extension, restriction, list, union}</xs:documentation>
    </xs:annotation>
    <xs:union>
      <xs:simpleType>
        <xs:restriction base="xs:token">
          <xs:enumeration value="#all"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType>
        <xs:list itemType="xs:typeDerivationControl"/>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:element name="schema" id="schema">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-schema"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:openAttrs">
          <xs:sequence>
            <xs:group ref="xs:composition" minOccurs="0" maxOccurs="unbounded"/>
            <xs:sequence minOccurs="0">
              <xs:element ref="xs:defaultOpenContent"/>
              <xs:element ref="xs:annotation" minOccurs="0"
                          maxOccurs="unbounded"/>
            </xs:sequence>
            <xs:sequence minOccurs="0" maxOccurs="unbounded">
              <xs:group ref="xs:schemaTop"/>
              <xs:element ref="xs:annotation" minOccurs="0"
                          maxOccurs="unbounded"/>
            </xs:sequence>
          </xs:sequence>
          <xs:attribute name="targetNamespace" type="xs:anyURI"/>
          <xs:attribute name="version" type="xs:token"/>
          <xs:attribute name="finalDefault" type="xs:fullDerivationSet"
                        default="" use="optional"/>
          <xs:attribute name="blockDefault" type="xs:blockSet" default=""
                        use="optional"/>
          <xs:attribute name="attributeFormDefault" type="xs:formChoice"
                        default="unqualified" use="optional"/>
          <xs:attribute name="elementFormDefault" type="xs:formChoice"
                        default="unqualified" use="optional"/>
          <xs:attribute name="defaultAttributes" type="xs:QName"/>
          <xs:attribute name="xpathDefaultNamespace" type="xs:xpathDefaultNamespace"
                        default="##local" use="optional"/>
          <xs:attribute name="id" type="xs:ID"/>
          <xs:attribute ref="xml:lang"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
    <xs:key name="element">
      <xs:selector xpath="xs:element"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="attribute">
      <xs:selector xpath="xs:attribute"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="type">
      <xs:selector xpath="xs:complexType|xs:simpleType"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="group">
      <xs:selector xpath="xs:group"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="attributeGroup">
      <xs:selector xpath="xs:attributeGroup"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="notation">
      <xs:selector xpath="xs:notation"/>
      <xs:field xpath="@name"/>
    </xs:key>
    <xs:key name="identityConstraint">
      <xs:selector xpath=".//xs:key|.//xs:unique|.//xs:keyref"/>
      <xs:field xpath="@name"/>
    </xs:key>
  </xs:element>
  <xs:simpleType name="allNNI">
    <xs:annotation>
      <xs:documentation>
   for maxOccurs</xs:documentation>
    </xs:annotation>
    <xs:union memberTypes="xs:nonNegativeInteger">
      <xs:simpleType>
        <xs:restriction base="xs:NMTOKEN">
          <xs:enumeration value="unbounded"/>
        </xs:restriction>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:attributeGroup name="occurs">
    <xs:annotation>
      <xs:documentation>
   for all particles</xs:documentation>
    </xs:annotation>
    <xs:attribute name="minOccurs" type="xs:nonNegativeInteger" default="1"
                  use="optional"/>
    <xs:attribute name="maxOccurs" type="xs:allNNI" default="1" use="optional"/>
  </xs:attributeGroup>
  <xs:attributeGroup name="defRef">
    <xs:annotation>
      <xs:documentation>
   for element, group and attributeGroup,
   which both define and reference</xs:documentation>
    </xs:annotation>
    <xs:attribute name="name" type="xs:NCName"/>
    <xs:attribute name="ref" type="xs:QName"/>
  </xs:attributeGroup>
  <xs:group name="typeDefParticle">
    <xs:annotation>
      <xs:documentation>
   'complexType' uses this</xs:documentation>
    </xs:annotation>
    <xs:choice>
      <xs:element name="group" type="xs:groupRef"/>
      <xs:element ref="xs:all"/>
      <xs:element ref="xs:choice"/>
      <xs:element ref="xs:sequence"/>
    </xs:choice>
  </xs:group>
  <xs:group name="nestedParticle">
    <xs:choice>
      <xs:element name="element" type="xs:localElement"/>
      <xs:element name="group" type="xs:groupRef"/>
      
      <xs:element ref="xs:choice"/>
      <xs:element ref="xs:sequence"/>
      <xs:element ref="xs:any"/>
    </xs:choice>
  </xs:group>
  <xs:group name="particle">
    <xs:choice>
      <xs:element name="element" type="xs:localElement"/>
      <xs:element name="group" type="xs:groupRef"/>
      <xs:element ref="xs:all"/>
      <xs:element ref="xs:choice"/>
      <xs:element ref="xs:sequence"/>
      <xs:element ref="xs:any"/>
    </xs:choice>
  </xs:group>
  <xs:complexType name="attribute">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:sequence>
          <xs:element name="simpleType" type="xs:localSimpleType" minOccurs="0"/>
        </xs:sequence>
        <xs:attributeGroup ref="xs:defRef"/>
        <xs:attribute name="type" type="xs:QName"/>
        <xs:attribute name="use" default="optional" use="optional">
          <xs:simpleType>
            <xs:restriction base="xs:NMTOKEN">
              <xs:enumeration value="prohibited"/>
              <xs:enumeration value="optional"/>
              <xs:enumeration value="required"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="default" type="xs:string"/>
        <xs:attribute name="fixed" type="xs:string"/>
        <xs:attribute name="form" type="xs:formChoice"/>
        <xs:attribute name="targetNamespace" type="xs:anyURI"/>
          
        <xs:attribute name="inheritable" type="xs:boolean"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="topLevelAttribute">
    <xs:complexContent>
      <xs:restriction base="xs:attribute">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:element name="simpleType" type="xs:localSimpleType" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="ref" use="prohibited"/>
        <xs:attribute name="form" use="prohibited"/>
        <xs:attribute name="use" use="prohibited"/>
        <xs:attribute name="targetNamespace" use="prohibited"/>
        <xs:attribute name="name" type="xs:NCName" use="required"/>
        <xs:attribute name="inheritable" type="xs:boolean"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:group name="attrDecls">
    <xs:sequence>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="attribute" type="xs:attribute"/>
        <xs:element name="attributeGroup" type="xs:attributeGroupRef"/>
      </xs:choice>
      <xs:element ref="xs:anyAttribute" minOccurs="0"/>
    </xs:sequence>
  </xs:group>
  <xs:element name="anyAttribute"  id="anyAttribute">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-anyAttribute"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:wildcard">
          <xs:attribute name="notQName" type="xs:qnameListA"
                        use="optional"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:group name="assertions">
    <xs:sequence>
      <xs:element name="assert" type="xs:assertion"
                  minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:group>
  <xs:complexType name="assertion">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:attribute name="test" type="xs:string"/>
        <xs:attribute name="xpathDefaultNamespace" type="xs:xpathDefaultNamespace"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:group name="complexTypeModel">
    <xs:choice>
      <xs:element ref="xs:simpleContent"/>
      <xs:element ref="xs:complexContent"/>
      <xs:sequence>
        <xs:annotation>
          <xs:documentation>
   This branch is short for
   &lt;complexContent>
   &lt;restriction base="xs:anyType">
   ...
   &lt;/restriction>
   &lt;/complexContent></xs:documentation>
        </xs:annotation>
        <xs:element ref="xs:openContent" minOccurs="0"/>
        <xs:group ref="xs:typeDefParticle" minOccurs="0"/>
        <xs:group ref="xs:attrDecls"/>
        <xs:group ref="xs:assertions"/>
      </xs:sequence>
    </xs:choice>
  </xs:group>
  <xs:complexType name="complexType" abstract="true">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:group ref="xs:complexTypeModel"/>
        <xs:attribute name="name" type="xs:NCName">
          <xs:annotation>
            <xs:documentation>
      Will be restricted to required or prohibited</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="mixed" type="xs:boolean" use="optional">
          <xs:annotation>
            <xs:documentation>
      Not allowed if simpleContent child is chosen.
      May be overridden by setting on complexContent child.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="abstract" type="xs:boolean" default="false"
                      use="optional"/>
        <xs:attribute name="final" type="xs:derivationSet"/>
        <xs:attribute name="block" type="xs:derivationSet"/>
        <xs:attribute name="defaultAttributesApply" type="xs:boolean"
                      default="true" use="optional"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="topLevelComplexType">
    <xs:complexContent>
      <xs:restriction base="xs:complexType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:complexTypeModel"/>
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName" use="required"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="localComplexType">
    <xs:complexContent>
      <xs:restriction base="xs:complexType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:complexTypeModel"/>
        </xs:sequence>
        <xs:attribute name="name" use="prohibited"/>
        <xs:attribute name="abstract" use="prohibited"/>
        <xs:attribute name="final" use="prohibited"/>
        <xs:attribute name="block" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="restrictionType">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:sequence>
          <xs:choice minOccurs="0">
            
            <xs:sequence>
              <xs:element ref="xs:openContent" minOccurs="0"/>
              <xs:group ref="xs:typeDefParticle"/>
            </xs:sequence>
            <xs:group ref="xs:simpleRestrictionModel"/>
          </xs:choice>
          <xs:group ref="xs:attrDecls"/>
          <xs:group ref="xs:assertions"/>
        </xs:sequence>
        <xs:attribute name="base" type="xs:QName" use="required"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="complexRestrictionType">
    <xs:complexContent>
      <xs:restriction base="xs:restrictionType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="0">
            <xs:annotation>
              <xs:documentation>This choice is added simply to
                   make this a valid restriction per the REC</xs:documentation>
            </xs:annotation>
            
            <xs:sequence>
              <xs:element ref="xs:openContent" minOccurs="0"/>
              <xs:group ref="xs:typeDefParticle"/>
            </xs:sequence>
          </xs:choice>
          <xs:group ref="xs:attrDecls"/>
          <xs:group ref="xs:assertions"/>
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="extensionType">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:sequence>
          <xs:element ref="xs:openContent" minOccurs="0"/>
          <xs:group ref="xs:typeDefParticle" minOccurs="0"/>
          <xs:group ref="xs:attrDecls"/>
          <xs:group ref="xs:assertions"/>
        </xs:sequence>
        <xs:attribute name="base" type="xs:QName" use="required"/>
        
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="complexContent" id="complexContent">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-complexContent"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:choice>
            <xs:element name="restriction" type="xs:complexRestrictionType"/>
            <xs:element name="extension" type="xs:extensionType"/>
          </xs:choice>
          <xs:attribute name="mixed" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>
       Overrides any setting on complexType parent.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="openContent" id="openContent">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-openContent"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:sequence>
            <xs:element name="any" minOccurs="0" type="xs:wildcard"/>
          </xs:sequence>
          <xs:attribute name="mode" default="interleave" use="optional">
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="none"/>
                <xs:enumeration value="interleave"/>
                <xs:enumeration value="suffix"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="defaultOpenContent" id="defaultOpenContent">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-defaultOpenContent"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:sequence>
            <xs:element name="any" type="xs:wildcard"/>
          </xs:sequence>
          <xs:attribute name="appliesToEmpty" type="xs:boolean"
                        default="false" use="optional"/>
          <xs:attribute name="mode" default="interleave" use="optional">
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="interleave"/>
                <xs:enumeration value="suffix"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="simpleRestrictionType">
    <xs:complexContent>
      <xs:restriction base="xs:restrictionType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="0">
            <xs:annotation>
              <xs:documentation>This choice is added simply to
                   make this a valid restriction per the REC</xs:documentation>
            </xs:annotation>
            <xs:group ref="xs:simpleRestrictionModel"/>
          </xs:choice>
          <xs:group ref="xs:attrDecls"/>
          <xs:group ref="xs:assertions"/>
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="simpleExtensionType">
    <xs:complexContent>
      <xs:restriction base="xs:extensionType">
        <xs:sequence>
          <xs:annotation>
            <xs:documentation>
      No typeDefParticle group reference</xs:documentation>
          </xs:annotation>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:attrDecls"/>
          <xs:group ref="xs:assertions"/>
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="simpleContent" id="simpleContent">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-simpleContent"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:choice>
            <xs:element name="restriction" type="xs:simpleRestrictionType"/>
            <xs:element name="extension" type="xs:simpleExtensionType"/>
          </xs:choice>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="complexType" type="xs:topLevelComplexType" id="complexType">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-complexType"/>
    </xs:annotation>
  </xs:element>
  <xs:simpleType name="blockSet">
    <xs:annotation>
      <xs:documentation>
    A utility type, not for public use</xs:documentation>
      <xs:documentation>
    #all or (possibly empty) subset of {substitution, extension,
    restriction}</xs:documentation>
    </xs:annotation>
    <xs:union>
      <xs:simpleType>
        <xs:restriction base="xs:token">
          <xs:enumeration value="#all"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base="xs:derivationControl">
              <xs:enumeration value="extension"/>
              <xs:enumeration value="restriction"/>
              <xs:enumeration value="substitution"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:complexType name="element" abstract="true">
    <xs:annotation>
      <xs:documentation>
   The element element can be used either
   at the top level to define an element-type binding globally,
   or within a content model to either reference a globally-defined
   element or type or declare an element-type binding locally.
   The ref form is not allowed at the top level.</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:sequence>
          <xs:choice minOccurs="0">
            <xs:element name="simpleType" type="xs:localSimpleType"/>
            <xs:element name="complexType" type="xs:localComplexType"/>
          </xs:choice>
          <xs:element name="alternative" type="xs:altType" 
                    minOccurs="0" maxOccurs="unbounded"/>
          <xs:group ref="xs:identityConstraint" minOccurs="0"
                    maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attributeGroup ref="xs:defRef"/>
        <xs:attribute name="type" type="xs:QName"/>
        
        <xs:attribute name="substitutionGroup">
         <xs:simpleType>
          <xs:list itemType="xs:QName"/>
         </xs:simpleType>
        </xs:attribute>
        <xs:attributeGroup ref="xs:occurs"/>
        <xs:attribute name="default" type="xs:string"/>
        <xs:attribute name="fixed" type="xs:string"/>
        <xs:attribute name="nillable" type="xs:boolean" use="optional"/>
        <xs:attribute name="abstract" type="xs:boolean" default="false"
                      use="optional"/>
        <xs:attribute name="final" type="xs:derivationSet"/>
        <xs:attribute name="block" type="xs:blockSet"/>
        <xs:attribute name="form" type="xs:formChoice"/>
        <xs:attribute name="targetNamespace" type="xs:anyURI"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="topLevelElement">
    <xs:complexContent>
      <xs:restriction base="xs:element">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="0">
            <xs:element name="simpleType" type="xs:localSimpleType"/>
            <xs:element name="complexType" type="xs:localComplexType"/>
          </xs:choice>
          <xs:element name="alternative" type="xs:altType" 
                    minOccurs="0" maxOccurs="unbounded"/>
          <xs:group ref="xs:identityConstraint" minOccurs="0"
                    maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="ref" use="prohibited"/>
        <xs:attribute name="form" use="prohibited"/>
        <xs:attribute name="targetNamespace" use="prohibited"/>
        <xs:attribute name="minOccurs" use="prohibited"/>
        <xs:attribute name="maxOccurs" use="prohibited"/>
        <xs:attribute name="name" type="xs:NCName" use="required"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="localElement">
    <xs:complexContent>
      <xs:restriction base="xs:element">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="0">
            <xs:element name="simpleType" type="xs:localSimpleType"/>
            <xs:element name="complexType" type="xs:localComplexType"/>
          </xs:choice>
          <xs:element name="alternative" type="xs:altType" 
                    minOccurs="0" maxOccurs="unbounded"/>
          <xs:group ref="xs:identityConstraint" minOccurs="0"
                    maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="substitutionGroup" use="prohibited"/>
        <xs:attribute name="final" use="prohibited"/>
        <xs:attribute name="abstract" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="element" type="xs:topLevelElement" id="element">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-element"/>
    </xs:annotation>
  </xs:element>
  <xs:complexType name="altType">
    <xs:annotation>
      <xs:documentation>
        This type is used for 'alternative' elements.
      </xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:choice minOccurs="0">
          <xs:element name="simpleType" type="xs:localSimpleType"/>
          <xs:element name="complexType" type="xs:localComplexType"/>
        </xs:choice>
        <xs:attribute name="test" type="xs:string" use="optional"/>
        <xs:attribute name="type" type="xs:QName" use="optional"/>
        <xs:attribute name="xpathDefaultNamespace" type="xs:xpathDefaultNamespace"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="group" abstract="true">
    <xs:annotation>
      <xs:documentation>
   group type for explicit groups, named top-level groups and
   group references</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        
          <xs:group ref="xs:particle" minOccurs="0" maxOccurs="unbounded"/>
          
        <xs:attributeGroup ref="xs:defRef"/>
        <xs:attributeGroup ref="xs:occurs"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="realGroup">
    <xs:complexContent>
      <xs:restriction base="xs:group">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="0" maxOccurs="1">
            <xs:element ref="xs:all"/>
            <xs:element ref="xs:choice"/>
            <xs:element ref="xs:sequence"/>
          </xs:choice>
          
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="namedGroup">
    <xs:complexContent>
      <xs:restriction base="xs:realGroup">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:choice minOccurs="1" maxOccurs="1">
            <xs:element name="all">
              <xs:complexType>
                <xs:complexContent>
                  <xs:restriction base="xs:all">
                    <xs:group ref="xs:allModel"/>
                    <xs:attribute name="minOccurs" use="prohibited"/>
                    <xs:attribute name="maxOccurs" use="prohibited"/>
                    <xs:anyAttribute namespace="##other" processContents="lax"/>
                  </xs:restriction>
                </xs:complexContent>
              </xs:complexType>
            </xs:element>
            <xs:element name="choice" type="xs:simpleExplicitGroup"/>
            <xs:element name="sequence" type="xs:simpleExplicitGroup"/>
          </xs:choice>
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName" use="required"/>
        <xs:attribute name="ref" use="prohibited"/>
        <xs:attribute name="minOccurs" use="prohibited"/>
        <xs:attribute name="maxOccurs" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="groupRef">
    <xs:complexContent>
      <xs:restriction base="xs:realGroup">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="ref" type="xs:QName" use="required"/>
        <xs:attribute name="name" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="explicitGroup">
    <xs:annotation>
      <xs:documentation>
   group type for the three kinds of group</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:restriction base="xs:group">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:nestedParticle" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="name" use="prohibited"/>
        <xs:attribute name="ref" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="simpleExplicitGroup">
    <xs:complexContent>
      <xs:restriction base="xs:explicitGroup">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:nestedParticle" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="minOccurs" use="prohibited"/>
        <xs:attribute name="maxOccurs" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:group name="allModel">
    <xs:sequence>
      <xs:element ref="xs:annotation" minOccurs="0"/>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:annotation>
          <xs:documentation>This choice with min/max is here to
                          avoid a pblm with the Elt:All/Choice/Seq
                          Particle derivation constraint</xs:documentation>
        </xs:annotation>
        <xs:element name="element" type="xs:localElement"/>
        <xs:element ref="xs:any"/>
        <xs:element name="group">
          <xs:complexType>
            <xs:complexContent>
              <xs:restriction base="xs:groupRef">
                <xs:sequence>
                  <xs:element ref="xs:annotation" minOccurs="0"/>
                </xs:sequence>
                <xs:attribute name="minOccurs" fixed="1" type="xs:nonNegativeInteger"/>
                <xs:attribute name="maxOccurs" fixed="1" type="xs:nonNegativeInteger"/>
              </xs:restriction>
            </xs:complexContent>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:sequence>
  </xs:group>
  <xs:complexType name="all">
    <xs:annotation>
      <xs:documentation>
   Only elements allowed inside</xs:documentation>
    </xs:annotation>
    <xs:complexContent>
      <xs:restriction base="xs:explicitGroup">
        <xs:group ref="xs:allModel"/>
        <xs:attribute name="minOccurs" default="1" use="optional">
          <xs:simpleType>
            <xs:restriction base="xs:nonNegativeInteger">
              <xs:enumeration value="0"/>
              <xs:enumeration value="1"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="maxOccurs" default="1" use="optional">
          <xs:simpleType>
            <xs:restriction base="xs:allNNI">
              <xs:enumeration value="0"/>
              <xs:enumeration value="1"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="all" type="xs:all" id="all">
    <xs:annotation>
      <xs:documentation source="../structures/structures.html#element-all"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="choice" type="xs:explicitGroup" id="choice">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-choice"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="sequence" type="xs:explicitGroup" id="sequence">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-sequence"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="group" type="xs:namedGroup" id="group">
    <xs:annotation>
      <xs:documentation source="../structures/structures.html#element-group"/>
    </xs:annotation>
  </xs:element>
  <xs:attributeGroup name="anyAttrGroup">
    <xs:attribute name="namespace" type="xs:namespaceList"
                  use="optional"/>
    <xs:attribute name="notNamespace" use="optional">
      <xs:simpleType>
        <xs:restriction base="xs:basicNamespaceList">
          <xs:minLength value="1"/>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
    <xs:attribute name="processContents" default="strict" use="optional">
      <xs:simpleType>
        <xs:restriction base="xs:NMTOKEN">
          <xs:enumeration value="skip"/>
          <xs:enumeration value="lax"/>
          <xs:enumeration value="strict"/>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:complexType name="wildcard">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        
         <xs:attributeGroup ref="xs:anyAttrGroup"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="any" id="any">
    <xs:annotation>
      <xs:documentation source="../structures/structures.html#element-any"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:wildcard">
          <xs:attribute name="notQName" type="xs:qnameList"
                        use="optional"/>
          <xs:attributeGroup ref="xs:occurs"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:annotation>
    <xs:documentation>
   simple type for the value of the 'namespace' attr of
   'any' and 'anyAttribute'</xs:documentation>
  </xs:annotation>
  <xs:annotation>
    <xs:documentation>
   Value is
              ##any      - - any non-conflicting WFXML/attribute at all

              ##other    - - any non-conflicting WFXML/attribute from
                              namespace other than targetNS

              ##local    - - any unqualified non-conflicting WFXML/attribute 

              one or     - - any non-conflicting WFXML/attribute from
              more URI        the listed namespaces
              references
              (space separated)

    ##targetNamespace or ##local may appear in the above list, to
        refer to the targetNamespace of the enclosing
        schema or an absent targetNamespace respectively</xs:documentation>
  </xs:annotation>
  <xs:simpleType name="namespaceList">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    
    <xs:union memberTypes="xs:specialNamespaceList xs:basicNamespaceList" />
  </xs:simpleType>
  <xs:simpleType name="basicNamespaceList">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:list>
      <xs:simpleType>
        <xs:union memberTypes="xs:anyURI">
          <xs:simpleType>
            <xs:restriction base="xs:token">
              <xs:enumeration value="##targetNamespace"/>
              <xs:enumeration value="##local"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:union>
      </xs:simpleType>
    </xs:list>
  </xs:simpleType>
  <xs:simpleType name="specialNamespaceList">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:token">
      <xs:enumeration value="##any"/>
      <xs:enumeration value="##other"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="qnameList">
    <xs:annotation>
      <xs:documentation>
        A utility type, not for public use
      </xs:documentation>
    </xs:annotation>
    <xs:list>
      <xs:simpleType>
        <xs:union memberTypes="xs:QName">
          <xs:simpleType>
            <xs:restriction base="xs:token">
              <xs:enumeration value="##defined"/>
              <xs:enumeration value="##definedSibling"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:union>
      </xs:simpleType>
    </xs:list>
  </xs:simpleType>
  <xs:simpleType name="qnameListA">
    <xs:annotation>
      <xs:documentation>
        A utility type, not for public use
      </xs:documentation>
    </xs:annotation>
    <xs:list>
      <xs:simpleType>
        <xs:union memberTypes="xs:QName">
          <xs:simpleType>
            <xs:restriction base="xs:token">
              <xs:enumeration value="##defined"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:union>
      </xs:simpleType>
    </xs:list>
  </xs:simpleType>
  <xs:simpleType name="xpathDefaultNamespace">
    <xs:union memberTypes="xs:anyURI">
      <xs:simpleType>
        <xs:restriction base="xs:token">
          <xs:enumeration value="##defaultNamespace"/>
          <xs:enumeration value="##targetNamespace"/>
          <xs:enumeration value="##local"/>
        </xs:restriction>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:element name="attribute" type="xs:topLevelAttribute" id="attribute">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-attribute"/>
    </xs:annotation>
  </xs:element>
  <xs:complexType name="attributeGroup" abstract="true">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        
          <xs:group ref="xs:attrDecls"/>
          
        <xs:attributeGroup ref="xs:defRef"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="namedAttributeGroup">
    <xs:complexContent>
      <xs:restriction base="xs:attributeGroup">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:attrDecls"/>
          
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName" use="required"/>
        <xs:attribute name="ref" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="attributeGroupRef">
    <xs:complexContent>
      <xs:restriction base="xs:attributeGroup">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="ref" type="xs:QName" use="required"/>
        <xs:attribute name="name" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="attributeGroup" type="xs:namedAttributeGroup"
              id="attributeGroup">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-attributeGroup"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="include" id="include">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-include"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:attribute name="schemaLocation" type="xs:anyURI" use="required"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="redefine" id="redefine">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-redefine"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:openAttrs">
          <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element ref="xs:annotation"/>
            <xs:group ref="xs:redefinable"/>
          </xs:choice>
          <xs:attribute name="schemaLocation" type="xs:anyURI" use="required"/>
          <xs:attribute name="id" type="xs:ID"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>

  <xs:element name="override" id="override">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-override"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:openAttrs">
          <xs:sequence>
            <xs:element ref="xs:annotation" minOccurs="0"/>
            <xs:group ref="xs:schemaTop" minOccurs="0" maxOccurs="unbounded"/>
          </xs:sequence>
          <xs:attribute name="schemaLocation" type="xs:anyURI" use="required"/>
          <xs:attribute name="id" type="xs:ID"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="import" id="import">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-import"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:attribute name="namespace" type="xs:anyURI"/>
          <xs:attribute name="schemaLocation" type="xs:anyURI"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="selector" id="selector">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-selector"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:attribute name="xpath" use="required">
            <xs:simpleType>
              <xs:annotation>
                <xs:documentation>A subset of XPath expressions for use
in selectors</xs:documentation>
                <xs:documentation>A utility type, not for public
use</xs:documentation>
              </xs:annotation>
              <xs:restriction base="xs:token"/>
                
            </xs:simpleType>
          </xs:attribute>
          <xs:attribute name="xpathDefaultNamespace" type="xs:xpathDefaultNamespace"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="field" id="field">
    <xs:annotation>
      <xs:documentation source="../structures/structures.html#element-field"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:attribute name="xpath" use="required">
            <xs:simpleType>
              <xs:annotation>
                <xs:documentation>A subset of XPath expressions for use
in fields</xs:documentation>
                <xs:documentation>A utility type, not for public
use</xs:documentation>
              </xs:annotation>
              <xs:restriction base="xs:token"/>
                
            </xs:simpleType>
          </xs:attribute>
          <xs:attribute name="xpathDefaultNamespace" type="xs:xpathDefaultNamespace"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="keybase">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:sequence minOccurs="0">
          <xs:element ref="xs:selector"/>
          <xs:element ref="xs:field" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName"/>
        <xs:attribute name="ref" type="xs:QName"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:group name="identityConstraint">
    <xs:annotation>
      <xs:documentation>The three kinds of identity constraints, all with
                     type of or derived from 'keybase'.
   </xs:documentation>
    </xs:annotation>
    <xs:choice>
      <xs:element ref="xs:unique"/>
      <xs:element ref="xs:key"/>
      <xs:element ref="xs:keyref"/>
    </xs:choice>
  </xs:group>
  <xs:element name="unique" type="xs:keybase" id="unique">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-unique"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="key" type="xs:keybase" id="key">
    <xs:annotation>
      <xs:documentation source="../structures/structures.html#element-key"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="keyref" id="keyref">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-keyref"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:keybase">
          <xs:attribute name="refer" type="xs:QName"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="notation" id="notation">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-notation"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:attribute name="name" type="xs:NCName" use="required"/>
          <xs:attribute name="public" type="xs:public"/>
          <xs:attribute name="system" type="xs:anyURI"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:simpleType name="public">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
      <xs:documentation>
   A public identifier, per ISO 8879</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:token"/>
  </xs:simpleType>
  <xs:element name="appinfo" id="appinfo">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-appinfo"/>
    </xs:annotation>
    <xs:complexType mixed="true">
      <xs:sequence minOccurs="0" maxOccurs="unbounded">
        <xs:any processContents="lax"/>
      </xs:sequence>
      <xs:attribute name="source" type="xs:anyURI"/>
      <xs:anyAttribute namespace="##other" processContents="lax"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="documentation" id="documentation">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-documentation"/>
    </xs:annotation>
    <xs:complexType mixed="true">
      <xs:sequence minOccurs="0" maxOccurs="unbounded">
        <xs:any processContents="lax"/>
      </xs:sequence>
      <xs:attribute name="source" type="xs:anyURI"/>
      <xs:attribute ref="xml:lang"/>
      <xs:anyAttribute namespace="##other" processContents="lax"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="annotation" id="annotation">
    <xs:annotation>
      <xs:documentation
           source="../structures/structures.html#element-annotation"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="xs:openAttrs">
          <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element ref="xs:appinfo"/>
            <xs:element ref="xs:documentation"/>
          </xs:choice>
          <xs:attribute name="id" type="xs:ID"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:annotation>
    <xs:documentation>
   notations for use within  schema documents</xs:documentation>
  </xs:annotation>
  <xs:notation name="XMLSchemaStructures" public="structures"
               system="http://www.w3.org/2000/08/XMLSchema.xsd"/>
  <xs:notation name="XML" public="REC-xml-19980210"
               system="http://www.w3.org/TR/1998/REC-xml-19980210"/>
  <xs:complexType name="anyType" mixed="true">
    <xs:annotation>
      <xs:documentation>
   Not the real urType, but as close an approximation as we can
   get in the XML representation</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:any minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
    </xs:sequence>
    <xs:anyAttribute processContents="lax"/>
  </xs:complexType>

  <xs:annotation>
    <xs:documentation>
      In keeping with the XML Schema WG's standard versioning policy, 
      the material in this schema document will persist at the URI
      http://www.w3.org/2012/04/XMLSchema.xsd.

      At the date of issue it can also be found at the URI
      http://www.w3.org/2009/XMLSchema/XMLSchema.xsd.

      The schema document at that URI may however change in the future, 
      in order to remain compatible with the latest version of XSD 
      and its namespace.  In other words, if XSD or the XML Schema 
      namespace change, the version of this document at 
      http://www.w3.org/2009/XMLSchema/XMLSchema.xsd will change accordingly; 
      the version at http://www.w3.org/2012/04/XMLSchema.xsd will not change.

      Previous dated (and unchanging) versions of this schema document 
      include:

       http://www.w3.org/2012/01/XMLSchema.xsd
          (XSD 1.1 Proposed Recommendation)

        http://www.w3.org/2011/07/XMLSchema.xsd
          (XSD 1.1 Candidate Recommendation)

        http://www.w3.org/2009/04/XMLSchema.xsd
          (XSD 1.1 Candidate Recommendation)

        http://www.w3.org/2004/10/XMLSchema.xsd
          (XSD 1.0 Recommendation, Second Edition)

        http://www.w3.org/2001/05/XMLSchema.xsd
          (XSD 1.0 Recommendation, First Edition)


    </xs:documentation>
  </xs:annotation>




  <xs:simpleType name="derivationControl">
    <xs:annotation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:NMTOKEN">
      <xs:enumeration value="substitution"/>
      <xs:enumeration value="extension"/>
      <xs:enumeration value="restriction"/>
      <xs:enumeration value="list"/>
      <xs:enumeration value="union"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:group name="simpleDerivation">
    <xs:choice>
      <xs:element ref="xs:restriction"/>
      <xs:element ref="xs:list"/>
      <xs:element ref="xs:union"/>
    </xs:choice>
  </xs:group>
  <xs:simpleType name="simpleDerivationSet">
    <xs:annotation>
      <xs:documentation>
   #all or (possibly empty) subset of {restriction, extension, union, list}
   </xs:documentation>
      <xs:documentation>
   A utility type, not for public use</xs:documentation>
    </xs:annotation>
    <xs:union>
      <xs:simpleType>
        <xs:restriction base="xs:token">
          <xs:enumeration value="#all"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType>
        <xs:list>
          <xs:simpleType>
            <xs:restriction base="xs:derivationControl">
              <xs:enumeration value="list"/>
              <xs:enumeration value="union"/>
              <xs:enumeration value="restriction"/>
              <xs:enumeration value="extension"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:list>
      </xs:simpleType>
    </xs:union>
  </xs:simpleType>
  <xs:complexType name="simpleType" abstract="true">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:group ref="xs:simpleDerivation"/>
        <xs:attribute name="final" type="xs:simpleDerivationSet"/>
        <xs:attribute name="name" type="xs:NCName">
          <xs:annotation>
            <xs:documentation>
              Can be restricted to required or forbidden
            </xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="topLevelSimpleType">
    <xs:complexContent>
      <xs:restriction base="xs:simpleType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:simpleDerivation"/>
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName" use="required">
          <xs:annotation>
            <xs:documentation>
              Required at the top level
            </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="localSimpleType">
    <xs:complexContent>
      <xs:restriction base="xs:simpleType">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
          <xs:group ref="xs:simpleDerivation"/>
        </xs:sequence>
        <xs:attribute name="name" use="prohibited">
          <xs:annotation>
            <xs:documentation>
              Forbidden when nested
            </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="final" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="simpleType" type="xs:topLevelSimpleType" id="simpleType">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-simpleType"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="facet" abstract="true">
    <xs:annotation>
      <xs:documentation>
        An abstract element, representing facets in general.
        The facets defined by this spec are substitutable for
        this element, and implementation-defined facets should
        also name this as a substitution-group head.
      </xs:documentation>
    </xs:annotation>
  </xs:element>
  <xs:group name="simpleRestrictionModel">
    <xs:sequence>
      <xs:element name="simpleType" type="xs:localSimpleType" minOccurs="0"/>
      <xs:choice minOccurs="0" 
          maxOccurs="unbounded">
        <xs:element ref="xs:facet"/>
        <xs:any processContents="lax"
            namespace="##other"/>
      </xs:choice>
    </xs:sequence>
  </xs:group>
  <xs:element name="restriction" id="restriction">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation
             source="http://www.w3.org/TR/xmlschema11-2/#element-restriction">
          base attribute and simpleType child are mutually
          exclusive, but one or other is required
        </xs:documentation>
      </xs:annotation>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:group ref="xs:simpleRestrictionModel"/>
          <xs:attribute name="base" type="xs:QName" use="optional"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="list" id="list">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation
             source="http://www.w3.org/TR/xmlschema11-2/#element-list">
          itemType attribute and simpleType child are mutually
          exclusive, but one or other is required
        </xs:documentation>
      </xs:annotation>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:sequence>
            <xs:element name="simpleType" type="xs:localSimpleType"
                        minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="itemType" type="xs:QName" use="optional"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="union" id="union">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation
             source="http://www.w3.org/TR/xmlschema11-2/#element-union">
          memberTypes attribute must be non-empty or there must be
          at least one simpleType child
        </xs:documentation>
      </xs:annotation>
      <xs:complexContent>
        <xs:extension base="xs:annotated">
          <xs:sequence>
            <xs:element name="simpleType" type="xs:localSimpleType"
                        minOccurs="0" maxOccurs="unbounded"/>
          </xs:sequence>
          <xs:attribute name="memberTypes" use="optional">
            <xs:simpleType>
              <xs:list itemType="xs:QName"/>
            </xs:simpleType>
          </xs:attribute>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="facet">
    <xs:complexContent>
      <xs:extension base="xs:annotated">
        <xs:attribute name="value" use="required"/>
        <xs:attribute name="fixed" type="xs:boolean" default="false"
                      use="optional"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="noFixedFacet">
    <xs:complexContent>
      <xs:restriction base="xs:facet">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="fixed" use="prohibited"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="minExclusive" type="xs:facet"  
    id="minExclusive"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-minExclusive"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="minInclusive" type="xs:facet" 
    id="minInclusive"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-minInclusive"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="maxExclusive" type="xs:facet" 
    id="maxExclusive"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-maxExclusive"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="maxInclusive" type="xs:facet"  
    id="maxInclusive"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-maxInclusive"/>
    </xs:annotation>
  </xs:element>
  <xs:complexType name="numFacet">
    <xs:complexContent>
      <xs:restriction base="xs:facet">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="value"  
            type="xs:nonNegativeInteger" use="required"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="intFacet">
    <xs:complexContent>
      <xs:restriction base="xs:facet">
        <xs:sequence>
          <xs:element ref="xs:annotation" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="value" type="xs:integer" use="required"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>

  <xs:element name="totalDigits" id="totalDigits"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-totalDigits"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:restriction base="xs:numFacet">
          <xs:sequence>
            <xs:element ref="xs:annotation" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="value" type="xs:positiveInteger" use="required"/>
          <xs:anyAttribute namespace="##other" processContents="lax"/>
        </xs:restriction>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="fractionDigits" type="xs:numFacet"  
    id="fractionDigits"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-fractionDigits"/>
    </xs:annotation>
  </xs:element>

  <xs:element name="length" type="xs:numFacet" id="length"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-length"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="minLength" type="xs:numFacet"  
    id="minLength"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-minLength"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="maxLength" type="xs:numFacet"  
    id="maxLength"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-maxLength"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="enumeration" type="xs:noFixedFacet"  
    id="enumeration"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-enumeration"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="whiteSpace" id="whiteSpace"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-whiteSpace"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:restriction base="xs:facet">
          <xs:sequence>
            <xs:element ref="xs:annotation" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="value" use="required">
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="preserve"/>
                <xs:enumeration value="replace"/>
                <xs:enumeration value="collapse"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          <xs:anyAttribute namespace="##other" processContents="lax"/>
        </xs:restriction>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="pattern" id="pattern"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-pattern"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:restriction base="xs:noFixedFacet">
          <xs:sequence>
            <xs:element ref="xs:annotation" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="value" type="xs:string"  
              use="required"/>
          <xs:anyAttribute namespace="##other"  
              processContents="lax"/>
        </xs:restriction>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="assertion" type="xs:assertion"
              id="assertion" substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-assertion"/>
    </xs:annotation>
  </xs:element>
  <xs:element name="explicitTimezone" id="explicitTimezone"
    substitutionGroup="xs:facet">
    <xs:annotation>
      <xs:documentation
           source="http://www.w3.org/TR/xmlschema11-2/#element-explicitTimezone"/>
    </xs:annotation>
    <xs:complexType>
      <xs:complexContent>
        <xs:restriction base="xs:facet">
          <xs:sequence>
            <xs:element ref="xs:annotation" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="value" use="required">
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="optional"/>
                <xs:enumeration value="required"/>
                <xs:enumeration value="prohibited"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          <xs:anyAttribute namespace="##other" processContents="lax"/>
        </xs:restriction>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>

  <xs:annotation>
    <xs:documentation>
      In keeping with the XML Schema WG's standard versioning policy, 
      this schema document will persist at the URI
      http://www.w3.org/2012/04/datatypes.xsd.

      At the date of issue it can also be found at the URI
      http://www.w3.org/2009/XMLSchema/datatypes.xsd.

      The schema document at that URI may however change in the future, 
      in order to remain compatible with the latest version of XSD 
      and its namespace.  In other words, if XSD or the XML Schema 
      namespace change, the version of this document at 
      http://www.w3.org/2009/XMLSchema/datatypes.xsd will change accordingly; 
      the version at http://www.w3.org/2012/04/datatypes.xsd will not change.

      Previous dated (and unchanging) versions of this schema document 
      include:

        http://www.w3.org/2012/01/datatypes.xsd
          (XSD 1.1 Proposed Recommendation)

        http://www.w3.org/2011/07/datatypes.xsd
          (XSD 1.1 Candidate Recommendation)

        http://www.w3.org/2009/04/datatypes.xsd
          (XSD 1.1 Candidate Recommendation)

        http://www.w3.org/2004/10/datatypes.xsd
          (XSD 1.0 Recommendation, Second Edition)

        http://www.w3.org/2001/05/datatypes.xsd
          (XSD 1.0 Recommendation, First Edition)

    </xs:documentation>
  </xs:annotation>



</xs:schema>