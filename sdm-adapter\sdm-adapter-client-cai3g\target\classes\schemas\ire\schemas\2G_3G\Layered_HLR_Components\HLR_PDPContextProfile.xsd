<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="pdpcp" type="pdpcpType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="SetPDPContextProfile">
<xs:annotation>
<xs:documentation>
				This command changes PDP context profile data in the
				Home Location Register (HLR).
			</xs:documentation>
</xs:annotation>
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="pdpcp" type="pdpcpType"/>
<xs:element name="pdpid" type="pdpidType"/>
<xs:choice>
<xs:sequence>
<xs:element name="erase" type="eraseType"/>
</xs:sequence>
<xs:sequence>
<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="vpaa" type="vpaaType"/>
<xs:choice>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
</xs:choice>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
</xs:sequence>
</xs:choice>
</xs:sequence>
<xs:attribute name="pdpcp" type="pdpcpType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="pdpcpAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetPDPContextProfile">
<xs:annotation>
<xs:documentation>
				This command prints the PDP contexts data of one,
				several or all the PDP context profiles within the Home
				Location Register (HLR).
			</xs:documentation>
</xs:annotation>
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" name="PDPContextData">
<xs:complexType>
<xs:sequence>
<xs:element name="pdpcp" type="pdpcpType"/>
<xs:element maxOccurs="unbounded" name="PDPContext">
<xs:complexType>
<xs:sequence>
<xs:element name="pdpid" type="pdpidType"/>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
<xs:element name="eqosid" type="eqosidType"/>
<xs:element name="vpaa" type="vpaaType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
