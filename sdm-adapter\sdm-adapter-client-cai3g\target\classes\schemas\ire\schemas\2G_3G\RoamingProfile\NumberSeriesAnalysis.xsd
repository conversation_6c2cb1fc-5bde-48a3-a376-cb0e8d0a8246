<!-- Customer Adaptation, NumberSeriesAnalysis -->
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:ns="http://schemas.ericsson.com/pg/hlr/13.5/"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
           targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/"
           elementFormDefault="qualified" attributeFormDefault="unqualified"
           jaxb:version="2.0">
    <xs:include schemaLocation="./types/rp_types.xsd"/>
    <!-- CAI3G MOId type definitions. -->
    <xs:element name="onsa" type="onsaType"/>
    <xs:element name="ns" type="nsType"/>
    <xs:element name="char" type="charType"/>
    <xs:element name="frontendid" type="hlrfeidType"/>
    <!-- CreateNumberSeriesAnalysis MOId: onsa,ns,char MOType: NumberSeriesAnalysis@http://schemas.ericsson.com/pg/hlr/13.5/ -->
    <xs:element name="CreateNumberSeriesAnalysis">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="onsa" type="onsaType"/>
                <xs:element name="ns" type="nsType"/>
                <xs:element name="char" type="charType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="onsa" type="onsaType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="onsaAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="ns" type="nsType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="nsAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="char" type="charType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="charAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <!-- DeleteNumberSeriesAnalysis MOId: onsa and ns MOType: NumberSeriesAnalysis@http://schemas.ericsson.com/pg/hlr/13.5/ -->
    <xs:element name="DeleteNumberSeriesAnalysis">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="onsa" type="onsaType" />
                <xs:element name="ns" type="nsType" />
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="onsa" type="onsaType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="onsaAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="ns" type="nsType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="nsAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>

        </xs:complexType>
    </xs:element>

    <xs:element name="HlrNumberSeriesAnalysisData">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="NsaData" type="nsaDataType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="nsaDataType">
        <xs:sequence>
            <xs:element name="onsa" type="onsaType" minOccurs="0"/>
            <xs:element name="char" type="charType" minOccurs="0"/>
            <xs:element name="ns" type="nsType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
