<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:hss="http://schemas.ericsson.com/ma/HSS/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/">
<xs:import namespace="http://schemas.ericsson.com/ma/HSS/" schemaLocation="../../../schemas/IMS_Core/types/hssla_types.xsd"/>
<xs:element name="imsi" type="IMSIType"/>
<xs:element name="SetMultiSCCat">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="AdminUserDisable" type="xs:boolean"/>
<xs:element minOccurs="0" name="NetworkAccessMode" type="NamType"/>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="optional"/>
</xs:complexType>
</xs:element>
<xs:element name="GetMultiSCCatResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="imsi" type="IMSIType"/>
<xs:element minOccurs="0" name="CSPS">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="NetworkAccessMode" type="NamType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="EPS">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="AdminUserDisable" type="xs:boolean"/>
<xs:element minOccurs="0" name="NetworkAccessMode" type="NamType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="imsi" type="IMSIType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:simpleType name="MSISDNType">
<xs:annotation>
<xs:documentation>
				the type definition for MSISDN
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{5,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IMSIType">
<xs:annotation>
<xs:documentation>
				the type definition for IMSI
			</xs:documentation>
</xs:annotation>
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{6,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="NamType">
<xs:sequence>
<xs:element name="prov" type="ZeroTwoType"/>
<xs:element minOccurs="0" name="keep" type="BinaryType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="ZeroTwoType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BinaryType">
<xs:restriction base="xs:unsignedByte">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="1"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
