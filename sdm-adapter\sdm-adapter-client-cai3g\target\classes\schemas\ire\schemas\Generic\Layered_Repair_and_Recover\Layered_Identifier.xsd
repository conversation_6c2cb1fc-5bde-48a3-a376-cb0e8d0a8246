<!-- CUDB, Identifier -->
<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:x="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:include schemaLocation="../../../schemas/Generic/Layered_Repair_and_Recover/types/cudb_types.xsd" />

	<!-- CreateIdentifier MOId: type and value MOType: Identifier@http://schemas.ericsson.com/pg/cudb/1.0/ -->
	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="imsiAux" type="imsiType" />
	<xs:element name="service" type="serviceType" />

	<xs:element name="CreateIdentifier">
		<xs:complexType>
			<xs:sequence>
				<xs:choice>
					<xs:element name="msisdn" type="msisdnType" />
					<xs:element name="imsi" type="imsiType" />
					<xs:element name="imsiAux" type="imsiType" />
				</xs:choice>
				<xs:element name="service" type="serviceType" />
				<xs:element name="mscId" type="mscIdType" />
				<xs:element name="idType" type="idTypeType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsi" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsiAux" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAuxAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="service" type="serviceType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="serviceAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>

		<xs:key name="key_msisdn">
			<xs:selector xpath="./x:msisdn" />
			<xs:field xpath="." />
		</xs:key>
		<xs:key name="key_imsi">
			<xs:selector xpath="./x:imsi" />
			<xs:field xpath="." />
		</xs:key>
		<xs:key name="key_imsiAux">
			<xs:selector xpath="./x:imsiAux" />
			<xs:field xpath="." />
		</xs:key>
		<xs:key name="key_service">
			<xs:selector xpath="./x:service" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_msisdn" refer="key_msisdn">
			<xs:selector xpath="." />
			<xs:field xpath="@msisdn" />
		</xs:keyref>
		<xs:keyref name="keyref_imsi" refer="key_imsi">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>
		<xs:keyref name="keyref_imsiAux" refer="key_imsiAux">
			<xs:selector xpath="." />
			<xs:field xpath="@imsiAux" />
		</xs:keyref>
		<xs:keyref name="keyref_service" refer="key_service">
			<xs:selector xpath="." />
			<xs:field xpath="@service" />
		</xs:keyref>
	</xs:element>

	<!-- SetIdentifier MOId: type and value MOType: Identifier@http://schemas.ericsson.com/pg/cudb/1.0/ -->
	<xs:element name="SetIdentifier">
		<xs:complexType>
			<xs:sequence>
				<xs:choice>
					<xs:element name="msisdn" type="msisdnType" />
					<xs:element name="imsi" type="imsiType" />
					<xs:element name="imsiAux" type="imsiType" />
				</xs:choice>
				<xs:element name="service" type="serviceType" />
				<xs:element name="action" type="actionType" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsi" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsiAux" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAuxAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="service" type="serviceType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="serviceAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<!-- DeleteIdentifier MOId: type and value MOType: Identifier@http://schemas.ericsson.com/pg/cudb/1.0/ -->
</xs:schema>
