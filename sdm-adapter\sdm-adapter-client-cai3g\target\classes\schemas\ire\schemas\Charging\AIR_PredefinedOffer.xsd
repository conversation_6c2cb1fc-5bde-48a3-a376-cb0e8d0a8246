<?xml version="1.0" encoding="UTF-8"?>
<!-- (<PERSON> (China) Communications Company Ltd) -->
<xs:schema elementFormDefault="qualified"
    attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
    jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/"
    xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
    xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
    xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/">

    <xs:include schemaLocation="air_common_types.xsd"/>

    <xs:element name="offerID" type="offerIDType" />
    <xs:element name="bundleID" type="bundleIDType" />
    <xs:element name="subscriberNumber" type="subscriberNumberType" />

    <!-- delete PredefinedOffer -->
    <xs:element name="deletePredefinedOffer">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="originNodeType" type="originNodeTypeType" minOccurs="0" />
                <xs:element name="originHostName" type="originHostNameType" minOccurs="0" />
                <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0" />
                <xs:element name="originTimeStamp" type="dateTimeType" minOccurs="0"/>
                <xs:element name="subscriberNumberNAI" type="subscriberNumberNAIType" minOccurs="0" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="offerServiceID" type="offerServiceIDType" minOccurs="0" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" maxOccurs="unbounded" />
            </xs:sequence>
            <xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="subscriberNumberAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="offerID" type="offerIDType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="offerIDAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="bundleID" type="bundleIDType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="bundleIDTypeAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <!-- delete PredefinedOffer Response -->
    <xs:element name="deletePredefinedOfferResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="subscriberNumber" type="subscriberNumberType"/>
                <xs:element name="originTransactionID" type="originTransactionIDType" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="offerID" type="offerIDType" minOccurs="0" />
                <xs:element name="attributeInformation" type="attributeInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="treeParameterSetInformation" type="treeParameterSetInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="productOfferingName" type="productOfferingNameType" minOccurs="0" />
                <xs:element name="bundlePredefinedOfferValuesInformation" type="bundlePredefinedOfferValuesInformationType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- set PredefinedOffer -->
    <xs:element name="setPredefinedOffer">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="originNodeType" type="originNodeTypeType" minOccurs="0"/>
                <xs:element name="originHostName" type="originHostNameType" minOccurs="0" />
                <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0" />
                <xs:element name="originTimeStamp" type="dateTimeType" minOccurs="0" />
                <xs:element name="subscriberNumberNAI" type="subscriberNumberNAIType" minOccurs="0" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="offerServiceID" type="offerServiceIDType" minOccurs="0" />
                <xs:element name="attributeUpdateInformation" type="attributeUpdateInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="treeParameterSetUpdateInformation" type="treeParameterSetUpdateInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
            <xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="subscriberNumberAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="offerID" type="offerIDType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="offerIDAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="bundleID" type="bundleIDType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="bundleIDTypeAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <!-- set PredefinedOffer Response -->
    <xs:element name="setPredefinedOfferResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="subscriberNumber" type="subscriberNumberType" />
                <xs:element name="originTransactionID" type="originTransactionIDType" />
                <xs:element name="originOperatorID" type="originOperatorIDType" minOccurs="0" />
                <xs:element name="offerID" type="offerIDType" minOccurs="0" />
                <xs:element name="bundleID" type="bundleIDType" minOccurs="0" />
                <xs:element name="offerServiceID" type="offerServiceIDType" minOccurs="0" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="attributeInformation" type="attributeInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="treeParameterSetInformation" type="treeParameterSetInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="productOfferingName" type="productOfferingNameType" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- get PredefinedOffer -->
    <xs:element name="getPredefinedOffer">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="originNodeType" type="originNodeTypeType" minOccurs="0" />
                <xs:element name="originHostName" type="originHostNameType" minOccurs="0" />
                <xs:element name="originTransactionID" type="originTransactionIDType" minOccurs="0" />
                <xs:element name="originTimeStamp" type="dateTimeType" minOccurs="0" />
                <xs:element name="subscriberNumberNAI" type="subscriberNumberNAIType" minOccurs="0" />
                <xs:choice>
                    <xs:element name="offerSelection" type="offerSelectionType" maxOccurs="unbounded" />
                    <xs:element name="bundleID" type="bundleIDType" />
                </xs:choice>
                <xs:element name="offerServiceID" type="offerServiceIDType" minOccurs="0" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" maxOccurs="unbounded" />
            </xs:sequence>
            <xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="subscriberNumberAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <!-- get PredefinedOffer Response -->
    <xs:element name="getPredefinedOfferResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="subscriberNumber" type="subscriberNumberType" />
                <xs:element name="originTransactionID" type="originTransactionIDType" />
                <xs:element name="predefinedOfferValuesInformation" type="predefinedOfferValuesInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="bundlePredefinedOfferValuesInformation" type="bundlePredefinedOfferValuesInformationType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="negotiatedCapabilities" type="negotiatedCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="availableServerCapabilities" type="availableServerCapabilitiesType" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- simple type definition -->
    <xs:simpleType name="originNodeTypeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="EXT" />
            <xs:enumeration value="AIR" />
            <xs:enumeration value="ADM" />
            <xs:enumeration value="UGW" />
            <xs:enumeration value="IVR" />
            <xs:enumeration value="OGW" />
            <xs:enumeration value="SDP" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="originTransactionIDType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,20}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="subscriberNumberNAIType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="2" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="subscriberNumberType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,28}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="originOperatorIDType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Za-z0-9 ]{1,255}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="negotiatedCapabilitiesType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="**********" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="offerStateType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="99" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="offerProviderIDType">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="28" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="availableServerCapabilitiesType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="**********" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="flagType">
        <xs:restriction base="xs:boolean" />
    </xs:simpleType>
    <xs:simpleType name="fafNumberType">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="30" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ownerType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Subscriber" />
            <xs:enumeration value="Account" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="fafIndicatorType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageCounterIDType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="**********" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageCounterValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageCounterNominalValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageCounterMonetaryValueType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,12}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageCounterMonetaryNominalValueType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,12}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageThresholdIDType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="**********" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageThresholdValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageThresholdMonetaryValueType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,12}" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="usageThresholdSourceType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="3" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="closestExpiryValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="-9223372036854775807" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="closestAccessibleValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="-9223372036854775807" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="dedicatedAccountActiveValueType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="changedAmountType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="-9223372036854775807" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="changedExpiryDateType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="-65535" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="changedStartDateType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="-65535" />
            <xs:maxInclusive value="65535" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="balanceType">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="9223372036854775807" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="bundlePredefinedOfferValuesInformationType">
        <xs:sequence>
            <xs:element name="bundleID" type="bundleIDType" />
            <xs:element name="predefinedOfferValuesInformation" type="predefinedOfferValuesInformationType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
