<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:air="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:gsmhlr="http://schemas.ericsson.com/ma/CS/AIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/CS/AIR/">
<xs:include schemaLocation="air_common_types.xsd"/>
<xs:element name="subscriberNumber" type="subscriberNumberType"/>
<xs:element name="setRefill">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="originNodeType" type="originNodeTypeType"/>
<xs:element minOccurs="0" name="originHostName" type="originHostNameType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originTimeStamp" type="originTimeStampType"/>
<xs:element minOccurs="0" name="subscriberNumberNAI" type="subscriberNumberNAIType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="externalData1" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData2" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData3" type="externalDataType"/>
<xs:element minOccurs="0" name="externalData4" type="externalDataType"/>
<xs:element minOccurs="0" name="transactionType" type="transactionTypeType"/>
<xs:element minOccurs="0" name="transactionCode" type="transactionCodeType"/>
<xs:element minOccurs="0" name="messageCapabilityFlag" type="messageCapabilityFlagStructType"/>
<xs:element minOccurs="0" name="requestRefillAccountBeforeFlag" type="requestRefillAccountBeforeFlagType"/>
<xs:element minOccurs="0" name="requestRefillAccountAfterFlag" type="requestRefillAccountAfterFlagType"/>
<xs:element minOccurs="0" name="requestRefillDetailsFlag" type="requestRefillDetailsFlagType"/>
<xs:element minOccurs="0" name="requestSubDedicatedAccountDetailsFlag" type="requestSubDedicatedAccountDetailsFlagType"/>
<xs:element minOccurs="0" name="transactionAmount" type="transactionAmountType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="refillProfileID" type="refillProfileIDType"/>
<xs:element minOccurs="0" name="voucherActivationCode" type="voucherActivationCodeType"/>
<xs:element minOccurs="0" name="selectedOption" type="selectedOptionType"/>
<xs:element minOccurs="0" name="locationNumber" type="locationNumberType"/>
<xs:element minOccurs="0" name="locationNumberNAI" type="locationNumberNAIType"/>
<xs:element minOccurs="0" name="refillType" type="refillTypeType"/>
<xs:element minOccurs="0" name="cellIdentifier" type="cellIdentifierType"/>
<xs:element minOccurs="0" name="validateSubscriberLocation" type="validateSubscriberLocationType"/>
<xs:element minOccurs="0" name="treeDefinedField" type="treeDefinedFieldRequestType"/>
<xs:element minOccurs="0" name="requestAttributesFlag" type="requestAttributesFlagType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
</xs:sequence>
<xs:attribute name="subscriberNumber" type="subscriberNumberType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="subscriberNumberAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="setRefillResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="subscriberNumber" type="subscriberNumberType"/>
<xs:element minOccurs="0" name="originTransactionID" type="originTransactionIDType"/>
<xs:element minOccurs="0" name="originOperatorID" type="originOperatorIDType"/>
<xs:element minOccurs="0" name="masterAccountNumber" type="masterAccountNumberType"/>
<xs:element minOccurs="0" name="languageIDCurrent" type="languageIDCurrentType"/>
<xs:element minOccurs="0" name="promotionAnnouncementCode" type="promotionAnnouncementCodeType"/>
<xs:element minOccurs="0" name="voucherAgent" type="voucherAgentType"/>
<xs:element minOccurs="0" name="voucherSerialNumber" type="voucherSerialNumberType"/>
<xs:element minOccurs="0" name="voucherGroup" type="voucherGroupType"/>
<xs:element minOccurs="0" name="transactionCurrency" type="transactionCurrencyType"/>
<xs:element minOccurs="0" name="transactionAmount" type="transactionAmountType"/>
<xs:element minOccurs="0" name="transactionAmountConverted" type="transactionAmountConvertedType"/>
<xs:element minOccurs="0" name="currency1" type="currencyType"/>
<xs:element minOccurs="0" name="currency2" type="currencyType"/>
<xs:element minOccurs="0" name="refillInformation" type="refillInformationType"/>
<xs:element minOccurs="0" name="accountBeforeRefill" type="accountBeforeAfterRefillType"/>
<xs:element minOccurs="0" name="accountAfterRefill" type="accountBeforeAfterRefillType"/>
<xs:element minOccurs="0" name="refillFraudCount" type="refillFraudCountType"/>
<xs:element minOccurs="0" name="selectedOption" type="selectedOptionType"/>
<xs:element minOccurs="0" name="segmentationID" type="segmentationIDType"/>
<xs:element minOccurs="0" name="refillType" type="refillTypeType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit1" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="accountPrepaidEmptyLimit2" type="accountPrepaidEmptyLimitType"/>
<xs:element minOccurs="0" name="treeDefinedField" type="treeDefinedFieldResponseType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="negotiatedCapabilities" type="negotiatedCapabilitiesType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="availableServerCapabilities" type="availableServerCapabilitiesType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:complexType name="messageCapabilityFlagStructType">
<xs:sequence>
<xs:element minOccurs="0" name="promotionNotificationFlag" type="promotionNotificationFlagType"/>
<xs:element minOccurs="0" name="firstIVRCallSetFlag" type="firstIVRCallSetFlagType"/>
<xs:element minOccurs="0" name="accountActivationFlag" type="accountActivationFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="treeDefinedFieldRequestType">
<xs:sequence>
<xs:element name="treeDefinedFieldName" type="treeDefinedFieldNameRequestType"/>
<xs:element name="treeDefinedFieldType" type="treeDefinedFieldTypeRequestType"/>
<xs:element name="treeDefinedFieldValue" type="treeDefinedFieldValueType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="treeDefinedFieldResponseType">
<xs:sequence>
<xs:element name="treeDefinedFieldName" type="treeDefinedFieldNameResponseType"/>
<xs:element name="treeDefinedFieldType" type="treeDefinedFieldTypeResponseType"/>
<xs:element name="treeDefinedFieldValue" type="treeDefinedFieldValueType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="accountBeforeAfterRefillType">
<xs:sequence>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="serviceClassOriginal" type="serviceClassOriginalType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="accountFlags" type="accountFlagsType"/>
<xs:element minOccurs="0" name="promotionPlanID" type="promotionPlanIDType"/>
<xs:element minOccurs="0" name="serviceFeeExpiryDate" type="serviceFeeExpiryDateType"/>
<xs:element minOccurs="0" name="supervisionExpiryDate" type="supervisionExpiryDateType"/>
<xs:element minOccurs="0" name="creditClearanceDate" type="creditClearanceDateType"/>
<xs:element minOccurs="0" name="serviceRemovalDate" type="serviceRemovalDateType"/>
<xs:element minOccurs="0" name="accountValue1" type="accountValueType"/>
<xs:element minOccurs="0" name="accountValue2" type="accountValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageAccumulatorInformation" type="usageAccumulatorInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="serviceOfferings" type="serviceOfferingsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="communityID" type="communityIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="offerInformation" type="offerInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageAccumulatorInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="accumulatorID" type="accumulatorIDType"/>
<xs:element minOccurs="0" name="accumulatorValue" type="accumulatorValueType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="offerInformationType">
<xs:sequence>
<xs:element name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="startDateType"/>
<xs:element minOccurs="0" name="startDateTime" type="startDateTimeType"/>
<xs:element minOccurs="0" name="expiryDateTime" type="expiryDateTimeType"/>
<xs:element minOccurs="0" name="carryOverExpiryDateTime" type="dateTimeType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerType" type="offerTypeType"/>
<xs:element minOccurs="0" name="offerState" type="offerStateType"/>
<xs:element minOccurs="0" name="offerProviderID" type="offerProviderIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageCounterUsageThresholdInformation" type="usageCounterUsageThresholdInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountChangeInformation" type="dedicatedAccountChangeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountInformation" type="dedicatedAccountInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="attributeInformation" type="attributeInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="treeParameterSetInformation" type="treeParameterSetInformationType"/>
<xs:element minOccurs="0" name="productOfferingName" type="productOfferingNameType"/>
<xs:element minOccurs="0" name="offerServiceID" type="offerServiceIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountChangeInformation" type="subDedicatedAccountChangeInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountChangeInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="changedAmount1" type="changedAmountType"/>
<xs:element minOccurs="0" name="changedAmount2" type="changedAmountType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="changedExpiryDate" type="changedExpiryDateType"/>
<xs:element minOccurs="0" name="newExpiryDate" type="newExpiryDateType"/>
<xs:element minOccurs="0" name="clearedExpiryDate" type="clearedExpiryDateType"/>
<xs:element minOccurs="0" name="changedStartDate" type="changedStartDateType"/>
<xs:element minOccurs="0" name="newStartDate" type="newStartDateType"/>
<xs:element minOccurs="0" name="clearedStartDate" type="clearedStartDateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageCounterUsageThresholdInformationType">
<xs:sequence>
<xs:element name="usageCounterID" type="usageCounterIDType"/>
<xs:element minOccurs="0" name="usageCounterValue" type="usageCounterValueType"/>
<xs:element minOccurs="0" name="usageCounterNominalValue" type="usageCounterNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue1" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue1" type="usageCounterMonetaryNominalValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryValue2" type="usageCounterMonetaryValueType"/>
<xs:element minOccurs="0" name="usageCounterMonetaryNominalValue2" type="usageCounterMonetaryNominalValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageThresholdInformation" type="usageThresholdInformationType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="usageThresholdInformationType">
<xs:sequence>
<xs:element name="usageThresholdID" type="usageThresholdIDType"/>
<xs:element minOccurs="0" name="usageThresholdValue" type="usageThresholdValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue1" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdMonetaryValue2" type="usageThresholdMonetaryValueType"/>
<xs:element minOccurs="0" name="usageThresholdSource" type="usageThresholdSourceType"/>
<xs:element minOccurs="0" name="associatedPartyID" type="associatedPartyIDType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="serviceOfferingsType">
<xs:sequence>
<xs:element name="serviceOfferingID" type="serviceOfferingIDType"/>
<xs:element name="serviceOfferingActiveFlag" type="serviceOfferingActiveFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountInformationType">
<xs:sequence>
<xs:element name="dedicatedAccountID" type="dedicatedAccountIDType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="pamServiceID" type="pamServiceIDType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element minOccurs="0" name="productID" type="productIDType"/>
<xs:element minOccurs="0" name="closestExpiryDate" type="closestExpiryDateType"/>
<xs:element minOccurs="0" name="closestExpiryValue1" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestExpiryValue2" type="closestExpiryValueType"/>
<xs:element minOccurs="0" name="closestAccessibleDate" type="closestAccessibleDateType"/>
<xs:element minOccurs="0" name="closestAccessibleValue1" type="closestAccessibleValueType"/>
<xs:element minOccurs="0" name="closestAccessibleValue2" type="closestAccessibleValueType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountInformation" type="subDedicatedAccountInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue1" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountActiveValue2" type="dedicatedAccountActiveValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
<xs:element minOccurs="0" name="compositeDedicatedAccountFlag" type="compositeDedicatedAccountFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="subDedicatedAccountInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountValue1" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="dedicatedAccountValue2" type="dedicatedAccountValueType"/>
<xs:element minOccurs="0" name="startDate" type="startDateType"/>
<xs:element minOccurs="0" name="expiryDate" type="expiryDateType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="accountFlagsType">
<xs:sequence>
<xs:element minOccurs="0" name="activationStatusFlag" type="activationStatusFlagType"/>
<xs:element minOccurs="0" name="negativeBarringStatusFlag" type="negativeBarringStatusFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodWarningActiveFlag" type="supervisionPeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodWarningActiveFlag" type="serviceFeePeriodWarningActiveFlagType"/>
<xs:element minOccurs="0" name="supervisionPeriodExpiryFlag" type="supervisionPeriodExpiryFlagType"/>
<xs:element minOccurs="0" name="serviceFeePeriodExpiryFlag" type="serviceFeePeriodExpiryFlagType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="refillInformationType">
<xs:sequence>
<xs:element name="refillValueTotal" type="refillValueType"/>
<xs:element minOccurs="0" name="refillValuePromotion" type="refillValueType"/>
<xs:element minOccurs="0" name="serviceClassCurrent" type="serviceClassCurrentType"/>
<xs:element minOccurs="0" name="serviceClassTemporaryExpiryDate" type="serviceClassTemporaryExpiryDateType"/>
<xs:element minOccurs="0" name="promotionPlanProgressed" type="promotionPlanProgressedType"/>
<xs:element minOccurs="0" name="supervisionDaysSurplus" type="supervisionDaysSurplusType"/>
<xs:element minOccurs="0" name="serviceFeeDaysSurplus" type="serviceFeeDaysSurplusType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue1" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillAccumulatedValue2" type="promotionRefillAccumulatedValueType"/>
<xs:element minOccurs="0" name="promotionRefillCounter" type="promotionRefillCounterType"/>
<xs:element minOccurs="0" name="progressionRefillValue1" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillValue2" type="progressionRefillValueType"/>
<xs:element minOccurs="0" name="progressionRefillCounter" type="progressionRefillCounterType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="refillValueType">
<xs:sequence>
<xs:element name="refillAmount1" type="refillAmountType"/>
<xs:element minOccurs="0" name="refillAmount2" type="refillAmountType"/>
<xs:element minOccurs="0" name="supervisionDaysExtended" type="DaysExtendedType"/>
<xs:element minOccurs="0" name="serviceFeeDaysExtended" type="DaysExtendedType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dedicatedAccountRefillInformation" type="dedicatedAccountRefillInformationType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="usageAccumulatorInformation" type="usageAccumulatorInformationType"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="dedicatedAccountRefillInformationType">
<xs:sequence>
<xs:element minOccurs="0" name="dedicatedAccountID" type="refillAmountType"/>
<xs:element minOccurs="0" name="refillAmount1" type="refillAmountType"/>
<xs:element minOccurs="0" name="refillAmount2" type="refillAmountType"/>
<xs:element minOccurs="0" name="expiryDateExtended" type="expiryDateExtendedType"/>
<xs:element minOccurs="0" name="clearedValue1" type="clearedValueType"/>
<xs:element minOccurs="0" name="clearedValue2" type="clearedValueType"/>
<xs:element minOccurs="0" name="offerID" type="offerIDType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="subDedicatedAccountRefillInformation" type="subDedicatedAccountRefillInformationType"/>
<xs:element minOccurs="0" name="dedicatedAccountUnitType" type="dedicatedAccountUnitTypeType"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="originNodeTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="EXT"/>
<xs:enumeration value="AIR"/>
<xs:enumeration value="ADM"/>
<xs:enumeration value="UGW"/>
<xs:enumeration value="IVR"/>
<xs:enumeration value="OGW"/>
<xs:enumeration value="SDP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTransactionIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originTimeStampType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="subscriberNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="subscriberNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="originOperatorIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,255}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="externalDataType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="128"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionTypeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="firstIVRCallSetFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="accountActivationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestRefillAccountBeforeFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestRefillAccountAfterFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestRefillDetailsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="requestSubDedicatedAccountDetailsFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="transactionAmountType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionCurrencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillProfileIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="voucherActivationCodeType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{8,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="selectedOptionType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="locationNumberType">
<xs:restriction base="xs:integer">
<xs:pattern value="[0-9]{1,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="locationNumberNAIType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="2"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="refillTypeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="cellIdentifierType">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="19"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="validateSubscriberLocationType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="treeDefinedFieldNameRequestType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeDefinedFieldTypeRequestType">
<xs:restriction base="xs:string">
<xs:enumeration value="Boolean"/>
<xs:enumeration value="String"/>
<xs:enumeration value="Long"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeDefinedFieldValueType">
<xs:restriction base="xs:string"/>
</xs:simpleType>
<xs:simpleType name="treeDefinedFieldNameResponseType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,200}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="treeDefinedFieldTypeResponseType">
<xs:restriction base="xs:string">
<xs:enumeration value="Boolean"/>
<xs:enumeration value="String"/>
<xs:enumeration value="Long"/>
<xs:enumeration value="Amount"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="requestAttributesFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="masterAccountNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="languageIDCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="4"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionAnnouncementCodeType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="voucherAgentType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,8}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="transactionAmountConvertedType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="currencyType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DaysExtendedType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="expiryDateExtendedType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassTemporaryExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionPlanProgressedType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionDaysSurplusType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeDaysSurplusType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionRefillAccumulatedValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="promotionRefillCounterType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillCounterType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="progressionRefillValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="activationStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="negativeBarringStatusFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodWarningActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="supervisionPeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceFeePeriodExpiryFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="serviceOfferingActiveFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="promotionPlanIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceFeeExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="supervisionExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="creditClearanceDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="serviceRemovalDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="accountValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestAccessibleDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="closestExpiryValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestAccessibleValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountActiveValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="compositeDedicatedAccountFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="segmentationIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="negotiatedCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="availableServerCapabilitiesType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accountPrepaidEmptyLimitType">
<xs:restriction base="xs:string">
<xs:pattern value="[-]{0,1}[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="accumulatorValueType">
<xs:restriction base="xs:int">
<xs:minInclusive value="-**********"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceOfferingIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="31"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="communityIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="9999999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="closestExpiryDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="startDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="offerStateType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="99"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="offerProviderIDType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,28}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedExpiryDateType">
<xs:restriction base="xs:int">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="changedStartDateType">
<xs:restriction base="xs:int">
<xs:minInclusive value="-65535"/>
<xs:maxInclusive value="65535"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="clearedExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="newExpiryDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="newStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="clearedStartDateType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="changedAmountType">
<xs:restriction base="xs:long">
<xs:minInclusive value="-9223372036854775807"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterNominalValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdIDType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="**********"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdMonetaryValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdValueType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageThresholdSourceType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="1"/>
<xs:maxInclusive value="3"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="usageCounterMonetaryNominalValueType">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,12}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassOriginalType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="serviceClassCurrentType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9999"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="expiryDateTimeType">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:simpleType name="promotionNotificationFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="refillFraudCountType">
<xs:restriction base="xs:integer">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="255"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="voucherSerialNumberType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9]{8,20}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="voucherGroupType">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Za-z0-9 ]{1,4}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="dedicatedAccountRealMoneyFlagType">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="dedicatedReservationType">
<xs:restriction base="xs:long">
<xs:minInclusive value="0"/>
<xs:maxInclusive value="9223372036854775807"/>
</xs:restriction>
</xs:simpleType>
</xs:schema>
