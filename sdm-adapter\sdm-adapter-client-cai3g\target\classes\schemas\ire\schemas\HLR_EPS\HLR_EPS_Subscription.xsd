<!-- Home Location Register, Home Subscriber Server, EPS, Subscription -->
<xs:schema xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/eda/cudb/HlrEps/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
		   xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.ericsson.com/eda/cudb/HlrEps/"
		   jaxb:version="2.0"
		   targetNamespace="http://schemas.ericsson.com/eda/cudb/HlrEps/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/hlreps_types.xsd"/>
	<xs:element name="msisdn" type="msisdnType"/>
	<xs:element name="imsi" type="imsiType"/>
	<xs:element name="type" type="typeType" default="ALL"/>
	<!-- CreateSubscription MOId: msisdn and imsi MOType: Subscription@http://schemas.ericsson.com/eda/cudb/HlrEps/ -->
	<xs:element name="CreateSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="profile" type="profileType" minOccurs="0"/>
				<xs:element name="rid" type="ridType" minOccurs="0"/>
				<xs:element name="epsProfileId" type="epsProfileIdType" minOccurs="0"/>
				<xs:element name="epsTenantId" type="epsTenantIdType" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imsi">
			<xs:selector xpath="./x:imsi"/>
			<xs:field xpath="."/>
		</xs:key>
		<xs:key name="key_msisdn">
			<xs:selector xpath="./x:msisdn"/>
			<xs:field xpath="."/>
		</xs:key>
		<xs:keyref name="keyref_imsi" refer="key_imsi">
			<xs:selector xpath="."/>
			<xs:field xpath="@imsi"/>
		</xs:keyref>
		<xs:keyref name="keyref_msisdn" refer="key_msisdn">
			<xs:selector xpath="."/>
			<xs:field xpath="@msisdn"/>
		</xs:keyref>
	</xs:element>
	<!-- DeleteSubscription MOId: msisdn MOType: Subscription@http://schemas.ericsson.com/eda/cudb/HlrEps/ -->
	<xs:element name="DeleteSubscription">
		<xs:complexType>
			<xs:sequence/>
		</xs:complexType>
	</xs:element>
</xs:schema>
