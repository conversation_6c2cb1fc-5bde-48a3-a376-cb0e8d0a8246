<?xml version="1.0" encoding="UTF-8" standalone="no"?><!-- edited with XMLSpy v2007 rel. 3 (http://www.altova.com) by <PERSON><PERSON> (<PERSON> (China) Communications Company Ltd) --><xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/" xmlns:gsmhlr="http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/">
	<xs:element name="apn" type="APNProfileAPNType"/>
	<xs:element name="eqosid" type="QoSProfileQoSIDType"/>
	<xs:element name="imsi" type="IMSIType"/>
	<xs:element name="nimsi" type="IMSIType"/>
	<xs:element name="msisdn" type="MSISDNType"/>
	<xs:element name="PrimaryHLRId" type="primaryhlridType"/>	
	
	<!-- create subscription -->
	<xs:element name="createSubscription" type="CreateSubscriptionType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating HLR13.5 subscription.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="msisdnKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@msisdn"/>
		</xs:key>
		<xs:keyref name="msisdnKeyRef_Create" refer="msisdnKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="msisdn"/>
		</xs:keyref>
		<xs:key name="imsiKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@imsi"/>
		</xs:key>
		<xs:keyref name="imsiKeyRef_Create" refer="imsiKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="imsi"/>
		</xs:keyref>
	</xs:element>
	<xs:complexType name="CreateSubscriptionType">
		<xs:sequence>
			<xs:element name="msisdn" type="MSISDNType"/>
			<xs:element name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="rid" type="RIDType"/>
			<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
			<xs:element minOccurs="0" name="profileId" type="ProfileIdType"/>
			<xs:element minOccurs="0" name="pdpcp" type="GPRSProfileIdType"/>
			<xs:element minOccurs="0" name="csp" type="CamelProfileIdType"/>
			<xs:element minOccurs="0" name="gsap" type="GsmSCFProfileIdType"/>
			<xs:element maxOccurs="16" minOccurs="0" name="amsisdn" type="AMSISDNType">
				<xs:key name="amsisdnKey_CREATE">
					<xs:selector xpath="."/>
					<xs:field xpath="@amsisdn"/>
				</xs:key>
				<xs:keyref name="amsisdnKeyRef_CREATE" refer="gsmhlr:amsisdnKey_CREATE">
					<xs:selector xpath="."/>
					<xs:field xpath="gsmhlr:amsisdn"/>
				</xs:keyref>
			</xs:element>
			<xs:element minOccurs="0" name="camel" type="CamelType"/>
			<!-- mmintMo type changed from MobManInTriggeringType to CreateMobManInTriggeringType due to HR81881-->
			<xs:element maxOccurs="2" minOccurs="0" name="mmintMo" type="CreateMobManInTriggeringType"/>
			<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" type="ClosedUserGroupType"/>
			<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionType"/>
			<xs:element minOccurs="0" name="removeReferences" type="xs:boolean"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gprs" type="GprsType"/>
			<xs:element minOccurs="0" name="nam" type="NamType"/>
			<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bac" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boc" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfu" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfb" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnrc" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnry" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="dcf" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="spn" type="CfSpnSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfs" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="ccfs" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="allss" type="AllSupplementaryServiceType"/>
			<xs:element default="0" minOccurs="0" name="acc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="aoc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="bs21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs23" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs24" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs25" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs26" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs2f" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="bs2g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs31" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs32" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs33" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs34" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs3f" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="bs3g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="capl" type="ZeroFifteenType"/>
			<xs:element default="0" minOccurs="0" name="cat" type="catType"/>
			<xs:element default="0" minOccurs="0" name="clip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="clir" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colr" type="BinaryType"/>
			<xs:element minOccurs="0" name="dbsg" type="dbsgType"/>
			<xs:element default="0" minOccurs="0" name="hold" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ici" type="BinaryType"/>
			<xs:element minOccurs="0" name="mpty" type="BinaryType"/>
			<xs:element minOccurs="0" name="oba" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obi" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="obo" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="obopre" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obopri" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obrf" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="obssm" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obr" type="obrType"/>
			<xs:element default="0" minOccurs="0" name="obzi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obzo" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="oin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="oick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="osb1" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb2" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb3" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb4" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element default="0" minOccurs="0" name="pai" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="regser" type="regserType"/>
			<xs:element minOccurs="0" name="pici" type="piciType"/>
			<xs:element minOccurs="0" name="pici2" type="piciType"/>
			<xs:element minOccurs="0" name="pici3" type="piciType"/>
			<xs:element default="0000" minOccurs="0" name="pwd" type="pwdType"/>
			<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfrc" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="socfry" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfu" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclir" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="socolp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="sodcf" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="soplcs" type="BinaryType"/>
			<xs:element minOccurs="0" name="sosdcf" type="sosdcfType"/>
			<xs:element default="0" minOccurs="0" name="stype" type="stypeType"/>
			<xs:element default="0" minOccurs="0" name="tick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="tin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts11" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts61" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts62" type="BinaryType"/>
			<xs:element minOccurs="0" name="locationData" type="LocationDataType"/>
			<xs:element default="0" minOccurs="0" name="emlpp" type="BinaryType"/>
			<xs:element default="4" minOccurs="0" name="demlpp" type="ZeroSixType"/>
			<xs:element default="4" minOccurs="0" name="memlpp" type="ZeroSixType"/>
			<xs:element default="0" minOccurs="0" name="tsmo" type="BinaryType"/>
			<!--<xs:element name="sendMemlppFirst" type="BinaryType" minOccurs="0" /> -->
			<xs:element default="0" minOccurs="0" name="rsa" type="rsaType"/>
			<xs:element minOccurs="0" name="obp" type="ZeroThreeType"/>
			<xs:element minOccurs="0" name="ect" type="BinaryType"/>
			<xs:element minOccurs="0" name="obct" type="ZeroFourType"/>
			<xs:element minOccurs="0" name="obdct" type="BinaryType"/>
			<xs:element minOccurs="0" name="obmct" type="BinaryType"/>
			<xs:element minOccurs="0" name="schar" type="scharType"/>
			<xs:element default="0" minOccurs="0" name="mmint" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="cug" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="red" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="crel" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="cunrl" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="plmno" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="univ" type="BinaryType"/>
			<xs:element minOccurs="0" name="servtl" type="BinaryType"/>
			<xs:element minOccurs="0" name="shplmn" type="shplmnType"/>
			<xs:element minOccurs="0" name="smshr1" type="smshrType"/>
			<xs:element minOccurs="0" name="smshr2" type="smshrType"/>
	        <xs:element minOccurs="0" name="mpid" type="mpidType"/><!-- new added parameter for monolithic -->
			<xs:element minOccurs="0" name="ste" type="BinaryType"/>
			<xs:element minOccurs="0" name="teardown" type="OneType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gmlca" type="GmlcAddressType"/>
			<xs:element minOccurs="0" name="gmlca12" type="GmlcAddType"/>
			<xs:element minOccurs="0" name="lcsd" type="LcsDataType"/>
			<xs:element minOccurs="0" name="steMo" type="SteType"/>
			<xs:element default="0" minOccurs="0" name="prbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="acr" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="rtca" type="BinaryType"/>
			<xs:element minOccurs="0" name="redmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mca" type="BinaryType"/>
			<xs:element minOccurs="0" name="smspam" type="BinaryType"/>
			<xs:element minOccurs="0" name="ist" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="istcso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istgso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istvso" type="BinaryType"/>
			<xs:element minOccurs="0" name="msim" type="BinaryType"/>
			
			
			
			<xs:element minOccurs="0" name="multiSim" type="MultiSimType"/>
			<xs:element minOccurs="0" name="smsSpam" type="SmsSpamType"/>
			<xs:element minOccurs="0" name="ard" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="imeisv" type="ImeisvType"/>
			<xs:element minOccurs="0" name="rdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="grdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="obcc" type="BinaryType"/>
			<xs:element minOccurs="0" name="mrdmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mderbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="ora" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="cbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cfnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="chnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clipnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clirnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cwnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ectnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="odbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tifcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ics" type="BinaryType"/>
			
			
			<!-- <xs:element name="" type="" minOccurs="0"/> -->
			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
            
            
		</xs:sequence>
		<xs:attribute name="msisdn" type="MSISDNType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="imsi" type="IMSIType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="imsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- set subscription -->
	<xs:element name="setSubscription" type="SetSubscriptionType">
		<xs:annotation>
			<xs:documentation>
				The attributes for setting HLR13.5 subscription.
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="SetSubscriptionType">
		<xs:sequence>
			<xs:element minOccurs="0" name="profileId" type="ProfileIdType"/>
			<xs:element minOccurs="0" name="pdpcp" type="GPRSProfileIdType"/>
			<xs:element minOccurs="0" name="csp" type="CamelProfileIdType"/>
			<xs:element minOccurs="0" name="gsap" type="GsmSCFProfileIdType"/>
			<xs:element maxOccurs="16" minOccurs="0" name="amsisdn" nillable="true" type="AMSISDNType">
				<xs:key name="amsisdnKey_SET">
					<xs:selector xpath="."/>
					<xs:field xpath="@amsisdn"/>
				</xs:key>
				<xs:keyref name="amsisdnKeyRef_SET" refer="gsmhlr:amsisdnKey_SET">
					<xs:selector xpath="."/>
					<xs:field xpath="gsmhlr:amsisdn"/>
				</xs:keyref>
			</xs:element>
			<xs:element minOccurs="0" name="camel" type="CamelType"/>
			<xs:element maxOccurs="2" minOccurs="0" name="mmintMo" nillable="true" type="MobManInTriggeringType"/>
			<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" nillable="true" type="ClosedUserGroupType"/>
			<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionType"/>
			<xs:element minOccurs="0" name="removeReferences" type="xs:boolean"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gprs" nillable="true" type="GprsType"/>
			<xs:element minOccurs="0" name="nam" type="NamType"/>
			<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bac" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boc" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfu" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfb" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnrc" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnry" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="dcf" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="spn" type="CfSpnSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfs" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="ccfs" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="allss" type="AllSupplementaryServiceType"/>
			<xs:element default="0" minOccurs="0" name="acc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="aoc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="bs21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs23" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs24" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs25" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs26" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs2f" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="bs2g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs31" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs32" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs33" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs34" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs3f" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="bs3g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="capl" type="ZeroFifteenType"/>
			<xs:element default="0" minOccurs="0" name="cat" type="catType"/>
			<xs:element default="0" minOccurs="0" name="clip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="clir" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colr" type="BinaryType"/>
			<xs:element minOccurs="0" name="dbsg" type="dbsgType"/>
			<xs:element default="0" minOccurs="0" name="hold" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ici" type="BinaryType"/>
			<xs:element minOccurs="0" name="mpty" type="BinaryType"/>
			<xs:element minOccurs="0" name="oba" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obi" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="obo" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="obopre" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obopri" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obrf" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="obssm" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obr" type="obrType"/>
			<xs:element default="0" minOccurs="0" name="obzi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obzo" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="oin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="oick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="osb1" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb2" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb3" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb4" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element default="0" minOccurs="0" name="pai" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="regser" type="regserType"/>
			<xs:element minOccurs="0" name="pici" type="piciType"/>
			<xs:element minOccurs="0" name="pici2" type="piciType"/>
			<xs:element minOccurs="0" name="pici3" type="piciType"/>
			<xs:element default="0000" minOccurs="0" name="pwd" type="pwdType"/>
			<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfrc" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="socfry" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfu" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclir" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="socolp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="sodcf" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="soplcs" type="BinaryType"/>
			<xs:element minOccurs="0" name="sosdcf" type="sosdcfType"/>
			<xs:element default="0" minOccurs="0" name="stype" type="stypeType"/>
			<xs:element default="0" minOccurs="0" name="tick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="tin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts11" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts61" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts62" type="BinaryType"/>
			<xs:element minOccurs="0" name="locationData" type="LocationDataType"/>
			<xs:element default="0" minOccurs="0" name="emlpp" type="BinaryType"/>
			<xs:element default="4" minOccurs="0" name="demlpp" type="ZeroSixType"/>
			<xs:element default="4" minOccurs="0" name="memlpp" type="ZeroSixType"/>
			<xs:element default="0" minOccurs="0" name="tsmo" type="BinaryType"/>
			<!--<xs:element name="sendMemlppFirst" type="BinaryType" minOccurs="0" /> -->
			<xs:element default="0" minOccurs="0" name="rsa" type="rsaType"/>
			<xs:element minOccurs="0" name="obp" type="ZeroThreeType"/>
			<xs:element minOccurs="0" name="ect" type="BinaryType"/>
			<xs:element minOccurs="0" name="obct" type="ZeroFourType"/>
			<xs:element minOccurs="0" name="obdct" type="BinaryType"/>
			<xs:element minOccurs="0" name="obmct" type="BinaryType"/>
			<xs:element minOccurs="0" name="schar" type="scharType"/>
			<xs:element default="0" minOccurs="0" name="mmint" type="BinaryType"/>
			<xs:element minOccurs="0" name="cug" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="red" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="crel" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="cunrl" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="plmno" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="univ" type="BinaryType"/>
			<xs:element minOccurs="0" name="servtl" type="BinaryType"/>
			<xs:element minOccurs="0" name="shplmn" type="shplmnType"/>
			<xs:element minOccurs="0" name="smshr1" type="smshrType"/>
			<xs:element minOccurs="0" name="smshr2" type="smshrType"/>
            <xs:element minOccurs="0" name="mpid" type="mpidType"/><!-- new added parameter for monolithic -->
			<xs:element minOccurs="0" name="ste" type="BinaryType"/>
			<xs:element minOccurs="0" name="teardown" type="OneType"/>
			
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gmlca" type="GmlcAddressType"/>
			<xs:element minOccurs="0" name="gmlca12" nillable="true" type="GmlcAddType"/>
			<xs:element minOccurs="0" name="lcsd" type="LcsDataType"/>
			<xs:element minOccurs="0" name="steMo" nillable="true" type="SteType"/>
			<xs:element default="0" minOccurs="0" name="prbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="acr" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="rtca" type="BinaryType"/>
			<xs:element minOccurs="0" name="redmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mca" type="BinaryType"/>
			<xs:element minOccurs="0" name="smspam" type="BinaryType"/>
			<xs:element minOccurs="0" name="ist" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="istcso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istgso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istvso" type="BinaryType"/>
			<xs:element minOccurs="0" name="msim" type="BinaryType"/>
			
			
			
			<xs:element minOccurs="0" name="multiSim" type="MultiSimType"/>
			<xs:element minOccurs="0" name="smsSpam" type="SmsSpamType"/>
			<xs:element minOccurs="0" name="ard" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="imeisv" type="ImeisvType"/>
			<xs:element minOccurs="0" name="rdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="grdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="obcc" type="BinaryType"/>
			<xs:element default="1" minOccurs="0" name="mrdmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mderbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="ora" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="cbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cfnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="chnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clipnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clirnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cwnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ectnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="odbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tifcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ics" type="BinaryType"/>
			<xs:element minOccurs="0" name="pbxLine" type="pbxLineType"/>

			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
			
			

			
			

			</xs:sequence>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="imsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="imsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- get subscription -->
	<xs:element name="getSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" name="SubscriberData"/>
				<xs:element minOccurs="0" name="SupplementaryServiceData"/>
				<xs:element minOccurs="0" name="GprsServiceData"/>
				<xs:element minOccurs="0" name="AMSISDNServiceData"/>
				<xs:element minOccurs="0" name="LocationServicesData"/>
				<xs:element minOccurs="0" name="LocationServicesAddressData"/>
				<xs:element minOccurs="0" name="MMINTServiceData"/>
				<xs:element minOccurs="0" name="ClosedUserGroupServiceData"/>
				<xs:element minOccurs="0" name="SpatialTriggersServiceData"/>
				<xs:element minOccurs="0" name="SpamSMSData"/>
				<xs:element minOccurs="0" name="camel"/>
				
				
			    
			</xs:sequence>
			<xs:attribute name="msisdn" type="MSISDNType" use="optional"/>
			<xs:attribute name="imsi" type="IMSIType" use="optional"/>
		</xs:complexType>
	</xs:element>
	<xs:element name="getResponseSubscription" type="GetSubscriptionType">
		<xs:annotation>
			<xs:documentation>
				The attributes for get HLR subscription response.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="msisdnKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="@msisdn"/>
		</xs:key>
		<xs:keyref name="msisdnKeyRef_Get" refer="msisdnKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="msisdn"/>
		</xs:keyref>
		<xs:key name="imsiKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="@imsi"/>
		</xs:key>
		<xs:keyref name="imsiKeyRef_Get" refer="imsiKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="imsi"/>
		</xs:keyref>
		<xs:key name="amsisdnKey_Get">
			<xs:selector xpath="./amsisdn"/>
			<xs:field xpath="@amsisdn"/>
		</xs:key>
		<xs:keyref name="amsisdnKeyRef_Get" refer="amsisdnKey_Get">
			<xs:selector xpath="./amsisdn"/>
			<xs:field xpath="amsisdn"/>
		</xs:keyref>
	</xs:element>
	<xs:complexType name="GetSubscriptionType">
		<xs:sequence>
			<xs:element name="msisdn" type="MSISDNType"/>
			<xs:element name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="msisdnstate" type="xs:string"/>
			<xs:element minOccurs="0" name="rid" type="RIDType"/>
			<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
			<xs:element minOccurs="0" name="authd" type="xs:string"/>
			<xs:element minOccurs="0" name="profileId" type="ProfileIdType"/>
			<xs:element minOccurs="0" name="pdpcp" type="GPRSProfileIdType"/>
			<xs:element minOccurs="0" name="csp" type="CamelProfileIdType"/>
			<xs:element minOccurs="0" name="gsap" type="GsmSCFProfileIdType"/>
			<xs:element maxOccurs="16" minOccurs="0" name="amsisdn" type="AMSISDNType"/>
			<xs:element minOccurs="0" name="camel" type="CamelType"/>
			<xs:element maxOccurs="2" minOccurs="0" name="mmintMo" type="MobManInTriggeringType"/>
			<xs:element maxOccurs="10" minOccurs="0" name="closedUserGroup" type="ClosedUserGroupType"/>
			<xs:element minOccurs="0" name="cugBsgOption" type="CugBsgOptionType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gprs" type="GprsGetType"/>
			<xs:element minOccurs="0" name="nam" type="NamType"/>
			<xs:element minOccurs="0" name="baic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="baoc" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boic" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="bicro" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="boiexh" type="BarringSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfu" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfb" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnrc" type="CfCfuCfbCfnrcSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="cfnry" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="dcf" type="CfCfnryDcfSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="spn" type="CfSpnSupplementaryServiceType"/>
			<xs:element minOccurs="0" name="caw" type="CawSupplementaryServiceType"/>
			<xs:element default="0" minOccurs="0" name="acc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="aoc" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="bs21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs23" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs24" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs25" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs26" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs2f" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="bs2g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs31" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs32" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs33" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs34" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bs3f" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="bs3g" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="capl" type="ZeroFifteenType"/>
			<xs:element default="0" minOccurs="0" name="cat" type="catType"/>
			<xs:element default="0" minOccurs="0" name="clip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="clir" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="colr" type="BinaryType"/>
			<xs:element minOccurs="0" name="dbsg" type="dbsgType"/>
			<xs:element default="0" minOccurs="0" name="hold" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ici" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="mpty" type="BinaryType"/>
			<xs:element minOccurs="0" name="oba" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obi" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="obo" type="ZeroFourType"/>
			<xs:element default="0" minOccurs="0" name="obopre" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obopri" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obrf" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="obssm" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obr" type="obrType"/>
			<xs:element default="0" minOccurs="0" name="obzi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="obzo" type="ZeroFiveType"/>
			<xs:element default="0" minOccurs="0" name="oin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="oick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="osb1" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb2" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb3" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osb4" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element default="0" minOccurs="0" name="pai" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="regser" type="regserType"/>
			<xs:element minOccurs="0" name="pici" type="piciType"/>
			<xs:element minOccurs="0" name="pici2" type="piciType"/>
			<xs:element minOccurs="0" name="pici3" type="piciType"/>
			<xs:element default="0000" minOccurs="0" name="pwd" type="pwdType"/>
			<xs:element default="0" minOccurs="0" name="socb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfb" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfrc" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="socfry" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="socfu" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclip" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="soclir" type="ZeroTwoType"/>
			<xs:element default="0" minOccurs="0" name="socolp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="sodcf" type="ZeroThreeType"/>
			<xs:element default="0" minOccurs="0" name="soplcs" type="BinaryType"/>
			<xs:element minOccurs="0" name="sosdcf" type="sosdcfType"/>
			<xs:element default="0" minOccurs="0" name="stype" type="stypeType"/>
			<xs:element default="0" minOccurs="0" name="tick" type="ZeroNineNineNineType"/>
			<xs:element default="0" minOccurs="0" name="tin" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts11" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts21" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts22" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts61" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ts62" type="BinaryType"/>
			<xs:element minOccurs="0" name="locationData" type="LocationDataType"/>
			<xs:element minOccurs="0" name="vlrData" type="vlrDataType"/>
			<xs:element default="0" minOccurs="0" name="emlpp" type="BinaryType"/>
			<xs:element default="4" minOccurs="0" name="demlpp" type="ZeroSixType"/>
			<xs:element default="4" minOccurs="0" name="memlpp" type="ZeroSixType"/>
			<xs:element default="0" minOccurs="0" name="tsmo" type="BinaryType"/>
			<!--<xs:element name="sendMemlppFirst" type="BinaryType" minOccurs="0" /> -->
			<xs:element default="0" minOccurs="0" name="rsa" type="rsaType"/>
			<xs:element minOccurs="0" name="obp" type="ZeroThreeType"/>
			<xs:element minOccurs="0" name="ect" type="BinaryType"/>
			<xs:element minOccurs="0" name="obct" type="ZeroFourType"/>
			<xs:element minOccurs="0" name="obdct" type="BinaryType"/>
			<xs:element minOccurs="0" name="obmct" type="BinaryType"/>
			<xs:element minOccurs="0" name="schar" type="scharType"/>
			<xs:element default="0" minOccurs="0" name="ocsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="tcsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="gprcsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="osmcsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="mcsi" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsi" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="vtcsi" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="mmint" type="BinaryType"/>
			<xs:element minOccurs="0" name="cug" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="tsd1" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="red" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="asl" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="bsl" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="crel" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="cunrl" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="plmno" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="ttp" type="BinaryType"/>
			<xs:element default="0" minOccurs="0" name="univ" type="BinaryType"/>
			<xs:element minOccurs="0" name="servtl" type="BinaryType"/>
			<xs:element minOccurs="0" name="shplmn" type="shplmnType"/>
			<xs:element minOccurs="0" name="smshr1" type="smshrType"/>
			<xs:element minOccurs="0" name="smshr2" type="smshrType"/>
            <xs:element minOccurs="0" name="mpid" type="mpidType"/><!-- new added parameter for monolithic -->
			<xs:element minOccurs="0" name="ste" type="BinaryType"/>
			<xs:element minOccurs="0" name="teardown" type="OneType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gmlca" type="GmlcAddressType"/>
			<xs:element minOccurs="0" name="gmlca12" type="GmlcAddType"/>
			<xs:element minOccurs="0" name="lcsd" type="LcsDataType"/>
			<xs:element minOccurs="0" name="steMo" type="SteType"/>
			<xs:element default="0" minOccurs="0" name="prbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="acr" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="rtca" type="BinaryType"/>
			<xs:element minOccurs="0" name="redmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mca" type="BinaryType"/>
			<xs:element minOccurs="0" name="smspam" type="BinaryType"/>
			<xs:element minOccurs="0" name="ist" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="istcso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istgso" type="BinaryType"/>
			<xs:element minOccurs="0" name="istvso" type="BinaryType"/>
			<xs:element minOccurs="0" name="msim" type="BinaryType"/>
			
			
			
			<xs:element minOccurs="0" name="multiSim" type="MultiSimType"/>
			<xs:element minOccurs="0" name="smsSpam" type="SmsSpamTypeResp"/>
			<xs:element minOccurs="0" name="ard" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="imeisv" type="ImeisvType"/>
			<xs:element minOccurs="0" name="rdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="grdp" type="ZeroThertytwoType"/>
			<xs:element minOccurs="0" name="obcc" type="BinaryType"/>
			<xs:element minOccurs="0" name="mrdpid" type="mrdpidType"/>
			<xs:element default="1" minOccurs="0" name="mrdmch" type="OneTwoType"/>
			<xs:element minOccurs="0" name="mderbt" type="BinaryType"/>
			<xs:element minOccurs="0" name="ora" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="cbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cfnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="chnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clipnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="clirnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="cwnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ectnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="odbnf" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tifcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsinf" type="BinaryType"/>
			<xs:element minOccurs="0" name="dcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="gprscsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ocsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="tsmcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="vtcsist" type="BinaryType"/>
			<xs:element minOccurs="0" name="ics" type="BinaryType"/>

			 
             
			

			
			

		</xs:sequence>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="imsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="imsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- delete subscription -->
	<xs:element name="deleteSubscription" type="DeleteSubscriptionType">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting HLR13.5 subscription
				response.
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="DeleteSubscriptionType">
		<xs:sequence>
			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ImsiChangeover -->
	<!-- Create ImsiChangeover -->
	<xs:element name="createImsiChangeover" type="CreateImsiChangeoverType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating HLR13.5 ImsiChangeover.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="nimsiKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@nimsi"/>
		</xs:key>
		<xs:keyref name="nimsiKeyRef_Create" refer="gsmhlr:nimsiKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="gsmhlr:nimsi"/>
		</xs:keyref>
	</xs:element>
	<xs:complexType name="CreateImsiChangeoverType">
		<xs:all>
			<xs:element minOccurs="0" name="msisdn" type="MSISDNType"/>
			<xs:element name="nimsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="date" type="dateType"/>
			<xs:element minOccurs="0" name="deleteOldRef" type="xs:boolean"/>
			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
		</xs:all>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="nimsi" type="IMSIType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nimsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="imsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="imsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- set ImsiChangeover -->
	<xs:element name="setImsiChangeover" type="SetImsiChangeoverType">
		<xs:annotation>
			<xs:documentation>
				The attributes for Setting HLR13.5 ImsiChangeover.
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="SetImsiChangeoverType">
		<xs:all>
			<xs:element minOccurs="0" name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="date" type="dateType"/>
			<xs:element minOccurs="0" name="deleteOldRef" type="xs:boolean"/>
			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
		</xs:all>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="nimsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nimsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- Get ImsiChangeover -->
	<xs:element name="getResponseImsiChangeover" type="GetImsiChangeoverType">
		<xs:annotation>
			<xs:documentation>
				The attributes for getting HLR13.5 ImsiChangeover.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="msisdnKey_imch_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="@msisdn"/>
		</xs:key>
		<xs:keyref name="msisdnKeyRef_imch_Get" refer="msisdnKey_imch_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="msisdn"/>
		</xs:keyref>
		<xs:key name="nimsiKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="@nimsi"/>
		</xs:key>
		<xs:keyref name="nimsiKeyRef_Get" refer="nimsiKey_Get">
			<xs:selector xpath="."/>
			<xs:field xpath="nimsi"/>
		</xs:keyref>
	</xs:element>
	<xs:complexType name="GetImsiChangeoverType">
		<xs:all>
			<xs:element minOccurs="0" name="msisdn" type="MSISDNType"/>
			<xs:element minOccurs="0" name="nimsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="date" type="dateType"/>
			<xs:element minOccurs="0" name="state" type="ZeroThreeType"/>
		</xs:all>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="nimsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nimsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- delete ImsiChangeover -->
	<xs:element name="deleteImsiChangeover" type="DeleteImsiChangeoverType">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting HLR13.5 ImsiChangeover.
			</xs:documentation>
		</xs:annotation>
		<xs:key name="msisdnKey_imch_Delete">
			<xs:selector xpath="."/>
			<xs:field xpath="@msisdn"/>
		</xs:key>
		<xs:keyref name="msisdnKeyRef_imch_Delete" refer="msisdnKey_imch_Delete">
			<xs:selector xpath="."/>
			<xs:field xpath="msisdn"/>
		</xs:keyref>
		<xs:key name="nimsiKey_Delete">
			<xs:selector xpath="."/>
			<xs:field xpath="@nimsi"/>
		</xs:key>
		<xs:keyref name="nimsiKeyRef_Delete" refer="nimsiKey_Delete">
			<xs:selector xpath="."/>
			<xs:field xpath="nimsi"/>
		</xs:keyref>
	</xs:element>
	<xs:complexType name="DeleteImsiChangeoverType">
		<xs:all>
			<xs:element minOccurs="0" name="msisdn" type="MSISDNType"/>
			<xs:element minOccurs="0" name="nimsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="doLock" type="xs:boolean"/>
		</xs:all>
		<xs:attribute name="msisdn" type="MSISDNType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="msisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="nimsi" type="IMSIType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nimsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- simple type definition -->
	<xs:simpleType name="NUMBERType">
		<xs:annotation>
			<xs:documentation>
				the type definition for fanumber
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="MSISDNType">
		<xs:annotation>
			<xs:documentation>
				the type definition for MSISDN
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{5,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IMSIType">
		<xs:annotation>
			<xs:documentation>
				the type definition for IMSI
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{6,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="RIDType">
		<xs:annotation>
			<xs:documentation>
				the type definition for RID
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="63"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ImeisvType">
		<xs:annotation>
			<xs:documentation>
				International Mobile Equipment Identity and Software
				Version
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9F]{16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pdpchType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="(1[0-5]|[0-9])(-(40[0-8][0-9]|409[0-5]|[0-3]?[0-9]{1,3}))?"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="PdpidType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="NA"/>
					<xs:enumeration value="ALL"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:annotation>
					<xs:documentation>PDP context identifier</xs:documentation>
				</xs:annotation>
				<xs:restriction base="xs:string">
					<xs:pattern value="50|[1-4][0-9]|[1-9](&amp;(50|[1-4][0-9]|[1-9])){0,}"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="mrdpidType">
		<xs:restriction base="xs:string">
			<xs:pattern value="([1-9]|1[0-5])-(3[0-2]|[1-2][0-9]|[1-9])|3[0-2]|[1-2][0-9]|[0-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dateType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BinaryType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroTwoType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroThreeType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroFourType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroFiveType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroSixType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="6"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroThertytwoType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroTwoFiveFiveType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroNineNineNineType">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="999"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneTwoType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneThreeType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneFourType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneFiveType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneNineType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OneThertyoneType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="31"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActivationType">
		<xs:restriction base="BinaryType"/>
	</xs:simpleType>
	<xs:simpleType name="ProvisionType">
		<xs:restriction base="BinaryType"/>
	</xs:simpleType>
	<xs:simpleType name="CugAccessibilityType">
		<xs:restriction base="ZeroThreeType"/>
	</xs:simpleType>
	<xs:simpleType name="EaddAttrType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{3,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="piciType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(25[0-5]|2[0-4][0-9]|[1]{0,1}[0-9]{1,2})(-[012]){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pwdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="sosdcfType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="stypeType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="127"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="vlrDataType">
		<xs:restriction base="xs:string">
			<xs:pattern value="UNKNOWN|RESTRICTED|BARRED|[3-4]-[0-9]{1,20}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="rsaType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="4096"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="scharType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(1[0-5]|[0-9])(-(40[0-8][0-9]|409[0-5]|[0-3]?[0-9]{1,3}))?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="GresType">
		<xs:restriction base="xs:unsignedByte">
			<xs:enumeration value="0"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ServtAttrType">
		<xs:restriction base="xs:string">
			<xs:pattern value="1[01][0-9]|12[0-7]|20|[1789][0-9]|6[4-9]|[0-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ZeroFifteenType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="15"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="catType">
		<xs:restriction base="xs:string">
			<xs:pattern value="22[4-9]|25[0-4]|2[34][0-9]|1[01235]|[0-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dbsgType">
		<xs:restriction base="xs:unsignedByte">
			<xs:enumeration value="1"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="5"/>
			<xs:enumeration value="6"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="obrType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="99"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ofaType">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="511"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="regserType">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="65534"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="shplmnType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="31"/>
		</xs:restriction>
	</xs:simpleType>
    <xs:simpleType name="mpidType">
        <xs:restriction base="xs:unsignedShort">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="65535"/>
        </xs:restriction>
    </xs:simpleType>
	<xs:simpleType name="smshrType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(12[0-8]|1[0-1][0-9]|[0-9]?[0-9])(-(3[0-2]|[1-2][0-9]|[0-9]))?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="gsaType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{3,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="bcType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(6553[0-4]|655[0-2][0-9]\d|65[0-4](\d){2}|6[0-4](\d){3}|[1-5](\d){4}|[0-9](\d){1,3}|[012389])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dialnumType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0134]-[0-9*#abcABC]{1,15}(&amp;[0134]-[0-9*#abcABC]{1,15}){0,}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="iType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="N"/>
			<xs:enumeration value="Y"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="bsType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TS11"/>
			<xs:enumeration value="TS21"/>
			<xs:enumeration value="TS22"/>			
			<xs:enumeration value="TS61"/>
			<xs:enumeration value="TS62"/>
			<xs:enumeration value="TSD1"/>
			<xs:enumeration value="BS21"/>
			<xs:enumeration value="BS22"/>
			<xs:enumeration value="BS23"/>
			<xs:enumeration value="BS24"/>
			<xs:enumeration value="BS25"/>
			<xs:enumeration value="BS26"/>
			<xs:enumeration value="BS2G"/>
			<xs:enumeration value="BS31"/>
			<xs:enumeration value="BS32"/>
			<xs:enumeration value="BS33"/>
			<xs:enumeration value="BS34"/>
			<xs:enumeration value="BS3G"/>
			<xs:enumeration value="ALL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="bsgType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TS10"/>
			<xs:enumeration value="TS60"/>
			<xs:enumeration value="TSD0"/>
			<xs:enumeration value="TS20"/>
			<xs:enumeration value="TS30"/>
			<xs:enumeration value="ALL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dnumType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-4]-[0-9*#]{1,15}(&amp;([0-4]-[0-9*#]{1,15}))*"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="dlghType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:integer">
					<xs:minInclusive value="1"/>
					<xs:maxInclusive value="15"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[\w&amp;]+"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="triggerPointType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="allType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ALL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="detectionPointType">
		<xs:union memberTypes="allType">
			<xs:simpleType>
				<xs:restriction base="xs:unsignedByte">
					<xs:minInclusive value="0"/>
					<xs:maxInclusive value="14"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="cchType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="serviceKeyType">
		<xs:restriction base="xs:unsignedInt">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="2147483647"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="MtyType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="E"/>
			<xs:enumeration value="I"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FtcType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="N"/>
			<xs:enumeration value="F"/>
			<xs:enumeration value=""/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="interlockCodeType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{4}-(6553[0-5]|655[0-2][0-9]\d|65[0-4](\d){2}|6[0-4](\d){3}|[1-5](\d){4}|[0-9](\d){1,3}|[0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="cugIndexType">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="32767"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="preferentialCugType">
		<xs:union memberTypes="cugIndexType">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="NONE"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="fnumType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="subAddressType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[012]-([0-9ABCDEF]{2}){1,20}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="noReplyTimeType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[1-2][05]|30|5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="scAddsType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	
	
	
	
	<!-- complex type definition -->
	<xs:complexType name="AMSISDNType">
		<xs:sequence>
			<xs:element minOccurs="0" name="amsisdn" type="MSISDNType"/>
			<xs:element name="bc" type="bcType"/>
			<xs:element minOccurs="0" name="bs" type="bsType"/>
		</xs:sequence>
		<xs:attribute name="amsisdn" type="MSISDNType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="amsisdnAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CamelTriggerDetectionPointType">
		<xs:sequence>
			<xs:element minOccurs="0" name="triggeringPoint" type="triggerPointType"/>
			<xs:element minOccurs="0" name="detectionPoint" type="detectionPointType"/>
			<xs:element minOccurs="0" name="gsa" type="gsaType"/>
			<xs:element minOccurs="0" name="serviceKey" type="serviceKeyType"/>
			<xs:element minOccurs="0" name="defaultErrorHandling" type="BinaryType"/>
			<xs:element minOccurs="0" name="cch" type="cchType"/>
			<xs:element minOccurs="0" name="i" type="iType"/>
			<xs:element minOccurs="0" name="dialnum" type="dialnumType"/>
		</xs:sequence>
		<xs:attribute name="triggeringPoint" type="triggerPointType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="triggeringPointAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="detectionPoint" type="detectionPointType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="detectionPointAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="cch" type="cchType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="cchAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CCamelOctdp2Type">
		<xs:sequence>
			<xs:element minOccurs="0" name="id" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="cch" type="cchType"/>
			<xs:element minOccurs="0" name="mty" type="MtyType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="dnum" type="dnumType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="dlgh" type="dlghType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
			<xs:element minOccurs="0" name="ftc" type="FtcType"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:unsignedByte" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="idAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="cch" type="cchType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="cchAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="dnum" type="dnumType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="dnumAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="dlgh" type="dlghType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="dlghAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ftc" type="FtcType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="ftcAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="bs" type="bsType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="bsAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="bsg" type="bsgType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="bsgAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CCamelTctdp12Type">
		<xs:sequence>
			<xs:element minOccurs="0" name="id" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="cch" type="cchType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
		</xs:sequence>
		<xs:attribute name="cch" type="cchType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="cchAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="bs" type="bsType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="bsAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="bsg" type="bsgType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="bsgAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CamelConditionalTriggerType">
		<xs:sequence>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="octdp2" nillable="true" type="CCamelOctdp2Type"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="tctdp12" nillable="true" type="CCamelTctdp12Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CamelType">
		<xs:sequence>
			<!-- extends EOCamelType -->
			<xs:element minOccurs="0" name="eoinci" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="eoick" type="ZeroNineNineNineType"/>
			<xs:element minOccurs="0" name="etinci" type="xs:unsignedByte"/>
			<xs:element minOccurs="0" name="etick" type="ZeroNineNineNineType"/>
			<xs:element minOccurs="0" name="gcso" type="BinaryType"/>
			<xs:element minOccurs="0" name="sslo" type="BinaryType"/>
			<xs:element minOccurs="0" name="mcso" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="gc2so" type="BinaryType"/>
			<xs:element minOccurs="0" name="mc2so" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="tif" type="BinaryType"/>
			<xs:element minOccurs="0" name="gc3so" type="BinaryType"/>
			<xs:element minOccurs="0" name="mc3so" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="gprsso" type="BinaryType"/>
			<xs:element minOccurs="0" name="osmsso" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="tsmsso" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="mmso" type="BinaryType"/>
			<xs:element minOccurs="0" name="gc4so" type="BinaryType"/>
			<xs:element minOccurs="0" name="mc4so" type="ZeroTwoType"/>
			<!-- End of extension of EOCamelType -->
			<xs:element maxOccurs="40" minOccurs="0" name="ctdp" nillable="true" type="CamelTriggerDetectionPointType"/>
			<xs:element minOccurs="0" name="ccamel" type="CamelConditionalTriggerType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NamType">
		<xs:sequence>
			<xs:element name="prov" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="keep" type="BinaryType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GprsType">
		<xs:sequence>
			<xs:element minOccurs="0" name="pdpid" type="PdpidType"/>
			<xs:choice>
				<xs:sequence>
					<xs:element name="epdpind" type="epdpindType"/>
					<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
					<xs:element minOccurs="0" name="apnid" type="apnidType"/>
					<xs:element minOccurs="0" name="pdpadd" type="epdpaddType"/>
					<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
					<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
					<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
					<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
					<xs:element minOccurs="0" name="vpaa" type="BinaryType"/>
				</xs:sequence>
				<xs:sequence>
					<xs:element minOccurs="0" name="apnid" type="apnidType"/>
					<xs:element minOccurs="0" name="pdpadd" type="epdpaddType"/>
					<xs:element minOccurs="0" name="eqosid" type="eqosidType"/>
					<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
					<xs:element minOccurs="0" name="pdpty" type="pdptyType"/>
					<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
					<xs:element minOccurs="0" name="vpaa" type="BinaryType"/>
				</xs:sequence>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="pdpid" type="PdpidType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="pdpidAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="apnid" type="apnidType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="apnidAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="pdpadd" type="epdpaddType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="pdpaddAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		</xs:complexType>
	<xs:complexType name="GprsGetType">
		<xs:sequence>
			<xs:element minOccurs="0" name="pdpid" type="PdpidType"/>
			<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
			<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
			<xs:element minOccurs="0" name="apnid" type="apnidType"/>
			<xs:element minOccurs="0" name="pdpadd" type="epdpaddType"/>
			<xs:element minOccurs="0" name="eqosid">
				<xs:simpleType>
					<xs:restriction base="xs:unsignedShort">
						<xs:minInclusive value="0"/>
						<xs:maxInclusive value="4095"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
			<xs:element minOccurs="0" name="pdpty">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="4"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
			<xs:element minOccurs="0" name="vpaa" type="BinaryType"/>
		</xs:sequence>
		<xs:attribute name="pdpid" type="PdpidType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="pdpidAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="mobManDetectionPointType">
		<xs:union memberTypes="allType">
			<xs:simpleType>
				<xs:restriction base="xs:unsignedByte">
					<xs:minInclusive value="0"/>
					<xs:maxInclusive value="1"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<!-- MobManINTriggeringType -->
	<xs:complexType name="MobManInTriggeringType">
		<xs:sequence>
			<xs:element minOccurs="0" name="detectionPoint" type="mobManDetectionPointType"/>
			<xs:element minOccurs="0" name="gsa" type="gsaType"/>
			<xs:element minOccurs="0" name="serviceKey" type="serviceKeyType"/>
			<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
		</xs:sequence>
		<xs:attribute name="detectionPoint" type="mobManDetectionPointType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="detectionPointAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- CreateMobManInTriggeringType, added due to HR81881-->
	<xs:complexType name="CreateMobManInTriggeringType">
		<xs:sequence>
			<xs:element minOccurs="0" name="detectionPoint" type="mobManDetectionPointType"/>
			<xs:element minOccurs="0" name="gsa" type="gsaType"/>
			<xs:element minOccurs="0" name="serviceKey" type="serviceKeyType"/>
			<xs:element minOccurs="0" name="activationState" type="OneType"/>
		</xs:sequence>
		<xs:attribute name="detectionPoint" type="mobManDetectionPointType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="detectionPointAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- ClosedUserGroupType -->
	<xs:complexType name="ClosedUserGroupType">
		<xs:sequence>
			<xs:element minOccurs="0" name="cugIndex" type="cugIndexType"/>
			<xs:element minOccurs="0" name="interlockCode" type="interlockCodeType"/>
			<xs:element minOccurs="0" name="restriction" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="ts10" type="ProvisionType"/>
			<xs:element minOccurs="0" name="ts60" type="ProvisionType"/>
			<xs:element minOccurs="0" name="tsd0" type="ProvisionType"/>
			<xs:element minOccurs="0" name="bs20" type="ProvisionType"/>
			<xs:element minOccurs="0" name="bs30" type="ProvisionType"/>
		</xs:sequence>
		<xs:attribute name="cugIndex" type="cugIndexType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="cugIndexAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- CugBsgOptionType -->
	<xs:complexType name="CugSingleBsgOptionType">
		<xs:sequence>
			<xs:element name="accessibility" type="CugAccessibilityType"/>
			<xs:element minOccurs="0" name="preferentialCug" type="preferentialCugType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CugBsgOptionType">
		<xs:sequence>
			<xs:element minOccurs="0" name="accessibility" type="CugAccessibilityType"/>
			<xs:element minOccurs="0" name="preferentialCug" type="preferentialCugType"/>
			<xs:element minOccurs="0" name="ts10" type="CugSingleBsgOptionType"/>
			<xs:element minOccurs="0" name="ts60" type="CugSingleBsgOptionType"/>
			<xs:element minOccurs="0" name="tsd0" type="CugSingleBsgOptionType"/>
			<xs:element minOccurs="0" name="bs20" type="CugSingleBsgOptionType"/>
			<xs:element minOccurs="0" name="bs30" type="CugSingleBsgOptionType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SimpleActivationStateType">
		<xs:sequence>
			<xs:element name="activationState" type="ZeroTwoType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BarringSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="ts20" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FnumCfuCfbCfnrcActivationStateType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element minOccurs="0" name="keep" type="BinaryType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CfCfuCfbCfnrcSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element minOccurs="0" name="keep" type="BinaryType"/>
			<xs:element minOccurs="0" name="ts10" type="FnumCfuCfbCfnrcActivationStateType"/>
			<xs:element minOccurs="0" name="ts60" type="FnumCfuCfbCfnrcActivationStateType"/>
			<xs:element minOccurs="0" name="tsd0" type="FnumCfuCfbCfnrcActivationStateType"/>
			<xs:element minOccurs="0" name="bs20" type="FnumCfuCfbCfnrcActivationStateType"/>
			<xs:element minOccurs="0" name="bs30" type="FnumCfuCfbCfnrcActivationStateType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FnumCfnryDcfActivationStateType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
			<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element minOccurs="0" name="keep" type="BinaryType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CfCfnryDcfSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="noReplyTime" type="noReplyTimeType"/>
			<xs:element minOccurs="0" name="subAddress" type="subAddressType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element minOccurs="0" name="keep" type="BinaryType"/>
			<xs:element minOccurs="0" name="ts10" type="FnumCfnryDcfActivationStateType"/>
			<xs:element minOccurs="0" name="ts60" type="FnumCfnryDcfActivationStateType"/>
			<xs:element minOccurs="0" name="tsd0" type="FnumCfnryDcfActivationStateType"/>
			<xs:element minOccurs="0" name="bs20" type="FnumCfnryDcfActivationStateType"/>
			<xs:element minOccurs="0" name="bs30" type="FnumCfnryDcfActivationStateType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FnumSpnActivationStateType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CfSpnSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="fnum" type="fnumType"/>
			<xs:element minOccurs="0" name="ofa" type="ofaType"/>
			<xs:element minOccurs="0" name="ts10" type="FnumSpnActivationStateType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CawSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="ZeroTwoType"/>
			<xs:element minOccurs="0" name="ts10" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="ts60" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="tsd0" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="bs20" type="SimpleActivationStateType"/>
			<xs:element minOccurs="0" name="bs30" type="SimpleActivationStateType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AllSupplementaryServiceType">
		<xs:sequence>
			<xs:element minOccurs="0" name="provisionState" type="BinaryType"/>
			<xs:element minOccurs="0" name="activationState" type="BinaryType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationDataType">
		<xs:sequence>
			<xs:element minOccurs="0" name="vlrAddress" type="vlrDataType"/>
			<xs:element minOccurs="0" name="msrn">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="15"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element minOccurs="0" name="mscNumber">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element minOccurs="0" name="lmsid">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element minOccurs="0" name="sgsnNumber" type="vlrDataType"/>
			<xs:element minOccurs="0" name="locState" type="xs:string"/>
			<xs:sequence minOccurs="0">
				<xs:element minOccurs="0" name="cgisailai" type="cgisailaiType"/>
				<xs:element minOccurs="0" name="loctype" type="loctypeType"/>
				<xs:element minOccurs="0" name="ageloc" type="agelocType"/>
				<xs:element minOccurs="0" name="substate" type="substateType"/>
				<xs:element minOccurs="0" name="lastcsupd" type="timestamp"/>
				<xs:element minOccurs="0" name="lastpsupd" type="timestamp"/>
				<xs:element minOccurs="0" name="cstimest1" type="timestamp"/>
				<xs:element minOccurs="0" name="cstimest2" type="timestamp"/>
				<xs:element minOccurs="0" name="cstimest3" type="timestamp"/>
				<xs:element minOccurs="0" name="cstimest4" type="timestamp"/>
				<xs:element minOccurs="0" name="cstimest5" type="timestamp"/>
				<xs:element minOccurs="0" name="pstimest1" type="timestamp"/>
				<xs:element minOccurs="0" name="pstimest2" type="timestamp"/>
				<xs:element minOccurs="0" name="pstimest3" type="timestamp"/>
				<xs:element minOccurs="0" name="pstimest4" type="timestamp"/>
				<xs:element minOccurs="0" name="pstimest5" type="timestamp"/>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GmlcAddressType">
		<xs:sequence>
			<xs:element name="gmlcId" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="gmlcAddress">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="[0-9]{3,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="gmlcId" type="ZeroTwoFiveFiveType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="gmlcIdAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="GmlcAddType">
		<xs:sequence>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="gmlcAdd" nillable="true" type="GmlcAddressType"/>
			<xs:element minOccurs="0" name="hgmlcId" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="hgmlcAdd" type="xs:string"/>
			<xs:element minOccurs="0" name="pprId" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="pprAdd" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnivType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CrelCunrlType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
			<xs:element minOccurs="0" name="notf" type="ZeroFourType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IntIdType">
		<xs:sequence>
			<xs:element minOccurs="0" name="intId0" type="xs:boolean"/>
			<xs:element minOccurs="0" name="intId1" type="xs:boolean"/>
			<xs:element minOccurs="0" name="intId2" type="xs:boolean"/>
			<xs:element minOccurs="0" name="intId3" type="xs:boolean"/>
			<xs:element minOccurs="0" name="intId4" type="xs:boolean"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PlmnoType">
		<xs:sequence>
			<xs:element minOccurs="0" name="activationState" type="ActivationType"/>
			<xs:element minOccurs="0" name="intId" type="IntIdType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MoclType">
		<xs:sequence>
			<xs:element default="false" minOccurs="0" name="asl" type="xs:boolean"/>
			<xs:element default="false" minOccurs="0" name="bsl" type="xs:boolean"/>
			<xs:element default="false" minOccurs="0" name="ttp" type="xs:boolean"/>
			<xs:element minOccurs="0" name="endAll" type="ActivationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EaddType">
		<xs:sequence>
			<xs:element minOccurs="0" name="eadd" type="EaddAttrType"/>
			<xs:element minOccurs="0" name="gres" type="GresType"/>
			<xs:element minOccurs="0" name="notf" type="ZeroFourType"/>
			<xs:element default="false" minOccurs="0" name="crel" type="xs:boolean"/>
		</xs:sequence>
		<xs:attribute name="eadd" type="EaddAttrType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="eaddAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="crel" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="crelAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ServtType">
		<xs:sequence>
			<xs:element minOccurs="0" name="servt" type="ServtAttrType"/>
			<xs:element minOccurs="0" name="gres" type="GresType"/>
			<xs:element minOccurs="0" name="notf" type="ZeroFourType"/>
		</xs:sequence>
		<xs:attribute name="servt" type="ServtAttrType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="servtAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="LcsDataType">
		<xs:sequence>
			<xs:element minOccurs="0" name="univ" type="UnivType"/>
			<xs:element minOccurs="0" name="crel" type="CrelCunrlType"/>
			<xs:element minOccurs="0" name="cunrl" type="CrelCunrlType"/>
			<xs:element minOccurs="0" name="plmno" type="PlmnoType"/>
			<xs:element minOccurs="0" name="mocl" type="MoclType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="eadd" nillable="true" type="EaddType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="servt" nillable="true" type="ServtType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SteType">
		<xs:sequence>
			<xs:element minOccurs="0" name="ste" type="ZeroFifteenType"/>
			<xs:element minOccurs="0" name="gmlcid" type="ZeroTwoFiveFiveType"/>
			<xs:element minOccurs="0" name="gmlca">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="0"/>
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ste" type="ZeroFifteenType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="steAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="gmlcid" type="ZeroTwoFiveFiveType" use="optional">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="gmlcidAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!-- MultiSimType -->
	<xs:complexType name="LinkedImsiType">
		<xs:sequence>
			<xs:element minOccurs="0" name="imsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="active" type="xs:boolean"/>
			
			
			
			
			
			
			
		</xs:sequence>
		<xs:attribute name="imsi" type="IMSIType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="imsiAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MultiSimType">
		<xs:sequence>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="imsis" nillable="true" type="LinkedImsiType"/>
			<xs:element minOccurs="0" name="mch" type="mchType"/>
			<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
			
			<xs:element minOccurs="0" name="acimsi" type="IMSIType"/>
			<xs:element minOccurs="0" name="mMsisdn" type="MSISDNType"/>
			<xs:element minOccurs="0" name="delall" type="BinaryType"/>			
			<!--xs:element name="initFlag" type="xs:boolean"/ -->
		</xs:sequence>
	</xs:complexType>
	<!-- SmsSpamType -->
	<xs:complexType name="SmsSpamScAddsType">
		<xs:sequence>
			<xs:element name="scAdds" type="scAddsType"/>
		</xs:sequence>
		<xs:attribute name="scAdds" type="scAddsType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="scAddsAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SmsSpamType">
		<xs:sequence>
			<xs:element minOccurs="0" name="active" type="activeType"/>
			<xs:element maxOccurs="unbounded" name="scAdds" nillable="true" type="SmsSpamScAddsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SmsSpamTypeResp">
		<xs:sequence>
			<xs:element minOccurs="0" name="active" type="activeType"/>
			<xs:element maxOccurs="unbounded" minOccurs="0" name="scAdds" type="SmsSpamScAddsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="activeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE"/>
			<xs:enumeration value="NACTIVE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="mchType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LOC"/>
			<xs:enumeration value="USSD"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileQoSIDType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="4095"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileTrafficClassType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="BACK"/>
			<xs:enumeration value="CON"/>
			<xs:enumeration value="INT"/>
			<xs:enumeration value="STR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfilePriorityType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileDOType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="YES"/>
			<xs:enumeration value="NO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileTDType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="100"/>
			<xs:maxInclusive value="4000"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileUplinkValueType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="8640"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="QoSProfileDownlinkValueType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="42000"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="APNProfileAPNType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="62"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="APNProfileAPNIdType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="16383"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ProfileIdType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="8191"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="GPRSProfileIdType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="8160"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CamelProfileIdType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="8160"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="GsmSCFProfileIdType">
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IPv4Type">
		<xs:restriction base="xs:string">
			<xs:minLength value="7"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="(\d{1,3}\.){3}\d{1,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IPv6Type">
		<xs:restriction base="xs:string">
			<xs:minLength value="2"/>
			<xs:maxLength value="39"/>
			<xs:pattern value="[0-9A-Fa-f:]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="epdpaddType">
		<xs:union memberTypes="IPv4Type IPv6Type">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ERASE"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="epdpindType">
		<xs:restriction base="xs:boolean"/>
	</xs:simpleType>
	<!-- ALL removed from apnidType in HU94292 -->
	<xs:simpleType name="apnidType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="NS"/>
					<xs:enumeration value="WILDCARD"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:unsignedShort">
					<xs:minInclusive value="0"/>
					<xs:maxInclusive value="16383"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="primaryhlridType">
			<xs:restriction base="xs:string">
				<xs:pattern value="[1-15]-[1-32]"/>
				<xs:maxLength value="5"/>
			</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="zoneidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="65535"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- PBXLineType -->
	<xs:complexType name="pbxLineType">
		<xs:sequence>
				<xs:element name="vlradds" type="vlraddsType"/>
		</xs:sequence>
	</xs:complexType>	
	<xs:simpleType name="vlraddsType">
	    <xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[34]{1}-[0-9]{5,15}"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ALL"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="eqosidType">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="4095"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="pdptyType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="fanumberType">
		<xs:sequence>
			<xs:element minOccurs="0" name="number" type="NUMBERType"/>
		</xs:sequence>
		<xs:attribute name="number" type="NUMBERType" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="numberAttr"/>
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	
	<xs:simpleType name="ssloType">
		<xs:restriction base="xs:integer">
			<xs:enumeration value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="cgisailaiType">
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9F]*"/>
					<xs:minLength value="10"/>
					<xs:maxLength value="10"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9F]*"/>
					<xs:minLength value="14"/>
					<xs:maxLength value="14"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>

	<xs:simpleType name="loctypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CGI"/>
			<xs:enumeration value="SAI"/>
			<xs:enumeration value="LAI"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="agelocType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="65534"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="substateType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ASSUMED IDLE"/>
			<xs:enumeration value="CAMEL BUSY"/>
			<xs:enumeration value="MS PURGED"/>
			<xs:enumeration value="IMSI DETACHED"/>
			<xs:enumeration value="RESTRICTED AREA"/>
			<xs:enumeration value="NOT REGISTERED"/>
			<xs:enumeration value="NOT FROM VLR"/>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="timestamp">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
    <xs:complexType name="StateAndLocationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="cgisailai">
                <xs:simpleType>
                    <xs:union>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:pattern value="[0-9F]*"/>
                                <xs:minLength value="10"/>
                                <xs:maxLength value="10"/>
                            </xs:restriction>
                        </xs:simpleType>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:pattern value="[0-9F]*"/>
                                <xs:minLength value="14"/>
                                <xs:maxLength value="14"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:union>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="loctype">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="CGI"/>
                        <xs:enumeration value="SAI"/>
                        <xs:enumeration value="LAI"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="ageloc">
                <xs:simpleType>
                    <xs:restriction base="xs:integer">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="65534"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="substate">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="ASSUMED IDLE"/>
                        <xs:enumeration value="CAMEL BUSY"/>
                        <xs:enumeration value="MS PURGED"/>
                        <xs:enumeration value="IMSI DETACHED"/>
                        <xs:enumeration value="RESTRICTED AREA"/>
                        <xs:enumeration value="NOT REGISTERED"/>
                        <xs:enumeration value="NOT FROM VLR"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>