<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/pg/1.0"
           xmlns="http://schemas.ericsson.com/pg/1.0" elementFormDefault="qualified"
           attributeFormDefault="unqualified">

    <xs:element name="PGFault" xmlns="http://schemas.ericsson.com/pg/1.0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="errorcode" type="xs:integer"/>
                <xs:element name="errormessage" type="xs:string"/>
                <xs:element name="errordetails" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="EPSFault" xmlns="http://schemas.ericsson.com/pg/1.0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="errorcode" type="xs:integer"/>
                <xs:element name="errormessage" type="xs:string"/>
                <xs:element name="errordetails" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AVGFault" xmlns="http://schemas.ericsson.com/pg/1.0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="errorcode" type="xs:integer"/>
                <xs:element name="errormessage" type="xs:string"/>
                <xs:element name="errordetails" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>