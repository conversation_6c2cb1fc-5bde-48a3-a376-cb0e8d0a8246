<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="SetSubscriberData">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="profile" type="profileType"/>
<xs:element name="sud" type="sudType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetSubscriberData">
<xs:complexType>
<xs:sequence>
<xs:element name="SubscriberData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="imsi" type="imsiType"/>
<xs:element name="state" type="stateType"/>
<xs:element minOccurs="0" name="authd" type="authdType"/>
<xs:element minOccurs="0" name="nam" type="namType"/>
<xs:element minOccurs="0" name="imeisv" type="imeisvType"/>
<xs:element minOccurs="0" name="rid" type="ridType"/>
<xs:element minOccurs="0" name="PermanentSubscriberData">
<xs:complexType>
<xs:sequence>
<xs:sequence>
<xs:element maxOccurs="unbounded" name="sud" type="sudType"/>
</xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="AdditionalMSISDNData">
<xs:complexType>
<xs:sequence>
<xs:element name="amsisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="bs" type="bsType"/>
<xs:element name="bc" type="bcType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="SupplementaryServiceData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="BasicServiceGroup">
<xs:complexType>
<xs:sequence>
<xs:element name="bsg" type="bsgType"/>
<xs:element maxOccurs="unbounded" name="SupplementaryService">
<xs:complexType>
<xs:sequence>
<xs:element name="ss" type="ssType"/>
<xs:element name="status" type="statusType"/>
<xs:element minOccurs="0" name="ForwardData">
<xs:complexType>
<xs:sequence>
<xs:element name="fnum" type="fnumType"/>
<xs:element minOccurs="0" name="sadd" type="saddType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="time" type="timeType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="passwordBarred" type="passwordBarredType"/>
<xs:element minOccurs="0" name="mcfActive" type="mcfActiveType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="LocationData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="CSLocationData">
<xs:complexType>
<xs:sequence>
<xs:element name="vlrAddress" type="vlrAddressType"/>
<xs:element minOccurs="0" name="msrn" type="msrnType"/>
<xs:element minOccurs="0" name="mscNumber" type="mscNumberType"/>
<xs:element minOccurs="0" name="lmsid" type="lmsidType"/>
<xs:element minOccurs="0" name="mscAreaRestricted" type="mscAreaRestrictedType"/>
<xs:element minOccurs="0" name="msPurgedInVlr" type="msPurgedInVlrType"/>
<xs:element minOccurs="0" name="ServicesRestrictedInVlr">
<xs:complexType>
<xs:sequence>
<xs:element name="rsp" type="rspType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="PSLocationData">
<xs:complexType>
<xs:sequence>
<xs:element name="sgsnNumber" type="sgsnNumberType"/>
<xs:element minOccurs="0" name="msPurgedInSgsn" type="msPurgedInSgsnType"/>
<xs:element minOccurs="0" name="ServicesRestrictedInSgsn">
<xs:complexType>
<xs:sequence>
<xs:element name="rsp" type="rspType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="lastcsupd" type="xs:string"/>
<xs:element minOccurs="0" name="lastpsupd" type="xs:string"/>
<xs:element minOccurs="0" name="cstimest1" type="xs:string"/>
<xs:element minOccurs="0" name="cstimest2" type="xs:string"/>
<xs:element minOccurs="0" name="cstimest3" type="xs:string"/>
<xs:element minOccurs="0" name="cstimest4" type="xs:string"/>
<xs:element minOccurs="0" name="cstimest5" type="xs:string"/>
<xs:element minOccurs="0" name="pstimest1" type="xs:string"/>
<xs:element minOccurs="0" name="pstimest2" type="xs:string"/>
<xs:element minOccurs="0" name="pstimest3" type="xs:string"/>
<xs:element minOccurs="0" name="pstimest4" type="xs:string"/>
<xs:element minOccurs="0" name="pstimest5" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="PDPContextData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="apnid" type="apnidType"/>
<xs:element minOccurs="0" name="pdpadd" type="pdpaddType"/>
<xs:element name="eqosid" type="eqosidType"/>
<xs:element name="vpaa" type="vpaaType"/>
<xs:element minOccurs="0" name="pdpch" type="pdpchType"/>
<xs:element name="pdpty" type="pdptyType"/>
<xs:element minOccurs="0" name="epdpind" type="epdpindType"/>
<xs:element minOccurs="0" name="epdpadd" type="epdpaddType"/>
<xs:element name="pdpid" type="pdpidType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="MultipleSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="mch" type="mchType"/>
<xs:element minOccurs="0" name="zoneid" type="zoneidType"/>
<xs:element maxOccurs="10" minOccurs="2" name="SubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="active" type="activeType"/>
<xs:element name="master" type="masterType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="imsi" type="imsiType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
