<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-party="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:amdocs-customer="http://xmlns.aua.oss.amdocs.com/auai/Common-CBECustomer/3" xmlns:cbelocation-v1-5="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:cbeparty-v1-5="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5" targetNamespace="http://amdocs/core/ossj-Common-CBEParty/dat/3" elementFormDefault="qualified">
	<xs:import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5" schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEParty/v1-5" schemaLocation="OSSJ-Common-CBEParty-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBELocation/v1-5" schemaLocation="OSSJ-Common-CBELocation-v1-5.xsd"/>
	<xs:simpleType name="PartyGender">
		<xs:restriction base="xs:string">
			<xs:enumeration value="male"/>
			<xs:enumeration value="female"/>
			<xs:enumeration value="transgender"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="PartyKey" type="amdocs-party:PartyKey"/>
	<xs:complexType name="PartyKey">
		<xs:complexContent>
			<xs:extension base="cbeparty-v1-5:PartyKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="PartyRoleKey" type="amdocs-party:PartyRoleKey"/>
	<xs:complexType name="PartyRoleKey">
		<xs:complexContent>
			<xs:extension base="cbeparty-v1-5:PartyRoleKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="PartyRoleValue" type="amdocs-party:PartyRoleValue"/>
	<xs:complexType name="PartyRoleValue">
		<xs:complexContent>
			<xs:extension base="cbeparty-v1-5:PartyRoleValue">
				<xs:sequence>
					<xs:element name="operator" type="xs:string" minOccurs="0"/>
					<xs:element name="contactMediums" type="amdocs-party:ArrayOfContactMediumValue" minOccurs="0"/>
					<xs:element name="party" type="amdocs-party:PartyValue" minOccurs="0"/>
					
					
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="PartyValue" type="amdocs-party:PartyValue"/>
	<xs:complexType name="PartyValue">
		<xs:complexContent>
			<xs:extension base="cbeparty-v1-5:PartyValue">
				<xs:sequence>
					<xs:element name="dateOfBirth" type="xs:date" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The date of birth for this party.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="gender" type="amdocs-party:PartyGender" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The gender of this party.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ContactMediumKey" type="amdocs-party:ContactMediumKey"/>
	<xs:complexType name="ContactMediumKey">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:CBEManagedEntityKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ContactMediumValue" type="amdocs-party:ContactMediumValue"/>
	<xs:complexType name="ContactMediumValue">
		<xs:complexContent>
			<xs:extension base="cbecore-v1-5:CBEManagedEntityValue">
				<xs:sequence>
					<xs:element name="address" type="cbelocation-v1-5:UrbanPropertyAddressValue" minOccurs="0" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>Contains address attributes.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="timeZone" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The time zone that is applicable to the customer.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="webAddress" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A web address associated with the customer.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="emailAddress" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A contact email address for the customer.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="telephoneNumber" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>A contact telephone number for the customer.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfContactMediumKey" type="amdocs-party:ArrayOfContactMediumKey"/>
	<xs:complexType name="ArrayOfContactMediumKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:ContactMediumKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfContactMediumValue" type="amdocs-party:ArrayOfContactMediumValue"/>
	<xs:complexType name="ArrayOfContactMediumValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:ContactMediumValue" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Each item element contains details of a single item of the parent element's type. For example, if the parent is Customer, each item contains details relating to one customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfPartyKey" type="amdocs-party:ArrayOfPartyKey"/>
	<xs:complexType name="ArrayOfPartyKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:PartyKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfPartyRoleKey" type="amdocs-party:ArrayOfPartyRoleKey"/>
	<xs:complexType name="ArrayOfPartyRoleKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:PartyRoleKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfPartyRoleValue" type="amdocs-party:ArrayOfPartyRoleValue"/>
	<xs:complexType name="ArrayOfPartyRoleValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:PartyRoleValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ArrayOfPartyValue" type="amdocs-party:ArrayOfPartyValue"/>
	<xs:complexType name="ArrayOfPartyValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-party:PartyValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
