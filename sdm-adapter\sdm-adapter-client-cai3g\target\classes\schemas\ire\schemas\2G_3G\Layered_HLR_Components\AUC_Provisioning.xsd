<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns:gsmhlr="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
           xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/" attributeFormDefault="unqualified" elementFormDefault="qualified"
           jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0"
           targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/">
    <xs:element name="imsi" type="IMSIType"/>
    <xs:element name="msisdn" type="MSISDNType"/>
    <xs:simpleType name="MSISDNType">
        <xs:annotation>
            <xs:documentation>
                the type definition for MSISDN
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="5"/>
            <xs:maxLength value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="IMSIType">
        <xs:annotation>
            <xs:documentation>
                the type definition for IMSI
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="6"/>
            <xs:maxLength value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="getSubscription">
        <xs:complexType>
            <xs:sequence>
                <!--<xs:element name="imsi" type="IMSIType"/>-->
                <xs:element minOccurs="0" name="akatype" type="xs:string"/>
            </xs:sequence>
            <xs:attribute name="imsi" type="IMSIType"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="createSubscription" type="CreateSubscriptionType">
        <xs:annotation>
            <xs:documentation>
                The attributes for creating AUC subscription.
            </xs:documentation>
        </xs:annotation>
        <xs:key name="imsiKey_Create">
            <xs:selector xpath="."/>
            <xs:field xpath="@imsi"/>
        </xs:key>
        <xs:keyref name="imsiKeyRef_Create" refer="imsiKey_Create">
            <xs:selector xpath="."/>
            <xs:field xpath="imsi"/>
        </xs:keyref>
    </xs:element>
    <xs:complexType name="CreateSubscriptionType">
        <xs:sequence>
            <xs:element name="imsi" type="IMSIType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="elementImsi"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="ki">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="32"/>
                        <xs:maxLength value="32"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="a38">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="15"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="fsetind">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="31"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="eopc">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:maxLength value="32"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="a4ind">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="7"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="adkey">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedShort">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="511"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="amf">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:pattern
                            value="6553[0-5]|655[0-2]\d|65[0-4]\d\d|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}|0|DEFAULT"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="zoneid">
                <xs:simpleType>
                    <xs:restriction base="xs:integer">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="65535"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="rid">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="63"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="imsi" type="IMSIType" use="required"/>
    </xs:complexType>
    <xs:element name="setSubscription" type="SetSubscriptionType">
        <xs:annotation>
            <xs:documentation>
                The attributes for setting AUC subscription.
            </xs:documentation>
        </xs:annotation>
        <xs:key name="imsiKey_Set">
            <xs:selector xpath="."/>
            <xs:field xpath="@imsi"/>
        </xs:key>
    </xs:element>
    <xs:complexType name="SetSubscriptionType">
        <xs:sequence>
            <xs:element minOccurs="0" name="amf">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:pattern
                            value="6553[0-5]|655[0-2]\d|65[0-4]\d\d|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}|0|DEFAULT"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="akaalgind">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="1"/>
                        <xs:enumeration value="2"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="imsi" type="IMSIType" use="required"/>
    </xs:complexType>
    <xs:element name="getResponseSubscription" type="GetSubscriptionType">
        <xs:annotation>
            <xs:documentation>
                The attributes for get AUC subscription response.
            </xs:documentation>
        </xs:annotation>
        <xs:key name="imsiKey_Get">
            <xs:selector xpath="."/>
            <xs:field xpath="@imsi"/>
        </xs:key>
    </xs:element>
    <xs:complexType name="GetSubscriptionType">
        <xs:sequence>
            <xs:element name="imsi" type="IMSIType">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="elementImsi"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="ki">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="32"/>
                        <xs:maxLength value="32"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="a38">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="15"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="fsetind">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="31"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="a4ind">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="7"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="adkey">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedShort">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="511"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="akatype">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="1"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="amf">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:pattern
                            value="6553[0-5]|655[0-2]\d|65[0-4]\d\d|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3}|0|DEFAULT"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="akaalgind">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="7"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="zoneid">
                <xs:simpleType>
                    <xs:restriction base="xs:integer">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="65535"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element minOccurs="0" name="rid">
                <xs:simpleType>
                    <xs:restriction base="xs:unsignedByte">
                        <xs:minInclusive value="0"/>
                        <xs:maxInclusive value="63"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="imsi" type="IMSIType" use="required"/>
    </xs:complexType>
    <xs:element name="deleteSubscription" type="DeleteSubscriptionType">
        <xs:annotation>
            <xs:documentation>
                The attributes for deleting AUC subscription
                response.
            </xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:complexType name="DeleteSubscriptionType">
        <xs:sequence/>
    </xs:complexType>
</xs:schema>
