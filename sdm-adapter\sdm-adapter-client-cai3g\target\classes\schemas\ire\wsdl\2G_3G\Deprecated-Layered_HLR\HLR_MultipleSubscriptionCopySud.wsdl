<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
</jaxws:bindings>
<types>
<xs:schema xmlns="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
<xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd"/>
<xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/HLR_MultipleSubscriptionCopySud.xsd"/>
</xs:schema>
</types>
<message name="HeadInfo">
<part element="cai3g:SessionId" name="sessionId"/>
</message>
<message name="Cai3gFault">
<part element="cai3g:Cai3gFault" name="parameters"/>
</message>
<message name="Cai3gHeaderFault">
<part element="cai3g:SessionIdFault" name="sessionIdFault"/>
<part element="cai3g:TransactionIdFault" name="transactionIdFault"/>
<part element="cai3g:SequenceIdFault" name="sequenceIdFault"/>
</message>
<portType name="HLR_MultipleSubscriptionCopySUD"/>
<binding name="HLR_MultipleSubscriptionCopySUD" type="cai3g:HLR_MultipleSubscriptionCopySUD">
<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
</binding>
<service name="Provisioning">
<port binding="cai3g:HLR_MultipleSubscriptionCopySUD" name="HLR_MultipleSubscriptionCopySUD">
<soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
</port>
</service>
</definitions>
