<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/ma/EIR/">
<xs:include schemaLocation="types/eirla_types.xsd"/>
<xs:element name="imsi" type="imsiType"/>
<xs:element name="CreateClonedHandsetUser">
<xs:complexType>
<xs:sequence>
<xs:element name="imsi" type="imsiType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ClonedImei">
<xs:complexType>
<xs:sequence>
<xs:element name="imei" type="imeiType"/>
<xs:element default="0F" minOccurs="0" name="svn" type="svnType"/>
</xs:sequence>
<xs:attribute name="imei" type="imeiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imeiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_create_imei">
<xs:selector xpath="./x:imei"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_create_imei" refer="key_create_imei">
<xs:selector xpath="."/>
<xs:field xpath="@imei"/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_imsi">
<xs:selector xpath="./x:imsi"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_imsi" refer="key_imsi">
<xs:selector xpath="."/>
<xs:field xpath="@imsi"/>
</xs:keyref>
</xs:element>
<xs:element name="SetClonedHandsetUser">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ClonedImei" nillable="true">
<xs:complexType>
<xs:sequence>
<xs:element name="imei" type="imeiType"/>
<xs:element default="0F" minOccurs="0" name="svn" type="svnType"/>
</xs:sequence>
<xs:attribute name="imei" type="imeiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imeiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute default="0F" name="svn" type="svnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="svnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_set_imei">
<xs:selector xpath="."/>
<xs:field xpath="@imei"/>
</xs:key>
<xs:keyref name="keyref_set_imei" refer="key_set_imei">
<xs:selector xpath="./x:imei"/>
<xs:field xpath="."/>
</xs:keyref>
</xs:element>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetResponseClonedHandsetUser">
<xs:complexType>
<xs:sequence>
<xs:element name="imsi" type="imsiType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ClonedImei">
<xs:complexType>
<xs:sequence>
<xs:element name="imei" type="imeiType"/>
<xs:element minOccurs="0" name="svn" type="svnType"/>
</xs:sequence>
<xs:attribute name="imei" type="imeiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imeiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="imsi" type="imsiType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="imsiAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
