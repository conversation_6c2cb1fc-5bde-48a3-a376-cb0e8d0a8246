<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
           xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"
            xmlns:mmtel-context="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"
           targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelContext/"
           elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
           jaxb:version="2.0">

    <xs:annotation>
        <xs:documentation xml:lang="en">
            Copyright notice: (c) <PERSON><PERSON> AB 2018-2020
            Warning text "All rights reserved. No parts of this program may be reproduced in any form without the written permission of the copyright holder."

            Version: 1.20
            Reason: Added Caller Categorization
            Author: <PERSON>sson
            Date: July, 2020

            Version: 2.1.0
            Reason: Added User Common Data and removed Context Data
            Author: Ericsson
            Date: March, 2019

            Version: 2.0.0
            Reason: Removed Dynamic Black List and Malicious Communication Rejection
            Author: Ericsson
            Date: March, 2019

            Version: 4.14.0/1.14.0
            Reason: Added Communication Event Logging
            Author: Ericsson
            Date: October, 2018

            Version: 4.14.0/1.14.0
            Reason: Added Communication Setup Announcement
            Author: Ericsson
            Date: October, 2018

            Version: 4.14.0/1.14.0
            Reason: Initial version.
            Author: Ericsson
            Date: October, 2018

        </xs:documentation>
    </xs:annotation>
    <xs:element name="publicId" type="publicIdentityType"/>
    <xs:element name="context-identity" type="string-length-255-type" />
    <xs:element name="createService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to create MMTel context</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The default public user identity for the subscriber. This identity must already be configured on the HSS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="context-identity" type="string-length-255-type" />
                <xs:element name="validate" type="xs:boolean" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The validate is used when the Create/Set request must be validated but not stored in the HSS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="services" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must
                            be maintained all new services shall be optional and inserted in alphabetical order within
                            the existing list where possible
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="caller-categorization" type="caller-categorization-type" minOccurs="0" />
                            <xs:element name="calling-name-identity-presentation" type="calling-name-identity-presentation-type" minOccurs="0" />
                            <xs:element name="communication-distribution" type="communication-distribution-type" minOccurs="0" />
                            <xs:element name="communication-diversion" type="communication-diversion-type" minOccurs="0" />
                            <xs:element name="communication-event-logging" type="communication-event-logging-type" minOccurs="0" />
                            <xs:element name="communication-setup-announcement" type="communication-setup-announcement-type" minOccurs="0" />
                            <xs:element name="communication-waiting" type="communication-waiting-type" minOccurs="0" />
                            <xs:element name="conference" type="conference-type" minOccurs="0" />
                            <xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" minOccurs="0" />
                            <xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" minOccurs="0" />
                            <xs:element name="flexible-identity-presentation" type="flexible-identity-presentation-type" minOccurs="0" />
                            <xs:element name="hotline" type="hotline-type" minOccurs="0" />
                            <xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0" />
                            <xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" minOccurs="0" />
                            <xs:element name="media-policy" type="media-policy-type" minOccurs="0" />
                            <xs:element name="multi-device-conference-policy" type="multi-device-conference-policy-type" minOccurs="0" />
                            <xs:element name="northbound-call-control" type="northbound-call-control-type" minOccurs="0" />
                            <xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" minOccurs="0" />
                            <xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" minOccurs="0" />
                            <xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0" />
                            <xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" minOccurs="0" />
                            <xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0" />
                            <xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type"
                                minOccurs="0" />
                            <xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0" />
                            <xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type"
                                minOccurs="0" />
                            <xs:element name="user-common-data" type="user-common-data-type" minOccurs="0" />
                            <xs:element name="voice-mail" type="voice-mail-type" minOccurs="0" />
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="context-identity" type="string-length-255-type" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="contextIdentityAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyCreate">
            <xs:selector xpath="." />
            <xs:field xpath="@publicId" />
        </xs:key>
        <xs:keyref name="publicIdKeyRefCreate" refer="publicIdKeyCreate">
            <xs:selector xpath="./mmtel-context:publicId" />
            <xs:field xpath="." />
        </xs:keyref>
        <xs:key name="contextIdentityKeyCreate">
            <xs:selector xpath="." />
            <xs:field xpath="@context-identity" />
        </xs:key>
        <xs:keyref name="contextIdentityKeyRefCreate" refer="contextIdentityKeyCreate">
            <xs:selector xpath="./mmtel-context:context-identity" />
            <xs:field xpath="." />
        </xs:keyref>
    </xs:element>
    <xs:element name="setService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to modify MMTel context</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="validate" type="xs:boolean" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The validate is used when the Create/Set request must be validated but not stored in the HSS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an optional element to control concurrent updates. If present then the set
                            request will be accepted only if the service data version is still at the value given in this element i.e. no
                            other updates have been performed. It is of type integer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="services" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must
                            be maintained all new services shall be optional and inserted in alphabetical order within
                            the existing list where possible
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="caller-categorization" type="caller-categorization-type" nillable="true" minOccurs="0" />
                            <xs:element name="calling-name-identity-presentation" type="calling-name-identity-presentation-type" nillable="true" minOccurs="0" />
                            <xs:element name="communication-distribution" type="communication-distribution-type"  nillable="true" minOccurs="0" />
                            <xs:element name="communication-diversion" type="communication-diversion-type" nillable="true" minOccurs="0" />
                            <xs:element name="communication-event-logging" type="communication-event-logging-type" nillable="true" minOccurs="0" />
                            <xs:element name="communication-setup-announcement" type="communication-setup-announcement-type" nillable="true" minOccurs="0" />
                            <xs:element name="communication-waiting" type="communication-waiting-type" nillable="true" minOccurs="0" />
                            <xs:element name="conference" type="conference-type" nillable="true" minOccurs="0" />
                            <xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" nillable="true" minOccurs="0" />
                            <xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" nillable="true" minOccurs="0" />
                            <xs:element name="flexible-identity-presentation" type="flexible-identity-presentation-type" nillable="true" minOccurs="0" />
                            <xs:element name="hotline" type="hotline-type" nillable="true" minOccurs="0" />
                            <xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" nillable="true" minOccurs="0" />
                            <xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" nillable="true" minOccurs="0" />
                            <xs:element name="media-policy" type="media-policy-type" nillable="true" minOccurs="0" />
                            <xs:element name="multi-device-conference-policy" type="multi-device-conference-policy-type" nillable="true" minOccurs="0" />
                            <xs:element name="northbound-call-control" type="northbound-call-control-type" nillable="true" minOccurs="0" />
                            <xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" nillable="true" minOccurs="0" />
                            <xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" nillable="true" minOccurs="0" />
                            <xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" nillable="true" minOccurs="0" />
                            <xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" nillable="true" minOccurs="0" />
                            <xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" nillable="true" minOccurs="0" />
                            <xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type"
                                nillable="true" minOccurs="0" />
                            <xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" nillable="true" minOccurs="0" />
                            <xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type"
                                nillable="true" minOccurs="0" />
                            <xs:element name="user-common-data" type="user-common-data-type" nillable="true" minOccurs="0" />
                            <xs:element name="voice-mail" type="voice-mail-type" nillable="true" minOccurs="0" />
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="context-identity" type="string-length-255-type" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="contextIdentityAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeySet">
            <xs:selector xpath="." />
            <xs:field xpath="@publicId" />
        </xs:key>
        <xs:key name="contextIdentityKeySet">
            <xs:selector xpath="." />
            <xs:field xpath="@context-identity" />
        </xs:key>
    </xs:element>
    <xs:element name="getResponseService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Contains the currently configured MMTel context
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">The default public user identity for the subscriber
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="context-identity" type="string-length-255-type" />
                <xs:element name="concurrency-control" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an integer value indicating the current version of the MMTel service data.
                            This value can be used in a subsequent setMMTel request to make sure that no changes have been made to
                            the service data since the version that was read
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="services" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">MMTel services, the relative order of the existing services must
                            be maintained all new services shall be optional and inserted in alphabetical order within
                            the existing list where possible
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="caller-categorization" type="caller-categorization-type" minOccurs="0" />
                            <xs:element name="calling-name-identity-presentation"  type="calling-name-identity-presentation-type" minOccurs="0" />
                            <xs:element name="communication-distribution" type="communication-distribution-type" minOccurs="0" />
                            <xs:element name="communication-diversion" type="communication-diversion-type" minOccurs="0" />
                            <xs:element name="communication-event-logging" type="communication-event-logging-type" minOccurs="0" />
                            <xs:element name="communication-setup-announcement" type="communication-setup-announcement-type" minOccurs="0" />
                            <xs:element name="communication-waiting" type="communication-waiting-type" minOccurs="0" />
                            <xs:element name="conference" type="conference-type" minOccurs="0" />
                            <xs:element name="customized-alerting-tone" type="customized-alerting-tone-type" minOccurs="0" />
                            <xs:element name="explicit-communication-transfer" type="explicit-communication-transfer-type" minOccurs="0" />
                            <xs:element name="flexible-identity-presentation" type="flexible-identity-presentation-type" minOccurs="0" />
                            <xs:element name="hotline" type="hotline-type" minOccurs="0" />
                            <xs:element name="incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0" />
                            <xs:element name="malicious-communication-identification" type="malicious-communication-identification-type" minOccurs="0" />
                            <xs:element name="media-policy" type="media-policy-type" minOccurs="0" />
                            <xs:element name="multi-device-conference-policy" type="multi-device-conference-policy-type" minOccurs="0" />
                            <xs:element name="northbound-call-control" type="northbound-call-control-type" minOccurs="0" />
                            <xs:element name="operator-controlled-outgoing-barring-programs" type="operator-controlled-outgoing-barring-programs-type" minOccurs="0" />
                            <xs:element name="outgoing-barring-programs" type="outgoing-barring-programs-type" minOccurs="0" />
                            <xs:element name="outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0" />
                            <xs:element name="originating-calling-name-identity-presentation" type="originating-calling-name-identity-presentation-type" minOccurs="0" />
                            <xs:element name="originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0" />
                            <xs:element name="originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type"
                                minOccurs="0" />
                            <xs:element name="terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0" />
                            <xs:element name="terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type"
                                minOccurs="0" />
                            <xs:element name="user-common-data" type="user-common-data-type" minOccurs="0" />
                            <xs:element name="voice-mail" type="voice-mail-type" minOccurs="0" />
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="context-identity" type="string-length-255-type" use="required" >
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="contextIdentityAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyGetResp">
            <xs:selector xpath="." />
            <xs:field xpath="@publicId" />
        </xs:key>
        <xs:key name="contextIdentityKeyGetResp">
            <xs:selector xpath="." />
            <xs:field xpath="@context-identity" />
        </xs:key>
    </xs:element>

    <xs:simpleType name="publicIdentityType">
        <xs:restriction base="xs:anyURI">
            <xs:pattern value="sip:.*"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="string-length-255-type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
        </xs:restriction>
    </xs:simpleType>

    <!--?caller-categorization? -->
    <xs:complexType name="caller-categorization-type">
        <xs:annotation>
            <xs:documentation>
                The caller categorization service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ccat-operator-configuration" type="ccat-operator-configuration-type" nillable="true"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the caller categorization service
                        that are available to the operator rather than the user.
                        This must be present on the creation of the caller-categorization service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ccat-user-configuration" type="ccat-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the caller categorization service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator.
                        This shall only be present if the service is provisioned
                        i.e. ccat-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?calling_name_identity_presentation? -->
    <xs:complexType name="calling-name-identity-presentation-type">
        <xs:annotation>
            <xs:documentation>
                The calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cnip-operator-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the calling name identity presentation service that are available to the operator rather than the user.
                        This must be present on the creation of the calling-name-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the calling name
                                    identity presentation service. If set to "false" this will withdraw the user service, but the cnip-user-configuration
                                    element is kept. This must be present on the creation of the calling-name-identity-presentation service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="cnip-user-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the calling name identity presentation service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        cnip-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="active" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Controls whether the calling name identity presentation service is active or not for this subscriber. The calling name
                                    identity presentation service requires that the user also has the originating identity presentation service active.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?communication-distribution? -->
    <xs:complexType name="communication-distribution-type" >
        <xs:annotation>
            <xs:documentation>
                The communication distribution service. Use xsi:nil="true" to withdraw the entire service.
                Users with the communication distribution service are not supported as targets for communication
                completion so the communication distribution service can only be activated if the user has
                call-completion-monitor-opt-out activated for all variants of communication completion.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fcd-operator-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the flexible communication distribution service that are available to the operator rather than the user.
                        This must be present on the creation of the communication-distribution service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned
                                    with the communication distribution service. If set to "false" this will withdraw the service from the user.
                                    This must be present on the creation of the communication-distribution service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="max-targets" type="max-fcd-targets-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The max-targets element controls the maximum number of distinct targets that the user can have
                                    for communication distribution in addition to the PRIMARY identity. Integer value between 2 and 10.
                                    This must be present on the creation of the communication-distribution service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="primary-hosting" type="hosting-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The primary-hosting element defines where the primary identity is hosted with
                                    values "IMS" for users hosted on the IMS network the MTAS is serving and
                                    "non-IMS" for users who have communication distribution performed by the IMS
                                    network but are not registered on the IMS network e.g. users on a separate
                                    circuit-switched network. This must be present on the creation of the
                                    communication-distribution service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The maximum number of allowed FCD rules in the user document. Not specified or zero limit means no limit
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fcd-divert-primary" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The fcd-divert-primary element has values "activated" or "deactivated". When set to "activated" the
                                    user is able to use divert-primary element to divert the "incoming communication distributed to PRIMARY"
                                    to different target. If set to "deactivated" this will withdraw the divert primary service from the user.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="user-no-reply-timer" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The user-no-reply-timer has values "activated" or "deactivated". When set to "activated" it allows the subscriber to control
                                    the length of the no reply timer for the user.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fcd-op-conditions" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The fcd-op-conditions element is a grouping element for fine-grain provisioning options that control which conditions
                                    the subscriber is permitted to use in communication distribution rules
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The anonymous-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the anonymous condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="busy-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The busy-condition element has values "activated" or "deactivated". When set to "activated" it allows the
                                                subscriber to use the fcd-call-state condition with the value of "busy" in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="identity-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the identity condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="media-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The media-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use media conditions in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="not-registered-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The not-registered-condition element has values "activated" or "deactivated". When set to "activated" it
                                                allows the subscriber to use the fcd-call-state condition with the value of "not-registered" in communication
                                                distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="no-answer-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The no-answer-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the fcd-call-state condition with the value of "no-answer" in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The presence-status-condition element has values "activated" or "deactivated". When set to "activated" it
                                                allows the subscriber to use presence-status conditions in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="validity-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The validity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the validity condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="not-reachable-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The not-reachable-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the fcd-call-state condition with the value of "not-reachable" in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The valid-periods-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the valid-periods condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The invalidity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the invalidity condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the served-identity condition in communication distribution rules.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="fcd-op-actions" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The fcd-op-actions element is a grouping element for fine-grain provisioning options to control which actions
                                    the user is permitted to use for communication distribution rules.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="rule-no-reply-timer" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The rule-no-reply-timer has values "activated" or "deactivated". When set to "activated it allows the subscriber to use the
                                                no reply timer in the action of communication distribution rules to control the length of the no reply timer on a per rule basis.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the play-announcement action in communication distribution rules to control whether the caller
                                                is presented by specific announcement handled by generic announcement service.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="notify-served-user-action" type="activatedType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The notify-served-user-action element has values "activated" or "deactivated". When set to "activated" it allows
                                                the subscriber to use the notify-served-user action in communication distribution rules to control whether the served
                                                user is notified that the call is not distributed towards served user devices.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="fcd-user-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the flexible communication distribution service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        fcd-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="active" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Controls whether the flexible communication distribution service is active or not for this subscriber.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="divert-primary" nillable="true" type="divert-primary-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The divert-primary element is used for diverting the "incoming communication distributed to PRIMARY" to
                                    an alternative target.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fcd-service-options" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Grouping element for a set of zero or more service options
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="NoReplyTimer" type="fcd-no-reply-timer-type" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The NoReplyTimer element specifies the time that must expire without any response before the no-answer
                                                condition is triggered. The value is an integer giving the timer in the range of 5 to 180 seconds. This
                                                value applies to rules with no-answer conditions which do not contain their own individual timer.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="target-list" type="target-list-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    A list defining related targets that can be included in communication distribution.
                                    The target-list in user-common-data is the preferred way to define related targets so
                                    they are available across multiple services. The target-list is retained within
                                    communication-distribution for backwards compatibility.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fcd-ruleset" type="fcd-ruleset-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Grouping element for a set of zero or more flexible communication distribution user rules
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="fcd-rule-key">
                                <xs:selector xpath="./mmtel-context:fcd-rule" />
                                <xs:field xpath="@id" />
                            </xs:key>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?communication-diversion? -->
    <xs:complexType name="communication-diversion-type">
        <xs:annotation>
            <xs:documentation>
                The communication diversion service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cdiv-operator-configuration" type="cdiv-operator-configuration-type" nillable="true"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication diversion service that are available to the
                        operator rather than
                        the user.
                        This must be present on the creation of the communication-diversion service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cdiv-user-configuration" type="cdiv-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication diversion service that are available for the
                        user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the
                        service is
                        provisioned
                        i.e. cdiv-operator-configuration is present and activated is "true"
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?communication-event-logging? -->
    <xs:complexType name="communication-event-logging-type">
        <xs:annotation>
            <xs:documentation>
                The Communication Event Logging service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cel-operator-configuration" type="cel-operator-configuration-type">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication event logging service that are available to the operator rather than the user.
                        This must be present on the creation of the communication event logging service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!--?communication-setup-announcement? -->
    <xs:complexType name="communication-setup-announcement-type">
        <xs:annotation>
            <xs:documentation>
                The communication setup announcement service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="csa-operator-configuration" type="csa-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication setup announcement service that are available to the operator rather than the user.
                        This must be present on the creation of the communication-setup-announcement service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="csa-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the communication setup announcement service.
                        This must be present on the creation of the communication-setup-announcement service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- ruleset is a structured parameter that is optional and nillable -->
            <xs:element name="csa-ruleset" type="csa-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more rules
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="csa-op-rule-key">
                    <xs:selector xpath="./mmtel-context:csa-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="csa-ruleset-type">
        <xs:sequence>
            <xs:element name="csa-rule" type="csa-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling communication setup announcement behavior. The csa-rule element is a sub-MO allowing multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
                <!-- sub MOs need a key. The key for rule is id. -->
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="csa-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete document.
                        This must be present on the creation of a csa-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="csa-conditions" type="csa-conditions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The csa-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied for the rule to take effect.
                        If no conditions are present then the rule is always applicable.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- could just update conditions so actions must be optional -->
            <xs:element name="csa-actions" type="csa-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The csa-actions element is a grouping element for the actions for a rule. This must be present on the creation of a csa-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="csa-conditions-type">
        <xs:sequence>
            <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0" >
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect
                        of deactivating the individual rule. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="identity" type="identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The identity element is a grouping element for conditions which are based on the called party's identity.
                        The condition is satisfied if any of the included one or many elements within it is matched.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="csa-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
                <xs:key name="csa-many-key">
                    <xs:selector xpath="./mmtel-context:many" />
                    <xs:field xpath="@domain" />
                </xs:key>
                <xs:key name="csa-except-id-key">
                    <xs:selector xpath=".//mmtel-context:except-id" />
                    <xs:field xpath="@id" />
                </xs:key>
                <xs:key name="csa-except-domain-key">
                    <xs:selector xpath=".//mmtel-context:except-domain" />
                    <xs:field xpath="@domain" />
                </xs:key>
                <xs:key name="csa-number-match-key">
                    <xs:selector xpath="./mmtel-context:number-match" />
                    <xs:field xpath="@starts-with" />
                </xs:key>
            </xs:element>
            <!-- media is a multiple value parameter -->
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be matched e.g. "audio" or "video".
                        This is a multi-value parameter so it can appear more than once once with several media values that must all be satisfied for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- validity is a structured parameter so it can be nillable -->
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for time periods (intervals) within which the rule is valid.
                        The validity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing validity element -->
                <xs:key name="csa-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>.
                        The valid-periods element is a grouping element that allows assembly of complex time condition based upon several sub-conditions.
                        In order for the valid-periods condition to be satisfied the current date/time must match with all the included sub-conditions.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for time periods (intervals) within which the rule is NOT valid.
                        The invalidity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="csa-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The served-identity element is a grouping element for conditions which are based on a served user’s identity. The condition is satisfied if any of the one elements within it is matched.
                        The served-identity condition must contain at least one sub-element to be valid. If an update would result in no contained sub-elements then the served-identity condition should be deleted.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="csa-served-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="in-sip-request" type="in-sip-request-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The in-sip-request element is a grouping element for regexp conditions on contents of a SIP request. It evaluates to true if ALL of the conditions included within it are fulfilled.
                        The in-sip-request element must contain at least one flexcondition sub-element to be valid. If an update would result in no contained sub-elements then the in-sip-request condition should be deleted.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="csa-in-sip-request-flexcondition-key">
                    <xs:selector xpath="./mmtel-context:flexcondition" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="subscriber-state" type="subscriber-state-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The subscriber-state specifies general purpose subscriber state value with which subscriber-state from operator's common-data would match, if present.
                        If value is matched, the condition is satisfied. It is a string with max length 255.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="device-group" type="device-group-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The device-group element specifies type of the device which must to be matched with the calling device type.
                        Allowed device types are "MOBILE" and "FIXED".
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="access-type" type="access-type-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The access-type element specifies the access type name which must to be matched with session access network type.
                        The access-type must contain only one access network type.
                        Allowed access type values are "CS", "4G", "5G", "WIFI".
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="international" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The international element has values "true" or "false".
                        If present with the value "true", this condition is satisfied if the subscriber calls someone who is in another country than the one where the subscriber calls from.
                        Set to "false" to remove this condition.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="international-exHC" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The international-exHC element has values "true" or "false".
                        If present with the value "true", this condition is satisfied if the subscriber calls someone who is in another country than the one where the subscriber calls from and subscriber’s home country.
                        Set to "false" to remove this condition.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="csa-actions-type">
        <xs:sequence>
            <xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-announcement element has string values from 0 to 32 characters. When the play-announcement action is set with the string value containing characters
                        with the length between 1 to 32, the caller will be presented with the specific announcement handled by generic announcement service. When the play-announcement
                        action is set with the string value containing character with the length of 0, any play-announcement action element in the rule will be deleted from the rule.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?communication-setup-announcement? -->

    <!--?communication-waiting? -->
    <xs:complexType name="communication-waiting-type">
        <xs:annotation>
            <xs:documentation>
                The communication waiting service. Use xsi:nil="true" to withdraw the entire service. If the
                communication waiting service is in mode 0 (normal mode) or alternate mode 1, then it depends on the
                user call admission control service. The communication waiting service can only be activated
                if the user call admission control service is also activated and the waiting-limit is set to
                greater than zero. Due to the mutual dependency with user call admission control, both services
                must be updated in the same request in which communication waiting is activated or deactivated.
                If the communication waiting service is in alternate mode 2, there is no dependency between the
                communication waiting service and the user call admission control service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cw-operator-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication waiting service that are available to the operator rather than the user.
                        This must be present on the creation of the communication-waiting service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Set to true to provision the user with the communication waiting service. This must be present on the creation of
                                    the communication-waiting service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="cw-user-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the communication waiting service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned
                        i.e. cw-operator-configuration is present and activated is "true"
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="active" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Controls whether the communication waiting service is active or not
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!--?communication-waiting? -->

    <!-- Conference -->
    <xs:complexType name="conference-type">
        <xs:annotation>
            <xs:documentation>
                The conference service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="conf-operator-configuration" type="conf-operator-configuration-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the conference service that are available to the operator rather than the user.
                        This must be present on the creation of the conference service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="conf-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with
                        the conference service. If set to "false" this will withdraw the service from the user. This must be present
                        on the creation of the conference service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-number-of-parties" type="max-number-of-parties-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of parties allowed in a conference created by this user. This is an integer in the range 3-32.
                        This must be present on the creation of the conference service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="answer-confirmation" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        If the  answer-confirmation element is present the called party will be played an entry announcement
                        and asked for a DTMF confirmation.
                        Use xsi:nil="true" to delete answer-confirmation element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="block-dialout-invitations" type="empty-element-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        When the element is present the user is blocked from requesting
                        the conference focus to invite participants using the dial-out
                        method (including URI-list usage).
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- Conference -->

    <!-- customized-alerting-tone -->
    <xs:complexType name="customized-alerting-tone-type">
        <xs:annotation>
            <xs:documentation>
                The customized alerting tones service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cat-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the customized alerting tones service that are available to the operator rather than the user.
                        This must be present on the creation of the customized alerting tones service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned
                                    with the customized alerting tones service. If set to "false" this will withdraw the service from the user.
                                    This must be present on the creation of the customized alerting tones service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- customized-alerting-tone -->

    <!-- explicit-communication-transfer -->
    <xs:complexType name="explicit-communication-transfer-type">
        <xs:annotation>
            <xs:documentation>
                The explicit communication transfer service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ect-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the explicit communication transfer service that are available to the operator rather than the user.
                        This must be present on the creation of the explicit communication transfer service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to
                                    "true" the user is provisioned with the explicit communication transfer
                                    service. If set to "false" this will withdraw the service from
                                    the user. This must be present on the creation of the
                                    explicit communication transfer service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- explicit-communication-transfer -->

    <!-- flexible-identity-presentation -->
    <xs:complexType name="flexible-identity-presentation-type">
        <xs:annotation>
            <xs:documentation>
                The flexible identity presentation service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fip-operator-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the flexible identity presentation service that are available to the operator rather than the user.
                        This must be present on the creation of the flexible-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true", "false" or "profile". When set to "true" the user is provisioned
                                    with the flexible identity presentation service.
                                    If set to "false" this will withdraw the user service, but the fip-user-configuration element is kept.
                                    This must be present on the creation of the flexible-identity-presentation service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fip-suppression" type="fip-suppression-enum-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    This parameter contains in what cases the flexible identity presentation shall be suppressed for the subscriber.
                                    The value TOLLFREE will suppress FIP service for toll-free call types.
                                    Allowed suppress value is "TOLLFREE".
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="fip-user-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the flexible identity presentation service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        fip-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="active" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Controls whether the flexible identity presentation service is active or not for this subscriber.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fip-identity" type="xs:anyURI" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity which replaces the served user’s own identity.
                                    It is read only for the user interface and read write for the operator interface.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="msn-fip-identity" nillable="true" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    The MSN identity which can be selected by the MSN service to replace the served user’s own identity.
                                    The msn-fip-identity element specifies the mapping between an id and the identity to be substituted when the
                                    id is used. The msn-fip-identity element is a sub-MO allowing multiple instances with "id" as the unique key.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="id" type="msn-number-type" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The id must be present on the creation of a msn-fip-identity element.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="identity" type="xs:anyURI" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                It is read only for the user interface and read write for the operator interface.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="id" type="msn-number-type" use="required" >
                                    <xs:annotation>
                                        <xs:appinfo>
                                            <jaxb:property name="idAttr"/>
                                        </xs:appinfo>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="fip-use-default-impu-identity" type="empty-element-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    When this element is provisioned, subscriber default IMPU replaces the served user’s own identity.
                                    It is read only for the user interface and read write for the operator interface.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fip-alternative-user-identity" type="fip-alternative-user-identity-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    When this element is provisioned, the element replaces the served user’s own identity.
                                    It is read only for the user interface and read write for the operator interface.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- flexible-identity-presentation -->

    <!-- hotline -->
    <xs:complexType name="hotline-type">
        <xs:annotation>
            <xs:documentation>
                The Hotline service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="hotline-operator-configuration" type="hotline-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the Hotline service that are available to the operator rather than the user.
                        This must be present on the creation of the Hotline service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hotline-user-configuration" type="hotline-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the Hotline service that are available to the user rather than the operator.
                        This must be present on the creation of the Hotline service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="hotline-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>Operator Part of Hotline.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the Hotline service.
                        If set to "false" this will withdraw the user service and the hotline-user-configuration element must be preserved.
                        This must be present on the creation of the Hotline service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unconditional-condition" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The unconditional-condition element groups parameters for Unconditional Hotline (Automatic Re-routing to Customer Care).
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the unconditional-condition is active and used by Hotline service.
                                    This must be present on the creation of an unconditional-condition element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="hotline-number" type="stored-number-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The hotline-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261
                                    contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC.
                                    This must be present on the creation of an unconditional-condition element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="instant-condition" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The instant-condition element groups parameters for Instant Hotline.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the instant-condition is active and used by Hotline service.
                                    This must be present on the creation of an instant-condition element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="hotline-number" type="stored-number-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The hotline-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261
                                    contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC.
                                    This must be present on the creation of an instant-condition element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="delayed-condition" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The delayed-condition element groups parameters for Delayed Hotline. Currently it contains only one element: activated.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the Delayed Hotline service.
                                    If set to "false" this will withdraw the user service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="whitelist-condition" type="whitelist-condition-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The whitelist-condition element groups parameters for Whitelist Hotline.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="hotline-user-configuration-type">
        <xs:annotation>
            <xs:documentation>User Part of Hotline.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the Delayed Hotline service is active or not for this subscriber.
                        It can be active only if it is active in the operator part as well.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hotline-number" type="stored-number-type">
                <xs:annotation>
                    <xs:documentation>
                        The stored number in its full form, which is substituted when the user dials the corresponding hotline service code.
                        The stored-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261
                        contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="called-number" type="stored-number-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The triggering number allows to specify additional criteria for Hotline service triggering (apart the service code defined in CM mtasHotlineServiceCode).
                        The triggering number takes precedence over mtasHotlineServiceCode CM.
                        The format of CDATA of this element is further specified in the documentation of stored-number-type.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="whitelist-condition-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the whitelist-condition is active and used by Hotline service.
                        This must be present on the creation of a whitelist-condition element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hotline-number" type="stored-number-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The hotline-number is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261
                        contains a normalized number, or a number that can be normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC.
                        This must be present on the creation of a whitelist-condition element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- hotline -->

    <!-- Communication Barring -->
    <xs:complexType name="incoming-communication-barring-type">
        <xs:annotation>
            <xs:documentation>
                The incoming communication barring service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="icb-operator-configuration" type="icb-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the incoming communication barring service that are available to the operator rather than the user.
                        This must be present on the creation of the incoming-communication-barring service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="icb-user-configuration" type="icb-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the incoming communication barring service that are available for the user to set
                        directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
                        provisioned i.e. icb-operator-configuration is present and activated is "true"
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="icb-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the incoming
                        communication barring service. If set to false this will withdraw the user service, but the icb-user-configuration
                        element must be preserved. This must be present on the creation of the incoming-communication-barring service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="icb-ruleset" type="icb-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more operator rules. These rules apply regardless of whether activated
                        is "true" or "false".
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="icb-op-rule-key">
                    <xs:selector xpath="./mmtel-context:icb-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="icb-op-conditions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The icb-op-conditions element is a grouping element for fine-grain provisioning options that control which
                        condition elements the user is permitted to use in incoming communication barring rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The anonymous-condition element has values "activated" or "deactivated". When set to "activated" it
                                    allows the subscriber to use the anonymous condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="roaming-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The roaming-condition element has values "activated" or "deactivated". When set to "deactivated"
                                    roaming to the user are barred.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="communication-diverted-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The communication-diverted-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows the subscriber to use the communication-diverted condition in incoming
                                    communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the identity condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="media-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The media-condition element has values "activated" and "deactivated". When set to "activated" it allows
                                    the subscriber to use media conditions in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The other-identity-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use the other-identity condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The presence-status-condition element has values "activated" and "deactivated". When set to
                                    "activated" it allows the subscriber to use presence-status conditions in incoming communication
                                    barring rules. This is not currently supported by incoming communication barring and should be
                                    omitted or set to "deactivated"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="validity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The validity-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use the validity condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The valid-periods-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the valid-periods condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The invalidity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the invalidity condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the served-identity condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="unconditional-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The unconditional-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the unconditional condition in incoming communication barring rules.
                                    This is when there is no element "icb-conditions" set, empty element "icb-conditions/" or
                                    elements "icb-conditions" "/icb-conditions".
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="subscriber-state-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The subscriber-state-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the subscriber-state condition in incoming communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="icb-op-actions" type="incoming-call-barring-op-actions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The icb-op-actions element is a grouping element for fine-grain provisioning options to control which action
                        elements the user is permitted to use in incoming communication barring rules.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of allowed incoming communication barring rules in the user document. Not specified or zero limit means no limit
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="icb-user-configuration-type">
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the incoming communication barring
                        service is active or not for this subscriber. Note that this controls the user rules but has no effect on the
                        operator rules.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="icb-ruleset" type="icb-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more user rules.
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="icb-user-rule-key">
                    <xs:selector xpath="./mmtel-context:icb-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="icb-ruleset-type">
        <xs:sequence>
            <xs:element name="icb-rule" type="icb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling incoming communication barring behaviour. The icb-rule element is a sub-MO
                        allowing multiple instances with "id" as the unique key
                    </xs:documentation>
                </xs:annotation>
                <!-- sub MOs need a key. The key for rule is id. -->
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- rule is a sub-MO -->
    <xs:complexType name="icb-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete document. This
                        must be present on the creation of an icb-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="icb-conditions" type="icb-conditions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The icb-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
                        for the rule to take effect. If no conditions are present then the rule is always applicable.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set -->
            <!-- could just update conditions so actions must be optional -->
            <xs:element name="cb-actions" type="incoming-call-barring-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cb-actions element is a grouping element for the actions for a rule. For communication barring an
                        allow action must be present in each rule. This must be present on the creation of an icb-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="icb-conditions-type">
        <xs:sequence>
            <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0" >
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect of
                        deactivating the individual rule. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="icb-caller-identity" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The icb-caller-identity element is a grouping element for conditions which are based on the caller's identity
                        (or lack of an identity in the case of anonymous).
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:element name="anonymous" type="empty-element-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The anonymous element is an empty element specifying a condition which is satisfied if the caller is
                                    anonymous. This can be removed by deleting the enclosing icb-caller-identity element or by replacing
                                    it with an identity or other-identity element. The elements anonymous, identity and other-identity are
                                    mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="other-identity" type="empty-element-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The other-identity element is an empty element which matches any identity that has not been
                                    specified by any of the other rules in the ruleset. It allows for setting a default policy. This can be
                                    removed by deleting the enclosing icb-caller-identity element or by replacing it with an anonymous
                                    or identity element. The elements anonymous, identity and other-identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <!-- no nilling at the level of identity - use nilling on icb-caller-identity to remove -->
                        <xs:element name="identity" type="identity-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity element is a grouping element for conditions which are based on the caller's identity.
                                    The condition is satisfied if any of the included one or many elements within it is matched. This
                                    can be removed by deleting the enclosing icb-caller-identity element or by replacing it with an
                                    anonymous or other-identity element. The elements anonymous, identity and other-identity are
                                    mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="icb-one-key">
                                <xs:selector xpath="./mmtel-context:one" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="icb-many-key">
                                <xs:selector xpath="./mmtel-context:many" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="icb-except-id-key">
                                <xs:selector xpath=".//mmtel-context:except-id" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="icb-except-domain-key">
                                <xs:selector xpath=".//mmtel-context:except-domain" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="icb-number-match-key">
                                <xs:selector xpath="./mmtel-context:number-match" />
                                <xs:field xpath="@starts-with" />
                            </xs:key>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="roaming" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The roaming element has values "true" or "false". If present with the value "true", the incoming calls when roaming are barred. Set to "false" to remove this
                        condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="communication-diverted" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The communication-diverted element has values "true" or "false". If present with the value "true", this
                        condition is satisfied if the incoming communication has been diverted. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be matched e.g.
                        "audio" or "video". This is a multi-value parameter so it can appear more than once with with several media values
                        that must all be satisfied for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- validity is a structured parameter so it can be nillable -->
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for time periods (intervals) within which the rule is valid.
                        The validity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing validity element -->
                <xs:key name="icb-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The presence-status element contains a presence status value that the user must satisfy for the condition to be
                        matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can appear more than
                        once with several presence status values that must all be satisfied for the overall condition to be matched. This
                        condition is not currently supported by incoming communication barring and will always evaluate to false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-periods element is a grouping element for recurring time periods (intervals) within which the rule is valid.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for time periods (intervals) within which the rule is NOT valid.
                        The invalidity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="icb-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The served-identity element is a grouping element for conditions which are based on the user's
                        served identity. The condition is satisfied if any of the included elements within it is matched.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="icb-served-identity-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="subscriber-state" type="subscriber-state-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The subscriber-state specifies general purpose subscriber state value with which subscriber-state
                        from operator's common-data would match, if present. If value is matched, the condition is satisfied.
                        A string value with max length 255.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="incoming-call-barring-actions-type">
        <xs:sequence>
            <xs:element name="allow" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>
                        The allow element has values "true" or "false". If set to "false" then any communications satisfying
                        the corresponding conditions will be barred unless overridden by another rule with allow set to "true".
                        If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. not barred.
                        This must be present on the creation of a communication barring rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="do-not-disturb" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The do-not-disturb element has values "true" or "false". When set to "true" the do-not-disturb element is added
                        into actions part of the rule and if there is any communications satisfying the corresponding conditions and being barred
                        (allow=false), then the incoming communication will be handled by Do Not Disturb service instead of normal incoming
                        communication barring service (e.g. treated with different charging scheme, etc.).
                        If set to "false" then the element is removed from the subscriber actions part of the rule and the incoming communication
                        will be handled by normal incoming communication barring service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:choice>
                <xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The play-announcement element has string values from 0 to 32 characters.
                            When the play-announcement action is set with the string value containing characters with the length between 1 to 32,
                            if there is any communications satisfying the corresponding conditions and being barred (allow=false), the caller will be
                            presented with the announcement associated with the announcement code pointed by the string value.
                            When the play-announcement action is set with the string value containing character with the length of 0,
                            any play-announcement action element in the rule will be deleted from the rule.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="play-segmented-announcement" minOccurs="0" maxOccurs="1" nillable="true">
                    <xs:annotation>
                        <xs:documentation>
                            If there is any communications satisfying the corresponding conditions, the caller will be presented with the segmented
                            announcement associated with the announcement code pointed by the "announcement-name" attribute of the element. Before
                            trying to invoke any, the segmented (generic) announcement must be configured in MTAS with the same name as given in
                            the "announcement-name" attribute.
                            The segmented announcement may contain contain embedded variables, which can be presented in the "announcement-variable"
                            child element. The configured segmented (generic) announencement shall contain as many standalone voice variable segments
                            as many "announcement-variable" child elements are defined for the "play-segmented-announcement" action.
                            The keyed "play-segmented-announcement" action with the "announcement-name" attribute can be deleted from the list of
                            actions by setting the "xs:nil" attribute to true.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:complexContent>
                            <xs:extension base="mmtel-context:play-segmented-announcement-type">
                                <xs:attribute name="announcement-name" type="announcement-name-type" use="required" >
                                    <xs:annotation>
                                        <xs:appinfo>
                                            <jaxb:property name="announcementNameAttr"/>
                                        </xs:appinfo>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:extension>
                        </xs:complexContent>
                    </xs:complexType>
                    <xs:key name="AnnouncementVariableNameKey_IncomingCommunicationBarring">
                        <xs:annotation>
                            <xs:documentation>
                                An announcement variable can be embedded into a segmented announcemet only once. Announcement variables, under
                                the scope of a segmented announcement, are made unique by the "variable-name" attribute.
                            </xs:documentation>
                        </xs:annotation>
                        <xs:selector xpath="./mmtel-context:announcement-variable" />
                        <xs:field xpath="@variable-name" />
                    </xs:key>
                </xs:element>
            </xs:choice>
            <xs:element name="status-code" type="status-code-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The status-code element defines the SIP error response which will be sent to the caller.
                        This element is ignored if allow action set to "true".
                        Allowed value range is 400-699.
                        Use xsi:nil="true" to delete this element.
                        This element is optional
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-header" type="reason-header-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element of reason-header elements to be sent in  SIP error response.
                        This element is ignored if allow action set to "true".
                        Use xsi:nil="true" to delete this element.
                        This element is optional
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="incoming-call-barring-op-actions-type">
        <xs:sequence>
            <xs:element name="allow-true-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The allow-true-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the allow action with the value of "true" in the associated communication barring rules to explicitly allow
                        communications that match the associated conditions. With this absent or set to "deactivated" the subscriber is only
                        permitted to use the allow action with the value of "false" to bar communications.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="do-not-disturb-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The do-not-disturb-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the do-not-disturb element by setting the value to "true" to add the element into the actions part of
                        the rule and setting the value to "false" to remove the element from the actions part of the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to set the do-not-disturb element with the value
                        of "true" to add the element into the rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the play-announcement action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-segmented-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the play-segmented-announcement action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the play-segmented-announcement action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status-code-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The status-code-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the status-code action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the status-code action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-header-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The reason-header-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the reason-header action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the reason-header action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- Communication Barring -->

    <!-- malicious-communication-identification -->
    <xs:complexType name="malicious-communication-identification-type">
        <xs:annotation>
            <xs:documentation>
                The malicious communication identification service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="mcid-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the malicious communication identification service that are available to the operator
                        rather than the user. This must be present on the creation of the  malicious-communication-identification service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the malicious
                                    communication identification service. If set to "false" this will withdraw the service from the user. This must be
                                    present on the creation of the malicious-communication-identification service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mcid-mode" type="mcid-mode-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The mcid-mode element has values "permanent", "temporary" or "inactive". If set to "permanent" then all
                                    terminating  communications are logged.  If set to "temporary" this allows a recent communication to be
                                    logged on user request. If set to "inactive" then logging of terminating calls is disabled. This must be
                                    present on the creation of the malicious-communication-identification service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mcid-orig-mode" type="mcid-orig-mode-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The mcid-orig-mode element has values "permanent" or "inactive". If set to "permanent" then all originating
                                    communications are logged. If set to "inactive" then logging of originating communications is disabled.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- malicious-communication-identification -->

    <!-- media-policy -->
    <xs:complexType name="media-policy-type">
        <xs:sequence>
            <xs:element name="mp-operator-configuration" type="mp-operator-configuration-type" nillable="true"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the media policy service that are available to the operator
                        rather than the user. This must be present on the creation of the media-policy service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mp-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with
                        the media policy service. This must be present on the creation of the media-policy service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- ruleset is a structured parameter that is optional and nillable -->
            <xs:element name="mp-ruleset" type="mp-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more user rules
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="mp-rule-key">
                    <xs:selector xpath="./mmtel-context:mp-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mp-ruleset-type">
        <xs:sequence>
            <xs:element name="mp-rule" type="mp-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling media policy behaviour. The mp-rule element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
                <!-- sub MOs need a key. The key for rule is id. -->
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- rule is a sub-MO -->
    <xs:complexType name="mp-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete
                        document. This must be present on the creation of an mp-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mp-conditions" type="mp-conditions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The mp-conditions element is a grouping element for conditions for a rule. All conditions must
                        be satisfied for the rule to take effect.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mp-actions" type="mp-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The mp-actions element is a grouping element for the actions for a rule. This must be present
                        on the creation of a mp-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required">
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="mp-conditions-type">
        <xs:sequence>
            <xs:element name="media" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be matched.
                        Possible values are "audio", "video", "text", "application" and "message". This parameter can appear
                        once in a rule and must be present on the creation of an mp-rule element.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="audio" />
                        <xs:enumeration value="video" />
                        <xs:enumeration value="text" />
                        <xs:enumeration value="application" />
                        <xs:enumeration value="message" />
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mp-actions-type">
        <xs:sequence>
            <xs:element name="allow" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The allow element has values "true" or "false". If set to "false" then any media line matching the
                        condition will be blocked. If set to "true" then the media line will not be affected. This must be
                        present on the creation of an mp-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- media-policy -->

    <!-- multi-device-conference-policy -->
    <xs:complexType name="multi-device-conference-policy-type">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                The multi device conference policy service.
                Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="mdcp-operator-configuration" type="mdcp-operator-configuration-type" nillable="true"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        The configuration parameters for the multi device conference policy
                        service that are available to the operator rather than the user.
                        This must be present on the creation of the multi-device-conference-policy service.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="mdcp-device-group-type-key">
                    <xs:selector xpath="./mmtel-context:mdcp-device-group" />
                    <xs:field xpath="@name" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mdcp-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        The activated element has values "true" or "false". When set to "true"
                        the user
                        is provisioned with the multi device conference policy service. If set
                        to "false" this will withdraw the service from the user.
                        This must be present on the creation of the multi-device-conference-policy service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mdcp-device-group" type="mdcp-device-group-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Defines the group of the devices. Operator can create several device groups.
                        The mdcp-device-group element is a sub-MO allowing multiple instances with "name" as the unique key
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mdcp-device-group-type">
        <xs:sequence>
            <xs:element name="name" type="mdcp-device-group-name-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Defines the name of the device group.
                        MTAS allows "FIXED" group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="block-conference-usage" type="empty-element-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        When the element is present a device belonging to the device-group is blocked
                        from using the conference service.
                        Use xsi:nil="true" to delete this element.
                        This element is optional
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="block-dialout-invitations" type="empty-element-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        When the element is present a device belonging to the device-group is blocked
                        from requesting the conference focus to invite participants using the dial-out
                        method (including URI-list usage).
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <!-- multi-device-conference-policy -->

    <!-- northbound-call-control -->
    <xs:complexType name="northbound-call-control-type" >
        <xs:annotation>
            <xs:documentation>
                The Northbound Call Control service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ncc-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the Northbound Call Control service are available to the operator only.
                        This element must be present on the creation of the Northbound Call Control service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with
                                    the northbound call control service. If set to "false" this will withdraw the service from the user. This must be present
                                    on the creation of the northbound call control service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:choice>
                            <xs:sequence>
                                <xs:element name="gsm-scf-address" type="E164-number-type" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This E.164 number is the address of the gsmSCF.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="originating-service-key" type="xs:int" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This is the key that identifies the application within the gsmSCF. A value of -1 can be provisioned, which means that
                                            CAMEL application will not be triggered for the user on the originating side.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="terminating-service-key" type="xs:int" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This is the key that identifies the application within the gsmSCF. A value of -1 can be provisioned, which means that
                                            CAMEL application will not be triggered for the user on the terminating side.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="default-call-handling" type="default-call-handling-type" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines how the call shall proceed in case of signaling failure towards the gsmSCF.
                                            Possible values are continue and release.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="imsi" type="imsi-type" minOccurs="0" nillable="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The IMSI for this subscriber. Used by the NCC service CAMEL interaction service in MTAS,
                                            for the case when the served user is unregistered. Typically this will happen
                                            when an IMS user originates a call from an CS access (ICS use-case).
                                            This element is mandatory if CM attribute mtasNccImsiBehavior is set.
                                            Use xsi:nil="true" to withdraw the element.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="terminating-gsm-scf-address" type="E164-number-type" minOccurs="0" nillable="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This E.164 number is the address of the terminating gsmSCF. If this element is not provisioned,
                                            the value of element gsm-scf-address is used as the address of the terminating gsmSCF.
                                            Use xsi:nil="true" to withdraw the element.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="terminating-service-suppression-override" type="xs:boolean" minOccurs="0" nillable="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This overrides the value of node level configuration attribute mtasSsfSuppressTerminatingServiceInHplmn
                                            used for the suppression of terminating CAMEL Application trigger.
                                            If this is set to "true", terminating CAMEL Application will be triggered if user is in home network.
                                            Use xsi:nil="true" to withdraw the element.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="terminating-default-call-handling" type="default-call-handling-type" minOccurs="0"
                                            nillable="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines how the call shall proceed in case of signaling failure towards the terminating gsmSCF.
                                            Possible values are continue and release.
                                            If this element is not provisioned, the value of element default-call-handling is used.
                                            Use xsi:nil="true" to withdraw the element.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                            <xs:sequence>
                                <xs:element name="px-originating-trigger" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This is the trigger that defines if the originating MTAS shall contact the Parlay X application server.
                                        </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                        <xs:sequence>
                                            <xs:element name="px-application-address" type="xs:anyURI" minOccurs="1">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        This is the URL, including the port, to the Parlay X application server.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:element>
                                            <xs:sequence>
                                                <xs:element name="px-call-notification" type="px-call-notification-type" minOccurs="1"
                                                            maxOccurs="6">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Defines the possible Call Events that shall be reported on the CallNotification interface.
                                                            Possible values are busy, not-reachable, no-answer, called-number, answer and disconnected.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="px-terminating-trigger" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This is the trigger that defines if the terminating MTAS shall contact the Parlay X application server.
                                        </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                        <xs:sequence>
                                            <xs:element name="px-application-address" type="xs:anyURI" minOccurs="1">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        This is the URL, including the port, to the Parlay X application server.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:element>
                                            <xs:sequence>
                                                <xs:element name="px-call-notification" type="px-call-notification-type" minOccurs="1"
                                                            maxOccurs="6">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Defines the possible Call Events that shall be reported on the CallNotification interface.
                                                            Possible values are busy, not-reachable, no-answer, called-number, answer and disconnected.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:choice>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- northbound-call-control -->

    <!-- operator-controlled-outgoing-barring-programs -->
    <xs:complexType name="operator-controlled-outgoing-barring-programs-type">
        <xs:annotation>
            <xs:documentation>
                The operator controlled outgoing barring programs service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ocobp-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the operator controlled outgoing barring programs service that are available to the operator rather than
                        the user. This must be present on the creation of the operator-controlled-outgoing-barring-programs service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the operator controlled
                                    outgoing barring programs service. If set to "false" this will withdraw the service from the user. This must be present on
                                    the creation of the operator-controlled-outgoing-barring-programs service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:choice>
                            <xs:element name="operator-barring-program" type="operator-barring-program-type" nillable="true" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>
                                        The operator-barring-program element is a container for each of the categories of outgoing communications that is to be
                                        barred by the service. The operator-barring-program and operator-permitted-program are mutually exclusive.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="operator-permitted-program" type="operator-permitted-program-type" nillable="true" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>
                                        The operator-permitted-program element is a container for each of the categories of outgoing communications that is to be
                                        allowed by the service - any identity not matched by one of these categories or the global white list is barred.
                                        The operator-barring-program and operator-permitted-program are mutually exclusive.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                        <xs:element name="operator-diversion-barring-program" type="operator-diversion-barring-program-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The operator-diversion-barring-program element is a container for each of the categories of outgoing communications
                                    that should be barred as diversion targets.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="operator-barring-program-type">
        <xs:sequence minOccurs="0" maxOccurs="83">
            <xs:element name="category-name" type="category-name-type" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The category-name element contains the name of a category of calls to be barred. This is a multi-value parameter and
                        can appear between 0 and 83 times to cover each category of outgoing communications to be barred. The value of each
                        category-name element is a string of up to 32 characters that should match one of the category names
                        defined by the mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values
                        "Local", "Non Local" or "Allow Local".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="operator-permitted-program-type">
        <xs:sequence minOccurs="0" maxOccurs="83">
            <xs:element name="category-name" type="category-name-type" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The category-name element contains the name of a category of calls to be permitted. This is a multi-value parameter and
                        can appear between 0 and 83 times to cover each category of outgoing communications to be permitted. The value of each
                        category-name element is a string of up to 32 characters that should match one of the category names
                        defined by the mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values
                        "Local" or "Non Local".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="operator-diversion-barring-program-type">
        <xs:sequence minOccurs="0" maxOccurs="83">
            <xs:element name="category-name" type="category-name-type">
                <xs:annotation>
                    <xs:documentation>
                        The category-name element contains the name of a category of calls to be barred for diverted communications. This is a multi-value parameter and
                        can appear between 0 and 83 times to cover each category of outgoing communications to be barred. The value of each
                        category-name element is a string of up to 32 characters that should match one of the category names
                        defined by the mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values
                        "Local", "Non Local" or "Allow Local".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- operator-controlled-outgoing-barring-programs -->

    <!-- outgoing-barring-programs -->
    <xs:complexType name="outgoing-barring-programs-type">
        <xs:annotation>
            <xs:documentation>
                The outgoing barring programs service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="obp-operator-configuration" type="bp-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the outgoing barring programs service that are available to the operator rather than the user.
                        This must be present on the creation of the outgoing-barring-programs service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="obp-user-configuration" type="bp-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the outgoing barring programs service that are available for the user to set directly. These
                        can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        obp-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="bp-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>Operator part of outgoing-barring-programs</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the outgoing barring programs
                        service. If set to "false" this will withdraw the user service, but the obp-user-configuration element is preserved.
                        This must be present on the creation of the outgoing-barring-programs service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="scheme" type="program-scheme-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The element scheme has values "single" and "multiple" and controls which type of barring programs apply to the subscriber. The
                        "single" scheme allows one program at a time. With the "multiple" scheme, several programs can be combined at any time,
                        allowing the individual programs to be simpler. This must be present on the creation of the outgoing-barring-programs service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="default-barring-program" type="program-number" minOccurs="0" maxOccurs="1" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The element specifies the default barring program. Only one barring program can be provisioned at the time.
                        It is an integer in the range 0-255, when the scheme is set to single. If the scheme is set to multiple the allowed range is 0-49.
                        When the user via SSC codes activates barring program without specifying a barring program the value in this element, if present,
                        will be added to the user part. In case of multiple scheme the number in this element will be translated to a category before written
                        to the user part. The attribute is nillable which means it can be deleted.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="program-scheme-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="single"/>
            <xs:enumeration value="multiple"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="bp-user-configuration-type">
        <xs:annotation>
            <xs:documentation>User part of outgoing-barring-programs</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the outgoing barring programs service is active or not.
                        for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!--	EMA does not support nilling of single value parameters so the wrapping structured parameter provisioned-program is
                    included to allow nilling at that level -->
            <xs:element name="provisioned-program" type="provisioned-program-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The provisioned-program element is a containing element allowing the choice between either single-program or multiple-programs.
                        The choice must reflect the provisioned value of scheme
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="provisioned-program-type">
        <xs:choice>
            <xs:element name="single-program" type="program-number" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The single-program element contains the number of the combined barring program to be used. It is an integer in the range 0-255.
                        The elements multiple-programs and single-program are mutually exclusive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="multiple-programs" type="multiple-program-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The multiple-programs element is a container for each of the categories of outgoing communications that is to be barred by the
                        service. The elements multiple-programs and single-program are mutually exclusive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="multiple-program-type">
        <xs:sequence minOccurs="0" maxOccurs="16">
            <xs:element name="category-name" type="category-name-type" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The category-name element contains the name of a category of calls to be barred. This is a multi-value parameter and
                        can appear between 0 and 16 times to cover each category of outgoing communications to be barred. The value of each
                        category-name element is a string of up to 32 characters that should match one of the category names defined by the
                        mtasOcbBCatName attributes or one of the special values "Local", "Non Local" or "Allow Local".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- outgoing-barring-programs -->

    <!-- outgoing-communication-barring -->
    <xs:complexType name="outgoing-communication-barring-type">
        <xs:annotation>
            <xs:documentation>
                The outgoing communication barring service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ocb-operator-configuration" type="ocb-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the outgoing communication barring service that are available to the operator rather
                        than the user. This must be present on the creation of the outgoing-communication-barring service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ocb-user-configuration" type="ocb-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the outgoing communication barring service that are available for the user to set
                        directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
                        provisioned i.e. ocb-operator-configuration is present and activated is "true"
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ocb-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the outgoing
                        communication barring service. If set to false this will withdraw the user service, but the ocb-user-configuration
                        element is preserved. This must be present on the creation of the outgoing-communication-barring service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ocb-ruleset" type="ocb-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more operator rules. These rules apply regardless of whether activated.
                        is "true" or "false".
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="ocb-op-rule-key">
                    <xs:selector xpath="./mmtel-context:ocb-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="ocb-op-conditions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ocb-op-conditions element is a grouping element for fine-grain provisioning options that control which condition
                        elements the user is permitted to use in outgoing communication barring rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity-condition element has values "activated" or "deactivated". When set to "activated" it allows the
                                    subscriber to use the identity condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="roaming-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The roaming-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use roaming conditions in outgoing communication barring rules.
                                    This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="international-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The international-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use international conditions in outgoing communication barring rules.
                                    This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="international-exHC-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The international-exHC-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use international-exHC conditions in outgoing communication barring rules.
                                    This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="media-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The media-condition element has values "activated" or "deactivated". When set to "activated" it allows the
                                    subscriber to use media conditions in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The other-identity-condition element has values "activated" or "deactivated". When set to "activated" it
                                    allows the subscriber to use the other-identity condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The presence-status-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use presence-status conditions in outgoing communication barring rules.
                                    This is not currently supported by outgoing communication barring and should be omitted or set to "deactivated"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="validity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The validity-condition element has values "activated" or "deactivated". When set to "activated" it allows the
                                    subscriber to use the validity condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The valid-periods-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the valid-periods condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The invalidity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the invalidity condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="carrier-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The carrier-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the carrier condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="carrier-select-code" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The carrier-select-code-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows the subscriber to use the carrier-select-code element of the carrier condition in outgoing
                                    communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The served-identity-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the served-identity condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="unconditional-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The unconditional-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the unconditional condition in outgoing communication barring rules.
                                    This is when there is no element "ocb-conditions" set, empty element "ocb-conditions/" or
                                    elements "ocb-conditions" "/ocb-conditions".
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="subscriber-state-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The subscriber-state-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the subscriber-state condition in outgoing communication barring rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="b-number-type-condition" type="activatedType" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The b-number-type-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the b-number-type condition in outgoing communication barring rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="b-network-type-condition" type="activatedType" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The b-network-type-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the b-network-type condition in outgoing communication barring rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="localness-condition" type="activatedType" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The localness-condition element has values "activated" or "deactivated". When set to "activated" it allows
                                    the subscriber to use the localness condition in outgoing communication barring rules.
                                    Use xsi:nil="true" to delete this element
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ocb-op-actions" type="outgoing-call-barring-op-actions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ocb-op-actions element is a grouping element for fine-grain provisioning options to control which action elements
                        the user is permitted to use in outgoing communication barring rules.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of allowed outgoing communication barring rules in the user document. Not specified or zero limit means no limit
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ocb-user-configuration-type">
        <xs:annotation>
            <xs:documentation>
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the outgoing communication barring service is active
                        or not for this subscriber. Note that this controls the user rules but has no effect on the operator rules.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ocb-ruleset" type="ocb-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more user rules.
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="ocb-user-rule-key">
                    <xs:selector xpath="./mmtel-context:ocb-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ocb-ruleset-type">
        <xs:sequence>
            <xs:element name="ocb-rule" type="ocb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling outgoing communication barring behaviour. The ocb-rule element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ocb-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete document.
                        This must be present on the creation of an ocb-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ocb-conditions" type="ocb-conditions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ocb-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
                        for the rule to take effect. If no conditions are present then the rule is always applicable.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set could just update conditions
                so actions must be optional -->
            <xs:element name="cb-actions" type="outgoing-call-barring-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cb-actions element is a grouping element for the actions for a rule. For outgoing communication barring an
                        allow action must be present in each rule. This must be present on the creation of an ocb-rule element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="ocb-conditions-type">
        <xs:sequence>
            <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0" >
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect
                        of deactivating the individual rule. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ocb-caller-identity" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ocb-caller-identity element is a grouping element for conditions which are based on the called party's identity.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:element name="other-identity" type="empty-element-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The other-identity element is an empty element which matches any identity that has not been specified by
                                    any of the other rules in the ruleset. It allows for setting a default policy. This can be removed by deleting the
                                    enclosing ocb-caller-identity element or by replacing it with an identity element. The elements identity
                                    and other-identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <!-- no nilling at the level of identity - use nilling on ocb-caller-identity to remove -->
                        <xs:element name="identity" type="identity-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity element is a grouping element for conditions which are based on the called party's identity. The
                                    condition is satisfied if any of the included one or many elements within it is matched. This can be removed
                                    by deleting the enclosing ocb-caller-identity element or by replacing it with an other-identity element. The
                                    elements identity and other-identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="ocb-one-key">
                                <xs:selector xpath="./mmtel-context:one" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="ocb-many-key">
                                <xs:selector xpath="./mmtel-context:many" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="ocb-except-id-key">
                                <xs:selector xpath=".//mmtel-context:except-id" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="ocb-except-domain-key">
                                <xs:selector xpath=".//mmtel-context:except-domain" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="ocb-number-match-key">
                                <xs:selector xpath="./mmtel-context:number-match" />
                                <xs:field xpath="@starts-with" />
                            </xs:key>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="roaming" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The roaming element has values "true" or "false". If present with the value "true", then outgoing calls when roaming are barred. Set to "false" to remove
                        this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="international" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The international element has values "true" or "false". If present with the value "true", the outgoing calls if they are international are barred. Set to
                        "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="international-exHC" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The international-exHC element has values "true" or "false". If present with the value "true", the outgoing calls if they are international excluding calls
                        to Home Country are barred. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be matched e.g. "audio" or
                        "video". This is a multi-value parameter so it can appear more than once with several media values that must all be satisfied
                        for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- validity is a structured parameter so it can be nillable -->
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for time periods (intervals) within which the rule is valid.
                        The validity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing validity element -->
                <xs:key name="ocb-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The presence-status element contains a presence status value that the user must satisfy for the condition to be matched
                        e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can appear more than once with several
                        presence status values that must all be satisfied for the overall condition to be matched. This condition is not currently
                        supported by outgoing communication barring and will always evaluate to false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-periods element is a grouping element for recurring time periods (intervals) within which the rule is valid.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for time periods (intervals) within which the rule is NOT valid.
                        The invalidity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="ocb-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="carrier" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The carrier element is a grouping element for conditions which are based on the carrier selected for the call
                        on call-by-call basis.
                        If no sub-element is specified, all carriers are matched.
                        The carriers that match to the pre-subscribed carriers for the current call-type are subject to this condition.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="carrier-select-code" type="carrier-select-code-type" nillable="true" minOccurs="0" maxOccurs="10">
                            <xs:annotation>
                                <xs:documentation>
                                    The carrier-select-code element contains the dialed Carrier Select Code.
                                    This is a multi-value parameter so it can appear more than once with several Carrier Select Codes.
                                    If any of them is matches, the carrier condition is fulfilled.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="carrier-name" type="carrier-name-type" nillable="true" minOccurs="0" maxOccurs="10">
                            <xs:annotation>
                                <xs:documentation>
                                    The carrier-name element contains an alias name of the carrier selected for the call on call-by-call basis.
                                    This is a multi-value parameter so it can appear more than once with several carrier names.
                                    If any of them is matches, the carrier condition is fulfilled.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The served-identity element is a grouping element for conditions which are based on the user's
                        served identity. The condition is satisfied if any of the included elements within it is matched.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="ocb-served-identity-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="subscriber-state" type="subscriber-state-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The subscriber-state specifies general purpose subscriber state value with which subscriber-state
                        from operator's common-data would match, if present. If value is matched, the condition is satisfied.
                        A string value with max length 255.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="b-number-type" type="b-number-type-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The b-number-type element contains the B Number type based on B number classification field value in Number Translation rule.
                        Possible values are "TOLLFREE", "NSC", "GP1" to "GP10".
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="b-network-type" type="string-length-255-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The b-network-type element contains the B number network type mapped by Number Portability service.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="localness" type="localness-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The localness element defines the call type of the call.
                        Possible values are "Local", "Non Local", "L_National", "L_International", "L_IntraLata", "L_IntraLataToll", "L_InterLata" and "L_NanpZone1".
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="outgoing-call-barring-actions-type">
        <xs:sequence>
            <xs:element name="allow" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>
                        The allow element has values "true" or "false". If set to "false" then any communications satisfying
                        the corresponding conditions will be barred unless overridden by another rule with allow set to "true".
                        If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. not barred.
                        This must be present on the creation of a communication barring rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:choice>
                <xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The play-announcement element has string values from 0 to 32 characters.
                            When the play-announcement action is set with the string value containing characters with the length between 1 to 32,
                            if there is any communications satisfying the corresponding conditions and being barred (allow=false), the caller will be
                            presented with the announcement associated with the announcement code pointed by the string value.
                            When the play-announcement action is set with the string value containing character with the length of 0,
                            any play-announcement action element in the rule will be deleted from the rule.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="play-segmented-announcement" minOccurs="0" maxOccurs="1" nillable="true">
                    <xs:annotation>
                        <xs:documentation>
                            If there is any communications satisfying the corresponding conditions, the caller will be presented with the segmented
                            announcement associated with the announcement code pointed by the "announcement-name" attribute of the element. Before
                            trying to invoke any, the segmented (generic) announcement must be configured in MTAS with the same name as given in
                            the "announcement-name" attribute.
                            The segmented announcement may contain contain embedded variables, which can be presented in the "announcement-variable"
                            child element. The configured segmented (generic) announencement shall contain as many standalone voice variable segments
                            as many "announcement-variable" child elements are defined for the "play-segmented-announcement" action.
                            The keyed "play-segmented-announcement" action with the "announcement-name" attribute can be deleted from the list of
                            actions by setting the "xs:nil" attribute to true.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:complexContent>
                            <xs:extension base="mmtel-context:play-segmented-announcement-type">
                                <xs:attribute name="announcement-name" type="announcement-name-type" use="required" >
                                    <xs:annotation>
                                        <xs:appinfo>
                                            <jaxb:property name="announcementNameAttr"/>
                                        </xs:appinfo>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:extension>
                        </xs:complexContent>
                    </xs:complexType>
                    <xs:key name="AnnouncementVariableNameKey_OutgoingCommunicationBarring">
                        <xs:annotation>
                            <xs:documentation>
                                An announcement variable can be embedded into a segmented announcemet only once. Announcement variables, under
                                the scope of a segmented announcement, are made unique by the "variable-name" attribute.
                            </xs:documentation>
                        </xs:annotation>
                        <xs:selector xpath="./mmtel-context:announcement-variable" />
                        <xs:field xpath="@variable-name" />
                    </xs:key>
                </xs:element>
            </xs:choice>
            <xs:element name="status-code" type="status-code-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The status-code element defines the SIP error response which will be sent to the caller.
                        This element is ignored if allow action set to "true".
                        Allowed value range is 400-699.
                        Use xsi:nil="true" to delete this element.
                        This element is optional
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-header" type="reason-header-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element of reason-header elements to be sent in SIP error response.
                        This element is ignored if allow action set to "true".
                        Use xsi:nil="true" to delete this element.
                        This element is optional
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="outgoing-call-barring-op-actions-type">
        <xs:sequence>
            <xs:element name="allow-true-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The allow-true-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the allow action with the value of "true" in the associated communication barring rules to explicitly allow
                        communications that match the associated conditions. With this absent or set to "deactivated" the subscriber is only
                        permitted to use the allow action with the value of "false" to bar communications.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the play-announcement action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-segmented-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the play-segmented-announcement action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the play-segmented-announcement action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status-code-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The status-code-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the status-code action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the status-code action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-header-action" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The reason-header-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
                        to use the reason-header action element by adding or removing the element into or from the rule.
                        With this absent or set to "deactivated" the subscriber is not permitted to use the reason-header action.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- outgoing-communication-barring -->

    <!-- originating-calling-name-identity-presentation -->
    <xs:complexType name="originating-calling-name-identity-presentation-type">
        <xs:annotation>
            <xs:documentation>
                The originating calling name identity presentation service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ocnip-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the originating calling name identity presentation service that are available to the operator rather than the user.
                        This must be present on the creation of the originating-calling-name-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the originating calling name
                                    identity presentation service. If set to "false" this will withdraw the user service, but the ocnip-user-configuration
                                    element is preserved. This must be present on the creation of the originating-calling-name-identity-presentation service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="external-query-type" type="external-query-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The external-query-type element has values "calling-name" or "company-number". It indicates that an external query to the
                                    Calling Name Server (CNAME) is needed to retrieve the caller's identity information.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- originating-calling-name-identity-presentation -->

    <!-- Originating Identity Presentation -->
    <xs:complexType name="originating-identity-presentation-type">
        <xs:annotation>
            <xs:documentation>
                The originating identity presentation service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="oip-operator-configuration" type="oip-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the originating identity presentation service that are available to the operator rather than the user.
                        This must be present on the creation of the originating-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="oip-user-configuration" type="oip-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the originating identity presentation service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        oip-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oip-user-configuration-type">
        <xs:annotation>
            <xs:documentation>User part of OIP (Originating Identity Presentation)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the originating identity presentation service is
                        active or not for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oip-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>
                Operator part of OIP (Originating Identity Presentation)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the originating
                        identity presentation service. If set to "false" this will withdraw the user service, but the oip-user-configuration
                        element is preserved. This must be present on the creation of the originating-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The restriction-override element has values "override-active" or "override-not-active". The value "override-active"
                        means that the originating identity will be presented even if the calling party has requested for their presentation
                        to be restricted.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- Originating Identity Presentation -->

    <!-- originating-identity-presentation-restriction -->
    <xs:complexType name="originating-identity-presentation-restriction-type">
        <xs:annotation>
            <xs:documentation>
                The originating identity presentation restriction service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="oir-operator-configuration" type="oir-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the originating identity presentation restriction service that are available to the
                        operator rather than the user. This must be present on the creation of the originating-identity-presentation-restriction
                        service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="oir-user-configuration" type="oir-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the originating identity presentation restriction service that are available for the user to set
                        directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        oir-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oir-user-configuration-type">
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the originating identity presentation restriction
                        service is active or not for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The default-behaviour element has values "presentation-restricted" or "presentation-not-restricted". It selects the default
                        behaviour in temporary mode when the user does not select explicitly within the call whether to restrict their identity or not.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oir-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>
                Operator part of originating identity presentation restriction (OIR)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the originating
                        identity presentation restriction service. If set to "false" this will withdraw the user service and the oir-user-configuration
                        element must be preserved. This must be present on the creation of the originating-identity-presentation-restriction
                        service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mode" type="identityPresentationModeType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The mode element has values "permanent" or "temporary". The value "permanent" is used to give the user a permanent
                        restriction service. In this case there must be no oir-user-configuration element. The value "temporary" gives an identity
                        presentation restriction service where the user can choose a default behaviour and also whether to override this on a
                        per-call basis. This must be present on the creation of the originating-identity-presentation-restriction service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="restriction" type="identityPresentationRestrictionType" default="all-private-information" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The restriction element has values "only-identity" or "all-private-information" and selects whether just the identity of the
                        user is restricted or all private information. This must be present on the creation of the originating-identity-presentation-restriction
                        service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priority-restriction" type="identityPresentationRestrictionType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The priority-restriction element has values "only-identity" or "all-private-information" and selects whether just the identity of the
                        user is restricted or all private information for priority calls.
                        Use xsi:nil="true" to delete this element.
                        This element is optional. If this element is not present then the restriction from the restriction element applies.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- originating-identity-presentation-restriction -->

    <!-- Terminating Identity Presentation/Restriction -->
    <xs:complexType name="terminating-identity-presentation-type">
        <xs:annotation>
            <xs:documentation>
                The terminating identity presentation service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tip-operator-configuration" type="tip-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the terminating identity presentation service that are available to the operator rather than
                        the user. This must be present on the creation of the terminating-identity-presentation service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tip-user-configuration" type="tip-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the terminating identity presentation service that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned i.e.
                        tip-operator-configuration is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="tip-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>
                Operator part of terminating identity presentation (TIP)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the terminating
                        identity presentation service. If set to "false" this will withdraw the user service and the tip-user-configuration
                        element must be preserved. This must be present on the creation of the terminating-identity-presentation
                        service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The restriction-override element has values "override-active" or "override-not-active". The value "override-active"
                        means that the terminating identity will be presented even if the called party has requested for their presentation
                        to be restricted.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="tip-user-configuration-type">
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the terminating identity presentation service is active or
                        not for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="terminating-identity-presentation-restriction-type">
        <xs:annotation>
            <xs:documentation>
                The terminating identity presentation restriction service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tir-operator-configuration" type="tir-operator-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the terminating identity presentation restriction service that are available to the operator
                        rather than the user. This must be present on the creation of the terminating-identity-presentation-restriction service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tir-user-configuration" type="tir-user-configuration-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the terminating identity presentation restriction service that are available for the user to
                        set directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
                        provisioned i.e. tir-operator-configuration is present and activated="true".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="tir-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>
                Operator part of terminating identity presentation restriction (TIPR)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned with the terminating identity
                        presentation restriction service. If set to "false" this will withdraw the user service and the tir-user-configuration element
                        must be preserved. This must be present on the creation of the terminating-identity-presentation-restriction
                        service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mode" type="terminatingIdentityPresentationModeType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The mode element has values "permanent" or "temporary". The value "permanent" is used to give the user a permanent
                        restriction service. In this case there must be no tir-user-configuration element. The value "temporary" gives an identity
                        presentation restriction service where the user can choose a default behaviour and also whether to override this on a
                        per-call basis. This must be present on the creation of the terminating-identity-presentation-restriction service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="tir-user-configuration-type">
        <xs:annotation>
            <xs:documentation>
                User part of terminating identity presentation restriction (TIPR)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" and "false". It controls whether the terminating identity presentation restriction
                        service is active or not for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The default-behaviour element has values "presentation-restricted" and "presentation-not-restricted". It selects the
                        default behaviour in temporary mode when the user does not select explicitly within the call whether to restrict their
                        identity or not
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- Terminating Identity Presentation/Restriction -->

    <!-- user-common-data -->
    <xs:complexType name="user-common-data-type">
        <xs:annotation>
            <xs:documentation>
                Common data available to the user across multiple services.
                Use xsi:nil="true" to delete the user-common-data.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ucd-operator-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the user common data that are available to the operator rather than the user.
                        This must be present on the creation of user-common-data.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned
                                    with the user common data. If set to "false" this will withdraw the service from the user.
                                    This must be present on the creation of user-common-data.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="max-targets" type="max-ucd-targets-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The max-targets element controls the maximum number of distinct targets that the user can have in the target-list.
                                    Integer value between 2 and 10.
                                    If this element is set both elements max-device-targets and target-device-list must be set.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="max-device-targets" type="max-ucd-device-targets-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The max-device-targets element controls the maximum number of distinct devices that the user can have in the target-device-list.
                                    Integer value between 2 and 10.
                                    If this element is set both elements max-targets and target-device-list must be set.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="target-device-list" type="activatedType" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The target-device-list element has values "activated" or "deactivated".
                                    When set to "activated" the user is allowed to use the target-device-list element of the user common data.
                                    If this element is set both elements max-targets and max-device-targets must be set.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="holiday-list" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The holiday-list element has values "activated" or "deactivated".
                                    When set to "activated" the user is allowed to use the holiday-list element of the user common data.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="home-location" type="home-location-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Confirming to P-Access-Network-Info header ABNF syntax described in 3GPP TS 24.229:
                                    IP Multimedia Call Control Protocol based on Session Initiation Protocol (SIP) and Session Description Protocol (SDP);
                                    Stage 3 (Release 11) section 7.2A.4.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="time-zone-area" type="time-zone-area-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The time-zone-area is the user home time zone area. The time-zone-area is in the form "Area/Location and
                                    must be included in the list of time zones in IANA Time Zone Database.
                                    Example: "Asia/Tokyo", "Canada/Central",
                                    "Europe/Copenhagen", "Australia/Canberra",
                                    "America/Los_Angeles".
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mmtel-charging-profile" type="xs:string" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The mmtel-charging-profile element specifies the name of the mmtel charging profile to be used.
                                    The mmtel  charging profile must have been configured in MTAS.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="auto-answer-avoidance-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The auto-answer-avoidance-condition element has values "activated" or "deactivated".
                                    When set to "activated" the user is provisioned with the Auto-Answer Avoidance feature.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="in-sip-request-condition" type="activatedType" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The in-sip-request-condition element has values "activated" or "deactivated".
                                    When set to "activated" it allows the subscriber to use the in-sip-request condition in
                                    supplementary service rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="in-sip-request-condition-list" type="in-sip-request-condition-list-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The in-sip-request-condition-list is a grouping element for definitions of SIP regexp conditions.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="in-sip-request-condition-list-flexcondition-definition-key">
                                <xs:selector xpath="./mmtel-context:flexcondition-definition" />
                                <xs:field xpath="@id" />
                            </xs:key>
                        </xs:element>
                        <xs:element name="feature-tag-preferences" type="feature-tag-preferences-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    Defines the target preferences feature tags controlled by MMTel AS.
                                    Use xsi:nil="true" to delete this element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="subscription" type="subscription-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The subscription element specifies the group of subscriber identity attributes.
                                    This element is a sub-MO allowing multiple instances with “id” as the unique key.
                                    Currently it is limited to one instance but is designed to be extended.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="subscription-key">
                                <xs:selector xpath="./mmtel-context:subscription" />
                                <xs:field xpath="@id" />
                            </xs:key>
                        </xs:element>
                        <xs:element name="ucr-served-identity" type="non-empty-anyURI-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    This element uniquely identifies the served user identity for the external unified communication system.
                                    The element is a global E.164 identity in the SIP or TEL format. SIP embedded Tel-URI can have user=phone attribute.
                                    When defined, the element cannot be empty. Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobile-subscription-list" type="mobile-subscription-list-type" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Grouping element of all mobile subscriptions associated with the user’s identity.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="mobile-subscription-list-subscription-key">
                                <xs:selector xpath="./mmtel-context:subscription" />
                                <xs:field xpath="@id" />
                            </xs:key>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>

            <xs:element name="ucd-user-configuration" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the user common data that are available for the user to set directly.
                        These can also be set on the user's behalf by the operator. This shall only be present if the service is provisioned
                        i.e. user common data is present and activated is "true".
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="target-device-list" type="target-device-list-type" nillable="true" minOccurs="0" >
                            <xs:annotation>
                                <xs:documentation>
                                    A list of all of the devices associated with the user’s identity which can be selected individually for distribution of calls.
                                    Up to 10 entries can be included.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="target-list" type="target-list-type" nillable="true" minOccurs="0" >
                            <xs:annotation>
                                <xs:documentation>
                                    A list of all of the related targets that can be included in communication distribution rules in addition
                                    to the PRIMARY number itself i.e. that of the served user. Up to 10 entries can be included.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="utc-offset" type="time-offset-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The utc-offset element specifies the offset to be taken from UTC when determining times of day and when each day starts and ends.
                                    This element is used for valid-periods conditions when utc-offset is not specified in the valid-periods condition.
                                    If utc-offset is omitted then the offset from the node CM attribute is used. It is also used for validity and invalidity
                                    conditions, when they are given with local time. If utc-offset is omitted then the offset from the node CM attribute is used.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="start-day-of-week" type="weekday-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The start-day-of-week element specifies the starting day of the week, used when evaluating time conditions related to weeks of year
                                    or containing weekly repetition. If start-day-of-week is omitted then the starting day from the node CM attribute is used.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="non-workday-list" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    A list of weekdays considered as workday during evaluation of the time conditions associated with the user’s identity.
                                    Up to 7 entries can be included. If workday-list is omitted then the node CM attribute defining the workday list is used.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="weekday" type="weekday-type" minOccurs="0" maxOccurs="7">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The workday element specifies the weekday used as workday in the valid-periods condition.
                                                This is a multi-value parameter.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="holiday-list" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    A list of private holidays to be used during evaluation of the time conditions associated with the user’s identity.
                                    Up to 20 entries can be included. Also inheritance of the public holidays configured on node level can be specified.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="holiday" type="xs:date" minOccurs="0" maxOccurs="20">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The holiday element specifies one private holiday for the user.
                                                This is a multi-value parameter.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="use-national" type="xs:boolean" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                When the use-national is set to TRUE, beside the private holidays set in element holiday,
                                                Also the public holidays configured on node level are used during evaluation of the time conditions associated
                                                with the user’s identity
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="target-device-list-type">
        <xs:sequence>
            <xs:element name="fixed-targets" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        If fixed-targets is set to "true" then the target identities are set by the operator and cannot be changed by the user.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target-device" type="target-device-type" nillable="true" minOccurs="0" maxOccurs="10">
                <xs:annotation>
                    <xs:documentation>
                        The target-device element is a sub-MO allowing multiple instances with "name" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="target-device-type">
        <xs:sequence>
            <xs:element name="name" type="target-name-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The name for the target device. This is the name by which distribution rules refer to devices as targets.
                        This must be present on the creation of a target-device element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="terminal-selector" type="terminal-selector-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The terminal selector is the way that the individual device is selected. It is an alphanumeric string of between 1 and 30 characters.
                        This must be present on the creation of a target-device element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="target-name-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <!-- user-common-data -->

    <!-- voice-mail -->
    <xs:complexType name="voice-mail-type">
        <xs:annotation>
            <xs:documentation>
                The voice mail service. Use xsi:nil="true" to withdraw the entire service.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="vm-operator-configuration" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the voice mail service that are available to the operator rather than the user.
                        This must be present on the creation of the voice-mail service.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The activated element has values "true" or "false". When set to "true" the user is provisioned with the
                                    voice mail service. This allows the user to include the special identity "voicemail:internal" as the target
                                    for communication diversion rules. If set to "false" this will withdraw the service from the user. This must
                                    be present on the creation of the voice-mail service.
                                    This element has a relationship with the voice-mail-retrieval-address element.
                                    When voice-mail-retrieval-address element is provisioned, then the target identity in
                                    the voice-mail-address element is used only for depositing the voicemail.
                                    Otherwise it is used both for depositing and retrieving the voicemail.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="voice-mail-address" type="xs:anyURI" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The voice-mail-address element specifies the target identity to be used if a user's communication
                                    diversion rule specifies diversion to "voicemail:internal". It takes the form of a normalized sip: or tel:
                                    URI or the special value "voicemail:internal". In the case of the special value of "voicemail:internal"
                                    the communication diversion will be sent to the identity specified in the node level configuration parameter.
                                    This must be present on the creation of the voice-mail service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="voice-mail-retrieval-address" type="xs:anyURI" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The voice-mail-retrieval-address element specifies the target identity to be used when the communication
                                    is redirected to retrieve the voicemail. It takes the form of a normalized sip: or tel: URI or the special
                                    value "voicemail:internal". In case of the special value of "voicemail:internal" the communication is
                                    redirected to the identity specified in the node level configuration parameter.
                                    Use xsi:nil="true" to remove voice-mail-retrieval-address element.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- voice-mail -->

    <xs:complexType name="ccat-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>Operator Part of Caller Categorization</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false".
                        When set to "true" the user is provisioned with the caller categorization service.
                        If set to false, this will withdraw the user service,
                        but the ccat-user-configuration element must be preserved.
                        This must be present on the creation of the caller-categorization service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ccat-profile" type="non-empty-string-length-64-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ccat-profile element contains the profile name to be
                        used for the subscriber for caller-categorization. It is a string.
                        This must be present on the creation of the caller-categorization service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ccat-ruleset" type="ccat-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This ruleset is evaluated before the ruleset in the user configuration.
                        Grouping element for a set of zero or more rules. These rules apply when activated is "true".
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="ccat-op-rule-key">
                    <xs:selector xpath="./mmtel-context:ccat-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="ccat-op-conditions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ccat-op-conditions element is a grouping element for
                        fine-grain provisioning options that control which condition elements the user is
                        permitted to use in caller categorization rules.
                        If a condition is absent, it disallows the subscriber to use that condition
                        in caller categorization rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="media-condition" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The media-condition element has values "activated" and "deactivated".
                                    When set to "activated" it allows the subscriber to use media
                                    conditions in caller categorization rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="validity-condition" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The validity-condition element has values "activated" or "deactivated".
                                    When set to "activated", it allows the subscriber to use the validity
                                    condition in caller categorization rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="valid-periods-condition" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The valid-periods-condition element has values "activated" or "deactivated".
                                    When set to "activated", it allows the subscriber to use the valid-periods
                                    condition in caller categorization rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="invalidity-condition" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The invalidity-condition element has values "activated" or "deactivated".
                                    When set to "activated", it allows the subscriber to use the invalidity
                                    condition in caller categorization rules.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ccat-op-actions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ccat-op-actions element is a grouping element for
                        fine-grain provisioning options to control which action elements
                        the user is permitted to use in caller categorization rules.
                        If an action is absent, it disallows the subscriber to use that action in caller categorization rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="allow-false-action" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The allow-false-action element has values "activated" or "deactivated".
                                    When set to "activated" it allows the subscriber to use the allow action
                                    with the value of "false" in caller categorization rules to explicitly bar
                                    incoming communications that match the associated conditions.
                                    With this absent or set to "deactivated" the subscriber is only permitted to use the
                                    allow action with the value of "true" to allow incoming communications.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="notify-caller-action" type="activatedType" minOccurs="0" nillable="true" >
                            <xs:annotation>
                                <xs:documentation>
                                    The notify-caller-action element has values "activated" or "deactivated".
                                    When set to "activated" it allows the subscriber to use the notify-caller
                                    action in caller categorization rules to control
                                    whether the caller is notified that the call is being forwarded.
                                    Use xsi:nil="true" to delete this element.
                                    This element is optional.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of allowed caller categorization rules in the user document.
                        Not specified or zero limit means no limit.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ccat-user-configuration-type">
        <xs:annotation>
            <xs:documentation>User Part of Caller Categorization</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false".
                        It controls whether the caller categorization service is active or not for this subscriber.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- ruleset is a structured parameter that is optional and nillable -->
            <xs:element name="ccat-ruleset" type="ccat-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more rules.
                        These rules apply when active is "true".
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="ccat-rule-key">
                    <xs:selector xpath="./mmtel-context:ccat-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ccat-ruleset-type">
        <xs:sequence>
            <xs:element name="ccat-rule" type="ccat-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling caller-categorization behavior.
                        The ccat-rule element is a sub-MO allowing multiple
                        instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
                <!-- sub MOs need a key. The key for rule is id. -->
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <!-- rule is a sub-MO -->
    <xs:complexType name="ccat-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule.
                        This must be unique within the scope of the complete document.
                        This must be present on the creation of a ccat-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ccat-conditions" type="ccat-conditions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ccat-conditions element is a grouping element for conditions for a rule.
                        All conditions must be satisfied for the rule to take effect.
                        caller-category condition is mandatory and should be present upon the creation of caller-categorization rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ccat-actions" type="ccat-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ccat-actions element is a grouping element for the actions for a rule.
                        This must be present on the creation of a ccat-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="ccat-conditions-type">
        <xs:sequence>
            <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false".
                        If present with the value "true" this has the effect of deactivating the individual
                        rule and the rule is not checked. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- media is a multiple value parameter -->
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must
                        include for the condition to be matched e.g. "audio" or "video".
                        This is a multi-value parameter, so it can appear more than once with
                        several media values that must all be satisfied for the overall condition to be matched.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- validity is a structured parameter so it can be nillable -->
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for time periods (intervals)
                        within which the rule is valid.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing validity element -->
                <xs:key name="ccat-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-periods element is a grouping element that allows assembly
                        of complex time condition based upon several sub-conditions.
                        For the valid-periods condition to be satisfied the current date/time
                        must match with all the included sub-conditions.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for absolute time periods
                        (intervals) within which the rule is NOT valid.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="ccat-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="caller-category" type="caller-category-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The caller-category element is a grouping element for category and/or sub-category values,
                        which are matched with caller’s category and/or sub-category values.
                        This condition is mandatory and should be present upon the creation of caller-categorization rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ccat-actions-type">
        <xs:choice>
            <xs:element name="allow" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The allow element has values "true" or "false".
                        If set to "false" then any incoming communications satisfying the
                        corresponding conditions will be barred unless overridden by another
                        rule with allow set to "true". If set to "true" then any incoming  communications
                        satisfying the corresponding conditions will be allowed i.e. not barred.
                        allow and forward-to elements are mutually exclusive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="forward-to" type="forward-to-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The forward-to element is a grouping element with details of the
                        target to which the communication should be diverted.
                        allow and forward-to elements are mutually exclusive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="caller-category-type">
        <xs:sequence>
            <xs:element name="category" type="category-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The category element specifies the category value in the range [0-31],
                        which will be matched with caller’s category value.
                        If the value is matched, condition is satisfied.
                        This element must be present when caller-category element is created and if present, cannot be empty.
                        This element can take a single value or multiple values separated by comma or
                        category range, which must have lower to upper bound values separated by ‘-‘.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sub-category" type="category-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The sub-category element specifies the sub-category value in the range [0-31],
                        which will be matched with caller’s sub-category value.
                        If the value is matched, condition is satisfied.
                        This element is optional and is applicable only when category element is present.
                        This element cannot be empty if present.
                        This element can take a single value or multiple values separated by comma or
                        category range, which must have lower to upper bound values separated by ‘-‘.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="activated-enum-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="true"/>
            <xs:enumeration value="false"/>
            <xs:enumeration value="profile"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="non-empty-string-length-64-type">
        <xs:annotation>
            <xs:documentation>
                The non-empty-string-type allows any non-empty string with
                non-whitespace characters and maximum length of 64.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="64" />
            <xs:pattern value="\S+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="activatedType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="activated" />
            <xs:enumeration value="deactivated" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="rule-limit-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="100" />
            <!-- 0 means no limit -->
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="validityType">
        <xs:annotation>
            <xs:documentation>
                The validity element is a grouping element for time periods (intervals) within which the rule is valid.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence maxOccurs="unbounded">
            <xs:element name="interval" type="interval-type" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The interval element specifies a date and time period within which the validity condition is satisfied. The
                        interval element is a sub-MO allowing multiple instances with "from" as the unique key
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="interval-type">
        <xs:sequence>
            <xs:element name="from" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The date and time that specifies the start of the valid interval. It is a standard dateTime value e.g.
                        "2008-11-27T20:00:00Z" for a UTC time or "2008-10-12T20:00:00-08:00" for a time with 8 hours offset from UTC.
                        This must be present on the creation of an interval element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="until" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The date and time that specifies the end of the valid interval. It is a standard dateTime value e.g.
                        "2008-11-27T20:00:00Z" for a UTC time or "2008-10-12T20:00:00-08:00" for a time with 8 hours offset from UTC.
                        This must be present on the creation of an interval element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="from" type="xs:dateTime" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="fromAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="valid-periods-type">
        <xs:annotation>
            <xs:documentation>
                The valid-periods element is a grouping element for recurring time periods (intervals) within which the rule is valid.
                In order for the valid-periods condition to be satisfied the current date/time must match one of the valid-days if present
                and one of the valid-times if present.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="utc-offset" type="time-offset-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The utc-offset element specifies the offset to be taken from UTC when
                        determining times of day and when each day starts and ends.
                        If utc-offset is omitted then days and times are based on UTC
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-days" type="days-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-days element specifies each of the days on which the condition would match, subject
                        to also meeting other subconditions if present. If valid-days is omitted then the condition applies to
                        all days of the week.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-times" type="intervals-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-times specifies periods of the day in which the condition would match, subject to also
                        meeting other subconditions, if present. If valid-times is omitted then the condition applies to all
                        times of the day. The valid-times condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="valid-times-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="valid-months" type="months-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-months element specifies each of the months on which the condition would match, subject
                        to also meeting other sub-conditions if present.
                        If valid-months is omitted then it applies to all months of the year.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-weeks" type="weeks-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-weeks element specifies each of the weeks on which the condition would match, subject
                        to also meeting other sub-conditions if present.
                        If valid-weeks is omitted then it applies to all weeks of the year.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-daily" type="repeat-daily-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The repeat-daily element specifies start day and repetition interval for the days on which the condition would match,
                        subject to also meeting other sub-conditions if present.
                        If repeat-daily is omitted then it applies to all days of the year
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-weekly" type="repeat-weekly-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The repeat-weekly element specifies start week and repetition interval for the weeks on which the condition would match,
                        subject to also meeting other sub-conditions if present.
                        If repeat-weekly is omitted then it applies to all weeks of the year
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-monthly" type="repeat-monthly-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The repeat-monthly element specifies start month and repetition interval for the months on which the condition would match,
                        subject to also meeting other sub-conditions if present.
                        If repeat-monthly is omitted then it applies to all months of the year
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-monthdays" type="monthdays-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-monthdays element specifies each of the days on which the condition would match, subject
                        to also meeting other sub-conditions if present.
                        If valid-monthdays is omitted then the condition applies to
                        all days of the month.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="except-holidays" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The except-holidays element specifies that if the current day matches to the holidays provisioned for the user,
                        then the valid-periods condition is evaluated to false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="invalidityType">
        <xs:annotation>
            <xs:documentation>
                The validity element is a grouping element for time periods (intervals) within which the rule is NOT valid.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence maxOccurs="unbounded">
            <xs:element name="interval" type="interval-type" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The intervals element specifies a date and time periods within which the invalidity condition is NOT satisfied.
                        The interval element is a sub-MO allowing multiple instances with "from" as the unique key
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="forward-to-type">
        <xs:sequence>
            <!-- 'target' is optional on CAI3G to allow individual updates of other elements but is mandatory in the updated document -->
            <xs:element name="target" type="xs:anyURI" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The target element specifies the identity to which the communication should be diverted. This takes the form of
                        a sip: or tel: URI or "voicemail:internal" for forwarding to voice mail. Each tel: URI and sip: URI that was converted
                        from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be
                        normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC. This must be present on the creation
                        of a cdiv-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- There is no need for these elements to be nillable because they can be set to false to achieve the same meaning -->
            <xs:element name="notify-caller" type="xs:boolean" default="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The notify-caller element has values "true" or "false". It controls whether the caller is notified that the call is
                        being forwarded. If it is not included then the default behaviour is to notify the caller (true).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reveal-identity-to-caller" type="xs:boolean" default="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The reveal-identity-to-caller element has values "true" or "false". It controls whether the caller being notified
                        that the call is being forwarded receives the target's identity information. If it is not included then the
                        default behaviour is to reveal the target's identity to the caller (true).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="notify-served-user" type="xs:boolean" default="false" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The notify-served-user element has values "true" or "false". It controls whether the served user is notified that
                        the call is being forwarded. If it is not included then the default behaviour is not to notify the served user (false).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="notify-served-user-on-outbound-call" type="xs:boolean" default="false" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The notify-served-user-on-outbound-call element has values true or false. It controls whether the served user
                        is notified that calls are being forwarded when he makes a call attempt. If it is not included then the default
                        behaviour is not to notify the served user on outbound calls (false).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reveal-identity-to-target" type="xs:boolean" default="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The reveal-identity-to-target element has values "true" and "false". It controls whether the diverted-to party
                        receives identity information of the diverting party. If it is not included then the default behaviour is to
                        reveal the diverting party's identity to the target (true).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="play-segmented-announcement-type">
        <xs:sequence>
            <xs:element name="announcement-name" type="announcement-name-type" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the announcement to be played. This must be present on the creation of a
                        play-segmented-announcement element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="announcement-variable" type="announcement-variable-type" minOccurs="0" maxOccurs="32" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The announcement variable to be embedded into the announcement. It's use is optional, i.e. a segmented
                        announcement may or may not contain any variable segment. Maximum 32 announcement variables can be
                        embedded into a segmented announcement.
                        A keyed "announcement-variable" element with the "variable-name" attribute can be deleted from the list
                        of announcement variables by setting the "xs:nil" attribute to true.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="announcement-name-type">
        <xs:annotation>
            <xs:documentation>
                The allowed characters in an announcement name are restricted to a-z, A-Z, _ and 0-9.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:normalizedString">
            <xs:minLength value="1" />
            <xs:maxLength value="32" />
            <xs:pattern value="[0-9a-zA-Z_]+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="announcement-variable-type">
        <xs:complexContent>
            <xs:extension base="announcement-variable-base-type">
                <xs:attribute name="variable-name" type="announcement-variable-name-type" use="required" >
                    <xs:annotation>
                        <xs:appinfo>
                            <jaxb:property name="variableNameAttr"/>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="announcement-variable-base-type">
        <xs:sequence>
            <xs:element name="variable-name" type="announcement-variable-name-type" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the announcement variable to be embedded. This must be present on the creation of an
                        announcement-variable element inside a play-segmented-announcement element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="variable-value" type="announcement-variable-value-type" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The variable value is defined in the variable-value child element of the announcement-variable element.
                        According to H.248.9, the allowed characters in place of a variable value are ASCII 0x09, 0x20-0x7E.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="announcement-variable-name-type">
        <xs:annotation>
            <xs:documentation>
                The allowed characters in a variable name are restricted to a-z, A-Z, _ and 0-9.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:normalizedString">
            <xs:minLength value="1" />
            <xs:maxLength value="32" />
            <xs:pattern value="[0-9a-zA-Z_]+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="announcement-variable-value-type">
        <xs:annotation>
            <xs:documentation>
                The allowed characters in a variable value are restricted to ASCII 0x09, 0x20-0x7e and ASCII.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:normalizedString">
            <xs:pattern value="[&#x0009;&#x0020;-&#x007e;]+" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="category-type">
        <xs:annotation>
            <xs:documentation>
                The category type allows value in the range [0-31] such that
                value can be a single value or multiple values separated by comma or
                category range, which must have lower to upper bound values separated by ‘-‘.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="85"/>
            <xs:pattern value="(3[01]|[12][0-9]|[0-9])( *- *(3[01]|[12][0-9]|[0-9]))?( *, *(3[01]|[12][0-9]|[0-9])( *- *(3[01]|[12][0-9]|[0-9]))?)*" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="time-offset-type">
        <xs:restriction base="xs:string">
            <xs:pattern value="[+\-]([0-1][0-9]|2[0-3]):[0-5][0-9]" />
            <!-- offset can be + or - with respect to UTC i.e. [+-] hour is 00-23 ([0-1][0-9]|2[0-3]) minute is 00-59 i.e [0-5][0-9] -->
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="days-type">
        <xs:sequence>
            <xs:element name="day" type="day-type" maxOccurs="10">
                <xs:annotation>
                    <xs:documentation>
                        The day of the week. This is a multi-value parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="day-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Sunday" />
            <xs:enumeration value="Monday" />
            <xs:enumeration value="Tuesday" />
            <xs:enumeration value="Wednesday" />
            <xs:enumeration value="Thursday" />
            <xs:enumeration value="Friday" />
            <xs:enumeration value="Saturday" />
            <xs:enumeration value="Workday" />
            <xs:enumeration value="NonWorkday" />
            <xs:enumeration value="Holiday" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="intervals-type">
        <xs:sequence>
            <xs:element name="interval" type="time-interval-type" nillable="true" maxOccurs="8">
                <xs:annotation>
                    <xs:documentation>
                        A time interval. The interval element is a sub-MO allowing multiple instances with "from" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="time-interval-type">
        <xs:sequence>
            <xs:element name="from" type="hours-minutes-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The time of day at which the interval starts. The format is HH:MM in the
                        24 hour clock. This must be present on the creation of an interval element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="until" type="hours-minutes-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The time of day at which the interval ends. The format is HH:MM in the
                        24 hour clock. The interval applies until the end of the specified minute.
                        This must be present on the creation of an interval element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="from" type="hours-minutes-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="fromAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="hours-minutes-type">
        <xs:restriction base="xs:string">
            <xs:pattern value="([0-1][0-9]|2[0-4]):[0-5][0-9]" />
            <!-- hour can be up to 24 to allow the time period right up to midnight to be specified hour is 00-24 ([0-1][0-9]|2[0-4])
                minute is 00-59 i.e [0-5][0-9] -->
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="months-type">
        <xs:sequence>
            <xs:element name="month" type="month-type" minOccurs="0" maxOccurs="12">
                <xs:annotation>
                    <xs:documentation>
                        The month of the year. The format is integer of the month number.
                        This is a multi-value parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="month-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="12" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="weeks-type">
        <xs:sequence>
            <xs:element name="week" type="week-type" minOccurs="0" maxOccurs="52">
                <xs:annotation>
                    <xs:documentation>
                        The week of the year. The format is integer of the week number.
                        This is a multi-value parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="week-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="53" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="repeat-daily-type">
        <xs:sequence>
            <xs:element name="begin-day" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The start day of the repetition. The format is YYYY-MM-DD.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-interval" type="repeat-interval-type" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The repetition interval in days. The format is integer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="repeat-interval-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="999" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="repeat-weekly-type">
        <xs:sequence>
            <xs:element name="begin-day" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The start day of the repetition. The format is YYYY-MM-DD.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-interval" type="repeat-interval-type" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The repetition interval in weeks. The format is integer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="repeat-monthly-type">
        <xs:sequence>
            <xs:element name="begin-day" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The start day of the repetition. The format is YYYY-MM-DD.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repeat-interval" type="repeat-interval-type" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The repetition interval in months. The format is integer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="monthdays-type">
        <xs:sequence>
            <xs:element name="monthday" type="monthday-type" minOccurs="0" maxOccurs="31">
                <xs:annotation>
                    <xs:documentation>
                        The day of the month.
                        Allowed formats:1..31, -1..-31, [-1..-5|1..5][Monday..Sunday]
                        This is a multi-value parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="monthday-type">
        <xs:restriction base="xs:string">
            <xs:pattern value="-*(([1-9]|1[0-9]|2[0-9]|3[0-1])|[1-5](Sunday|Monday|Tuesday|Wednesday|Thursday|Friday|Saturday))" />
            <!-- monthday has the format of [-]1..31 or [-][1-5]weekday, where -1 means the last day of the month, 2Monday means second
                Monday, -1Wednesday means last Wednesday -->
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="max-fcd-targets-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="2" />
            <xs:maxInclusive value="10" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="hosting-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IMS" />
            <xs:enumeration value="non-IMS" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="divert-primary-type">
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The "active" element has values "true" or "false". It indicates whether FCD divert primary service is
                        activated or not. This must be present on the creation of divert-primary element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="forward-to" type="forward-to-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The forward-to element is a grouping element with details of the target to which the communication towards
                        the PRIMARY should be diverted and optional control of notifications and which identities are revealed to whom.
                        This must be present on the creation of divert-primary.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="fcd-no-reply-timer-type">
        <xs:restriction base="xs:positiveInteger">
            <xs:minInclusive value="5" />
            <xs:maxInclusive value="180" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="target-list-type">
        <xs:sequence>
            <xs:element name="fixed-targets" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        If fixed-targets is set to "true" then the target identities are set by the operator and cannot be changed
                        by the user.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target" type="target-type" nillable="true" minOccurs="0" maxOccurs="10">
                <xs:annotation>
                    <xs:documentation>
                        The target element is a sub-MO allowing multiple instances with "name" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="target-type">
        <xs:sequence>
            <xs:element name="name" type="target-name-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The name for the distribution target. This is the name by which distribution rules refer to targets.
                        This must be present on the creation of a target element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="id" type="xs:anyURI" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The id is the identity of the target. It is a sip: or tel: URI. Each tel: URI and sip: URI
                        that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number,
                        or a number that can be normalized after removing a dynamic ad-hoc presentation supplementary service code
                        and/or a carrier select code. This must be present on the creation of a target element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="auto-answer-avoidance" type="xs:boolean" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The auto-answer-avoidance flag marks the target as applicable for the auto-answer avoidance feature.
                        If set to true DTMF confirmation will be required to confirm the call establishment.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="target-name-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="target-name-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="0" />
            <xs:maxLength value="30" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="fcd-ruleset-type">
        <xs:sequence>
            <xs:element name="fcd-rule" type="fcd-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling communication distribution behaviour. The fcd-rule element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="fcd-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete document. This
                        must be present on the creation of an fcd-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fcd-conditions" type="fcd-conditions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The fcd-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
                        for the rule to take effect. If no conditions are present then the rule is always applicable.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- NOTE fcd-actions is optional but not nillable. Every FCD rule must have fcd-actions/parallel-distribution -->
            <!-- or fcd-actions/serial-distribution or fcd-actions/flexible-distribution to be valid but cai3g:Set could just update conditions so actions -->
            <!-- must be optional -->
            <xs:element name="fcd-actions" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The fcd-actions element is a grouping element for the actions for a rule. This must be present on the
                        creation of an fcd-rule. Either parallel-distribution or serial-distribution must be present on the creation of an fcd-rule.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:choice>
                            <xs:element name="parallel-distribution" nillable="true" minOccurs="0" >
                                <xs:annotation>
                                    <xs:documentation>
                                        The parallel-distribution element is a grouping element with details of the targets to which the communication
                                        should be distributed in parallel.
                                    </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ring-period" type="parallel-ring-timer-type" nillable="true" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The maximum time period for which all targets that are not provisioned with a specific target ring-period
                                                    shall be left ringing in parallel without an answer.
                                                    This is an optional element.
                                                    Use xsi:nil="true" to delete this element.
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="target" type="parallel-target-type" nillable="true" minOccurs="0" maxOccurs="10">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The target element is a sub-MO allowing multiple instances with "name" as the unique key.
                                                    It is a reference by name to a target identity to which the communication should be distributed.
                                                    At least one target must be present on creation of a parallel-distribution element.
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                                <xs:key name="parallel-target-key">
                                    <xs:selector xpath="./mmtel-context:target" />
                                    <xs:field xpath="@name" />
                                </xs:key>
                            </xs:element>
                            <xs:element name="serial-distribution" nillable="true" minOccurs="0" >
                                <xs:annotation>
                                    <xs:documentation>
                                        The serial-distribution element is a grouping element with details of the targets to which the communication
                                        should be distributed in series.
                                    </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="target" type="serial-target-type" nillable="true" minOccurs="0" maxOccurs="10">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The target element is a sub-MO allowing multiple instances with "name" as the unique key.
                                                    It is a reference by name to a target identity to which the communication should be distributed.
                                                    At least one target must be present on creation of a serial-distribution element.
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                                <xs:key name="serial-target-key">
                                    <xs:selector xpath="./mmtel-context:target" />
                                    <xs:field xpath="@name" />
                                </xs:key>
                            </xs:element>
                            <xs:element name="flexible-distribution" nillable="true" minOccurs="0" >
                                <xs:annotation>
                                    <xs:documentation>
                                        The flexible-distribution element is a grouping element with details of the targets
                                        to which the communication should be distributed in a flexible way.
                                    </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ring-period" type="flexible-ring-timer-type" nillable="true" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The maximum time period for which the targets that are not provisioned with a target ring-period shall be left
                                                    ringing in without an answer before switching to the next target.
                                                    This is an optional element.
                                                    Use xsi:nil="true" to delete this element.
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="target" type="flexible-target-type" nillable="true" minOccurs="0" maxOccurs="10">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The target element is a sub-MO allowing multiple instances.
                                                    It is a reference by name to a target identity to which the communication
                                                    should be distributed.
                                                    At least one target must be present on creation of a flexible-distribution
                                                    element.
                                                    A target element with the same name cannot be present more than once in the same
                                                    parallel group. (There is always one member in a serial group.)
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:choice>
                        <xs:element name="play-announcement" type="play-announcement-type" minOccurs="0" />
                        <!-- fcd-action-options is a structured parameter allowing it to be made nillable -->
                        <xs:element name="fcd-action-options" nillable="true" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    Grouping element for a set of zero or more action options
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <!-- New rule level CFNR timer config -->
                                    <xs:element name="NoReplyTimer" type="fcd-no-reply-timer-type" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The NoReplyTimer element specifies the time that must expire without answer before the no-answer
                                                condition is triggered. The value is an integer giving the timer in the range of 5 to 180
                                                seconds. This shall only be present in rules with the value "no-answer" in an fcd-call-state condition.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="notify-served-user" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The notify-served-user element has values "true" or "false". It controls whether the served user is notified that
                                    the call is not distributed towards served user devices. If it is not included then the default behaviour is to
                                    not notify the served user.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="parallel-ring-timer-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="5" />
            <xs:maxInclusive value="360" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="parallel-target-type">
        <xs:sequence>
            <xs:element name="name" type="target-name-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The name of a target identity. The name must be one of the following: the name of a target
                        defined in user-common-data; the name of a target-device defined in user-common-data; the
                        special value PRIMARY for all of the user’s devices or, in the case of communication distribution
                        the name of a target defined in the target-list within that service.
                        The name must be present on the creation of a target element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ring-period" type="parallel-ring-timer-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum time period for which this target shall be left ringing without an answer.
                        Upon ring period expiry, the call attempt towards the target shall be cancelled and alerting
                        of the remaining targets continue.
                        Use xsi:nil="true" to delete this element.
                        This is an optional element and if present it takes precedence for this target over the ring-period
                        specified for the whole group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="target-name-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="serial-target-type">
        <xs:sequence>
            <xs:element name="name" type="target-name-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The name of a target identity. The name must be one of the following: the name of a target
                        defined in user-common-data; the name of a target-device defined in user-common-data; the
                        special value PRIMARY for all of the user’s devices or, in the case of communication distribution
                        the name of a target defined in the target-list within that service.
                        The name must be present on the creation of a target element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ring-period" type="serial-ring-timer-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum time period for which this target shall be left ringing
                        without an answer before switching to the next target.
                        This is an optional element.
                        Use xsi:nil="true" to delete this element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="target-name-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="serial-ring-timer-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="5" />
            <xs:maxInclusive value="360" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="fcd-conditions-type">
        <xs:sequence>
            <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect
                        of deactivating the individual rule. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-periods element is a grouping element for recurring time periods (intervals) within which the rule is valid.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for absolute time periods (intervals) within which the rule is valid.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="fcd-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for time periods (intervals) within which the rule is NOT valid.
                        The invalidity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="fcd-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="fcd-call-state" type="fcd-call-state-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The fcd-call-state condition controls which state the user must be in for the rule to apply. The value "busy"
                        is satisfied if the user is busy in other calls. The value "no-answer" applies when there is no answer from the
                        user. The value "not-registered" applies when the user is not registered on the MTAS. The value "not-reachable"
                        applies when the user is not reachable because either a specific response has been received or the not reachable timer expires.
                        The value "unconditional" is used to clear the other call state values so that the condition is satisfied regardless of the
                        user's call state.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- A structured parameter is used to cover all possible caller identity values - being structured it can be nilled -->
            <xs:element name="fcd-caller-identity" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The fcd-caller-identity element is a grouping element for conditions which are based on the caller's identity
                        (or lack of an identity in the case of anonymous).
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:element name="anonymous" type="empty-element-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The anonymous element is an empty element specifying a condition which is satisfied if the caller
                                    is anonymous. This can be removed by deleting the enclosing fcd-caller-identity element or by
                                    replacing it with an identity element. The elements anonymous and identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <!-- no nilling at the level of identity - use nilling on fcd-caller-identity to remove -->
                        <xs:element name="identity" type="identity-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity element is a grouping element for conditions which are based on the caller's identity.
                                    The condition is satisfied if any of the included one or many elements within it is matched. The
                                    elements anonymous and identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="fcd-one-key">
                                <xs:selector xpath="./mmtel-context:one" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="fcd-many-key">
                                <xs:selector xpath="./mmtel-context:many" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="fcd-except-id-key">
                                <xs:selector xpath=".//mmtel-context:except-id" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="fcd-except-domain-key">
                                <xs:selector xpath=".//mmtel-context:except-domain" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="fcd-number-match-key">
                                <xs:selector xpath="./mmtel-context:number-match" />
                                <xs:field xpath="@starts-with" />
                            </xs:key>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <!-- media is a multiple value parameter -->
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be matched e.g.
                        "audio" or "video". This is a multi-value parameter so it can appear more than once with several media
                        values that must all be satisfied for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The presence-status element contains a presence status value that the user must satisfy for the condition to
                        be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can appear more
                        than once with several presence status values that must all be satisfied for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The served-identity element is a grouping element for conditions which are based on the user's
                        served identity. The condition is satisfied if any of the included elements within it is matched.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="fcd-served-identity-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="in-sip-request" type="in-sip-request-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The in-sip-request element is a grouping element for regexp conditions on contents of a SIP request.
                        It evaluates to true if ALL of the conditions included within it are fulfilled.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="fcd-in-sip-request-flexcondition-key">
                    <xs:selector xpath="./mmtel-context:flexcondition" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="flexible-ring-timer-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="5" />
            <xs:maxInclusive value="360" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="flexible-target-type">
        <xs:sequence>
            <xs:element name="name" type="target-name-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The name of a target identity. The name must be one of the following: the name of a
                        target
                        defined in user-common-data; the name of a target-device defined in user-common-data;
                        the
                        special value PRIMARY for all of the user\u2019s devices or, in the case of
                        communication distribution
                        the name of a target defined in the target-list within that service.
                        The name must be present on the creation of a target element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ring-mode" type="ring-mode-type" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The ring mode type - serial or parallel - that is to be used within the flexible
                        distribution.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ring-period" type="flexible-ring-timer-type" nillable="true" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The maximum time period for which this target shall be left ringing
                        without an answer. In case of parallel mode, upon ring period expiry, the call attempt towards the target
                        shall be cancelled and alerting of the remaining targets continue. In case of serial mode upon ring-period
                        expiry the target call shall be cancelled before switching to the next target or target group.
                        Use xsi:nil="true" to delete this element.
                        This is an optional element and if present it takes precedence for this target over the ring-period
                        specified for the whole group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="target-name-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="nameAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="in-sip-request-type">
        <xs:annotation>
            <xs:documentation>
                The in-sip-request element is a grouping element for regexp conditions on contents of a SIP request.
                It evaluates to true if ALL of the conditions included within it are fulfilled.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="flexcondition" type="flexcondition-type" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The flexcondition element refers to the actual definition of the SIP regexp condition in the User Common Data.
                        It evaluates to true when a value of the specified header or header parameter in the SIP request triggering
                        FCD service matches the regular expression (or if it does not match if the "match-inverse" attribute
                        in the condition definition is set to true). The flexcondition element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="flexcondition-type">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This element holds reference to actual definition of the SIP regexp condition in the User Common Data.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="play-announcement-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="0" />
            <xs:maxLength value="32" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ring-mode-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="serial" />
            <xs:enumeration value="parallel" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="fcd-call-state-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="unconditional" />
            <xs:enumeration value="busy" />
            <xs:enumeration value="no-answer" />
            <xs:enumeration value="not-registered" />
            <xs:enumeration value="not-reachable" />
            <!-- EMA does not support nilling a single value parameter so assigning the value of "unconditional" means none of the three conditions apply -->
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="empty-element-type" />
    <xs:complexType name="identity-type">
        <xs:annotation>
            <xs:documentation>
                The identity element is a grouping element for conditions which are based on a user's identity. The condition
                is satisfied if any of the one or many elements within it is matched.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice maxOccurs="unbounded">
                <xs:element name="one" type="one-type" nillable="true">
                    <xs:annotation>
                        <xs:documentation>
                            The one element specifies an individual identity to be matched. The one element is a sub-MO allowing
                            multiple instances with "id" as the unique key.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="many" type="many-type" nillable="true">
                    <xs:annotation>
                        <xs:documentation>
                            The many element specifies a match for a set of identities. The many element is a sub-MO allowing
                            multiple instances with "domain" as the unique key
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="number-match" type="number-match-type" nillable="true">
                    <xs:annotation>
                        <xs:documentation>
                            The number-match element specifies a match for a set of numerical identities. The number-match element is a
                            sub-MO allowing multiple instances with "starts-with" as the unique key.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="served-identity-type">
        <xs:annotation>
            <xs:documentation>
                The served-identity element is a grouping element for conditions which shall match one of the served users MSN alias PUIs
                The condition is satisfied if any of the MSN alias PUIs is matched.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="one" type="one-type" maxOccurs="unbounded" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The one element specifies an individual identity to be matched. The one element is a sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="one-type">
        <xs:sequence>
            <xs:element name="id" type="xs:anyURI" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The individual identity to be matched. For all uses except incoming communication barring user rules, this takes
                        the form of a sip: or tel: URI. For use within incoming communication barring user rules, this takes the form of a
                        sip: or tel: or hidden: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6
                        of RFC 3261 contains a normalized number. This must be present on the creation of a one element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:anyURI" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="many-type">
        <xs:sequence>
            <xs:element name="domain" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The individual domain to be matched. A many element with an explicit domain value matches all identities
                        within that domain. A many element with the special wildcard value "*" matches all identities. This must be
                        present on the creation of a many element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="except-domain" type="except-domain-type" nillable="true" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            An individual domain to be excluded from a many with special value "*" that would otherwise match all identities.
                            The except-domain element is a sub-MO allowing multiple instances with "domain" as the unique key.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="except-id" type="except-id-type" nillable="true" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            An individual identity to be excluded from the identities matching the enclosing many. The except-id element
                            is a sub-MO allowing multiple instances with "id" as the unique key.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="domain" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="domainAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="number-match-type">
        <xs:sequence>
            <xs:element name="starts-with" type="starts-with-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The first few characters of the normalised form of the number to be matched.
                        This must be present on the creation of a number-match element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="starts-with" type="starts-with-type" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="startsWithAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="except-domain-type">
        <xs:sequence>
            <xs:element name="domain" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The individual domain to be excluded from the match. This must be present on the creation of an except-domain
                        element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="domain" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="domainAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="except-id-type">
        <xs:sequence>
            <xs:element name="id" type="xs:anyURI" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The individual identity to be excluded from the match. If this is within a many element with a specific domain
                        then the excluded identity must be a sip: URI within that domain. If this is within a many element with the
                        special wildcard value of "*", then it can be a sip: or tel: URI. Each tel: URI and sip: URI that was converted
                        from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number. This must be present
                        on the creation of an except-id element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:anyURI" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="starts-with-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:pattern value="\+{0,1}\d{0,32}" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="cdiv-operator-configuration-type">
        <xs:annotation>
            <xs:documentation>Operator Part of Communication Diversion</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true" the user is provisioned
                        with the communication
                        diversion service. If set to "false" this will withdraw the user service and the
                        cdiv-user-configuration element
                        must be
                        deleted at the same time. This must be present on the creation of the
                        communication-diversion service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cdiv-ruleset" type="cdiv-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more operator rules
                        that will be evaluation before any user rules. These rules apply
                        regardless of whether activated is “true” or “false”..
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="cdiv-op-rule-key">
                    <xs:selector xpath="./mmtel-context:cdiv-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
            <xs:element name="cdiv-ruleset-for-post-evaluation" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more operator rules
                        that will be evaluation after any user
                        rules. These rules apply
                        regardless of whether activated is “true” or “false”.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="cdiv-ruleset" type="cdiv-ruleset-type">
                            <xs:annotation>
                                <xs:documentation>
                                    Grouping element for a set of zero or more user rules.
                                </xs:documentation>
                            </xs:annotation>
                            <!-- NOTE makes rule id unique within the ruleset -->
                            <xs:key name="cdiv-op-post-rule-key">
                                <xs:selector xpath="./mmtel-context:cdiv-rule" />
                                <xs:field xpath="@id" />
                            </xs:key>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="user-no-reply-timer" type="activatedType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The user-no-reply-timer has values "activated" or "deactivated". When set to "activated" it
                        allows the subscriber to
                        control
                        the length of the no reply timer for the user, thus overriding the configured CFNR
                        nodal timer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cdiv-op-conditions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-op-conditions element is a grouping element for fine-grain provisioning options that
                        control which
                        conditions
                        the subscriber is permitted to use in communication diversion rules
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The anonymous-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the anonymous condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="busy-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The busy-condition element has values "activated" or "deactivated". When set to "activated" it
                                    allows the
                                    subscriber to use the cdiv-call-state condition with the value of "busy" in communication diversion
                                    rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the identity condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="media-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The media-condition element has values "activated" or "deactivated". When set to "activated"
                                    it allows
                                    the subscriber to use media conditions in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="not-registered-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The not-registered-condition element has values "activated" or "deactivated". When set to
                                    "activated" it
                                    allows the subscriber to use the cdiv-call-state condition with the value of "not-registered" in
                                    communication
                                    diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="no-answer-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The no-answer-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the cdiv-call-state condition with the value of "no-answer" in
                                    communication diversion
                                    rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="presence-status-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The presence-status-condition element has values "activated" or "deactivated". When set to
                                    "activated" it
                                    allows the subscriber to use presence-status conditions in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="validity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The validity-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the validity condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="not-reachable-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The not-reachable-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the cdiv-call-state condition with the value of "not-reachable" in
                                    communication diversion
                                    rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="valid-periods-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The valid-periods-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the valid-periods condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="invalidity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The invalidity-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the invalidity condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="served-identity-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The served-identity-condition element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the served-identity condition in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="unconditional-condition" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The unconditional-condition element has values "activated" or "deactivated". When set to
                                    "activated" or is ABSENT it allows the subscriber to use the cdiv-call-state condition
                                    with the value of "unconditional" in communication diversion rules.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="cdiv-op-actions" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-op-actions element is a grouping element for fine-grain provisioning options to control
                        which actions
                        the user is permitted to use for communication diversion rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="notify-caller-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The notify-caller-action element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the notify-caller action in communication diversion rules to control
                                    whether the caller
                                    is notified that the call is being forwarded
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="notify-served-user-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The notify-served-user-action element has values "activated" or "deactivated". When set to
                                    "activated" it
                                    allows the subscriber to use the notify-served-user action in communication diversion rules to
                                    control
                                    whether the served user is notified that the call is being forwarded
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="notify-served-user-on-outbound-call-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The notify-served-user-on-outbound-call-action element has values "activated" or
                                    "deactivated". When
                                    set to "activated" it allows the subscriber to use the notify-served-user-on-outbound-call
                                    action in
                                    communication diversion rules to control whether the served user is notified that calls are being
                                    forwarded
                                    when he makes a call attempt
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="reveal-identity-to-caller-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The reveal-identity-to-caller-action has values "activated" or "deactivated". When set to
                                    "activated" it
                                    allows the subscriber to use the reveal-identity-to-caller action in communication diversion
                                    rules to
                                    control whether the caller being notified that the call is being forwarded receives the target's
                                    identity information
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="reveal-identity-to-target-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The reveal-identity-to-target-action has values "activated" or "deactivated". When set to
                                    "activated"
                                    it allows the subscriber to use the reveal-identity-to-target action in communication diversion
                                    rules
                                    to control whether the diverted-to party receives identity information of the diverting party.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="rule-no-reply-timer" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The rule-no-reply-timer has values "activated" or "deactivated". When set to "activated“ it
                                    allows the subscriber
                                    to use the
                                    no reply timer in the action of communication diversion rules to control the
                                    length of the no reply timer on a
                                    per rule basis.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="do-not-disturb-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The do-not-disturb-action element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the do-not-disturb action in communication diversion rules to
                                    control whether the caller
                                    is handled by do-not-disturb service (e.g. treated with specific charging scheme,
                                    etc.)
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The play-announcement-action element has values "activated" or "deactivated". When set to
                                    "activated" it allows
                                    the subscriber to use the play-announcement action in communication diversion rules to
                                    control whether the
                                    caller
                                    is presented by specific announcement handled by generic announcement service.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of allowed CDIV rules in the user document. Not specified or zero limit means
                        no limit
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cdiv-user-configuration-type">
        <xs:sequence>
            <xs:element name="active" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The active element has values "true" or "false". It controls whether the communication diversion
                        service is
                        active or not for this subscriber
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- cdiv-service-options is a structured parameter allowing it to be made nilable -->
            <xs:element name="cdiv-service-options" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more service options
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <!-- New user level CFNR timer config -->
                        <xs:element name="NoReplyTimer" type="no-reply-timer-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The NoReplyTimer element specifies the time that must expire without answer before the no
                                    answer
                                    condition is triggered. The value is an integer giving the timer in the range of 5 to 180 seconds. This
                                    value applies to rules with no-answer conditions which do not contain their own individual timer.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <!-- ruleset is a structured parameter that is optional and nillable -->
            <xs:element name="cdiv-ruleset" type="cdiv-ruleset-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more user rules
                    </xs:documentation>
                </xs:annotation>
                <!-- NOTE makes rule id unique within the ruleset -->
                <xs:key name="cdiv-rule-key">
                    <xs:selector xpath="./mmtel-context:cdiv-rule" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cdiv-ruleset-type">
        <xs:sequence>
            <xs:element name="cdiv-rule" type="cdiv-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        An individual rule controlling communication diversion behaviour. The cdiv-rule element is a
                        sub-MO allowing
                        multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
                <!-- sub MOs need a key. The key for rule is id. -->
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="no-reply-timer-type">
        <xs:restriction base="xs:positiveInteger">
            <xs:minInclusive value="5" />
            <xs:maxInclusive value="180" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="cdiv-rule-type">
        <xs:sequence>
            <xs:element name="id" type="xs:NCName" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique identifier for an individual rule. This must be unique within the scope of the complete
                        document. This
                        must be present on the creation of a cdiv-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cdiv-conditions" type="cdiv-conditions-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-conditions element is a grouping element for conditions for a rule. All conditions must
                        be satisfied
                        for the rule to take effect. If no conditions are present then the rule is always applicable. The
                        conditions that
                        are permitted depend on the fine grain provisioning options in cdiv-op-conditions
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- NOTE cdiv-actions is optional but not nillable. Every CDIV rule must have actions/forward-to/target to be valid but
                cai3g:Set -->
            <!-- could just update conditions so actions must be optional -->
            <xs:element name="cdiv-actions" type="cdiv-actions-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-actions element is a grouping element for the actions for a rule. This must be present
                        on the
                        creation of a cdiv-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:NCName" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="cdiv-conditions-type">
        <xs:sequence>
           <xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0" >
                <xs:annotation>
                    <xs:documentation>
                        The rule-deactivated element has values "true" or "false". If present with the value "true" this
                        has the effect
                        of deactivating the individual rule. Set to "false" to remove this condition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cdiv-call-state" type="cdiv-call-state-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-call-state condition controls which state the user must be in for the rule to apply. The
                        value "busy"
                        is satisfied if the user is busy in other calls. The value "no-answer" applies when there is no answer
                        from the
                        user. The value "not-registered" applies when the user is not registered on the MTAS. The value
                        "not-reachable"
                        applies when the user is not reachable because either a specific response has been received or the
                        not reachable
                        timer expires.
                        The value "unconditional" is used to clear the other call state values so that the
                        condition is satisfied regardless
                        of the
                        user's call state.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- A structured parameter is used to cover all possible caller identity values - being structured it can be nilled -->
            <xs:element name="cdiv-caller-identity" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The cdiv-caller-identity element is a grouping element for conditions which are based on the
                        caller's identity
                        (or lack of an identity in the case of anonymous).
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:element name="anonymous" type="empty-element-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The anonymous element is an empty element specifying a condition which is satisfied if the
                                    caller
                                    is anonymous. This can be removed by deleting the enclosing cdiv-caller-identity element or by
                                    replacing
                                    it with an identity element. The elements anonymous and identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <!-- no nilling at the level of identity - use nilling on cdiv-caller-identity to remove -->
                        <xs:element name="identity" type="identity-type">
                            <xs:annotation>
                                <xs:documentation>
                                    The identity element is a grouping element for conditions which are based on the caller's
                                    identity.
                                    The condition is satisfied if any of the included one or many elements within it is matched. The
                                    elements anonymous and identity are mutually exclusive.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:key name="cdiv-one-key">
                                <xs:selector xpath="./mmtel-context:one" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="cdiv-many-key">
                                <xs:selector xpath="./mmtel-context:many" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="cdiv-except-id-key">
                                <xs:selector xpath=".//mmtel-context:except-id" />
                                <xs:field xpath="@id" />
                            </xs:key>
                            <xs:key name="cdiv-except-domain-key">
                                <xs:selector xpath=".//mmtel-context:except-domain" />
                                <xs:field xpath="@domain" />
                            </xs:key>
                            <xs:key name="cdiv-number-match-key">
                                <xs:selector xpath="./mmtel-context:number-match" />
                                <xs:field xpath="@starts-with" />
                            </xs:key>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <!-- media is a multiple value parameter -->
            <xs:element name="media" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The media element contains a media type that the session must include for the condition to be
                        matched e.g.
                        "audio" or "video". This is a multi-value parameter so it can appear more than once once with several
                        media
                        values that must all be satisfied for the overall condition to be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- validity is a structured parameter so it can be nillable -->
            <xs:element name="validity" type="validityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The validity element is a grouping element for time periods (intervals) within which the rule is
                        valid.
                        The validity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing validity element -->
                <xs:key name="cdiv-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="presence-status" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The presence-status element contains a presence status value that the user must satisfy for the
                        condition to
                        be matched e.g. "meal", "meeting", "travel", "vacation". This is a multi-value parameter so it can
                        appear more
                        than once with several presence status values that must all be satisfied for the overall condition to
                        be matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valid-periods" type="valid-periods-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The valid-periods element is a grouping element for recurring time periods (intervals) within
                        which the rule is
                        valid.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidity" type="invalidityType" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The invalidity element is a grouping element for time periods (intervals) within which the rule
                        is NOT valid.
                        The invalidity condition must contain at least one interval.
                    </xs:documentation>
                </xs:annotation>
                <!-- make sure the interval key "from" is unique within the enclosing invalidity element -->
                <xs:key name="cdiv-invalidity-interval-key">
                    <xs:selector xpath="./mmtel-context:interval" />
                    <xs:field xpath="@from" />
                </xs:key>
            </xs:element>
            <xs:element name="served-identity" type="served-identity-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The served-identity element is a grouping element for conditions which are based on the user's
                        served identity. The condition is satisfied if any of the included elements within it is matched.
                    </xs:documentation>
                </xs:annotation>
                <xs:key name="cdiv-served-identity-one-key">
                    <xs:selector xpath="./mmtel-context:one" />
                    <xs:field xpath="@id" />
                </xs:key>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cdiv-actions-type">
        <xs:sequence>
            <xs:element name="forward-to" type="forward-to-type">
                <xs:annotation>
                    <xs:documentation>
                        The forward-to element is a grouping element with details of the target to which the
                        communication should be
                        diverted and optional control of notifications and which identities are revealed to whom.
                        This must be present
                        on the creation of a cdiv-rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="do-not-disturb" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The do-not-disturb element has values "true" and "false". If it's set to "true" the element is
                        added into
                        the actions part of the rule. If it's set to "false" the element is removed from the actions part of the
                        rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The play-announcement element has string values from 0 to 32 characters.
                        When the
                        play-announcement action is set with the string value containing characters with the length between 1 to 32,
                        if
                        there is any satisfying corresponding conditions and being diverted, the caller will be presented with the
                        specific announcement handled by generic announcement service.
                        When the play-announcement action is set with the
                        string value containing character with the length of 0,
                        any play-announcement action element in the rule will be
                        deleted from the rule.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <!-- cdiv-action-options is a structured parameter allowing it to be made nillable -->
            <xs:element name="cdiv-action-options" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Grouping element for a set of zero or more action options
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <!-- New rule level CFNR timer config -->
                        <xs:element name="NoReplyTimer" type="no-reply-timer-type" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    The NoReplyTimer element specifies the time that must expire without answer before the no
                                    answer condition is triggered. The value is an integer giving the timer in the range of 5 to 180
                                    seconds. This
                                    value applies to no answer rules which do not contain their own individual
                                    timer.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="cdiv-call-state-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="unconditional" />
            <xs:enumeration value="busy" />
            <xs:enumeration value="no-answer" />
            <xs:enumeration value="not-registered" />
            <xs:enumeration value="not-reachable" />
            <!-- EMA does not support nilling a single value parameter so assigning the value of "unconditional" means none of the
                three conditions apply -->
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="cel-operator-configuration-type">
        <xs:sequence>
            <xs:element name="activated" type="activated-enum-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The activated element has values "true" or "false". When set to "true", the user is provisioned with
                        the communication event logging service. If set to "false", this will withdraw the service from the user.
                        This must be present on the creation of the communication event logging service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="events" type="cel-events-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The events element is a grouping of active events. Nothing is reported if this element is not set.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cel-events-type">
        <xs:sequence>
            <xs:element name="established" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        When established element is set then outgoing established attempts are reported.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unsuccessful-attempt" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        When unsuccessful-attempt element is set then outgoing unsuccessful attempts are reported.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="outgoing-attempt" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        When outgoing-attempt element is set then any outgoing attempts are reported.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="incoming-established" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        When incoming-established element is set then incoming established attempts are reported.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="subscriber-state-type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="device-group-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MOBILE"/>
            <xs:enumeration value="FIXED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="access-type-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CS"/>
            <xs:enumeration value="4G"/>
            <xs:enumeration value="5G"/>
            <xs:enumeration value="WIFI"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="max-number-of-parties-type">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="3"/>
            <xs:maxInclusive value="32"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="msn-number-type">
        <xs:annotation>
            <xs:documentation>
                MSN number
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="fip-alternative-user-identity-type">
        <xs:restriction base="xs:anyURI">
            <xs:minLength value="5" />
            <xs:maxLength value="16" />
            <xs:pattern value="[+][0-9]+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="fip-suppression-enum-type">
        <xs:annotation>
            <xs:documentation>
                MTAS allows
                - TOLLFREE
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TOLLFREE" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="stored-number-type">
        <xs:annotation>
            <xs:documentation>
                The stored-number-type is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to
                section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be normalized after removing a
                dynamic ad-hoc presentation supplementary service code and/or a carrier select code.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:anyURI" />
    </xs:simpleType>
    <xs:simpleType name="status-code-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="400" />
            <xs:maxInclusive value="699" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="reason-header-type">
        <xs:sequence>
            <xs:element name="reason-protocol" type="reason-protocol-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The reason-protocol element defines the protocol.
                        Allowed values are Q.850 or SIP.
                        This must be present on the creation of a reason-header element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-cause" type="reason-cause-type" minOccurs="0"  nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The reason-cause element defines Q.850/SIP cause code.
                        Allowed value range is 1-699.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason-text" type="reason-text-type" minOccurs="0"  nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The reason-text element includes reason text as a string with length between 1 to 128.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="reason-protocol-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Q.850" />
            <xs:enumeration value="SIP" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="reason-cause-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1" />
            <xs:maxInclusive value="699" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="reason-text-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="128" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="mcid-mode-type">
        <xs:annotation>
            <xs:documentation>
                Mode of MCID operation
                - permanent - logs all communications
                - temporary - allows the last communication to be logged on user request
                - inactive - logging of calls disabled
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="permanent" />
            <xs:enumeration value="temporary" />
            <xs:enumeration value="inactive" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="mcid-orig-mode-type">
        <xs:annotation>
            <xs:documentation>
                Mode of originating MCID operation
                - permanent - logs all communications
                - inactive - logging of calls disabled
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="permanent" />
            <xs:enumeration value="inactive" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="mdcp-device-group-name-enum-type">
        <xs:annotation>
            <xs:documentation>
                MTAS allows
                - FIXED
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIXED" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="E164-number-type">
        <xs:restriction base="xs:string">
            <xs:pattern value="\d{4,15}" />
            <!-- A E.164 number can have 4 to 15 digits. -->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="default-call-handling-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="release" />
            <xs:enumeration value="continue" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="imsi-type">
        <xs:restriction base="xs:string">
            <xs:pattern value="\d{5,15}" />
            <!-- An IMSI can have 5 to 15 digits. -->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="px-call-notification-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="busy" />
            <xs:enumeration value="not-reachable" />
            <xs:enumeration value="no-answer" />
            <xs:enumeration value="called-number" />
            <xs:enumeration value="answer" />
            <xs:enumeration value="disconnected" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="category-name-type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="32" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="program-number">
        <xs:restriction base="xs:nonNegativeInteger">
            <xs:maxInclusive value="255" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="carrier-select-code-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="8" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="carrier-name-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="32" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="b-number-type-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TOLLFREE" />
            <xs:enumeration value="NSC" />
            <xs:enumeration value="GP1" />
            <xs:enumeration value="GP2" />
            <xs:enumeration value="GP3" />
            <xs:enumeration value="GP4" />
            <xs:enumeration value="GP5" />
            <xs:enumeration value="GP6" />
            <xs:enumeration value="GP7" />
            <xs:enumeration value="GP8" />
            <xs:enumeration value="GP9" />
            <xs:enumeration value="GP10" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="localness-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Local" />
            <xs:enumeration value="Non Local" />
            <xs:enumeration value="L_National" />
            <xs:enumeration value="L_International" />
            <xs:enumeration value="L_IntraLata" />
            <xs:enumeration value="L_IntraLataToll" />
            <xs:enumeration value="L_InterLata" />
            <xs:enumeration value="L_NanpZone1" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="external-query-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="calling-name" />
            <xs:enumeration value="company-number" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="identityPresentationRestrictionOverrideType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="override-active" />
            <xs:enumeration value="override-not-active" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="identityPresentationDefaultBehaviourType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="presentation-restricted" />
            <xs:enumeration value="presentation-not-restricted" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="identityPresentationModeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="permanent" />
            <xs:enumeration value="temporary" />
            <xs:enumeration value="ad-hoc-temporary-presentation-restricted" />
            <xs:enumeration value="ad-hoc-temporary-presentation-not-restricted" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="identityPresentationRestrictionType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="only-identity" />
            <xs:enumeration value="all-private-information" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="terminatingIdentityPresentationModeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="permanent" />
            <xs:enumeration value="temporary" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="max-ucd-targets-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="2" />
            <xs:maxInclusive value="10" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="max-ucd-device-targets-type">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="2" />
            <xs:maxInclusive value="10" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="home-location-type">
        <xs:annotation>
            <xs:documentation>
                Confirming to P-Access-Network-Info header ABNF syntax described in 3GPP TS 24.229:
                IP Multimedia Call Control Protocol based on Session Initiation Protocol (SIP) and
                Session Description Protocol (SDP); Stage 3 (Release 11) section 7.2A.4.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[^;]+;[^=]+=[^;]+; *network-provided *" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="time-zone-area-type">
        <xs:annotation>
            <xs:documentation>
                The time-zone-area is the user home time zone area.
                The time-zone-area is in the form "Area/Location and must be included in the list
                of time zones in IANA Time Zone Database. Example: "Asia/Tokyo", "Canada/Central",
                "Europe/Copenhagen", "Australia/Canberra", "America/Los_Angeles".
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/_\+\-]+" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="in-sip-request-condition-list-type">
        <xs:sequence>
            <xs:element name="flexcondition-definition" type="flexcondition-definition-type" minOccurs="0" maxOccurs="unbounded" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The flexcondition-definition element is a grouping element for attributes which actually define a SIP regexp condition.
                        The flexcondition-definition element is a sub-MO allowing multiple instances with "id" as the unique key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="flexcondition-definition-type">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A key uniquely identifying the condition.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="header" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A SIP header matched.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="parameter" type="xs:string" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A SIP header parameter matched.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A regular expression to match against a given header or header parameter value.
                        This must be present on the creation of a flexcondition-definition element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="match-inverse" type="xs:boolean" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        If set to "true", the SIP regexp condition will evaluate to true if the parameter value does
                        NOT match the regular expression.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="feature-tag-preferences-type">
        <xs:sequence>
            <xs:element name="feature-tags" type="feature-tags-type">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Defines the feature tags. In case of multiple feature tags, they must be separated with a semicolon.
                        The string must be between minimum 1 and maximum 255 characters.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="feature-tags-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="subscription-type">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The "id" is a unique key for the subscription group, currently limited to 1.
                        It must be present at the creation of a subscription element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="imsi" type="ucd-imsi-type" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The IMSI that is connected to this subscriber.
                        The IMSI is a maximum 15-digit number as defined in 3GPP TS 23.003.
                        Used by MTAS in charging reports both for registered and unregistered users.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="ucd-imsi-type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="15" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="non-empty-anyURI-type">
        <xs:restriction base="xs:anyURI">
            <xs:minLength value="1"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="mobile-subscription-list-type">
        <xs:sequence>
            <xs:element name="subscription" type="mobile-subscription-type" minOccurs="0" maxOccurs="unbounded" nillable="true">
                <xs:annotation>
                    <xs:documentation>
                        The &lt;subscription&gt; element is a sub-MO allowing multiple instances with "id" as the unique key.
                        At least 1 &lt;subscription&gt; element must be present on the creation of a &lt;mobile-subscription-list&gt; element.
                        Use xsi:nil="true" to delete this element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="mobile-subscription-type">
        <xs:sequence>
            <xs:element name="id" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        A unique string of 1-50 characters, identifier for a subscription.
                        This must be unique within the scope of the complete document.
                        This must be present on the creation of a &lt;subscription&gt; element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="impi" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Subscriber device Identity. An IMPI is provisioned for each mobile(SIM) device as defined in 3GPP TS 23.003.
                        For a mobile device, the IMPI is normally constructed from the IMSI associated with the device.
                        Allowed format:
                        &lt;MCC&gt;&lt;MNC&gt;&lt;MSIN&gt;@ims.mnc&lt;MNC&gt;.mcc&lt;MCC&gt;.3gppnetwork.org
                        This must be present on the creation of a &lt;subscription&gt; element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cs-capable" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines capability of mobile subscription to camp on 2G/3G network.
                        This element has value "true" or "false".
                        This must be present on the creation of a &lt;subscription&gt; element.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="msisdn" type="E164-number-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines the MSISDN number for the SIM subscription.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="imsi" type="ucd-imsi-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines the IMSI number for the SIM subscription.
                        The IMSI is a maximum 15-digit number as defined in 3GPP TS 23.003.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="default-subscription" type="empty-element-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines the default subscription.
                        Use xsi:nil="true" to delete this element.
                        This element is optional but must be defined for one entry in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sip-control-channel-impi" type="sip-control-channel-impi-type" nillable="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines the SIP control channel IMPI used at registration of a SIP control channel that is established between a device and
                        MMTel AS for application specific control signaling.
                        Allowed length is 1-300 characters.
                        Use xsi:nil="true" to delete this element.
                        This element is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required" >
            <xs:annotation>
                <xs:appinfo>
                    <jaxb:property name="idAttr"/>
                </xs:appinfo>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="sip-control-channel-impi-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="300" />
            <xs:pattern value=".*@.+" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="terminal-selector-type">
        <xs:restriction base="xs:string">
            <xs:minLength value="1" />
            <xs:maxLength value="300" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="weekday-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Sunday" />
            <xs:enumeration value="Monday" />
            <xs:enumeration value="Tuesday" />
            <xs:enumeration value="Wednesday" />
            <xs:enumeration value="Thursday" />
            <xs:enumeration value="Friday" />
            <xs:enumeration value="Saturday" />
        </xs:restriction>
    </xs:simpleType>


</xs:schema>
