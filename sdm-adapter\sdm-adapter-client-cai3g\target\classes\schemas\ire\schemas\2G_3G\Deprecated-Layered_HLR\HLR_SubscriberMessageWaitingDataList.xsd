<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" jaxb:extensionBindingPrefixes="xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
	jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/hlrla_types.xsd" />
	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="PrimaryHLRId" type="primaryhlridType" />
	<!-- GetSubscriberMessageWaitingDataList MOId: msisdn or nimsi MOType: SubscriberMessageWaitingDataList@http://schemas.ericsson.com/pg/hlr/13.5/ -->
	<xs:element name="GetSubscriberMessageWaitingDataList">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SubscriberMessageWaitingData">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdn" type="msisdnType" />
							<xs:element name="imsi" type="imsiType" />
							<xs:element name="mce" type="mceType" />
							<xs:element name="mnrf" type="mnrfType" minOccurs="0" />
							<xs:element name="mnrg" type="mnrgType" minOccurs="0" />
							<xs:element name="scadd" type="scaddType" minOccurs="0" maxOccurs="unbounded" />
						</xs:sequence>
						<xs:attribute name="msisdn" type="msisdnType" use="optional">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="imsi" type="imsiType" use="optional">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>