<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.1.v20070730-0941-IXOqsospgq-tNz7
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\projectBased\sessionSingleSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative. All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<!-- Project based schema -->
<schema targetNamespace="http://ossj.org/xml/Inventory/v1-2"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:inv-v1-2="http://ossj.org/xml/Inventory/v1-2"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cbereport-v1-5="http://ossj.org/xml/Common-CBEReport/v1-5"
    version = "v1-2"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEReport/v1-5"
        schemaLocation="OSSJ-Common-CBEReport-v1-5.xsd"/>


    <!-- Tigerstripe : Datatype definitions for ReconciliationPolicy  (basic, ArrayOf) -->
    <complexType name="ReconciliationPolicy">
        <annotation>
            <documentation>
The ReconciliationPolicy interface is used to indicate the type of reconciliation to be used for file-based import of inventory data. The reconciliation policy is expressed in terms of allowed operations as defined in the AllowedReconciliationOperations interface (&quot;create&quot;, &quot;update&quot; or &quot;delete&quot;) and expected behavior as defined in the ReconciliationBehavior interface (i.e. &quot;perform_and_report&quot;, &quot;perform_only&quot; or &quot;report_only&quot;). If the &quot;perform_only&quot; behavior is set, the inventory system will perform the changes according to the data contained in the report and the allowed operations specified by the client. If the &quot;report_only&quot; behavior is set, the inventory system will not perform the changes but will generate a report representing the result of the reconciliation according to the data contained in the imported report and the allowed operations specified by the client. The generated report is equivalent to the report that would be generated by an export operation after completion of the import operation.  If the &quot;perform_and_report&quot; behavior is set, the inventory system will perform the changes and will generate a report representing the result of the reconciliation. 

            </documentation>

        </annotation>
                <sequence>
                    <element name="allowedOperations" type="int" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="expectedBehavior" type="int" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
    </complexType>
    <complexType name="ArrayOfReconciliationPolicy">
        <sequence>
            <element name="item" type="inv-v1-2:ReconciliationPolicy" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReconciliationPolicy -->
    <!-- Tigerstripe : End of Datatype definition for ReconciliationPolicy -->
    <!-- Tigerstripe : Datatype definitions for ReportScope  (basic, ArrayOf) -->
    <complexType name="ReportScope">
        <annotation>
            <documentation>
The ReportScope interface is used for file (report) based import/export of inventory data. The scope of a report is defined using an array of template inventory objects. All inventory objects maching the templates are included in the report. A template matches a managed entity if the following rules hold true:  
- The template type is the same as the managed entity's, or is a supertype of the managed entity's. Thus, a template and a managed entity can potentially match only if they are of the same class. Thus matching occurs only against managed entities of the same type as the template or extensions of that type. If the template is of type A implementing the X interface, then it will match the entities of type B that implement the X interface. 
- Every attribute in the template matches its corresponding attribute in the managed entity where:  
- If a template's attribute is not populated then it matches the managed entity's corresponding attribute 
- If a template's attribute is populated then it matches the managed entity's corresponding attribute if the two have the same value 
- Thus, a template and a managed entity will match if, for all attributes that are populated in the specification, each attribute has the same value as its corresponding attribute in the managed entity value. The meaning of &quot;same value&quot; is tied to the semantic of the equals() operation supported by the attribute. 
 
- If a ManagedEntityValue does not support a populated attribute present in the template then that attribute value is considered a wildcard and is always matched.  
 Note that managed entity template matching is not equivalent to managed entity equality testing. In template matching only the populated attributes of the template are tested for equality while in equality testing both managed entity values must contain the same populated attributes (and each attribute must be equal).
            </documentation>

        </annotation>
                <sequence>
                    <element name="cBEManagedEntityValues" type="cbecore-v1-5:ArrayOfCBEManagedEntityValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
    </complexType>
    <complexType name="ArrayOfReportScope">
        <sequence>
            <element name="item" type="inv-v1-2:ReportScope" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ReportScope -->
    <!-- Tigerstripe : End of Datatype definition for ReportScope -->
    <!-- Tigerstripe : Datatype definitions for SpecificationValidator  (basic, ArrayOf) -->
    <complexType name="SpecificationValidator">
        <annotation>
            <documentation>
This interface defines a SpecificationValidator. SpecificationValidator are used for client-side validation of entity specifications. EntitySpecifications provide a factory methods for SpecificationValidators.
            </documentation>
        </annotation>

                <sequence>
                </sequence>
    </complexType>
    <complexType name="ArrayOfSpecificationValidator">
        <sequence>
            <element name="item" type="inv-v1-2:SpecificationValidator" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of SpecificationValidator -->

    <!-- Tigerstripe : End of Datatype definition for SpecificationValidator -->

    <!-- Tigerstripe : Enumeration definitions for AllowedReconciliationOperations  -->
    <simpleType name="AllowedReconciliationOperations">
        <annotation>
            <documentation>
This interface defines the AllowedReconciliationOperations enumeration.
            </documentation>
        </annotation>
        <restriction base="int">

            <!-- name = CREATE_UPDATE_DELETE -->
            <enumeration value="0" />
            <!-- name = CREATE_UPDATE -->
            <enumeration value="1" />
            <!-- name = CREATE_DELETE -->
            <enumeration value="2" />
            <!-- name = UPDATE_DELETE -->
            <enumeration value="3" />
            <!-- name = CREATE -->

            <enumeration value="4" />
            <!-- name = UPDATE -->
            <enumeration value="5" />
            <!-- name = DELETE -->
            <enumeration value="6" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for AllowedReconciliationOperations  -->
    <!-- Tigerstripe : Enumeration definitions for NamingMode  -->

    <simpleType name="NamingMode">
        <annotation>
            <documentation>
This interface defines the NamingMode enumeration. Particular implementation may support one of the following naming modes:  
-  auto-naming, i.e. the server auto generates keys for the objects in the system 
-  client-controlled naming, i.e. the client provides the keys 
-  auto-naming and client-controlled naming, i.e. both of the above modes are supported 

            </documentation>
        </annotation>
        <restriction base="int">
            <!-- name = AUTO_NAMING -->
            <enumeration value="0" />
            <!-- name = CLIENT_CONTROLLED_NAMING -->

            <enumeration value="1" />
            <!-- name = CLIENT_CONTROLLED_AND_AUTO_NAMING -->
            <enumeration value="2" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for NamingMode  -->
    <!-- Tigerstripe : Enumeration definitions for ReconciliationBehavior  -->
    <simpleType name="ReconciliationBehavior">
        <annotation>

            <documentation>
This interface defines the ReconciliationBehavior enumeration.
            </documentation>
        </annotation>
        <restriction base="int">
            <!-- name = PERFORM_AND_REPORT -->
            <enumeration value="10" />
            <!-- name = REPORT_ONLY -->
            <enumeration value="11" />
            <!-- name = PERFORM_ONLY -->

            <enumeration value="12" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ReconciliationBehavior  -->
    <!-- Tigerstripe : Enumeration definitions for ValidationMode  -->
    <simpleType name="ValidationMode">
        <annotation>
            <documentation>
This interface defines the ValidationMode enumeration.
            </documentation>

        </annotation>
        <restriction base="int">
            <!-- name = CONTINUOUS_VALIDATION -->
            <enumeration value="0" />
            <!-- name = VALIDATION_ON_DEMAND -->
            <enumeration value="1" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ValidationMode  -->

    <!-- Tigerstripe : Exception definition for ReportScopeException  -->
    <complexType name="ReportScopeException">
        <annotation>
            <documentation>
A ReportScopeException is thrown to indicate that the scope of the report used in export/import operation contains unsupported types or instances that cannot be found.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:BaseException" >

                <sequence>
                </sequence>
             </extension>
        </complexContent>
    </complexType>
    <!-- Tigerstripe : End of Exception definition for ReportScopeException  -->
    <!-- Tigerstripe : Exception definition for SemanticRuleViolationException  -->
    <complexType name="SemanticRuleViolationException">
        <annotation>

            <documentation>
A SemanticRuleViolationException is thrown to indicate that an update operation (e.g. a creation, a removal or a set operation on an entity) violates a semantic rule enforced by the inventory system.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:BaseException" >
                <sequence>
                </sequence>
             </extension>
        </complexContent>

    </complexType>
    <!-- Tigerstripe : End of Exception definition for SemanticRuleViolationException  -->
    <!-- Tigerstripe : Exception definition for SpecificationViolationException  -->
    <complexType name="SpecificationViolationException">
        <annotation>
            <documentation>
A SpecificationViolationException is thrown to indicate that an update operation on an inventory entitiy violates the constraints defined in the specification associated with this entity. A SpecificationViolationException may also be thrown to indicate an attempt to modify an entity specification referenced by entities in a way that invalidates the compliance of these entities or to indicate an attempt to remove this entity specification.
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "co-v1-5:BaseException" >
                <sequence>
                </sequence>
             </extension>
        </complexContent>
    </complexType>
    <!-- Tigerstripe : End of Exception definition for SpecificationViolationException  -->

    <!-- Tigerstripe : Query definitions for InventoryQuery  -->

    <complexType name="InventoryQueryValue">
        <annotation>
            <documentation>
Base Interface for all Inventory Query Values.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:NamedQueryValue" >    
                <sequence>
                    <element name="responseType" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="InventoryQueryResponse">
        <annotation>
            <documentation>
                The InventoryQueryResponse collects the result 
                of query using InventoryQueryRequest.
            </documentation>

        </annotation>   
        <complexContent>
            <extension base = "co-v1-5:NamedQueryResponse" >
               <sequence>
                  <element name="sequence" type="int" minOccurs="0"/>
                  <element name="endOfReply" type="boolean" minOccurs="0"/>
                  <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"/>
               </sequence>
            </extension>

        </complexContent>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of InventoryQuery -->
    <!-- Tigerstripe : End of Query definitions for InventoryQuery  -->
    
    
    

        <!-- Tigerstripe : Update Procedure definitions for InventoryUpdateProcedure  -->
    <complexType name="InventoryUpdateProcedureValue">
        <annotation>
            <documentation>
Base Interface for all Inventory Update Procedure Values
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "co-v1-5:UpdateProcedureValue" >    
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="InventoryUpdateProcedureResponse">

        <annotation>
            <documentation>
                The InventoryUpdateProcedureResponse contains the result of 
                an update using InventoryUpdateProcedureRequest.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:UpdateProcedureResponse" >
                 <sequence>
                      <element name="status" minOccurs="0" nillable="true" type="int" />

                      <element name="successful" minOccurs="0" nillable="true" type="boolean" />
                  </sequence>
            </extension>
        </complexContent>
    </complexType>    
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of InventoryUpdateProcedure -->
    <!-- Tigerstripe : End of Update Procedure definitions for InventoryUpdateProcedure  -->


    <!-- Tigerstripe : Event definitions for AssociationValueAttributeChangeEvent  -->

    <element name="AssociationValueAttributeChangeEvent">
        <annotation>
            <documentation>
This event provides notification that an attribute value of an Association object has been modified.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:AssociationValueAttributeChangeEventType"/>
            </sequence>

        </complexType>
    </element>
    <complexType name="AssociationValueAttributeChangeEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="associationValue" type="cbecore-v1-5:AssociationValue" minOccurs="0" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AssociationValueAttributeChangeEvent -->
    <!-- Tigerstripe : End of Event definitions for AssociationValueAttributeChangeEvent  -->
    <!-- Tigerstripe : Event definitions for AssociationValueCreateEvent  -->
    <element name="AssociationValueCreateEvent">
        <annotation>
            <documentation>

This event subclass indicates that an AssociationValue has been created within the inventory management system.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:AssociationValueCreateEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="AssociationValueCreateEventType">

        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="associationValue" type="cbecore-v1-5:AssociationValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AssociationValueCreateEvent -->

    <!-- Tigerstripe : End of Event definitions for AssociationValueCreateEvent  -->
    <!-- Tigerstripe : Event definitions for AssociationValueRemoveEvent  -->
    <element name="AssociationValueRemoveEvent">
        <annotation>
            <documentation>
This event subclass indicates that a AssociationValue has been removed within the inventory management system.
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="event" type="inv-v1-2:AssociationValueRemoveEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="AssociationValueRemoveEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="associationKey" type="cbecore-v1-5:AssociationKey" minOccurs="0" />

                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AssociationValueRemoveEvent -->
    <!-- Tigerstripe : End of Event definitions for AssociationValueRemoveEvent  -->
    <!-- Tigerstripe : Event definitions for EntitySpecificationValueAttributeChangeEvent  -->
    <element name="EntitySpecificationValueAttributeChangeEvent">

        <annotation>
            <documentation>
This event provides notification that an attribute value of a specification object has been modified.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntitySpecificationValueAttributeChangeEventType"/>
            </sequence>
        </complexType>

    </element>
    <complexType name="EntitySpecificationValueAttributeChangeEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntitySpecificationValueAttributeChangeEvent -->
    <!-- Tigerstripe : End of Event definitions for EntitySpecificationValueAttributeChangeEvent  -->
    <!-- Tigerstripe : Event definitions for EntitySpecificationValueCreateEvent  -->
    <element name="EntitySpecificationValueCreateEvent">
        <annotation>
            <documentation>
This event subclass indicates that an EntitySpecificationValue has been created within the inventory management system.
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntitySpecificationValueCreateEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="EntitySpecificationValueCreateEventType">
        <complexContent>

            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntitySpecificationValueCreateEvent -->
    <!-- Tigerstripe : End of Event definitions for EntitySpecificationValueCreateEvent  -->

    <!-- Tigerstripe : Event definitions for EntitySpecificationValueRemoveEvent  -->
    <element name="EntitySpecificationValueRemoveEvent">
        <annotation>
            <documentation>
This event subclass indicates that an EntitySpecificationValue has been created within the inventory management system.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntitySpecificationValueRemoveEventType"/>

            </sequence>
        </complexType>
    </element>
    <complexType name="EntitySpecificationValueRemoveEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="entitySpecificationKey" type="cbecore-v1-5:EntitySpecificationKey" minOccurs="0" />
                </sequence>

            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntitySpecificationValueRemoveEvent -->
    <!-- Tigerstripe : End of Event definitions for EntitySpecificationValueRemoveEvent  -->
    <!-- Tigerstripe : Event definitions for EntityValueAttributeChangeEvent  -->
    <element name="EntityValueAttributeChangeEvent">
        <annotation>

            <documentation>
This event provides notification that an attribute value of a Entity object has been modified.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntityValueAttributeChangeEventType"/>
            </sequence>
        </complexType>
    </element>

    <complexType name="EntityValueAttributeChangeEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="entityValue" type="cbecore-v1-5:EntityValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntityValueAttributeChangeEvent -->
    <!-- Tigerstripe : End of Event definitions for EntityValueAttributeChangeEvent  -->
    <!-- Tigerstripe : Event definitions for EntityValueCreateEvent  -->
    <element name="EntityValueCreateEvent">
        <annotation>
            <documentation>
This event subclass indicates that an EntityValue has been created within the inventory management system.
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntityValueCreateEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="EntityValueCreateEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>

                    <element name="entityValue" type="cbecore-v1-5:EntityValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntityValueCreateEvent -->
    <!-- Tigerstripe : End of Event definitions for EntityValueCreateEvent  -->
    <!-- Tigerstripe : Event definitions for EntityValueRemoveEvent  -->

    <element name="EntityValueRemoveEvent">
        <annotation>
            <documentation>
This event subclass indicates that a EntityValue has been removed within the inventory management system.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:EntityValueRemoveEventType"/>
            </sequence>

        </complexType>
    </element>
    <complexType name="EntityValueRemoveEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="entityKey" type="cbecore-v1-5:EntityKey" minOccurs="0" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of EntityValueRemoveEvent -->
    <!-- Tigerstripe : End of Event definitions for EntityValueRemoveEvent  -->
    <!-- Tigerstripe : Event definitions for InventoryExportCompletedEvent  -->
    <element name="InventoryExportCompletedEvent">
        <annotation>
            <documentation>

This event provides notification that an inventory export operation has been completed.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:InventoryExportCompletedEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="InventoryExportCompletedEventType">

        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="reportType" type="int" minOccurs="0" />
                    <element name="reportInfo" type="cbereport-v1-5:ReportInfo" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of InventoryExportCompletedEvent -->
    <!-- Tigerstripe : End of Event definitions for InventoryExportCompletedEvent  -->
    <!-- Tigerstripe : Event definitions for InventoryImportCompletedEvent  -->
    <element name="InventoryImportCompletedEvent">
        <annotation>
            <documentation>
This event provides notification that an inventory import has been completed.
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:InventoryImportCompletedEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="InventoryImportCompletedEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>

                    <element name="reportInfo" type="cbereport-v1-5:ReportInfo" minOccurs="0" />
                    <element name="reportProviderName" type="string" minOccurs="0" />
                    <element name="reportType" type="int" minOccurs="0" />
                    <element name="reportScope" type="inv-v1-2:ReportScope" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of InventoryImportCompletedEvent -->
    <!-- Tigerstripe : End of Event definitions for InventoryImportCompletedEvent  -->
    <!-- Tigerstripe : Event definitions for SpecificationValueAttributeChangedEvent  -->
    <element name="SpecificationValueAttributeChangedEvent">
        <annotation>
            <documentation>
This event provides notification that an attribute value of a Specification object has been modified.
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="event" type="inv-v1-2:SpecificationValueAttributeChangedEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="SpecificationValueAttributeChangedEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>

                    <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of SpecificationValueAttributeChangedEvent -->
    <!-- Tigerstripe : End of Event definitions for SpecificationValueAttributeChangedEvent  -->
    <!-- Tigerstripe : Event definitions for SpecificationValueRemoveEvent  -->

    <element name="SpecificationValueRemoveEvent">
        <annotation>
            <documentation>

            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="inv-v1-2:SpecificationValueRemoveEventType"/>

            </sequence>
        </complexType>
    </element>
    <complexType name="SpecificationValueRemoveEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                </sequence>
            </extension>

        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of SpecificationValueRemoveEvent -->
    <!-- Tigerstripe : End of Event definitions for SpecificationValueRemoveEvent  -->

    <!-- Tigerstripe : Session entity Operations -->
    <!-- Tigerstripe : Managed Entity Operations for Entity ( NB These may be overridden in session)  -->
    <element name="createEntityByValueRequest">
        <annotation>

            <documentation>
                This is the Request for the createEntityByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="createEntityByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createEntityByValue Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="entityKey" type="cbecore-v1-5:EntityKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntityByValueException">
        <annotation>
            <documentation>
                This is the Exception for the createEntityByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
            </choice>
        </complexType>

    </element>
    <element name="createEntitiesByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createEntitiesByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitiesByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createEntitiesByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitiesByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the createEntitiesByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="createEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the createEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />           
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitiesByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the createEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />
            </sequence>

        </complexType>
    </element>
    <element name="createEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the createEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="tryCreateEntitiesByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the tryCreateEntitiesByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"  />

            </sequence>
        </complexType>
    </element>
    <element name="tryCreateEntitiesByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the tryCreateEntitiesByValues Operation
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="entityKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateEntitiesByValuesException">
        <annotation>
            <documentation>

                This is the Exception for the tryCreateEntitiesByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>

        </complexType>
    </element>
    <element name="tryCreateEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryCreateEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />           
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateEntitiesByKeysResponse">
        <annotation>
            <documentation>

                This is the Response for the tryCreateEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
            </sequence>
        </complexType>
    </element>

    <element name="tryCreateEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryCreateEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="createEntitiesByAutoNamingRequest">
        <annotation>
            <documentation>
                This is the Request for the createEntitiesByAutoNaming Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="howMany" type="int"/>
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitiesByAutoNamingResponse">

        <annotation>
            <documentation>
                This is the Response for the createEntitiesByAutoNaming Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />           
            </sequence>
        </complexType>

    </element>
    <element name="createEntitiesByAutoNamingException">
        <annotation>
            <documentation>
                This is the Exception for the createEntitiesByAutoNaming Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="getEntityByKeyRequest">
        <annotation>

            <documentation>
                This is the Request for the getEntityByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKey" type="cbecore-v1-5:EntityKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>

        </complexType>
    </element>
    <element name="getEntityByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntityByKey Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getEntityByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the getEntityByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
            </choice>
        </complexType>
    </element>

    <element name="getEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitiesByKeys Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />

                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getEntitiesByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitiesByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getEntitiesByKeysException">
        <annotation>

            <documentation>
                This is the Exception for the getEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>

            </choice>
        </complexType>
    </element>
    <element name="getEntitiesByTemplateRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitiesByTemplate Operation
                This operation is MANDATORY
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:EntityValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="getEntitiesByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitiesByTemplate Operation
            </documentation>
        </annotation>
        <complexType>

            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="getEntitiesByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitiesByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
            </choice>
        </complexType>
    </element>
    <element name="getEntitiesByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitiesByTemplates Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="cbecore-v1-5:ArrayOfEntityValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>

            </complexContent>
        </complexType>
    </element>
    <element name="getEntitiesByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitiesByTemplates Operation
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>

    </element>
    <element name="getEntitiesByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitiesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="setEntityByValueRequest">
        <annotation>

            <documentation>
                This is the Request for the setEntityByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>

        </complexType>
    </element>
    <element name="setEntityByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the setEntityByValue Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setEntityByValueException">
        <annotation>
            <documentation>
                This is the Exception for the setEntityByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
            </choice>

        </complexType>
    </element>
    <element name="setEntitiesByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntitiesByValues Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByValuesResponse">
        <annotation>
            <documentation>

                This is the Response for the setEntitiesByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByValuesException">

        <annotation>
            <documentation>
                This is the Exception for the setEntitiesByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
            </choice>
        </complexType>
    </element>
    <element name="setEntitiesByKeysRequest">
        <annotation>

            <documentation>
                This is the Request for the setEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />           
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="setEntitiesByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the setEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the setEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="setEntitiesByTemplateRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntitiesByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="template" type="cbecore-v1-5:EntityValue"  />
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the setEntitiesByTemplate Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByTemplateException">
        <annotation>
            <documentation>

                This is the Exception for the setEntitiesByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="setEntitiesByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntitiesByTemplates Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="templates" type="cbecore-v1-5:ArrayOfEntityValue"  />
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="setEntitiesByTemplatesResponse">

        <annotation>
            <documentation>
                This is the Response for the setEntitiesByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>

    </element>
    <element name="setEntitiesByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the setEntitiesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="trySetEntitiesByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the trySetEntitiesByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityValues" type="cbecore-v1-5:ArrayOfEntityValue"  />

                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="trySetEntitiesByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the trySetEntitiesByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetEntitiesByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the trySetEntitiesByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="trySetEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the trySetEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />           
                <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetEntitiesByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the trySetEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
            </sequence>
        </complexType>

    </element>
    <element name="trySetEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the trySetEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="trySetEntitiesByTemplateRequest">
        <annotation>
            <documentation>

                This is the Request for the trySetEntitiesByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:EntityValue"  />
                    <element name="entityValue" type="cbecore-v1-5:EntityValue"  />

                    <element name="failuresOnly" type="boolean"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="trySetEntitiesByTemplateResponse">
        <annotation>
            <documentation>

                This is the Response for the trySetEntitiesByTemplate Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityKeyResult"/>
                </sequence>

                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="trySetEntitiesByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the trySetEntitiesByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="trySetEntitiesByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the trySetEntitiesByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">

                <sequence>
                    <element name="templates" type="cbecore-v1-5:ArrayOfEntityValue"  />
                    <element name="entityValue" type="cbecore-v1-5:EntityValue"  />
                    <element name="failuresOnly" type="boolean"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="trySetEntitiesByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the trySetEntitiesByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">

                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityKeyResult"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="trySetEntitiesByTemplatesException">
        <annotation>

            <documentation>
                This is the Exception for the trySetEntitiesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="removeEntityByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the removeEntityByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="entityKey" type="cbecore-v1-5:EntityKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeEntityByKeyResponse">
        <annotation>
            <documentation>

                This is the Response for the removeEntityByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeEntityByKeyException">

        <annotation>
            <documentation>
                This is the Exception for the removeEntityByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the removeEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitiesByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the removeEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="removeEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the removeEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeEntitiesByTemplateRequest">
        <annotation>

            <documentation>
                This is the Request for the removeEntitiesByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="template" type="cbecore-v1-5:EntityValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="removeEntitiesByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the removeEntitiesByTemplate Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

            </sequence>
        </complexType>
    </element>
    <element name="removeEntitiesByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the removeEntitiesByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="removeEntitiesByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the removeEntitiesByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="templates" type="cbecore-v1-5:ArrayOfEntityValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitiesByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the removeEntitiesByTemplates Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitiesByTemplatesException">
        <annotation>
            <documentation>

                This is the Exception for the removeEntitiesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryRemoveEntitiesByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeys" type="cbecore-v1-5:ArrayOfEntityKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the tryRemoveEntitiesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entityKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
            </sequence>
        </complexType>

    </element>
    <element name="tryRemoveEntitiesByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveEntitiesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplateRequest">
        <annotation>
            <documentation>

                This is the Request for the tryRemoveEntitiesByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:EntityValue"  />
                    <element name="failuresOnly" type="boolean"/>

                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the tryRemoveEntitiesByTemplate Operation
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityKeyResult"/>
                </sequence>
                </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveEntitiesByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplatesRequest">
        <annotation>

            <documentation>
                This is the Request for the tryRemoveEntitiesByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="cbecore-v1-5:EntityValue"  />

                    <element name="failuresOnly" type="boolean"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplatesResponse">
        <annotation>
            <documentation>

                This is the Response for the tryRemoveEntitiesByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntityKeyResult"/>
                </sequence>

                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="tryRemoveEntitiesByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveEntitiesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <!-- Tigerstripe : End of Managed Entity Operations for Entity  -->
    <!-- Tigerstripe : Managed Entity Operations for EntitySpecification ( NB These may be overridden in session)  -->
    <element name="createEntitySpecificationByValueRequest">
        <annotation>
            <documentation>
                This is the Request for the createEntitySpecificationByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitySpecificationByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createEntitySpecificationByValue Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitySpecificationByValueException">
        <annotation>

            <documentation>
                This is the Exception for the createEntitySpecificationByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
            </choice>
        </complexType>
    </element>
    <element name="createEntitySpecificationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createEntitySpecificationsByValues Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createEntitySpecificationsByValuesResponse">
        <annotation>

            <documentation>
                This is the Response for the createEntitySpecificationsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKeys" type="cbecore-v1-5:ArrayOfEntitySpecificationKey"  />
            </sequence>
        </complexType>

    </element>
    <element name="createEntitySpecificationsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the createEntitySpecificationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryCreateEntitySpecificationsByValuesRequest">

        <annotation>
            <documentation>
                This is the Request for the tryCreateEntitySpecificationsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />
            </sequence>

        </complexType>
    </element>
    <element name="tryCreateEntitySpecificationsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the tryCreateEntitySpecificationsByValues Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="entitySpecificationKeyResults" type="cbecore-v1-5:ArrayOfEntitySpecificationKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateEntitySpecificationsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the tryCreateEntitySpecificationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="getEntitySpecificationByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitySpecificationByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />

                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getEntitySpecificationByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitySpecificationByKey Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getEntitySpecificationByKeyException">
        <annotation>

            <documentation>
                This is the Exception for the getEntitySpecificationByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>

            </choice>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitySpecificationsByKeys Operation
                This operation is MANDATORY
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="entitySpecificationKeys" type="cbecore-v1-5:ArrayOfEntitySpecificationKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the getEntitySpecificationsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="getEntitySpecificationsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitySpecificationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
            </choice>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplateRequest">
        <annotation>
            <documentation>

                This is the Request for the getEntitySpecificationsByTemplate Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:EntitySpecificationValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />

                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitySpecificationsByTemplate Operation
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"/>
                </sequence>
                </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitySpecificationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
            </choice>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplatesRequest">
        <annotation>

            <documentation>
                This is the Request for the getEntitySpecificationsByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />

                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplatesResponse">
        <annotation>
            <documentation>

                This is the Response for the getEntitySpecificationsByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"/>
                </sequence>

                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitySpecificationsByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="setEntitySpecificationByValueRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntitySpecificationByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="entitySpecificationValue" type="cbecore-v1-5:EntitySpecificationValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitySpecificationByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the setEntitySpecificationByValue Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitySpecificationByValueException">
        <annotation>
            <documentation>

                This is the Exception for the setEntitySpecificationByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>

                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
            </choice>
        </complexType>
    </element>
    <element name="setEntitySpecificationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntitySpecificationsByValues Operation
                This operation is MANDATORY
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setEntitySpecificationsByValuesResponse">

        <annotation>
            <documentation>
                This is the Response for the setEntitySpecificationsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>

    </element>
    <element name="setEntitySpecificationsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the setEntitySpecificationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
            </choice>
        </complexType>
    </element>

    <element name="trySetEntitySpecificationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the trySetEntitySpecificationsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />

                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="trySetEntitySpecificationsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the trySetEntitySpecificationsByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKeyResults" type="cbecore-v1-5:ArrayOfEntitySpecificationKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetEntitySpecificationsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the trySetEntitySpecificationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="removeEntitySpecificationByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the removeEntitySpecificationByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="entitySpecificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitySpecificationByKeyResponse">
        <annotation>
            <documentation>

                This is the Response for the removeEntitySpecificationByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitySpecificationByKeyException">

        <annotation>
            <documentation>
                This is the Exception for the removeEntitySpecificationByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeEntitySpecificationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the removeEntitySpecificationsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKeys" type="cbecore-v1-5:ArrayOfEntitySpecificationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeEntitySpecificationsByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the removeEntitySpecificationsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="removeEntitySpecificationsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the removeEntitySpecificationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeEntitySpecificationsByTemplateRequest">
        <annotation>

            <documentation>
                This is the Request for the removeEntitySpecificationsByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="template" type="cbecore-v1-5:EntitySpecificationValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="removeEntitySpecificationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the removeEntitySpecificationsByTemplate Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

            </sequence>
        </complexType>
    </element>
    <element name="removeEntitySpecificationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the removeEntitySpecificationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="tryRemoveEntitySpecificationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryRemoveEntitySpecificationsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="entitySpecificationKeys" type="cbecore-v1-5:ArrayOfEntitySpecificationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveEntitySpecificationsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the tryRemoveEntitySpecificationsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="entitySpecificationKeyResults" type="cbecore-v1-5:ArrayOfEntitySpecificationKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveEntitySpecificationsByKeysException">
        <annotation>

            <documentation>
                This is the Exception for the tryRemoveEntitySpecificationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="tryRemoveEntitySpecificationsByTemplateRequest">
        <annotation>
            <documentation>
                This is the Request for the tryRemoveEntitySpecificationsByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:EntitySpecificationValue"  />
                    <element name="failuresOnly" type="boolean"/>
                </sequence>
                </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="tryRemoveEntitySpecificationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the tryRemoveEntitySpecificationsByTemplate Operation
            </documentation>
        </annotation>
        <complexType>

            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfEntitySpecificationKeyResult"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="tryRemoveEntitySpecificationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveEntitySpecificationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <!-- Tigerstripe : End of Managed Entity Operations for EntitySpecification  -->
    <!-- Tigerstripe : Managed Entity Operations for Association ( NB These may be overridden in session)  -->
    <element name="createAssociationByValueRequest">
        <annotation>

            <documentation>
                This is the Request for the createAssociationByValue Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="associationValue" type="cbecore-v1-5:AssociationValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="createAssociationByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createAssociationByValue Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="associationKey" type="cbecore-v1-5:AssociationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAssociationByValueException">
        <annotation>
            <documentation>
                This is the Exception for the createAssociationByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>

        </complexType>
    </element>
    <element name="createAssociationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createAssociationsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="associationValues" type="cbecore-v1-5:ArrayOfAssociationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAssociationsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createAssociationsByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationKeys" type="cbecore-v1-5:ArrayOfAssociationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAssociationsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the createAssociationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryCreateAssociationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the tryCreateAssociationsByValues Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationValues" type="cbecore-v1-5:ArrayOfAssociationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateAssociationsByValuesResponse">
        <annotation>

            <documentation>
                This is the Response for the tryCreateAssociationsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="associationKeyResults" type="cbecore-v1-5:ArrayOfAssociationKeyResult"  />
            </sequence>
        </complexType>

    </element>
    <element name="tryCreateAssociationsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the tryCreateAssociationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="getAssociationByKeyRequest">
        <annotation>
            <documentation>

                This is the Request for the getAssociationByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="associationKey" type="cbecore-v1-5:AssociationKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>

    </element>
    <element name="getAssociationByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the getAssociationByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="associationValue" type="cbecore-v1-5:AssociationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getAssociationByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the getAssociationByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="getAssociationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the getAssociationsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="associationKeys" type="cbecore-v1-5:ArrayOfAssociationKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getAssociationsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the getAssociationsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationValues" type="cbecore-v1-5:ArrayOfAssociationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getAssociationsByKeysException">
        <annotation>

            <documentation>
                This is the Exception for the getAssociationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="getAssociationsByTemplateRequest">
        <annotation>
            <documentation>
                This is the Request for the getAssociationsByTemplate Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:AssociationValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>

            </complexContent>
        </complexType>
    </element>
    <element name="getAssociationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the getAssociationsByTemplate Operation
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfAssociationValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>

    </element>
    <element name="getAssociationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the getAssociationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="getAssociationsByTemplatesRequest">
        <annotation>

            <documentation>
                This is the Request for the getAssociationsByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="cbecore-v1-5:ArrayOfAssociationValue"  />

                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getAssociationsByTemplatesResponse">
        <annotation>
            <documentation>

                This is the Response for the getAssociationsByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfAssociationValue"/>
                </sequence>

                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getAssociationsByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the getAssociationsByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="setAssociationByValueRequest">
        <annotation>
            <documentation>
                This is the Request for the setAssociationByValue Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="associationValue" type="cbecore-v1-5:AssociationValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setAssociationByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the setAssociationByValue Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setAssociationByValueException">
        <annotation>
            <documentation>

                This is the Exception for the setAssociationByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>

                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="setAssociationsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the setAssociationsByValues Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationValues" type="cbecore-v1-5:ArrayOfAssociationValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setAssociationsByValuesResponse">

        <annotation>
            <documentation>
                This is the Response for the setAssociationsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>

    </element>
    <element name="setAssociationsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the setAssociationsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="removeAssociationByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the removeAssociationByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="associationKey" type="cbecore-v1-5:AssociationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the removeAssociationByKey Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationByKeyException">
        <annotation>
            <documentation>

                This is the Exception for the removeAssociationByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeAssociationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the removeAssociationsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationKeys" type="cbecore-v1-5:ArrayOfAssociationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationsByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the removeAssociationsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="removeAssociationsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the removeAssociationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeAssociationsByTemplateRequest">
        <annotation>

            <documentation>
                This is the Request for the removeAssociationsByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="template" type="cbecore-v1-5:AssociationValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="removeAssociationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the removeAssociationsByTemplate Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the removeAssociationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="removeAssociationsByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the removeAssociationsByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="templates" type="cbecore-v1-5:ArrayOfAssociationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationsByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the removeAssociationsByTemplates Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeAssociationsByTemplatesException">
        <annotation>
            <documentation>

                This is the Exception for the removeAssociationsByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveAssociationsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryRemoveAssociationsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="associationKeys" type="cbecore-v1-5:ArrayOfAssociationKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveAssociationsByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the tryRemoveAssociationsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="associationKeyResults" type="cbecore-v1-5:ArrayOfAssociationKeyResult"  />
            </sequence>
        </complexType>

    </element>
    <element name="tryRemoveAssociationsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveAssociationsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveAssociationsByTemplateRequest">
        <annotation>
            <documentation>

                This is the Request for the tryRemoveAssociationsByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbecore-v1-5:AssociationValue"  />
                    <element name="failuresOnly" type="boolean"/>

                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="tryRemoveAssociationsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the tryRemoveAssociationsByTemplate Operation
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="cbecore-v1-5:ArrayOfAssociationKeyResult"/>
                </sequence>
                </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="tryRemoveAssociationsByTemplateException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveAssociationsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <!-- Tigerstripe : End of Managed Entity Operations for Association  -->
    <!-- Tigerstripe : Operations on the interface JVTInventorySession =================================== -->

   <element name="getEntitySpecificationTypesRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitySpecificationTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>

        </complexType>
    </element>
    <element name="getEntitySpecificationTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitySpecificationTypes Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="return" type="co-v1-5:ArrayOfString"  />
                </sequence>
        </complexType>
    </element>
    <element name="getEntitySpecificationTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitySpecificationTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="makeEntityValueFromSpecificationRequest">
        <annotation>

            <documentation>
                This is the Request for the makeEntityValueFromSpecification Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="specificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
                </sequence>
        </complexType>

    </element>
    <element name="makeEntityValueFromSpecificationResponse">
        <annotation>
            <documentation>
                This is the Response for the makeEntityValueFromSpecification Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                    <element name="returnValues" type="cbecore-v1-5:EntityValue"  />
                </sequence>
        </complexType>
    </element>
    <element name="makeEntityValueFromSpecificationException">
        <annotation>
            <documentation>
                This is the Exception for the makeEntityValueFromSpecification Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>

    </element>
  
   <element name="validateEntityValuesByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the validateEntityValuesByKeys Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                    <element name="keys" type="cbecore-v1-5:ArrayOfEntityKey"  />
                </sequence>
        </complexType>
    </element>
    <element name="validateEntityValuesByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the validateEntityValuesByKeys Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="returnKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
                </sequence>
        </complexType>
    </element>
    <element name="validateEntityValuesByKeysException">
        <annotation>

            <documentation>
                This is the Exception for the validateEntityValuesByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

            </choice>
        </complexType>
    </element>
  
   <element name="getAllAssociationsRelatedToEntityRequest">
        <annotation>
            <documentation>
                This is the Request for the getAllAssociationsRelatedToEntity Operation
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="assocTypes" type="co-v1-5:ArrayOfString"  />
                    <element name="key" type="cbecore-v1-5:EntityKey"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString"  />
                </sequence>
            </extension>

            </complexContent>
        </complexType>
    </element>
    <element name="getAllAssociationsRelatedToEntityResponse">
        <annotation>
            <documentation>
                This is the Response for the getAllAssociationsRelatedToEntity Operation
            </documentation>
        </annotation>

        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="returnValues" type="cbecore-v1-5:ArrayOfAssociationValue"  />
                </sequence>
            </extension>
            </complexContent>
        </complexType>

    </element>
    <element name="getAllAssociationsRelatedToEntityException">
        <annotation>
            <documentation>
                This is the Exception for the getAllAssociationsRelatedToEntity Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getInventoryUpdateProcedureTypesRequest">
        <annotation>

            <documentation>
                This is the Request for the getInventoryUpdateProcedureTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getInventoryUpdateProcedureTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getInventoryUpdateProcedureTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="co-v1-5:ArrayOfString"  />

                </sequence>
        </complexType>
    </element>
    <element name="getInventoryUpdateProcedureTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getInventoryUpdateProcedureTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="validateEntityValuesByTemplatesRequest">
        <annotation>
            <documentation>

                This is the Request for the validateEntityValuesByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="cbecore-v1-5:ArrayOfEntityValue"  />
                </sequence>

            </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="validateEntityValuesByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the validateEntityValuesByTemplates Operation
            </documentation>

        </annotation>
        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="returnKeyResults" type="cbecore-v1-5:ArrayOfEntityKeyResult"  />
                </sequence>
            </extension>
            </complexContent>

        </complexType>
    </element>
    <element name="validateEntityValuesByTemplatesException">
        <annotation>
            <documentation>
                This is the Exception for the validateEntityValuesByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getAssociationTypesRequest">
        <annotation>

            <documentation>
                This is the Request for the getAssociationTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getAssociationTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getAssociationTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="co-v1-5:ArrayOfString"  />

                </sequence>
        </complexType>
    </element>
    <element name="getAssociationTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getAssociationTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="createEntityFromSpecificationRequest">
        <annotation>

            <documentation>
                This is the Request for the createEntityFromSpecification Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="specificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
                    <element name="value" type="cbecore-v1-5:EntityValue"  />
                </sequence>

        </complexType>
    </element>
    <element name="createEntityFromSpecificationResponse">
        <annotation>
            <documentation>
                This is the Response for the createEntityFromSpecification Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="returnKeys" type="cbecore-v1-5:EntityKey"  />
                </sequence>
        </complexType>
    </element>
    <element name="createEntityFromSpecificationException">
        <annotation>
            <documentation>
                This is the Exception for the createEntityFromSpecification Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="specificationViolationException" type="inv-v1-2:SpecificationViolationException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>

    </element>
  
   <element name="checkEntityComplianceToSpecificationRequest">
        <annotation>
            <documentation>
                This is the Request for the checkEntityComplianceToSpecification Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                    <element name="specificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
                    <element name="entityKey" type="cbecore-v1-5:EntityKey"  />
                </sequence>
        </complexType>
    </element>
    <element name="checkEntityComplianceToSpecificationResponse">
        <annotation>
            <documentation>
                This is the Response for the checkEntityComplianceToSpecification Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="boolean"  />
                </sequence>
        </complexType>
    </element>
    <element name="checkEntityComplianceToSpecificationException">
        <annotation>

            <documentation>
                This is the Exception for the checkEntityComplianceToSpecification Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

            </choice>
        </complexType>
    </element>
  
   <element name="exportInventoryDataRequest">
        <annotation>
            <documentation>
                This is the Request for the exportInventoryData Operation
            </documentation>
        </annotation>

        <complexType>
                <sequence>
                    <element name="scope" type="inv-v1-2:ReportScope"  />
                    <element name="format" type="cbereport-v1-5:ReportInfo"  />
                </sequence>
        </complexType>
    </element>
    <element name="exportInventoryDataResponse">
        <annotation>

            <documentation>
                This is the Response for the exportInventoryData Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="cbereport-v1-5:ReportData"  />
                </sequence>
        </complexType>

    </element>
    <element name="exportInventoryDataException">
        <annotation>
            <documentation>
                This is the Exception for the exportInventoryData Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                
                <element name="reportScopeException" type="inv-v1-2:ReportScopeException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getEntityTypesRequest">
        <annotation>

            <documentation>
                This is the Request for the getEntityTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getEntityTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntityTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="co-v1-5:ArrayOfString"  />

                </sequence>
        </complexType>
    </element>
    <element name="getEntityTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getEntityTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="importInventoryDataRequest">
        <annotation>
            <documentation>

                This is the Request for the importInventoryData Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="importedData" type="cbereport-v1-5:ReportData"  />
                    <element name="reconciliation" type="inv-v1-2:ReconciliationPolicy"  />
                    <element name="generatedReportFormat" type="cbereport-v1-5:ReportInfo"  />
                </sequence>

        </complexType>
    </element>
    <element name="importInventoryDataResponse">
        <annotation>
            <documentation>
                This is the Response for the importInventoryData Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="return" type="cbereport-v1-5:ReportInfo"  />
                </sequence>
        </complexType>
    </element>
    <element name="importInventoryDataException">
        <annotation>
            <documentation>
                This is the Exception for the importInventoryData Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="semanticRuleViolationException" type="inv-v1-2:SemanticRuleViolationException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                
                <element name="reportScopeException" type="inv-v1-2:ReportScopeException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>

        </complexType>
    </element>
  
   <element name="getValidationModeRequest">
        <annotation>
            <documentation>
                This is the Request for the getValidationMode Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                </sequence>
        </complexType>
    </element>
    <element name="getValidationModeResponse">
        <annotation>
            <documentation>
                This is the Response for the getValidationMode Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="int"  />
                </sequence>
        </complexType>
    </element>
    <element name="getValidationModeException">
        <annotation>

            <documentation>
                This is the Exception for the getValidationMode Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>

    </element>
  
   <element name="makeInventoryUpdateProcedureValueRequest">
        <annotation>
            <documentation>
                This is the Request for the makeInventoryUpdateProcedureValue Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                    <element name="type" type="string"  />
                </sequence>
        </complexType>
    </element>
    <element name="makeInventoryUpdateProcedureValueResponse">
        <annotation>
            <documentation>
                This is the Response for the makeInventoryUpdateProcedureValue Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="inv-v1-2:InventoryUpdateProcedureValue"  />
                </sequence>
        </complexType>
    </element>
    <element name="makeInventoryUpdateProcedureValueException">
        <annotation>

            <documentation>
                This is the Exception for the makeInventoryUpdateProcedureValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>

        </complexType>
    </element>
  
   <element name="setEntityToDefaultRequest">
        <annotation>
            <documentation>
                This is the Request for the setEntityToDefault Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="entityKey" type="cbecore-v1-5:EntityKey"  />
                    <element name="specificationKey" type="cbecore-v1-5:EntitySpecificationKey"  />
                    <element name="allAttributes" type="boolean"  />
                </sequence>
        </complexType>
    </element>
    <element name="setEntityToDefaultResponse">
        <annotation>

            <documentation>
                This is the Response for the setEntityToDefault Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="setEntityToDefaultException">
        <annotation>
            <documentation>
                This is the Exception for the setEntityToDefault Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="semanticRuleViolationException" type="inv-v1-2:SemanticRuleViolationException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="specificationViolationException" type="inv-v1-2:SpecificationViolationException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>

  
   <element name="getNamingModeRequest">
        <annotation>
            <documentation>
                This is the Request for the getNamingMode Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>

        </complexType>
    </element>
    <element name="getNamingModeResponse">
        <annotation>
            <documentation>
                This is the Response for the getNamingMode Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="return" type="int"  />
                </sequence>
        </complexType>
    </element>
    <element name="getNamingModeException">
        <annotation>
            <documentation>
                This is the Exception for the getNamingMode Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getAssociationRuleRequest">
        <annotation>

            <documentation>
                This is the Request for the getAssociationRule Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="associationType" type="string"  />
                </sequence>
        </complexType>

    </element>
    <element name="getAssociationRuleResponse">
        <annotation>
            <documentation>
                This is the Response for the getAssociationRule Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                    <element name="return" type="cbecore-v1-5:AssociationRule"  />
                </sequence>
        </complexType>
    </element>
    <element name="getAssociationRuleException">
        <annotation>
            <documentation>
                This is the Exception for the getAssociationRule Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

  
   <element name="getEntitySpecificationsByEntityTypeRequest">
        <annotation>
            <documentation>
                This is the Request for the getEntitySpecificationsByEntityType Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorRequest">

                <sequence>
                    <element name="entityType" type="string"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString"  />
                </sequence>
            </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByEntityTypeResponse">

        <annotation>
            <documentation>
                This is the Response for the getEntitySpecificationsByEntityType Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
            <extension base="co-v1-5:IteratorResponse">
                <sequence>

                    <element name="returnValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />
                </sequence>
            </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getEntitySpecificationsByEntityTypeException">
        <annotation>
            <documentation>

                This is the Exception for the getEntitySpecificationsByEntityType Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>

        </complexType>
    </element>
  
   <element name="setOverridingSpecificationConstraintsRequest">
        <annotation>
            <documentation>
                This is the Request for the setOverridingSpecificationConstraints Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="value" type="cbecore-v1-5:EntityValue"  />
                    <element name="resyncRequired" type="boolean"  />
                </sequence>
        </complexType>
    </element>
    <element name="setOverridingSpecificationConstraintsResponse">
        <annotation>
            <documentation>

                This is the Response for the setOverridingSpecificationConstraints Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>
    <element name="setOverridingSpecificationConstraintsException">

        <annotation>
            <documentation>
                This is the Exception for the setOverridingSpecificationConstraints Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="semanticRuleViolationException" type="inv-v1-2:SemanticRuleViolationException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getEntitySpecificationTypeRequest">

        <annotation>
            <documentation>
                This is the Request for the getEntitySpecificationType Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="entityType" type="string"  />
                </sequence>

        </complexType>
    </element>
    <element name="getEntitySpecificationTypeResponse">
        <annotation>
            <documentation>
                This is the Response for the getEntitySpecificationType Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="return" type="string"  />
                </sequence>
        </complexType>
    </element>
    <element name="getEntitySpecificationTypeException">
        <annotation>
            <documentation>
                This is the Exception for the getEntitySpecificationType Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getSpecificationDefinedEntityTypesRequest">
        <annotation>

            <documentation>
                This is the Request for the getSpecificationDefinedEntityTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getSpecificationDefinedEntityTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getSpecificationDefinedEntityTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="return" type="co-v1-5:ArrayOfString"  />

                </sequence>
        </complexType>
    </element>
    <element name="getSpecificationDefinedEntityTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getSpecificationDefinedEntityTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="validateEntityValueByKeyRequest">
        <annotation>
            <documentation>

                This is the Request for the validateEntityValueByKey Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="key" type="cbecore-v1-5:EntityKey"  />
                </sequence>
        </complexType>
    </element>

    <element name="validateEntityValueByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the validateEntityValueByKey Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>

        </complexType>
    </element>
    <element name="validateEntityValueByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the validateEntityValueByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
            </choice>
        </complexType>
    </element>

  
    <!-- Tigerstripe : End of Operations on the interface JVTInventorySession =================================== -->

</schema>
