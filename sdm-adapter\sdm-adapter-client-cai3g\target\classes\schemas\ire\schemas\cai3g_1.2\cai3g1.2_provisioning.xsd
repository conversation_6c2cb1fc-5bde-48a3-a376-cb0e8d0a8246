<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<!--	<xs:element name="Create">-->
	<!--		<xs:complexType>-->
	<!--			<xs:sequence>-->
	<!--				<xs:element name="MOType" type="MoType" />-->
	<!--				<xs:element name="MOId" type="AnyMOIdType" minOccurs="0" />-->
	<!--				<xs:element name="MOAttributes" minOccurs="0">-->
	<!--					<xs:complexType>-->
	<!--						<xs:sequence>-->
	<!--							<xs:element ref="CreateMODefinition" />-->
	<!--						</xs:sequence>-->
	<!--					</xs:complexType>-->
	<!--				</xs:element>-->
	<!--				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />-->
	<!--			</xs:sequence>-->
	<!--		</xs:complexType>-->
	<!--	</xs:element>-->
    
   
	<!-- <xs:element name="CreateResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MOId" type="AnyMOIdType" />
				<xs:element name="MOAttributes" type="ResponseMOAttributesType" minOccurs="0" />
				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element> -->
  
	
    <!--	<xs:element name="Get">-->
	<!--		<xs:complexType>-->
	<!--			<xs:sequence>-->
	<!--				<xs:element name="MOType" type="MoType" />-->
	<!--				<xs:element name="MOId" type="AnyMOIdType" minOccurs="0" />-->
	<!--				<xs:element name="MOAttributes" minOccurs="0">-->
	<!--					<xs:complexType>-->
	<!--						<xs:sequence>-->
	<!--							<xs:element ref="GetRequestMODefinition" />-->
	<!--						</xs:sequence>-->
	<!--					</xs:complexType>-->
	<!--				</xs:element>-->
	<!--				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />-->
	<!--			</xs:sequence>-->
	<!--		</xs:complexType>-->
	<!--	</xs:element>-->
	
	<!--	<xs:element name="GetResponse">-->
	<!--		<xs:complexType>-->
	<!--			<xs:sequence>-->
	<!--				<xs:element name="MOId" type="AnyMOIdType" minOccurs="0" maxOccurs="unbounded" />-->
	<!--				<xs:element name="MOAttributes" type="ResponseMOAttributesType" minOccurs="0" />-->
	<!--				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />-->
	<!--			</xs:sequence>-->
	<!--		</xs:complexType>-->
	<!--	</xs:element>-->
	<!--	<xs:element name="Set">-->
	<!--		<xs:complexType>-->
	<!--			<xs:sequence>-->
	<!--				<xs:element name="MOType" type="MoType" />-->
	<!--				<xs:element name="MOId" type="AnyMOIdType" />-->
	<!--				<xs:element name="MOAttributes">-->
	<!--					<xs:complexType>-->
	<!--						<xs:sequence>-->
	<!--							<xs:element ref="SetMODefinition" />-->
	<!--						</xs:sequence>-->
	<!--					</xs:complexType>-->
	<!--				</xs:element>-->
	<!--				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />-->
	<!--			</xs:sequence>-->
	<!--		</xs:complexType>-->
	<!--	</xs:element>-->
	<!--  <xs:element name="SetResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MOAttributes" type="ResponseMOAttributesType" minOccurs="0" />
				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>  -->
	<!--	<xs:element name="Delete">-->
	<!--		<xs:complexType>-->
	<!--			<xs:sequence>-->
	<!--				<xs:element name="MOType" type="MoType" />-->
	<!--				<xs:element name="MOId" type="AnyMOIdType" />-->
	<!--				<xs:element name="MOAttributes" minOccurs="0">-->
	<!--					<xs:complexType>-->
	<!--						<xs:sequence>-->
	<!--							<xs:element ref="DeleteRequestMODefinition" />-->
	<!--						</xs:sequence>-->
	<!--					</xs:complexType>-->
	<!--				</xs:element>-->
	<!--				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />-->
	<!--			</xs:sequence>-->
	<!--		</xs:complexType>-->
	<!--	</xs:element>-->
    
	<!-- <xs:element name="DeleteResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MOId" type="AnyMOIdType" minOccurs="0" />
				<xs:element name="MOAttributes" type="ResponseMOAttributesType" minOccurs="0" />
				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element> -->
   
	<xs:element name="Search">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MOType" type="MoType" />
				<xs:element name="filters" type="SearchFiltersType" />
				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="SearchResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MOId" type="AnyMOIdType" minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="extension" type="AnySequenceType" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Login">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="userId" type="xs:string" />
				<xs:element name="pwd" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="LoginResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="sessionId" type="SessionIdType" />
				<xs:element name="baseSequenceId" type="xs:unsignedLong" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Logout">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="sessionId" type="SessionIdType" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="LogoutResponse">
		<xs:complexType />
	</xs:element>
	<xs:element name="Subscribe">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="managerRef" type="xs:anyURI" />
				<xs:element name="filters" type="NotificationFiltersType" />
			</xs:sequence>
		</xs:complexType>
		<xs:unique name="OperationUnique">
			<xs:selector xpath="filters/filter/operation" />
			<xs:field xpath="." />
		</xs:unique>
	</xs:element>
	<xs:element name="SubscribeResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="subscriptionId" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Unsubscribe">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="managerRef" type="xs:anyURI" />
				<xs:element name="subscriptionId" type="xs:string" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UnsubscribeResponse">
		<xs:complexType />
	</xs:element>
	<xs:element name="Notify">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="notificationHeader" type="NotificationHeaderType" />
				<xs:element name="correlatedNotifications" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="additionalText" type="xs:string" minOccurs="0" />
				<xs:element name="sourceIndicator" type="xs:anyURI" minOccurs="0" />
				<xs:element name="notificationData">
					<xs:complexType>
						<xs:sequence>
							<xs:any namespace="##any" processContents="lax" maxOccurs="unbounded" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="NotifyResponse">
		<xs:complexType />
	</xs:element>
	<xs:complexType name="ResponseMOAttributesType">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	

	<xs:complexType name="AbstractCreateAttributeType" abstract="true"/>
	<xs:complexType name="AbstractGetRequestAttributeType" abstract="true"/>
	<xs:complexType name="AbstractDeleteRequestAttributeType" abstract="true"/>
	<xs:complexType name="AbstractSetAttributeType" abstract="true"/>
	<xs:complexType name="AbstractGetAttributeType" abstract="true"/>
	<xs:element name="CreateMODefinition" type="AbstractCreateAttributeType" abstract="true"/>
	<xs:element name="GetRequestMODefinition" type="AbstractGetRequestAttributeType" abstract="true"/>
	<xs:element name="DeleteRequestMODefinition" type="AbstractDeleteRequestAttributeType" abstract="true"/>
	<xs:element name="SetMODefinition" type="AbstractSetAttributeType" abstract="true"/>
	<xs:element name="GetMODefinition" type="AbstractGetAttributeType" abstract="true"/>	

	
	
	<xs:complexType name="AnyMOIdType">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AnySequenceType">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="MoType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z][_A-Za-z0-9]*@.*" />
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="SessionId" type="SessionIdType" />
	<xs:element name="TransactionId" type="xs:unsignedLong" />
	<xs:element name="SequenceId" type="xs:unsignedLong" />
	<xs:simpleType name="SessionIdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[\d\w]{1,}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="SearchFiltersType">
		<xs:sequence>
			<xs:element name="filter" type="SearchFilterType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SearchFilterType">
		<xs:sequence>
			<xs:element name="MOAttributes" type="xs:string" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NotificationHeaderType">
		<xs:sequence>
			<xs:element name="cai3gUser" type="xs:string" />
			<xs:element name="MOType" type="MoType" />
			<xs:element name="MOId" type="AnyMOIdType" />
			<xs:element name="notificationId" type="xs:string" minOccurs="0" />
			<xs:element name="eventTime" type="xs:dateTime" />
			<xs:element name="notificationActor" type="xs:anyURI" minOccurs="0" />
			<xs:element name="operation" type="NotificationOperationType" />
			<xs:element name="subscriptionId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NotificationFilterType">
		<xs:sequence>
			<xs:element name="cai3gUser" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="MOType" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="operation" type="NotificationOperationType" minOccurs="0" maxOccurs="3" />
			<xs:element name="MOId" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="MOAttributes" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<!-- How to make sure there must exist one element -->
	</xs:complexType>
	<xs:complexType name="NotificationFiltersType">
		<xs:sequence>
			<xs:element name="filter" type="NotificationFilterType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="NotificationOperationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Create" />
			<xs:enumeration value="Delete" />
			<xs:enumeration value="Set" />
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="Cai3gFault" type="Cai3gFaultType">
	</xs:element>
	<xs:complexType name="Cai3gFaultType">
		<xs:sequence>
			<xs:element name="faultcode" type="xs:integer" />
			<xs:element name="faultreason">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="reasonText" type="xs:string" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="faultrole" type="xs:string" />
			<xs:element name="details" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HeaderFaultType">
		<xs:sequence>
			<xs:element name="faultactor" type="xs:string" />
			<xs:element name="description" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SessionIdFault">
	<xs:complexType>
		<xs:complexContent>
			<xs:extension base="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultcode">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Invalid SessionId" />
								<xs:enumeration value="Session Timeout" />
								<xs:enumeration value="SessionId Syntax Error" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	</xs:element>
	<xs:element name="SequenceIdFault">
	<xs:complexType>
		<xs:complexContent>
			<xs:extension base="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultcode">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Invalid SequenceId" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	</xs:element>
	<xs:element name="TransactionIdFault">
	<xs:complexType>
		<xs:complexContent>
			<xs:extension base="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultcode">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Invalid TransactionId" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	</xs:element>
</xs:schema>