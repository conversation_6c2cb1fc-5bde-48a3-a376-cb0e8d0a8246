 <!-- 
Build_Label: REL500 
QualityCenter_MR#: 00348796 
Build_Date: 2016-12-07-14:56:28 
-->
<xsd:schema xmlns:enterprise_ar_payment_xsd="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types" xmlns:enterprise_payment_xsd="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v2_1">
	<xsd:annotation>
		<xsd:documentation>Payment Service Types.</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" schemaLocation="enterprise_payment_types_v1_0.xsd"/>
	<!-- Types -->
	<xsd:simpleType name="EntityIdType">
		<xsd:annotation>
			<xsd:documentation>Definition of Entity Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:long"/>
	</xsd:simpleType>
	<xsd:simpleType name="EntityPeriodKeyType">
		<xsd:annotation>
			<xsd:documentation>Definition of Entity Period Key.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int"/>
	</xsd:simpleType>
	<xsd:simpleType name="PartitionIdType">
		<xsd:annotation>
			<xsd:documentation>Definition of Partition Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int"/>
	</xsd:simpleType>
	<xsd:simpleType name="PaymentTypeType">
		<xsd:annotation>
			<xsd:documentation>Definition of PaymentType.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PeriodKeyType">
		<xsd:annotation>
			<xsd:documentation>Definition of Period Key.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int"/>
	</xsd:simpleType>
	<xsd:simpleType name="TransactionIdType">
		<xsd:annotation>
			<xsd:documentation>Definition of Transaction Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:long">
			<xsd:totalDigits value="12"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Enumerations -->
	<xsd:simpleType name="PaymentMethodType">
		<xsd:annotation>
			<xsd:documentation>Populated with values from Payment Methods reference table.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="CC">
				<xsd:annotation>
					<xsd:documentation>Credit Card.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DD">
				<xsd:annotation>
					<xsd:documentation>Direct Debit (ACH).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Structures -->
	<xsd:complexType name="AddressType">
		<xsd:annotation>
			<xsd:documentation>US postal address.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="addressLine1">
				<xsd:annotation>
					<xsd:documentation>Street address or PO Box.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="200"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="city">
				<xsd:annotation>
					<xsd:documentation>City.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="30"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="state" type="enterprise_common_xsd:StateType">
				<xsd:annotation>
					<xsd:documentation>Two letter US state code.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="zipcode" type="enterprise_common_xsd:ZipCodeType">
				<xsd:annotation>
					<xsd:documentation>5 or 9 digit zip code - 5 digit US zip code required at a minimum.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AddBankPaymentType">
		<xsd:annotation>
			<xsd:documentation>Contains information needed to add a bank account payment</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="enterprise_ar_payment_xsd:BankPaymentType">
				<xsd:sequence>
					<xsd:element name="payMeansId" type="enterprise_payment_xsd:PayMeansIdType" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Pay Means Id. Mandatory when payment submitted through saved pay means.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="BankPaymentType">
		<xsd:annotation>
			<xsd:documentation>Contains information of a generic bank account
				payment</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="accountNumber" type="enterprise_payment_xsd:BankAccountNumberType">
				<xsd:annotation>
					<xsd:documentation>Bank account number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="routingNumber" type="enterprise_payment_xsd:RoutingNumberType">
				<xsd:annotation>
					<xsd:documentation>Routing number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="accountType" type="enterprise_payment_xsd:BankAccountTypeType">
				<xsd:annotation>
					<xsd:documentation>Bank account type.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CardPaymentType">
		<xsd:annotation>
			<xsd:documentation>Contains information of a generic credit/debit card payment.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="cardNumber" type="enterprise_payment_xsd:CreditCardNumberType">
				<xsd:annotation>
					<xsd:documentation>The credit/debit card number that was used in
						the transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="cvv" type="enterprise_payment_xsd:CVVType">
				<xsd:annotation>
					<xsd:documentation>The CVV number of the credit/debit card used.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="expirationMonth" type="enterprise_payment_xsd:CardExpirationMonthType">
				<xsd:annotation>
					<xsd:documentation>The expiration month of the credit/debit card
						that was used.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="expirationYear" type="enterprise_payment_xsd:CardExpirationYearType">
				<xsd:annotation>
					<xsd:documentation>The expiration year of the credit/debit card
						that was used.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="cardHolderName" type="enterprise_payment_xsd:CardHolderNameType">
				<xsd:annotation>
					<xsd:documentation>Card holder's name</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="creditCardType" type="enterprise_payment_xsd:CreditCardTypeType">
				<xsd:annotation>
					<xsd:documentation>The type of the credit/debit card that was used
						in the transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="billingAddress" type="enterprise_ar_payment_xsd:AddressType">
				<xsd:annotation>
					<xsd:documentation>Billing address of the card holder.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentTransactionDetailsResultType">
		<xsd:annotation>
			<xsd:documentation>Payment transaction details results type.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="transactionCreateDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>System date when the transaction was created.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionPostDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>Logical date of the transaction.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionId" type="enterprise_ar_payment_xsd:TransactionIdType">
				<xsd:annotation>
					<xsd:documentation>Transaction Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionAmount" type="enterprise_common_xsd:AmountType">
				<xsd:annotation>
					<xsd:documentation>Transaction Amount.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="declineReasonCode" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Decline reason code. Populated if there is refund or backout.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="declineReasonDescription" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Decline reason description. Populated if there is refund or backout.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="reasonCodeDescription" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Reason code description. Populated if there is a credit or credit reversal.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="status" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Status for the transaction. Will be populated only for the below list of transactions.
							Bundle Credit;
							Release Or Cancel Deposit;
							Deposit Details;
							Payment Refund Reversal;
							Payment Refund;
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="chargeDescription" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Charge description. Will be populated if it is a charge.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentMethodDescription" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Payment method description.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="bankAccountNumber" type="enterprise_payment_xsd:BankAccountNumberType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Bank account number. Populated if is a DD payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="bankCode" type="enterprise_payment_xsd:BankCodeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Bank code. Populated if is a DD payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="bankAccountType" type="enterprise_payment_xsd:BankAccountTypeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Bank account type. Populated if is a DD payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="creditCardNumber" type="enterprise_payment_xsd:CreditCardNumberType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Credit Card Number(Last 4 digits). Populated if is a CC payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ccExpiryDate" type="enterprise_payment_xsd:DateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Credit card expiration date. Populated if is a CC payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="creditCardType" type="enterprise_payment_xsd:CreditCardTypeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Credit card type. Populated if is a CC payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="checkNo" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Check number. Populated if is a CQ payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentType" type="enterprise_ar_payment_xsd:PaymentTypeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Payment type. Possible values are:
						I - Immediate
						S - Scheduled
						A - Auto
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="reverseIdInfo" type="xsd:long" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Related Reverse transaction ID. Populated if payment was reversed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="reversedActivities" type="enterprise_ar_payment_xsd:ReversedActivitiesType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Reversed activities for the related transactions. Populated if payment was reversed.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentTransactionHistoryResultType">
		<xsd:annotation>
			<xsd:documentation>Payment transaction history result type.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="paymentTransaction" type="enterprise_ar_payment_xsd:PaymentTransactionType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Array of payment transactions.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentTransactionType">
		<xsd:annotation>
			<xsd:documentation>Defintion of PaymentTransaction element.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="activityDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>Logical date when activity occurred.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="amount" type="enterprise_common_xsd:AmountType">
				<xsd:annotation>
					<xsd:documentation>Amount of the activity.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionId" type="enterprise_ar_payment_xsd:TransactionIdType">
				<xsd:annotation>
					<xsd:documentation>Transaction Id for the transaction.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityId" type="enterprise_ar_payment_xsd:EntityIdType">
				<xsd:annotation>
					<xsd:documentation>Entity ID is like Charge ID or Credit ID or
						Payment ID etc for each transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="impactInd" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Indicates whether the transaction is a credit or a debit.
						Valid values are:
						C – Credit or D – Debit.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="partitionId" type="enterprise_ar_payment_xsd:PartitionIdType">
				<xsd:annotation>
					<xsd:documentation>Partition Id for the account.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="periodKey" type="enterprise_ar_payment_xsd:PeriodKeyType">
				<xsd:annotation>
					<xsd:documentation>Period key for the transaction.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityPeriodKey" type="enterprise_ar_payment_xsd:EntityPeriodKeyType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Period key of the entity.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentInfoType">
		<xsd:annotation>
			<xsd:documentation>Payment info type.</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="bankPayment" type="enterprise_ar_payment_xsd:AddBankPaymentType">
				<xsd:annotation>
					<xsd:documentation>Contains all the data for a bank payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:choice>
				<xsd:element name="creditCardPayment" type="enterprise_ar_payment_xsd:CardPaymentType">
					<xsd:annotation>
						<xsd:documentation>Contains all the data for a credit card payment.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="subscriptionId" type="enterprise_payment_xsd:SubscriptionIdType"/>
			</xsd:choice>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="ReversedActivitiesType">
		<xsd:annotation>
			<xsd:documentation>Reversed activities type.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="transactionId" type="enterprise_ar_payment_xsd:TransactionIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionType" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Type.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionAmount" type="enterprise_common_xsd:AmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction amount.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionDate" type="enterprise_payment_xsd:DateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Logical date when the activity occurred.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="partitionId" type="enterprise_ar_payment_xsd:PartitionIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Partition id for the account.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="periodKey" type="enterprise_ar_payment_xsd:PeriodKeyType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Period key.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="transactionTypeCode" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						Transaction type code. Possible values are :
						CHG - Charge
						PYMTD - Payment
						BCK - Backout
						CREDIT - Credit 
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityId" type="enterprise_ar_payment_xsd:EntityIdType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Entity Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityPeriodKey" type="enterprise_ar_payment_xsd:EntityPeriodKeyType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Entity period key.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="accountId" type="enterprise_common_xsd:AccountNumberType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Account Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaymentResultType">
		<xsd:annotation>
			<xsd:documentation>Type of the paymentResult element.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="entitySequenceNumber" type="enterprise_payment_xsd:EntitySequenceNumberType">
				<xsd:annotation>
					<xsd:documentation>Sequence number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="referenceNumber" type="enterprise_payment_xsd:ReferenceNumberType">
				<xsd:annotation>
					<xsd:documentation>The Reference Number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AccountIdOptionType">
		<xsd:annotation>
			<xsd:documentation>The option type of ID used to retrieve the Payment history. Financial Account Id, or Customer Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:choice minOccurs="1">
			<xsd:element name="faId" type="enterprise_common_xsd:FaIdType">
				<xsd:annotation>
					<xsd:documentation>Financial Account Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="customerId" type="enterprise_common_xsd:CustomerIdType">
				<xsd:annotation>
					<xsd:documentation>Customer Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:choice>
	</xsd:complexType>
	<!-- Exceptions -->
	<xsd:element name="PaymentException" type="enterprise_ar_payment_xsd:PaymentExceptionType">
		<xsd:annotation>
			<xsd:documentation>Service exception used for Payment Service.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="PaymentExceptionType">
		<xsd:complexContent>
			<xsd:extension base="enterprise_common_xsd:ServiceExceptionType">
				<xsd:annotation>
					<xsd:documentation>Payment exception extension based on USCC fault
						definition.</xsd:documentation>
				</xsd:annotation>
				<xsd:sequence/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
</xsd:schema>
