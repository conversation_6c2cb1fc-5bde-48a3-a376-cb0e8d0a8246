<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/"
	xmlns="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/"
	xmlns:ns="http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="createSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType" />
				<xs:element name="ki" type="kiType" />
				<xs:choice minOccurs="0">
					<xs:element name="a38">
						<xs:simpleType>
							<xs:restriction base="xs:unsignedByte">
								<xs:minInclusive value="0" />
								<xs:maxInclusive value="15" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
					<xs:group ref="fsetindAmfGroup" />
				</xs:choice>
				<xs:element name="adkey">
					<xs:simpleType>
						<xs:restriction base="xs:unsignedShort">
							<xs:minInclusive value="0" />
							<xs:maxInclusive value="511" />
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="a4ind" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="xs:unsignedByte">
							<xs:minInclusive value="0" />
							<xs:maxInclusive value="7" />
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="zoneid" type="zoneidType" minOccurs="0"/>				
				<xs:element name="rid" type="ridType" minOccurs="0" />				
			</xs:sequence>
			<xs:attribute name="imsi" type="xs:string" use="required" />
		</xs:complexType>
		<xs:key name="imsiKey_Create">
			<xs:selector xpath="./ns:imsi" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="imsiKeyRef_Create" refer="imsiKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>
	</xs:element>

	<xs:element name="setSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType" />
				<xs:element name="amf" type="amfType" minOccurs="0" />
				<xs:element name="fsetind" type="fsetindType" minOccurs="0" />
				<xs:element name="akaalgind" type="akaalgindType"
					minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="required" />
		</xs:complexType>
		<xs:key name="imsiKey_Set">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:key>
	</xs:element>

  <xs:element name="GetSubscription">
	<xs:complexType>
	  <xs:sequence>
		<xs:element name="imsi" type="imsiType" />
		<xs:element name="akatype" type="xs:string" minOccurs="0"/>
	  </xs:sequence>
	  <xs:attribute name="imsi" type="imsiType"/>
	</xs:complexType>
  </xs:element>

	<xs:element name="getResponseSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType" />
				<xs:element name="ki" type="kiType" />
				<xs:element name="adkey">
					<xs:simpleType>
						<xs:restriction base="xs:unsignedShort">
							<xs:minInclusive value="0" />
							<xs:maxInclusive value="511" />
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:choice>
					<xs:element name="a38">
						<xs:simpleType>
							<xs:restriction base="xs:unsignedByte">
								<xs:minInclusive value="0" />
								<xs:maxInclusive value="15" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
					<xs:element name="fsetind">
						<xs:simpleType>
							<xs:restriction base="xs:unsignedByte">
								<xs:minInclusive value="0" />
								<xs:maxInclusive value="15" />
							</xs:restriction>
						</xs:simpleType>
					</xs:element>
				</xs:choice>
				<xs:element name="a4ind">
					<xs:simpleType>
						<xs:restriction base="xs:unsignedByte">
							<xs:minInclusive value="0" />
							<xs:maxInclusive value="7" />
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="amf" type="amfType" minOccurs="0" />
				<xs:element name="akatype">
					<xs:simpleType>
						<xs:restriction base="xs:unsignedByte">
							<xs:minInclusive value="0" />
							<xs:maxInclusive value="1" />
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="akaalgind" type="akaalgindType" />
				<xs:element name="zoneid" type="zoneidType" minOccurs="0"/>				
				<xs:element name="rid" type="ridType" />
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="required" />
		</xs:complexType>
		<xs:key name="imsiKey_Get">
			<xs:selector xpath="./ns:imsi" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="imsiKeyRef_Get" refer="imsiKey_Get">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>
	</xs:element>

	<xs:group name="fsetindAmfGroup">
		<xs:sequence>
			<xs:element name="fsetind" type="fsetindType" />
			<xs:element name="amf" type="amfType" minOccurs="0" />
		</xs:sequence>
	</xs:group>

	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6" />
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="kiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9A-F]{32}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="amfType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="fsetindType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="31" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="akaalgindType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1" />
			<xs:enumeration value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ridType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="63" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="zoneidType">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="65535" />
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
