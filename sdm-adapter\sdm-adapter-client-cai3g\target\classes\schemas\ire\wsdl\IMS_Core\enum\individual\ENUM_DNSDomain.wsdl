<?xml version="1.0" encoding="UTF-8"?>
<!--2004/2/19 ewuwang Update in EMA32 for notification
	1)Replace the namespace part in soapAction with "CAI3G#"
	2)add soap:headerfault in every operaton
--><!--2004/3/31 ewuwang Update in EMA32
	1)Update all the soap:headerfault to cai3g:HeaderFault and corresponding "part"
--><!--2004/11/23 rdcdape Update in EMA4.0
	1) Add "Search" operation definition.
--><definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:up="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" xmlns:ma="http://schemas.ericsson.com/ema/UserProvisioning/" xmlns:pg="http://schemas.ericsson.com/pg/1.0" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema xmlns="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
			<xs:import namespace="http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" schemaLocation="../../../../schemas/IMS_Core/enum/individual/ENUM_DNSDomain.xsd" />
			<xs:import namespace="http://schemas.ericsson.com/pg/1.0" schemaLocation="../../../../schemas/IMS_Core/enum/individual/PGFault.xsd" />
			<xs:import namespace="http://schemas.ericsson.com/ema/UserProvisioning/" schemaLocation="../../../../schemas/IMS_Core/enum/individual/UserProvisioningFault.xsd" />
			<xs:element name="Set">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="DNSDomain@http://schemas.ericsson.com/ema/UserProvisioning/IPWorks/5.0/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="up:domainsId" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MOAttributes">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="up:setDNSDomain" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SetResponse">
				<xs:complexType>
					<xs:sequence>
					</xs:sequence>
				</xs:complexType>
			</xs:element>

			<xs:element name="Cai3gFault">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="faultcode" type="xs:integer" />
						<xs:element name="faultreason">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="reasonText" type="xs:string" maxOccurs="unbounded" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="faultrole" type="xs:string" />
						<xs:element name="details" minOccurs="0">
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element ref="ma:UserProvisioningFault"/>
										<xs:element ref="pg:PGFault"/>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>

			<xs:element name="SessionIdFault" type="SessionIdFaultType" />
			<xs:element name="SequenceIdFault" type="SequenceIdFaultType" />
			<xs:element name="TransactionIdFault" type="TransactionIdFaultType" />
			<xs:element name="SessionId" type="SessionIdType" />
			<xs:element name="TransactionId" type="xs:string" />
			<xs:element name="SequenceId" type="xs:unsignedLong" />
			<xs:simpleType name="SessionIdType">
				<xs:restriction base="xs:string">
					<xs:pattern value="[\d\w]{1,}" />
				</xs:restriction>
			</xs:simpleType>


			<xs:complexType name="HeaderFaultType">
				<xs:sequence>
					<xs:element name="faultactor" type="xs:string" />
					<xs:element name="description" type="xs:string" />
				</xs:sequence>
			</xs:complexType>
			<xs:complexType name="SessionIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid SessionId" />
										<xs:enumeration value="Session Timeout" />
										<xs:enumeration value="SessionId Syntax Error" />
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
			<xs:complexType name="SequenceIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid SequenceId" />
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
			<xs:complexType name="TransactionIdFaultType" final="restriction">
				<xs:complexContent>
					<xs:extension base="HeaderFaultType">
						<xs:sequence>
							<xs:element name="faultcode">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Invalid TransactionId" />
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:extension>
				</xs:complexContent>
			</xs:complexType>
		</xs:schema>
	</types>
	<message name="Cai3G12FaultException">
		<part name="fault" element="cai3g:Cai3gFault" />
	</message>
	<message name="Set">
		<part name="Set" element="cai3g:Set" />
		<part name="SessionId" element="cai3g:SessionId" />
		<part name="TransactionId" element="cai3g:TransactionId" />
		<part name="SequenceId" element="cai3g:SequenceId" />    
	</message>
	<message name="SetResponse">
		<part name="SetResponse" element="cai3g:SetResponse" />
		<part name="SessionId" element="cai3g:SessionId" />
		<part name="TransactionId" element="cai3g:TransactionId" />
		<part name="SequenceId" element="cai3g:SequenceId" />     
	</message>
	<message name="Cai3gHeaderFault">
		<part name="sessionIdFault" element="cai3g:SessionIdFault" />
		<part name="transactionIdFault" element="cai3g:TransactionIdFault" />
		<part name="sequenceIdFault" element="cai3g:SequenceIdFault" />
	</message>
	<portType name="EnumDnsDomain">
		<operation name="Set">
			<input message="cai3g:Set" />
			<output message="cai3g:SetResponse" />
			<fault name="Cai3G12FaultException" message="cai3g:Cai3G12FaultException" />
		</operation>
	</portType>
	<binding name="EnumDnsDomain" type="cai3g:EnumDnsDomain">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<operation name="Set">
			<soap:operation soapAction="CAI3G#Set" style="document" />
			<input>
				<soap:body use="literal" parts="Set" />
				<soap:header message="cai3g:Set" part="SessionId" use="literal" />
				<soap:header message="cai3g:Set" part="TransactionId" use="literal" />
				<soap:header message="cai3g:Set" part="SequenceId" use="literal" />
			</input>
			<output>
				<soap:body use="literal" parts="SetResponse" />
				<soap:header message="cai3g:SetResponse" part="SessionId" use="literal" >
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:SetResponse" part="TransactionId" use="literal" >
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="transactionIdFault" use="literal"/>
				</soap:header>
				<soap:header message="cai3g:SetResponse" part="SequenceId" use="literal" >
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sequenceIdFault" use="literal"/>
				</soap:header>
			</output>
			<fault name="Cai3G12FaultException">
				<soap:fault name="Cai3G12FaultException" use="literal" />
			</fault>
		</operation>
	</binding>
	<service name="CAI3G">
		<port name="EnumDnsDomain" binding="cai3g:EnumDnsDomain">
			<soap:address location="http://anyema.anyprovisioningprovider.com/cai3g" />
		</port>
	</service>
</definitions>
