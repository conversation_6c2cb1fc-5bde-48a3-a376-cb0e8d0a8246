<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 sp1 (http://www.altova.com) by <PERSON><PERSON> (<PERSON> (China) Communications Company Ltd) -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/ECAS/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://schemas.ericsson.com/ma/ECAS/" elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           jaxb:version="2.0">

    <xs:element name="username" type="usernameType"/>
    <xs:element name="CreateECASCertificate">
        <xs:annotation>
            <xs:documentation>CSR Enrollment</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="username" type="usernameType"/>
                <xs:element name="password" type="passwordType" minOccurs="0"/>
                <xs:element name="caName" type="caNameType" minOccurs="0"/>
                <xs:element name="csr" type="csrType"/>
                <xs:element name="subjectDN" type="subjectDNType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="username" type="usernameType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="usernameAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="key_create_identity">
            <xs:selector xpath="."/>
            <xs:field xpath="@username"/>
        </xs:key>
        <xs:keyref name="keyref_create_identity" refer="key_create_identity">
            <xs:selector xpath="./username"/>
            <xs:field xpath="."/>
        </xs:keyref>
    </xs:element>
    <xs:element name="CreateResponseECASCertificate">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="certificate" type="certType"/>
                <xs:element name="certificateSN" type="certIdType"/>
                <xs:element name="certificateExpireTime" type="certExpireTimeType"/>
                <xs:element name="certificateIssuerName" type="certificateIssuerNameType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetECASCertificate">
        <xs:annotation>
            <xs:documentation>Certificate Renew</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="password" type="passwordType" minOccurs="0"/>
                <xs:element name="certificateSN" type="certIdType"/>
                <xs:element name="caName" type="caNameType" minOccurs="0"/>
                <xs:element name="csr" type="csrType"/>
                <xs:element name="subjectDN" type="subjectDNType" minOccurs="0"/>
                <xs:element name="issuerDN" type="issuerDNType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="username" type="usernameType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="usernameAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetResponseECASCertificate">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="certificate" type="certType"/>
                <xs:element name="certificateSN" type="certIdType"/>
                <xs:element name="certificateExpireTime" type="certExpireTimeType"/>
                <xs:element name="certificateIssuerName" type="certificateIssuerNameType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DeleteECASCertificate">
        <xs:annotation>
            <xs:documentation>Certificate Revoke</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="password" type="passwordType" minOccurs="0"/>
                <xs:element name="issuerDN" type="issuerDNType" minOccurs="0"/>
                <xs:element name="certificateSN" type="certIdType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="username" type="usernameType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="usernameAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetECASCertificate">
        <xs:annotation>
            <xs:documentation>Certificate Get</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="certificateSN" type="certIdType"/>
                <xs:element name="issuerDN" type="issuerDNType" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetResponseECASCertificate">
        <xs:annotation>
            <xs:documentation>Certificate response</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="certificate" type="certType"/>
                <xs:element name="certificateSN" type="certIdType"/>
                <xs:element name="certificateExpireTime" type="certExpireTimeType"/>
                <xs:element name="issuerDN" type="issuerDNType"/>
                <xs:element name="isRevoked" type="isRevokedType"/>
                <xs:element name="revocationDate" type="revocationDateType" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <!-- the definition of data type -->
    <xs:simpleType name="usernameType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="256"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="passwordType">
        <xs:restriction base="xs:string">
            <xs:minLength value="8"/>
            <xs:maxLength value="256"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="caNameType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="512"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="subjectDNType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="512"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="issuerDNType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="512"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="csrType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="certType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="certIdType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="64"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="certExpireTimeType">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="isRevokedType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Yes"/>
            <xs:enumeration value="No"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="revocationDateType">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="certificateIssuerNameType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="512"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
