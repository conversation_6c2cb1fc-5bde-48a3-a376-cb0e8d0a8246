<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/"
	xmlns:ns="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/"
	targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/GsmFnr/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="msisdn" type="msisdnType" />
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="createFlexibleNumbering">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementMsisdn" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="imsi" type="imsiType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementImsi" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="address" type="addressType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional" />
			<xs:attribute name="imsi" type="imsiType" use="optional" />
		</xs:complexType>
		<xs:key name="msisdnKey_FNSUB_Create">
			<xs:selector xpath="./ns:msisdn" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_msisdn" refer="msisdnKey_FNSUB_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@msisdn" />
		</xs:keyref>
		<xs:key name="imsiKey_FNSUB_Create">
			<xs:selector xpath="./ns:imsi" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imsi" refer="imsiKey_FNSUB_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>
	</xs:element>
	<xs:element name="setFlexibleNumbering">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementMsisdn" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="imsi" type="imsiType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementImsi" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="address" type="addressType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional" />
			<xs:attribute name="imsi" type="imsiType" use="optional" />
		</xs:complexType>
	</xs:element>
	<xs:element name="getResponseFlexibleNumbering">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="msisdn" type="msisdnType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementMsisdn" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="imsi" type="imsiType" minOccurs="0">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="elementImsi" />
						</xs:appinfo>
					</xs:annotation>
				</xs:element>
				<xs:element name="nprefix" type="nprefixType" minOccurs="0" />
				<xs:element name="subtype" type="subTypeType" minOccurs="0" />
				<xs:element name="subcond" type="subcondType" minOccurs="0" />
				<xs:element name="address" type="addressType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="msisdn" type="msisdnType" use="optional" />
			<xs:attribute name="imsi" type="imsiType" use="optional" />
		</xs:complexType>
		<xs:key name="msisdnKey_FNSUB_Get">
			<xs:selector xpath="./ns:msisdn" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="msisdnKeyRef_FNSUB_Get" refer="msisdnKey_FNSUB_Get">
			<xs:selector xpath="." />
			<xs:field xpath="@msisdn" />
		</xs:keyref>
	</xs:element>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{5,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{6,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="nprefixType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){1,10}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="subTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HOME" />
			<xs:enumeration value="EXPORTED" />
			<xs:enumeration value="IMPORTED" />
			<xs:enumeration value="OTHER" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="subcondType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="GSM/WCDMA" />
			<xs:enumeration value="PSTN/ISDN" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="addressType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(#1(0|1|2|3|4)|[0-9]){5,28}" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
