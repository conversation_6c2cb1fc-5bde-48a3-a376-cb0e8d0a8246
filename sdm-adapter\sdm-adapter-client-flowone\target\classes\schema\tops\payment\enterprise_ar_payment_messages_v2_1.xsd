<!-- 
Build_Label: REL500
QualityCenter_MR#: 00348796 
Build_Date: 2016-12-07-14:56:28
--><xsd:schema targetNamespace="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/messages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v2_0" xmlns:enterprise_ar_payment_messages="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/messages" xmlns:enterprise_ar_payment_xsd="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types" xmlns:enterprise_payment_xsd="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<xsd:annotation>
		<xsd:documentation>Contains Messages used for operations in Payment service.</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/ar/payment/v2_0/types" schemaLocation="enterprise_ar_payment_types_v2_1.xsd"/>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" schemaLocation="enterprise_payment_types_v1_0.xsd"/>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<xsd:element name="makePayment_Request" type="enterprise_ar_payment_messages:makePayment_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for makePayment() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="makePayment_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'makePaymemt_Request'</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="faId" type="enterprise_common_xsd:FaIdType">
				<xsd:annotation>
					<xsd:documentation>Financial Account Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentId" type="enterprise_payment_xsd:PaymentIdType">
				<xsd:annotation>
					<xsd:documentation>This id is a unique id that is created by service consumer.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentAmount" type="enterprise_common_xsd:AmountType">
				<xsd:annotation>
					<xsd:documentation>Payment amount.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentMethod" type="enterprise_ar_payment_xsd:PaymentMethodType">
				<xsd:annotation>
					<xsd:documentation>
						Payment method. Valid values are 
						CC - CreditCard
						DD - Direct Debit (ACH)
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>Payment Date.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentInfo" type="enterprise_ar_payment_xsd:PaymentInfoType">
				<xsd:annotation>
					<xsd:documentation>Payment information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="originatorInfo" type="enterprise_payment_xsd:OriginatorInfoType">
				<xsd:annotation>
					<xsd:documentation>Originator information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="makePayment_Response" type="enterprise_ar_payment_messages:makePayment_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for makePayment() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="makePayment_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'makePaymemt_Response'</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="response" type="enterprise_common_xsd:ResponseType">
				<xsd:annotation>
					<xsd:documentation>Response information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paymentResult" type="enterprise_ar_payment_xsd:PaymentResultType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The payment result.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getPaymentTransactionDetails_Request" type="enterprise_ar_payment_messages:getPaymentTransactionDetails_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for getPaymentTransactionDetails() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getPaymentTransactionDetails_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getTransactionDetails_Request'</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="transactionId" type="enterprise_ar_payment_xsd:TransactionIdType">
				<xsd:annotation>
					<xsd:documentation>Transaction Id returned by getPaymentTransactionHistory operation.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="faId" type="enterprise_common_xsd:FaIdType">
				<xsd:annotation>
					<xsd:documentation>Financial Account Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="partitionId" type="enterprise_ar_payment_xsd:PartitionIdType">
				<xsd:annotation>
					<xsd:documentation>Partition Id returned by getPaymentTransactionHistory operation.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="periodKey" type="enterprise_ar_payment_xsd:PeriodKeyType">
				<xsd:annotation>
					<xsd:documentation>Period Key returned by getPaymentTransactionHistory operation.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityPeriodKey" type="enterprise_ar_payment_xsd:EntityPeriodKeyType">
				<xsd:annotation>
					<xsd:documentation>Period key for the entity ( Charge, Payment,etc) returned by getPaymentTransactionHistory operation. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="entityId" type="enterprise_ar_payment_xsd:EntityIdType">
				<xsd:annotation>
					<xsd:documentation>Entity Id that is returned by getPaymentTransactionHistory operation.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getPaymentTransactionDetails_Response" type="enterprise_ar_payment_messages:getPaymentTransactionDetails_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for getPaymentTransactionDetails() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getPaymentTransactionDetails_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getPaymentTransactionDetails_Response'</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="response" type="enterprise_common_xsd:ResponseType">
				<xsd:annotation>
					<xsd:documentation>Response Information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="result" type="enterprise_ar_payment_xsd:PaymentTransactionDetailsResultType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Payment transaction Details result.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getPaymentTransactionHistory_Request" type="enterprise_ar_payment_messages:getPaymentTransactionHistory_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request element for getPaymentTransactionHistory() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getPaymentTransactionHistory_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getPaymentTransactionHistory_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="accountIdOption" type="enterprise_ar_payment_xsd:AccountIdOptionType">
				<xsd:annotation>
					<xsd:documentation>The option type of ID used to retrieve the Payment history. Financial Account Id, or Customer Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="fromDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>Start Date for the transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="toDate" type="enterprise_payment_xsd:DateType">
				<xsd:annotation>
					<xsd:documentation>End Date for the transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paginationInfo" type="enterprise_payment_xsd:PaginationInfoType">
				<xsd:annotation>
					<xsd:documentation>End Date for the transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getPaymentTransactionHistory_Response" type="enterprise_ar_payment_messages:getPaymentTransactionHistory_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response element for getPaymentTransactionHistory() operation.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getPaymentTransactionHistory_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getPaymentTransactionHistory_Response'</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="response" type="enterprise_common_xsd:ResponseType">
				<xsd:annotation>
					<xsd:documentation>Response Information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="result" type="enterprise_ar_payment_xsd:PaymentTransactionHistoryResultType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Payment transaction History result.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="paginationInfo" type="enterprise_payment_xsd:PaginationResultsInfoType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Search result pagination information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>