<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
			<xs:include schemaLocation="../../schemas/Generic/cai3g1.2_provisioning.xsd" />
		</xs:schema>
	</types>
	<message name="LoginRequest">
		<part name="parameters" element="cai3g:Login" />
	</message>
	<message name="LoginResponse">
		<part name="parameters" element="cai3g:LoginResponse" />
	</message>
	<message name="LogoutRequest">
		<part name="parameters" element="cai3g:Logout" />
	</message>
	<message name="LogoutResponse">
		<part name="parameters" element="cai3g:LogoutResponse" />
	</message>
	<message name="HeadInfo">
		<part name="sessionId" element="cai3g:SessionId" />
		<part name="transactionId" element="cai3g:TransactionId" />
		<part name="context" element="cai3g:Context" />
		<part name="sequenceId" element="cai3g:SequenceId" />
	</message>
	<message name="Cai3gFault">
		<part name="parameters" element="cai3g:Cai3gFault" />
	</message>
	<message name="Cai3gHeaderFault">
		<part name="sessionIdFault" element="cai3g:SessionIdFault" />
		<part name="transactionIdFault" element="cai3g:TransactionIdFault" />
		<part name="contextFault" element="cai3g:ContextFault" />
		<part name="sequenceIdFault" element="cai3g:SequenceIdFault" />
	</message>
	<portType name="SessionControl">
		<operation name="Login">
			<input message="cai3g:LoginRequest" />
			<output message="cai3g:LoginResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
		<operation name="Logout">
			<input message="cai3g:LogoutRequest" />
			<output message="cai3g:LogoutResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
	</portType>
	<binding name="SessionControl" type="cai3g:SessionControl">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<operation name="Login">
			<soap:operation soapAction="CAI3G#Login" style="document" />
			<input>
				<soap:body use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
		<operation name="Logout">
			<soap:operation soapAction="CAI3G#Logout" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
	</binding>
	<service name="SessionControl">
		<port name="SessionControl" binding="cai3g:SessionControl">
			<soap:address location="http://PG_NGN_VIP:8080/CAI3G1.2/services/CAI3G1.2" />
		</port>
	</service>
</definitions>
