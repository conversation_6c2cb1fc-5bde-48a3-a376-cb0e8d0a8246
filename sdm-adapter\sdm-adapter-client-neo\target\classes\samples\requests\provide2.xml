<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Header>
        <Security
            xmlns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <UsernameToken>
                <Username>${#Project#OUA_SOAP_USER}</Username>
                <Password
                    Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"
                >${#Project#OUA_SOAP_TOKEN}</Password>
            </UsernameToken>
        </Security>
    </soapenv:Header>
    <soapenv:Body>
        <ns4:createAndStartRequestByValueRequest xmlns:ns2="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns4="http://ossj.org/xml/OrderManagement/v1-0" xmlns:ns3="http://ossj.org/xml/Common/v1-5" xmlns:ns6="http://amdocs/core/ossj-Common/dat/3" xmlns:ns31="http://www.tmforum.org/xml/tip/internal/entity" xmlns:ns5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:ns30="http://www.tmforum.org/xml/tip/internal/notifications" xmlns:ns8="http://ossj.org/xml/Inventory/v1-2" xmlns:ns7="http://ossj.org/xml/Common-CBEBi/v1-5" xmlns:ns13="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:ns9="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:ns12="http://ossj.org/xml/Common-CBELocation/v1-5" xmlns:ns34="http://www.amdocs.com/xml/tip/internal/entity" xmlns:ns11="http://ossj.org/xml/Common-CBEDatatypes/v1-5" xmlns:ns33="http://www.amdocs.com/xml/tip/common/notifications" xmlns:ns10="http://amdocs/core/ossj-OrderManagement/dat/3" xmlns:ns32="http://www.tmforum.org/xml/tip/common/notifications" xmlns:ns17="http://amdocs/core/ossj-Common-CBEProduct/dat/3" xmlns:ns16="http://ossj.org/xml/Common-CBEProduct/v1-5" xmlns:ns15="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:ns14="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:ns19="http://ossj.org/xml/Common-CBEProductOffering/v1-5" xmlns:ns18="http://amdocs/core/ossj-Common-CBECustomer/dat/3" xmlns:ns20="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:ns24="http://amdocs/core/ossj-Inventory/dat/3" xmlns:ns23="http://ossj.org/xml/Common-CBEUser/v1-5" xmlns:ns22="http://ossj.org/xml/Common-CBECustomer/v1-5" xmlns:ns21="http://ossj.org/xml/Common-CBEParty/v1-5" xmlns:ns28="http://docs.oasis-open.org/wsn/b-2" xmlns:ns27="http://www.w3.org/2005/08/addressing" xmlns:ns26="http://docs.oasis-open.org/wsrf/bf-2" xmlns:ns25="http://ossj.org/xml/Common-CBEReport/v1-5" xmlns:ns29="http://docs.oasis-open.org/wsn/t-1">
            <ns4:requestValue xsi:type="ns10:ServiceOrderValue" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns3:key xsi:type="ns10:ServiceOrderKey">
                    <ns3:type>Service</ns3:type>
                    <ns3:primaryKey>
                        <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">Provide-AIA-${=java.util.UUID.randomUUID()}</primaryKey>
                    </ns3:primaryKey>
                </ns3:key>
                <ns10:priority_Request>2</ns10:priority_Request>
                <ns4:serviceOrderItems>
                    <ns4:item xsi:type="ns10:ServiceOrderItemValue">
                        <ns7:action>provide</ns7:action>
                        <ns4:service xsi:type="ns9:ServiceValue">
                            <ns3:key xsi:type="ns9:ServiceKey">
                                <ns3:type>Service</ns3:type>
                                <ns3:primaryKey>
                                    <primaryKey xmlns="http://amdocs/core/ossj-Common/dat/3">cce15a0a-b14e-413e-8e93-6a246574edc1</primaryKey>
                                </ns3:primaryKey>
                            </ns3:key>
                            <ns5:describedBy xsi:type="ns6:ArrayOfCharacteristicValue">
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>subscriptionClass</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">WA</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>subscriberProfile</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">LTEUSIM</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>equipmentType</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">G</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>msisdn</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">2292550365</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>subscriptionId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">9E112DB9D6B141B0A127E067CF27F8BE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>networkAccessMode</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">00</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>effectiveDate</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">20210301000000</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>operator</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">BSSe</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>imsi</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">310950000065099</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>imei</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">354820290003000</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>iccid</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">89019507330000648462</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>mms</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>gprsRoamingRestriction</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">DREP_ZED_SA_USCANMX</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>resubmitMode</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">failed</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>acwsvcnetwork</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">5gsaphone</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>carrierId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>accountType</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">I</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>emlppActive</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>uploadSpeed</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>roamingRestriction</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ZED_SA_USCANMX</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>paymentType</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">PO</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>offerLevel</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">S</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>externalOfferId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">990029</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>downloadSpeed</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns6:values>
                                </ns5:item>


                                <!-- NEW OR UPDATED ATTRIBUTES USED FOR ACTIVATING YELLOWSTONE IOT SERVICE -->
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>originatingClassOfService</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">65536</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>sms</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ALP</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>market</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ACM</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>subMarket</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ACM</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>accountSubType</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">Y</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>operatorId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">21</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarWap</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">N</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>contentFilter</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">Off</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isAgeVerified</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">N</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>OTAProvisioning</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>hssPoolId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">EOD</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarOutgoingSms</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarIncomingSms</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarOutgoingInternationalSms</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>pdpName</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">D50006,D50004</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>defaultPdpName</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">D50003</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>homeSMSC</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">DALi1</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>nrAsSecondaryRatAllowed</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>clipAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>clipAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>subsUssdEnabled</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>mptyAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>mptyAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>holdAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>holdAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>passwordOptionForSdb</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarIncomingCalls</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>isBarOutgoingCalls</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cfbAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cfnrcAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cfnryAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cfdAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfdAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfdFtnAddr</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1/1/1/0</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfdFtnSubAddr</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1/0/</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfbNotifCallParty</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfnrcNotifCallParty</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfnryNotifCallParty</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>clirAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>clirAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>callerId</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cfuAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>teleCfuAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>interzonalIntlOgNotHplmn</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>intlOgCallsNotHplmn</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>gsmSubscriptRestrict</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">FALSE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cwAuth</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>cwAct</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>clientapplicationid</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ACM</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>dnnName</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">d50006.etr,d50004.etr</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>defaultDnnName</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">d50003.etr</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>chargingCharacteristic</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">0000</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>5gQosProfile</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">9</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>arpPreemptCap</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">NOT_PREEMPT</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>arpPreemptVuln</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">PREEMPTABLE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>arpPriorityLevel</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">12</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>priorityLevel</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">12</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>allowedSessionTypes</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">IPV4V6</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>defaultSessionTypes</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">IPV4V6</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>sessionAmbrDownlink</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>sessionAmbrUplink</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">10 Gbps</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>defaultSscMode</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">SSC_MODE_1</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>sliceServiceType</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>ipMessaging</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">TRUE</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>slicedifferentiator</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">1</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>abrQuality</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">ABR2</ns6:values>
                                </ns5:item>
                                <ns5:item xsi:type="ns6:CharacteristicValue">
                                    <ns5:characteristic>coreInd</ns5:characteristic>
                                    <ns6:values xsi:type="xs:string" xmlns:xs="http://www.w3.org/2001/XMLSchema">NorthRIM</ns6:values>
                                </ns5:item>
                            </ns5:describedBy>
                            <ns9:serviceType>IOT-CMP</ns9:serviceType>
                            <ns9:activationTargets>
                                <ns6:item>
                                    <ns6:id>Activation</ns6:id>
                                </ns6:item>
                            </ns9:activationTargets>
                        </ns4:service>
                        <ns10:subAction></ns10:subAction>
                    </ns4:item>
                </ns4:serviceOrderItems>
                <dox-om:maxResponseWaitTime xmlns:dox-om="http://amdocs/core/ossj-OrderManagement/dat/3">10</dox-om:maxResponseWaitTime>
                <ns10:operator>CMP</ns10:operator>
                <ns10:resubmitMode>failed</ns10:resubmitMode>
            </ns4:requestValue>
        </ns4:createAndStartRequestByValueRequest>
    </soapenv:Body>
</soapenv:Envelope>
