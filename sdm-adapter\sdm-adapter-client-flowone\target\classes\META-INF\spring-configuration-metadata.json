{"groups": [{"name": "flowone", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.eps", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$EpsProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties.EpsProperties getEps() "}, {"name": "flowone.http", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$HttpProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties.HttpProperties getHttp() "}, {"name": "flowone.tops", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties.Tops getTops() "}, {"name": "flowone.tops.soap-action", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$SoapAction", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties.SoapAction getSoapAction() "}, {"name": "kafka", "type": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.KafkaProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.KafkaProperties"}], "properties": [{"name": "flowone.csd", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.eps.max-bandwidth-down", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$EpsProperties"}, {"name": "flowone.eps.max-bandwidth-up", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$EpsProperties"}, {"name": "flowone.http.connection-time-out", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$HttpProperties"}, {"name": "flowone.http.max-connections", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$HttpProperties"}, {"name": "flowone.http.max-connections-per-route", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$HttpProperties"}, {"name": "flowone.il-req-group", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.include-qos-in-apn", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.kafka", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.key-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.net-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.order-no", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.product-version", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.req-header-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.req-header-username", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.req-user", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.technical-product", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.tops.auth-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.auth-value", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.bill-mgmt-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.client-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.delay", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.key-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.key-store-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.max-retry", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.payment-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.soap-action.bill-list-action", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$SoapAction"}, {"name": "flowone.tops.soap-action.bill-pdf-action", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$SoapAction"}, {"name": "flowone.tops.soap-action.trx-history-action", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$SoapAction"}, {"name": "flowone.tops.trust-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.trust-store-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.tops.usc-security-token", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties$Tops"}, {"name": "flowone.trust-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "flowone.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.FlowOneClientProperties"}, {"name": "kafka.boot-strap-servers", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.KafkaProperties"}, {"name": "kafka.consumer-props", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.KafkaProperties"}, {"name": "kafka.producer-props", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.flowone.config.KafkaProperties"}], "hints": []}