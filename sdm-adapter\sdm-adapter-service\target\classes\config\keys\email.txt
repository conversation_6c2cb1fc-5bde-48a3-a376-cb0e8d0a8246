<EMAIL>

SivakumarReddy
Pass: iDNv71^dOG#3

C1ock$peEd

SindhuraNilamikantiExt
v>JJ^haPZkE7Y,wd

kolapalliSatyasureshExt
cHUyxA39S0e79uaB


 ssh -i RJ2TAkey.pem ubuntu@10.6.200.6


 kubectl get pods -n hmd-fra -o wide | grep -i app-15

 kubectl patch pvc {PVC_NAME} -p '{"metadata":{"finalizers":null}}'


 /elektra-deployment/openstack/multimaster/out/elektra/secrets

 kubectl get pods -A -o wide | grep -i app-15

/home/<USER>/elektra-deployment/vmware/multimaster/out/elektra

kubectl get pods -A | grep rating
kubectl logs -f    conductor-es-1       -n hmd-fra
kubectl logs -f   conductor-es-1      -n hmd-fra | grep -i error

kubectl delete deployment/report-service services/report-service -n usccdemo

kubectl scale --replicas=0 deployment/report-service  -n usccdemo

kubectl get pods -n tis-ams -o wide

sudo journalctl --rotate --vacuum-size=100M


kubectl get configmaps -A
  679  kubectl edit configmap notify-kafka-config -n chk-aut-ams
  680  kubectl get pods -A | grep -i 'notify'
  681  kubectl scale --replicas=0 deployment/notify-kafka -n chk-aut-ams
  682  kubectl get pods -A | grep -i 'notify'
  683  kubectl scale --replicas=1 deployment/notify-kafka -n chk-aut-ams
  684  kubectl get pods -A | grep -i 'notify'
  685  kubectl logs -f notify-kafka-7fdd87dbdc-s65n8 -n chk-aut-ams
  686  kubectl edit configmap notify-kafka-config -n chk-aut-ams
  687  kubectl get pods -A | grep -i 'notify'
  688  kubectl edit configmap notify-kafka-config -n chk-aut-ams
  689  kubectl scale --replicas=0 deployment/notify-kafka -n chk-aut-ams
  690  kubectl scale --replicas=1 deployment/notify-kafka -n chk-aut-ams
  691  kubectl get pods -A | grep -i 'notify'
  692  kubectl logs -f notify-kafka-7fdd87dbdc-dkjlj -n chk-aut-ams

--------------------------------------------------------------------
Hi Team , I got this list from Rajat that Harman is supporting :

elektraportal
resourcemanagement
nokiaonendsadapter
resourceuploader
securitypolicymanagement
simlcmanagement
acc-mgmt-service-app
servicecatalogue
automationrules
nokiacmgadapter_final
conductor-server and
conductor-ui
Esim

--------------------------------------------------------------------
UAM Microservice	Amit
Tenant Management Microservice	Amit
Tenant Management UI	Amit
CPS Adaptor	Chidananda
Keycloak extensions	Amit
Reports Service - Autmation Role Engine Service	Sindhura / Neelima
Rating service	Sindhura
Asset Service	Amit
API Gateway	Amit
Rest Gateway	Shiva
ELK	Eswar

--------------------------------------------------------------------

POD:	fra3-hmd-app-8
ENV:	hmd-fra
CMD:	kubectl logs -f   acc-mgmt-bd575bc56-49bbg      -n hmd-fra | grep -i error
OUTPUT:	{"@timestamp":"2021-11-19T10:08:48.340Z","@version":"1","message":"HHH015007: Illegal argument on static metamodel field injection : com.nokia.wing.domain.AccountMaster_#custFields; expected type :  org.hibernate.metamodel.model.domain.internal.SingularAttributeImpl; encountered type : jakarta.persistence.metamodel.MapAttribute","logger_name":"org.hibernate.metamodel.internal.MetadataContext","thread_name":"main","level":"ERROR","level_value":40000}

----------------------------------------------------------------------
delete imsi from restgw
[4:21 PM] Jonnala, Sivakumar (EXT - IN/Hyderabad)
select * from tbl_subscriber ts where subscription_id_data0 ='***************';

[4:21 PM] Jonnala, Sivakumar (EXT - IN/Hyderabad)
select * from tbl_subscriber_data_balance tsdb where subscription_id_data0 ='***************';

[4:22 PM] Jonnala, Sivakumar (EXT - IN/Hyderabad)
curl --location --request GET '**********:30395/clearCache'

----------------------------------------------------------------------
cronjob and workflow deployment

mv elektra-wfm-master elektra-wfm-master_20220506
tar -xvzf elektra-wfm-22.5.19.tar
cd elektra-wfm-master/
chmod +x provision.sh
kubectl get svc -A |grep ingress
kubectl get svc -A |grep conducter-server
./provision.sh http://************:30952
cd /home/<USER>/cronjob/
kubectl apply -f sftp-job.yaml

C1ock$peEd

------------------------------------------------------------------------
db backup
C1ock$peEd
 cd /mnt/ceph/backup/
  787  ls -lrt
  788  sudo mkdir 20220509
  789  sudo chown postgres.postgres 20220509/
  790  cd 20220509/
  sudo su postgres
  791  pwd
  792  pg_basebackup -p 5432 -U postgres -D /mnt/ceph/backup/20220530/ -Ft -z -Xs -P

  run under postgres user for taking schema level bkp
	pg_dump -n cdrhandlerdb elektradb > cdrhandlerdb.sql
	to restore shema
	pg_restore -n restgw < restgwbkp.sql

  793  sudo su postgres
---------------------------------------------------------------------------
<EMAIL> | testing team group mail id
---------------------------------------------------------------------------
USCCDEMO :
If CSI cinder nodeplugin under crashloopback then follow the below steps in Master node



cd elektra-deployment/openstack/multimaster/ansible/playbooks/extras/
ansible-playbook -i /etc/ansible/hosts csi-clear-label.yml
kubectl get pods -A | csi
kubectl delete pod csi-cinder-nodeplugin-x554r -n kube-system
ansible-playbook -i /etc/ansible/hosts csi-apply-label.yml
----------------------------------------------------------------------------

sudo systemctl restart postgresql
----------------------------------------------------------------------------
to delete error state pods

kubectl delete pod `kubectl get pods --namespace chk-aut-ams | awk '$3 == "Error" {print $1}'` --namespace chk-aut-ams
----------------------------------------------------------------------------

select * from pg_stat_activity
where datname = 'elektradb'
and state='active';

--------------------------------------------------------------------------------
manual image push

sudo docker image pull amsstg2gitlabcicd05v:5000/rating:22.2.4-hmd-fixes.11
sudo docker image tag amsstg2gitlabcicd05v:5000/rating:22.2.4-hmd-fixes.11 ***********:5000/rating:22.2.4-hmd-fixes.11
sudo docker image push ***********:5000/rating:22.2.4-hmd-fixes.11


 sudo docker image pull ***********:5000/rating:22.2.4-hmd-fixes.11
 sudo docker save ***********:5000/rating:22.2.4-hmd-fixes.11 | gzip > /mnt/ceph1/data/smartimage/20220523/rating_22_2_4-hmd-fixes_11.tar.gz


 sftp -i ~/ams3nginx.pem ubuntu@************
 ssh -i ~/ams3nginx.pem ubuntu@************

 sftp -i /home/<USER>/ssh_key_smart_lightsail.pem ubuntu@*************
 ssh -i /home/<USER>/ssh_key_smart_lightsail.pem ubuntu@*************




 sudo docker image load --input rating_22_2_4-hmd-fixes_11.tar.gz
 sudo docker image tag ***********:5000/rating:22.2.4-hmd-fixes.11 ************:5000/rating:22.2.4-hmd-fixes.11
 sudo docker image push ************:5000/rating:22.2.4-hmd-fixes.11


 /home/<USER>/elektra-deployment/vmware/multimaster/out/elektra
 kubectl delete -f rating.yaml
 kubectl get pods -n cebustg |grep rating
 kubectl apply -f rating.yaml

 --------------------------------------------------------------------------------


