<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.1.v20070730-0941-IXOqsospgq-tNz7
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\projectBased\sessionSingleSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2004 - 2007 The Members of the OSS through Java(TM) Initiative, Amdocs Management Ltd., <PERSON>, SAP AG, Telcordia Technologies Inc., TeleManagement Forum, Artur Uzieblo, Gero Vermaas.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<!-- Project based schema -->
<schema targetNamespace="http://ossj.org/xml/OrderManagement/v1-0"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:om-v1-0="http://ossj.org/xml/OrderManagement/v1-0"
    xmlns:cbebi-v1-5="http://ossj.org/xml/Common-CBEBi/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5"
    xmlns:cbeproductoffering-v1-5="http://ossj.org/xml/Common-CBEProductOffering/v1-5"
    xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5"
    xmlns:cbeparty-v1-5="http://ossj.org/xml/Common-CBEParty/v1-5"
    version = "v1-0"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEBi/v1-5"
        schemaLocation="OSSJ-Common-CBEBi-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEResource/v1-5"
        schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>

    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5"
        schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEProductOffering/v1-5"
        schemaLocation="OSSJ-Common-CBEProductOffering-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEService/v1-5"
        schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEParty/v1-5"
        schemaLocation="OSSJ-Common-CBEParty-v1-5.xsd"/>

    <!-- Tigerstripe : Entity definitions for CustomerAccountInteractionRole  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="CustomerAccountInteractionRoleValue" >
        <annotation>

            <documentation>
Interface definition for the javax.oss.om.bi.CustomerAccountInteractionRoleValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleValue" >    
                <sequence>
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfCustomerAccountInteractionRoleValue">
        <sequence>
            <element name="item" type="om-v1-0:CustomerAccountInteractionRoleValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerAccountInteractionRoleKey">
        <annotation>
            <documentation>

                This CustomerAccountInteractionRoleKey encapsulates all the information that is necessary to 
                identify a particular instance of a CustomerAccountInteractionRoleValue. The type of the 
                primary key for this CustomerAccountInteractionRoleKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerAccountInteractionRoleKey">

        <sequence>
            <element name="item" type="om-v1-0:CustomerAccountInteractionRoleKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerAccountInteractionRoleKeyResult">
        <annotation>
            <documentation>
                The CustomerAccountInteractionRoleKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a CustomerAccountInteractionRoleValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKeyResult">
                <sequence>
                     <element name="customerAccountInteractionRoleKey" type="om-v1-0:CustomerAccountInteractionRoleKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfCustomerAccountInteractionRoleKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:CustomerAccountInteractionRoleKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of CustomerAccountInteractionRole -->
    <!-- Tigerstripe : End of Entity definition for CustomerAccountInteractionRole -->
    <!-- Tigerstripe : Entity definitions for PartyInteractionRole  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="PartyInteractionRoleValue" >

        <annotation>
            <documentation>
Interface definition for the javax.oss.om.bi.PartyInteractionRoleValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleValue" >    
                <sequence>
                    <element name="identifiedBy" type="cbeparty-v1-5:PartyRoleValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfPartyInteractionRoleValue">
        <sequence>
            <element name="item" type="om-v1-0:PartyInteractionRoleValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="PartyInteractionRoleKey">

        <annotation>
            <documentation>
                This PartyInteractionRoleKey encapsulates all the information that is necessary to 
                identify a particular instance of a PartyInteractionRoleValue. The type of the 
                primary key for this PartyInteractionRoleKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfPartyInteractionRoleKey">
        <sequence>
            <element name="item" type="om-v1-0:PartyInteractionRoleKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="PartyInteractionRoleKeyResult">
        <annotation>
            <documentation>

                The PartyInteractionRoleKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a PartyInteractionRoleValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKeyResult">
                <sequence>
                     <element name="partyInteractionRoleKey" type="om-v1-0:PartyInteractionRoleKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfPartyInteractionRoleKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:PartyInteractionRoleKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of PartyInteractionRole -->
    <!-- Tigerstripe : End of Entity definition for PartyInteractionRole -->

    <!-- Tigerstripe : Entity definitions for Request  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="RequestValue" >
        <annotation>
            <documentation>
Interface definition RequestValue.java 
 The Request is a business interaction, where the sender's intention is to request something from the receiver of this message. SID describes:  
- A Request is an interaction as a communication with a managed Entity. 
- The Business Interaction represents an agreement, contract, or communication between enterprise and one or more other entities as individuals and organisations (or parts of organisations). 
- Interactions take on the form of Request, Response, Notification and Agreement. 
- A Request is an act of asking that something be done (that typically involves a Response). Request is a type of a Business Interaction. Request may take a form of a InquiryRequest, CustomerOrder, Command. 
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionValue" >    
                <sequence>

                    <element ref="om-v1-0:basePriority_Request" minOccurs="0"/>
                    <element name="expectedCompletionDate" type="dateTime" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="requestedCompletionDate" type="dateTime" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="clientId" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="bulk" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfRequestValue">
        <sequence>
            <element name="item" type="om-v1-0:RequestValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="RequestKey">
        <annotation>
            <documentation>

                This RequestKey encapsulates all the information that is necessary to 
                identify a particular instance of a RequestValue. The type of the 
                primary key for this RequestKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfRequestKey">

        <sequence>
            <element name="item" type="om-v1-0:RequestKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="RequestKeyResult">
        <annotation>
            <documentation>
                The RequestKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a RequestValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionKeyResult">
                <sequence>
                     <element name="requestKey" type="om-v1-0:RequestKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfRequestKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:RequestKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of Request -->
    <element name="basePriority_Request" type="int" abstract="true"/>
     
    <element name="priority_Request" 
        type="om-v1-0:RequestPriority" 
        substitutionGroup="om-v1-0:basePriority_Request" /> 

    <!-- Tigerstripe : End of Entity definition for Request -->
    <!-- Tigerstripe : Entity definitions for RequestSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->

    <complexType name="RequestSpecificationValue" >
        <annotation>
            <documentation>
This specification captures characteristics and constraints applicable to instances of Request. 
 To support dynamic requests and simplify the main interface, the RequestSpecificationValue contains all pertinent information about the types of requests supported by an implementation, such as:   
- businessInteractionType 
- supportedTransitions 
- supportedItems 
  Specifications can be obtained from the getRequestSpecifications
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionSpecificationValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="namespace" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="itemTypeActionsPairs" type="om-v1-0:ArrayOfItemTypeSupportedActionsPair" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="supportedTransitions" type="om-v1-0:ArrayOfRequestStateTransition" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfRequestSpecificationValue">
        <sequence>

            <element name="item" type="om-v1-0:RequestSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="RequestSpecificationKey">
        <annotation>
            <documentation>
                This RequestSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a RequestSpecificationValue. The type of the 
                primary key for this RequestSpecificationKey definition is: string 
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfRequestSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:RequestSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="RequestSpecificationKeyResult">
        <annotation>
            <documentation>
                The RequestSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a RequestSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "cbebi-v1-5:BusinessInteractionSpecificationKeyResult">
                <sequence>
                     <element name="requestSpecificationKey" type="om-v1-0:RequestSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfRequestSpecificationKeyResult">
        <sequence>

            <element name="item" type="om-v1-0:RequestSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestSpecification -->
    <!-- Tigerstripe : End of Entity definition for RequestSpecification -->
    <!-- Tigerstripe : Entity definitions for ResourceInteractionRole  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceInteractionRoleValue" >
        <annotation>
            <documentation>

Interface definition for the javax.oss.cbe.bi.ResourceInteractionRoleValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleValue" >    
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfResourceInteractionRoleValue">
        <sequence>
            <element name="item" type="om-v1-0:ResourceInteractionRoleValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceInteractionRoleKey">
        <annotation>
            <documentation>
                This ResourceInteractionRoleKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceInteractionRoleValue. The type of the 
                primary key for this ResourceInteractionRoleKey definition is: anyType 
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceInteractionRoleKey">
        <sequence>

            <element name="item" type="om-v1-0:ResourceInteractionRoleKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceInteractionRoleKeyResult">
        <annotation>
            <documentation>
                The ResourceInteractionRoleKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceInteractionRoleValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRoleKeyResult">
                <sequence>
                     <element name="resourceInteractionRoleKey" type="om-v1-0:ResourceInteractionRoleKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceInteractionRoleKeyResult">

        <sequence>
            <element name="item" type="om-v1-0:ResourceInteractionRoleKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceInteractionRole -->
    <!-- Tigerstripe : End of Entity definition for ResourceInteractionRole -->
    <!-- Tigerstripe : Entity definitions for BulkBusinessInteractionRelationship  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="BulkBusinessInteractionRelationshipValue" >
        <annotation>

            <documentation>

This relationship is used to model an aggregation between BusinessInteractions (Requests). One BusinessInteraction (Bulk Requests), the parent, contains a number of dependant BusinessInteractions (Subrequests), the children, although a single relationship can only model a 1:1 relationship between parent and child. For having a 1:n relationship, multiple instances of this interface need to be instantiated. There is no assumption made about execution order or other dependencies between the children.  
The attribute {@link javax.oss.cbe.AssociationValue#A_END_KEY} refers to the parent of the bulk relationship, the attribute {@link javax.oss.cbe.AssociationValue#Z_END_KEY} refers to the child. When a parent is aggregating more than one child, multiple instances of this interface need to be instantiated.  
The state of a parent BusinessInteraction that participates in a BulkBusinessInteractionRelationship is based on the states of all its aggregated BusinessInteractions, i.e. not only that one that is the child in this relationship. The bulk state is defined as follows:   
- 
For all states of all subrequests, ignore any substate until one of the following states: OPEN, NOT_RUNNING, NOT_STARTED, SUSPENDED, RUNNING, CLOSED, COMPLETED, ABORTED is reached. 
- 
Sort all subrequests according to the following list. The state of the bulk request is the first state that a subrequest belongs to. 
 
- OPEN 
- NOT_RUNNING 
- NOT_STARTED 
- SUSPENDED 
- RUNNING 
- CLOSED 
- COMPLETED 
- ABORTED  
- 
If not all subRequests are in the same state, append a &quot;.partially&quot; to the bulk state value, or prefix the state constant with &quot;PARTIALLY_&quot;. See {@link javax.oss.om.bi.RequestState}. Note that there cannot by definition, be a state &quot;partially aborted&quot;.   
Modifications of the BulkRequest (e.g. suspending, aborting, modification, etc) only change those subrequests, which are still OPEN. The affected children are processed atomically. When one of the children is changing it's state, a {@link RequestStateChangeEvent} is always sent. For the parent, a {@link RequestStateChangeEvent} is only sent, when the state change of one of the children also affects and changes the parent's state.  
If a client wants the children of a bulk request to be processed on a best-effort basis, then the client first needs to obtain the keys of all children (with the {@link ChildrenFromBulkParentQueryValue}) and then use the appropriate best-effort method of the {@link JVTOrderManagementSession} (they are all prefixed with try..., as in {@link javax.oss.om.JVTOrderManagementSession#tryStartRequestsByKeys(javax.oss.om.bi.RequestKey[])} on all the childred.
            </documentation>
        </annotation>

        <complexContent>

            <extension base = "cbebi-v1-5:BusinessInteractionRelationshipValue" >    
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfBulkBusinessInteractionRelationshipValue">
        <sequence>
            <element name="item" type="om-v1-0:BulkBusinessInteractionRelationshipValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="BulkBusinessInteractionRelationshipKey">
        <annotation>
            <documentation>
                This BulkBusinessInteractionRelationshipKey encapsulates all the information that is necessary to 
                identify a particular instance of a BulkBusinessInteractionRelationshipValue. The type of the 
                primary key for this BulkBusinessInteractionRelationshipKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "cbebi-v1-5:BusinessInteractionRelationshipKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfBulkBusinessInteractionRelationshipKey">
        <sequence>
            <element name="item" type="om-v1-0:BulkBusinessInteractionRelationshipKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="BulkBusinessInteractionRelationshipKeyResult">
        <annotation>
            <documentation>
                The BulkBusinessInteractionRelationshipKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a BulkBusinessInteractionRelationshipValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionRelationshipKeyResult">

                <sequence>
                     <element name="bulkBusinessInteractionRelationshipKey" type="om-v1-0:BulkBusinessInteractionRelationshipKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfBulkBusinessInteractionRelationshipKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:BulkBusinessInteractionRelationshipKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of BulkBusinessInteractionRelationship -->
    <!-- Tigerstripe : End of Entity definition for BulkBusinessInteractionRelationship -->
    <!-- Tigerstripe : Entity definitions for CustomerOrder  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="CustomerOrderValue" >
        <annotation>
            <documentation>

 A Customer Order is a Request from a customer to perform some action. From an Order Management and Fulfilment perspective this is usually in the form of a Product Order. However, there are other types of customer requests, such as AccessServiceRequests (ASRs), LocalServiceRequests (LSRs), DirectoryServiceRequests (DSRs), ProductOrders (PSRs).
            </documentation>

        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestValue" >    
                <sequence>
                    <element name="purchaseOrder" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfCustomerOrderValue">
        <sequence>
            <element name="item" type="om-v1-0:CustomerOrderValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerOrderKey">
        <annotation>
            <documentation>
                This CustomerOrderKey encapsulates all the information that is necessary to 
                identify a particular instance of a CustomerOrderValue. The type of the 
                primary key for this CustomerOrderKey definition is: string 
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerOrderKey">
        <sequence>

            <element name="item" type="om-v1-0:CustomerOrderKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerOrderKeyResult">
        <annotation>
            <documentation>
                The CustomerOrderKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a CustomerOrderValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestKeyResult">
                <sequence>
                     <element name="customerOrderKey" type="om-v1-0:CustomerOrderKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerOrderKeyResult">

        <sequence>
            <element name="item" type="om-v1-0:CustomerOrderKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of CustomerOrder -->
    <!-- Tigerstripe : End of Entity definition for CustomerOrder -->
    <!-- Tigerstripe : Entity definitions for CustomerOrderSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="CustomerOrderSpecificationValue" >
        <annotation>

            <documentation>

 Specifications can be obtained from the {@link javax.oss.om.JVTOrderManagementSession#getRequestSpecifications} and contain supported item types, transitions etc, see {@link javax.oss.om.bi.RequestSpecificationValue}. 
 See {@link javax.oss.om.CustomerOrderValue} for details.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationValue" >    
                <sequence>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerOrderSpecificationValue">
        <sequence>
            <element name="item" type="om-v1-0:CustomerOrderSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerOrderSpecificationKey">
        <annotation>

            <documentation>
                This CustomerOrderSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a CustomerOrderSpecificationValue. The type of the 
                primary key for this CustomerOrderSpecificationKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfCustomerOrderSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:CustomerOrderSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerOrderSpecificationKeyResult">
        <annotation>
            <documentation>
                The CustomerOrderSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a CustomerOrderSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKeyResult">
                <sequence>
                     <element name="customerOrderSpecificationKey" type="om-v1-0:CustomerOrderSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfCustomerOrderSpecificationKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:CustomerOrderSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of CustomerOrderSpecification -->
    <!-- Tigerstripe : End of Entity definition for CustomerOrderSpecification -->
    <!-- Tigerstripe : Entity definitions for ProductOrder  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductOrderValue" >

        <annotation>
            <documentation>

 A Product Order is an order intended to carry out some action on a Product (or Product Specification or Product Offering) e.g. create a Product (using a Product Specification), modify a Product (that has been previously created) or remove a Product. Product Orders represent orders that would typically originate at an end user. Product Orders usually decompose into Service Orders. 
E.g. a specific request to subscribe to the VOIP offering Product.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:CustomerOrderValue" >    
                <sequence>
                    <element name="productOrderItems" type="om-v1-0:ArrayOfProductOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="failedProductOrderItems" type="om-v1-0:ArrayOfProductOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderValue">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="ProductOrderKey">
        <annotation>
            <documentation>
                This ProductOrderKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductOrderValue. The type of the 
                primary key for this ProductOrderKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:CustomerOrderKey">        
                <sequence/>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderKey">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOrderKeyResult">

        <annotation>
            <documentation>
                The ProductOrderKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductOrderValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:CustomerOrderKeyResult">
                <sequence>
                     <element name="productOrderKey" type="om-v1-0:ProductOrderKey" nillable="true" minOccurs="0"/>

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductOrder -->
    <!-- Tigerstripe : End of Entity definition for ProductOrder -->
    <!-- Tigerstripe : Entity definitions for ProductOrderItem  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductOrderItemValue" >
        <annotation>
            <documentation>

An Order Item is the actual specific instruction that is to be carried out. One or more Order Items may be present within a Product Order. Order Items have Action types e.g. Create, Modify and Remove.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemValue" >    
                <sequence>
                    <element name="product" type="cbeproduct-v1-5:ProductValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="productOffering" type="cbeproductoffering-v1-5:ProductOfferingValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="productSpecification" type="cbeproduct-v1-5:ProductSpecificationValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfProductOrderItemValue">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderItemValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOrderItemKey">
        <annotation>
            <documentation>

                This ProductOrderItemKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductOrderItemValue. The type of the 
                primary key for this ProductOrderItemKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderItemKey">

        <sequence>
            <element name="item" type="om-v1-0:ProductOrderItemKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOrderItemKeyResult">
        <annotation>
            <documentation>
                The ProductOrderItemKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductOrderItemValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKeyResult">
                <sequence>
                     <element name="productOrderItemKey" type="om-v1-0:ProductOrderItemKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductOrderItemKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderItemKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductOrderItem -->
    <!-- Tigerstripe : End of Entity definition for ProductOrderItem -->
    <!-- Tigerstripe : Entity definitions for ProductOrderSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductOrderSpecificationValue" >

        <annotation>
            <documentation>

 Specifications can be obtained from the {@link javax.oss.om.JVTOrderManagementSession#getRequestSpecifications getRequestSpecifications} and contain supported item types, transitions etc, see {@link javax.oss.om.bi.RequestSpecificationValue}. 
 See {@link javax.oss.om.ProductOrderValue} for details.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:CustomerOrderSpecificationValue" >    
                <sequence>
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderSpecificationValue">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOrderSpecificationKey">

        <annotation>
            <documentation>
                This ProductOrderSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductOrderSpecificationValue. The type of the 
                primary key for this ProductOrderSpecificationKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:CustomerOrderSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfProductOrderSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductOrderSpecificationKeyResult">
        <annotation>
            <documentation>

                The ProductOrderSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductOrderSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:CustomerOrderSpecificationKeyResult">
                <sequence>
                     <element name="productOrderSpecificationKey" type="om-v1-0:ProductOrderSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductOrderSpecificationKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ProductOrderSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductOrderSpecification -->
    <!-- Tigerstripe : End of Entity definition for ProductOrderSpecification -->

    <!-- Tigerstripe : Entity definitions for ResourceOrder  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceOrderValue" >
        <annotation>
            <documentation>

The Resource Order is a request to perform some action on a Resource or Resource Specification. Resource Orders usually result from the decomposition of a Service Order into the sub-requests required to activate Resources. 
E.g. A Request to reboot a Cable Modem.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestValue" >    
                <sequence>

                    <element name="resourceOrderItems" type="om-v1-0:ArrayOfResourceOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="failedResourceOrderItems" type="om-v1-0:ArrayOfResourceOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderValue">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="ResourceOrderKey">
        <annotation>
            <documentation>
                This ResourceOrderKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceOrderValue. The type of the 
                primary key for this ResourceOrderKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "om-v1-0:RequestKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderKey">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="ResourceOrderKeyResult">
        <annotation>
            <documentation>
                The ResourceOrderKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceOrderValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestKeyResult">

                <sequence>
                     <element name="resourceOrderKey" type="om-v1-0:ResourceOrderKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceOrder -->
    <!-- Tigerstripe : End of Entity definition for ResourceOrder -->
    <!-- Tigerstripe : Entity definitions for ResourceOrderItem  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceOrderItemValue" >
        <annotation>
            <documentation>

An Order Item is the actual specific instruction that is to be carried out. One or more Order Items may be present within a Resource Order. Order Items have Action types e.g. Create, Modify and Remove.
            </documentation>

        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemValue" >    
                <sequence>
                    <element name="resource" type="cberesource-v1-5:ResourceValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="resourceSpecification" type="cberesource-v1-5:ResourceSpecificationValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfResourceOrderItemValue">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderItemValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceOrderItemKey">
        <annotation>
            <documentation>

                This ResourceOrderItemKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceOrderItemValue. The type of the 
                primary key for this ResourceOrderItemKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderItemKey">

        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderItemKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceOrderItemKeyResult">
        <annotation>
            <documentation>
                The ResourceOrderItemKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceOrderItemValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKeyResult">
                <sequence>
                     <element name="resourceOrderItemKey" type="om-v1-0:ResourceOrderItemKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfResourceOrderItemKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderItemKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceOrderItem -->
    <!-- Tigerstripe : End of Entity definition for ResourceOrderItem -->
    <!-- Tigerstripe : Entity definitions for ResourceOrderSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceOrderSpecificationValue" >

        <annotation>
            <documentation>

 Specifications can be obtained from the {@link javax.oss.om.JVTOrderManagementSession#getRequestSpecifications getRequestSpecifications} and contain supported item types, transitions etc, see {@link javax.oss.om.bi.RequestSpecificationValue}. 
 See {@link javax.oss.om.ResourceOrderValue} for details.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationValue" >    
                <sequence>
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderSpecificationValue">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceOrderSpecificationKey">

        <annotation>
            <documentation>
                This ResourceOrderSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceOrderSpecificationValue. The type of the 
                primary key for this ResourceOrderSpecificationKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfResourceOrderSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceOrderSpecificationKeyResult">
        <annotation>
            <documentation>

                The ResourceOrderSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceOrderSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKeyResult">
                <sequence>
                     <element name="resourceOrderSpecificationKey" type="om-v1-0:ResourceOrderSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceOrderSpecificationKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ResourceOrderSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceOrderSpecification -->
    <!-- Tigerstripe : End of Entity definition for ResourceOrderSpecification -->

    <!-- Tigerstripe : Entity definitions for ServiceOrder  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceOrderValue" >
        <annotation>
            <documentation>

A Service Order is a request to carry out some action on a Service or Service Specification in support of a Product Order. Actions may include Create, Modify and Remove. Service Orders usually decompose into Resource Orders.  
E.g. a request to configure the DSL or Cable Data Service in order to support the VOIP Product 
This interface replaces the deprecated OrderValue from JSR 89 (OSS Service Activation API)
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestValue" >    
                <sequence>

                    <element name="serviceOrderItems" type="om-v1-0:ArrayOfServiceOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="failedServiceOrderItems" type="om-v1-0:ArrayOfServiceOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderValue">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="ServiceOrderKey">
        <annotation>
            <documentation>
                This ServiceOrderKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceOrderValue. The type of the 
                primary key for this ServiceOrderKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "om-v1-0:RequestKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderKey">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="ServiceOrderKeyResult">
        <annotation>
            <documentation>
                The ServiceOrderKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceOrderValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestKeyResult">

                <sequence>
                     <element name="serviceOrderKey" type="om-v1-0:ServiceOrderKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ServiceOrder -->
    <!-- Tigerstripe : End of Entity definition for ServiceOrder -->
    <!-- Tigerstripe : Entity definitions for ServiceOrderItem  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceOrderItemValue" >
        <annotation>
            <documentation>

An Order Item is the actual specific instruction that is to be carried out. One or more Order Items may be present within a Service Order. Order Items have Action types e.g. Create, Modify and Remove.
            </documentation>

        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemValue" >    
                <sequence>
                    <element name="service" type="cbeservice-v1-5:ServiceValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="serviceSpecification" type="cbeservice-v1-5:ServiceSpecificationValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfServiceOrderItemValue">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderItemValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceOrderItemKey">
        <annotation>
            <documentation>

                This ServiceOrderItemKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceOrderItemValue. The type of the 
                primary key for this ServiceOrderItemKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderItemKey">

        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderItemKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceOrderItemKeyResult">
        <annotation>
            <documentation>
                The ServiceOrderItemKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceOrderItemValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKeyResult">
                <sequence>
                     <element name="serviceOrderItemKey" type="om-v1-0:ServiceOrderItemKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfServiceOrderItemKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderItemKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ServiceOrderItem -->
    <!-- Tigerstripe : End of Entity definition for ServiceOrderItem -->
    <!-- Tigerstripe : Entity definitions for ServiceOrderSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceOrderSpecificationValue" >

        <annotation>
            <documentation>

 Specifications can be obtained from the {@link javax.oss.om.JVTOrderManagementSession#getRequestSpecifications getRequestSpecifications} and contain supported item types, transitions etc. See also {@link javax.oss.om.bi.RequestSpecificationValue}. 
 See {@link javax.oss.om.ServiceOrderValue} for details.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationValue" >    
                <sequence>
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderSpecificationValue">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceOrderSpecificationKey">

        <annotation>
            <documentation>
                This ServiceOrderSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceOrderSpecificationValue. The type of the 
                primary key for this ServiceOrderSpecificationKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfServiceOrderSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceOrderSpecificationKeyResult">
        <annotation>
            <documentation>

                The ServiceOrderSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceOrderSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKeyResult">
                <sequence>
                     <element name="serviceOrderSpecificationKey" type="om-v1-0:ServiceOrderSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceOrderSpecificationKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:ServiceOrderSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ServiceOrderSpecification -->
    <!-- Tigerstripe : End of Entity definition for ServiceOrderSpecification -->

    <!-- Tigerstripe : Entity definitions for WorkOrder  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="WorkOrderValue" >
        <annotation>
            <documentation>

The Work Order is a request to a Workforce Management function to perform some, possibly manual, action. Product, Service or Resource Orders may all result in one or more WorkOrders being created in order to support the activation requirements of a Customer Order.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestValue" >    
                <sequence>

                    <element name="workOrderItems" type="om-v1-0:ArrayOfWorkOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="failedWorkOrderItems" type="om-v1-0:ArrayOfWorkOrderItemValue" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderValue">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="WorkOrderKey">
        <annotation>
            <documentation>
                This WorkOrderKey encapsulates all the information that is necessary to 
                identify a particular instance of a WorkOrderValue. The type of the 
                primary key for this WorkOrderKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "om-v1-0:RequestKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderKey">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="WorkOrderKeyResult">
        <annotation>
            <documentation>
                The WorkOrderKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a WorkOrderValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestKeyResult">

                <sequence>
                     <element name="workOrderKey" type="om-v1-0:WorkOrderKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of WorkOrder -->
    <!-- Tigerstripe : End of Entity definition for WorkOrder -->
    <!-- Tigerstripe : Entity definitions for WorkOrderItem  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="WorkOrderItemValue" >
        <annotation>
            <documentation>

An Order Item is the actual specific instruction that is to be carried out. One or more Order Items may be present within a Work Order. The WorkOrder contains possibly many items, each identifying a portion of the work to be done. This way the work can be decomposed into manageable/reusable pieces as well as associated to other items in customer, product, service or resource orders.Order Items have Action types e.g. Create, Modify and Remove.
            </documentation>

        </annotation>

        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemValue" >    
                <sequence>
                    <element name="workorder" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="workorderInstructions" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfWorkOrderItemValue">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderItemValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="WorkOrderItemKey">
        <annotation>
            <documentation>

                This WorkOrderItemKey encapsulates all the information that is necessary to 
                identify a particular instance of a WorkOrderItemValue. The type of the 
                primary key for this WorkOrderItemKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderItemKey">

        <sequence>
            <element name="item" type="om-v1-0:WorkOrderItemKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="WorkOrderItemKeyResult">
        <annotation>
            <documentation>
                The WorkOrderItemKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a WorkOrderItemValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbebi-v1-5:BusinessInteractionItemKeyResult">
                <sequence>
                     <element name="workOrderItemKey" type="om-v1-0:WorkOrderItemKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfWorkOrderItemKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderItemKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of WorkOrderItem -->
    <!-- Tigerstripe : End of Entity definition for WorkOrderItem -->
    <!-- Tigerstripe : Entity definitions for WorkOrderSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="WorkOrderSpecificationValue" >

        <annotation>
            <documentation>

 Specifications can be obtained from the {@link javax.oss.om.JVTOrderManagementSession#getRequestSpecifications getRequestSpecifications} and contain supported item types, transitions etc, see {@link javax.oss.om.bi.RequestSpecificationValue}. 
 See {@link javax.oss.om.WorkOrderValue} for details.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationValue" >    
                <sequence>
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderSpecificationValue">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="WorkOrderSpecificationKey">

        <annotation>
            <documentation>
                This WorkOrderSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a WorkOrderSpecificationValue. The type of the 
                primary key for this WorkOrderSpecificationKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfWorkOrderSpecificationKey">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="WorkOrderSpecificationKeyResult">
        <annotation>
            <documentation>

                The WorkOrderSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a WorkOrderSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "om-v1-0:RequestSpecificationKeyResult">
                <sequence>
                     <element name="workOrderSpecificationKey" type="om-v1-0:WorkOrderSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfWorkOrderSpecificationKeyResult">
        <sequence>
            <element name="item" type="om-v1-0:WorkOrderSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of WorkOrderSpecification -->
    <!-- Tigerstripe : End of Entity definition for WorkOrderSpecification -->

    <!-- Tigerstripe : Datatype definitions for ItemTypeSupportedActionsPair  (basic, ArrayOf) -->
    <complexType name="ItemTypeSupportedActionsPair">
        <annotation>
            <documentation>
This components list the supported actions related to a Business Interaction Item type. It is mainly used to describe a Request.
            </documentation>
        </annotation>
                <sequence>
                    <element name="itemType" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="actions" type="om-v1-0:ArrayOfActions" minOccurs="0"/>
                </sequence>
    </complexType>
    <complexType name="ArrayOfItemTypeSupportedActionsPair">
        <sequence>
            <element name="item" type="om-v1-0:ItemTypeSupportedActionsPair" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ItemTypeSupportedActionsPair -->

    <element name="baseActions_ItemTypeSupportedActionsPair" type="string" abstract="true"/>
     
    <element name="actions_ItemTypeSupportedActionsPair" 
        type="om-v1-0:BusinessInteractionItemAction" 
        substitutionGroup="om-v1-0:baseActions_ItemTypeSupportedActionsPair" /> 

    <complexType name="ArrayOfActions">
        <sequence>
            <element ref="om-v1-0:baseActions_ItemTypeSupportedActionsPair" maxOccurs="unbounded" minOccurs="0"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : End of Datatype definition for ItemTypeSupportedActionsPair -->
    <!-- Tigerstripe : Datatype definitions for RequestStateTransition  (basic, ArrayOf) -->

    <complexType name="RequestStateTransition">
        <annotation>
            <documentation>
This type defines the possible transitions between request states. All transitions must be   
- Between START and NOT_STARTED or substates of NOT_STARTED 
- Between START and RUNNING or substates of RUNNING 
- Between NOT_STARTED or a substates of NOT_STARTED and RUNNING or substates of RUNNING 
- Between RUNNING or substates of RUNNING and SUSPENDED or substates of SUSPENDED 
- Between RUNNING or substates of RUNNING and AWAITING_INPUT or substates of AWAITING_INPUT 
- Between RUNNING or substates of RUNNING and AWAITING_VALIDATION or substates of AWAITING_VALIDATION 
- Between SUSPENDED or substates of SUSPENDED and RUNNING or substates of RUNNING 
- Between RUNNING or substates of RUNNING and COMPLETED or substates of COMPLETED 
- Between OPEN or substates of OPEN and ABORTED_BYCLIENT or substates of ABORTED_BYCLIENT 
- Between OPEN or substates of OPEN and ABORTED_BYSERVER or substates of ABORTED_BYSERVER 
- Between CLOSED or substates of CLOSED and END 

            </documentation>
        </annotation>
                <sequence>
                    <element ref="om-v1-0:baseOriginatingState_RequestStateTransition" minOccurs="0"/>
                    <element ref="om-v1-0:baseTargetState_RequestStateTransition" minOccurs="0"/>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                </sequence>
    </complexType>
    <complexType name="ArrayOfRequestStateTransition">
        <sequence>
            <element name="item" type="om-v1-0:RequestStateTransition" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestStateTransition -->
    <element name="baseOriginatingState_RequestStateTransition" type="string" abstract="true"/>

     
    <element name="originatingState_RequestStateTransition" 
        type="om-v1-0:RequestState" 
        substitutionGroup="om-v1-0:baseOriginatingState_RequestStateTransition" /> 

    <element name="originatingState_RequestStateTransitionBusinessInteractionState" 
        type="cbebi-v1-5:BusinessInteractionState" 
        substitutionGroup="om-v1-0:baseOriginatingState_RequestStateTransition"/>

    <element name="originatingState_RequestStateTransitionState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="om-v1-0:baseOriginatingState_RequestStateTransition"/>

    <element name="baseTargetState_RequestStateTransition" type="string" abstract="true"/>
     
    <element name="targetState_RequestStateTransition" 
        type="om-v1-0:RequestState" 
        substitutionGroup="om-v1-0:baseTargetState_RequestStateTransition" /> 

    <element name="targetState_RequestStateTransitionBusinessInteractionState" 
        type="cbebi-v1-5:BusinessInteractionState" 
        substitutionGroup="om-v1-0:baseTargetState_RequestStateTransition"/>

    <element name="targetState_RequestStateTransitionState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="om-v1-0:baseTargetState_RequestStateTransition"/>

    <!-- Tigerstripe : End of Datatype definition for RequestStateTransition -->

    <!-- Tigerstripe : Enumeration definitions for BusinessInteractionBulk  -->
    <simpleType name="BusinessInteractionBulk">
        <annotation>
            <documentation>
Describes weather a BusinessInteraction acts on its own, or is an aggregation of other BusinessInteractions. This attribute is controlled by the server and set to one of the values below.
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="single" />

            <enumeration value="bulk.ancestor" />
            <enumeration value="bulk.parent" />
            <enumeration value="bulk.child" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for BusinessInteractionBulk  -->
    <!-- Tigerstripe : Enumeration definitions for BusinessInteractionItemAction  -->
    <simpleType name="BusinessInteractionItemAction">
        <annotation>

            <documentation>

            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="cancel" />
            <enumeration value="create" />
            <enumeration value="modify" />
        </restriction>

    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for BusinessInteractionItemAction  -->
    <!-- Tigerstripe : Enumeration definitions for RequestPriority  -->
    <simpleType name="RequestPriority">
        <annotation>
            <documentation>

Constants defining the possible priorities that can be used with {@link RequestValue#setPriority(int)}.  Requests with the priority EXPEDITE are processed first, follow by HIGH, NORMAL, MEDIUM and the Requests with lowest priority are with priority LOW.
            </documentation>
        </annotation>

        <restriction base="int">
            <!-- name = UNDEFINED -->
            <enumeration value="0" />
            <!-- name = LOW -->
            <enumeration value="1" />
            <!-- name = MEDIUM -->
            <enumeration value="2" />
            <!-- name = NORMAL -->
            <enumeration value="3" />

            <!-- name = HIGH -->
            <enumeration value="4" />
            <!-- name = EXPEDITE -->
            <enumeration value="5" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for RequestPriority  -->
    <!-- Tigerstripe : Enumeration definitions for RequestState  -->
    <simpleType name="RequestState">

        <annotation>
            <documentation>

This enumeration lists all states, that a request can be in. The only two exceptions are the states START and END, which are solely existing to allow to design a complete state model in the RequestSpecification as it is used in the {@link RequestStateTransition}.   
All states, except START and END, can be extended by appending a substate to a state, with a {@link javax.oss.cbe.datatypes.State#SEPARATOR} seperating them. Substates may only contain upper- and lowercase characters, numbers and underscores '_'.  
The states PARTIALLY_OPEN, PARTIALLY_NOT_RUNNING, PARTIALLY_NOT_STARTED, PARTIALLY_SUSPENDED, PARTIALLY_RUNNING, PARTIALLY_CLOSED, PARTIALLY_COMPLETED are reserved for bulk Requests. Please see {@link javax.oss.om.BulkBusinessInteractionRelationshipValue} for details.
  Request State Diagram 

            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="open" />
            <enumeration value="closed" />
            <enumeration value="closed.aborted" />

            <enumeration value="closed.aborted.aborted_byclient" />
            <enumeration value="closed.aborted.aborted_byserver" />
            <enumeration value="closed.completed" />
            <enumeration value="open.not_running" />
            <enumeration value="open.running" />
            <enumeration value="open.not_running.suspended" />
            <enumeration value="open.not_running.suspended.awaiting_input" />
            <enumeration value="open.not_running.suspended.awaiting_validation" />
            <enumeration value="open.not_running.not_started" />

            <enumeration value=".start" />
            <enumeration value=".end" />
            <enumeration value="open.partially" />
            <enumeration value="open.not_running.partially" />
            <enumeration value="open.not_running.not_started.partially" />
            <enumeration value="open.not_running.suspended.partially" />
            <enumeration value="open.running.partially" />
            <enumeration value="closed.partially" />
            <enumeration value="closed.completed.partially" />

        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for RequestState  -->
	
    <element name="interactionStatus_BusinessInteractionRequestState" 
        type="om-v1-0:RequestState" 
        substitutionGroup="cbebi-v1-5:baseInteractionStatus_BusinessInteraction"/>


    <!-- Tigerstripe : Exception definition for IncompleteClientInputException  -->
    <complexType name="IncompleteClientInputException">
        <annotation>
            <documentation>

This Exception is thrown when a client tries to resume a request, which  
- Is in the state {@link javax.oss.om.bi.RequestState#AWAITING_INPUT} or a substate of that state; means the server required some information from the client, before it can continue to process the request. 
- That client has failed to provide that required information, before it tried to resume the request. 
 
Via the attribute {@link #getRequiredInformation() requiredInformation}, the IncompleteClientInputException will also inform the client about what information is still missing, before the request can be resumed successfully.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:OssIllegalStateException" >
                <sequence>
                    <element name="requiredInformation" type="co-v1-5:ArrayOfString" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
             </extension>

        </complexContent>
    </complexType>
    <!-- Tigerstripe : End of Exception definition for IncompleteClientInputException  -->

    <!-- Tigerstripe : Query definitions for AllOrdersQuery  -->
    <complexType name="AllOrdersQueryValue">
        <annotation>
            <documentation>

 Interface to query all requests, regardless of their current interactionStatus. 
The result can be sorted based on some attributes, by setting:  
- {@link #setSortingAttributes sortingAttributes} - see this for more details 
- {@link #setSortAscending sortAscending}. 
  About {@link #setSortingAttributes sortingAttributes}  
 The server will sort the results based on these attributes, in the given sequence (the first attribute has the highest priority etc). 
 If not populated, the sorting done by the server, if any, is unspecified and depends on the implementation. 
 To ensure a consistent behavior, the attributes should be valid for all the types of requests supported by the implementation (i.e. ProductOrder, ResourceOrder etc), including the optional attributes supported. If an attribute is not defined or optional and not supported in a request, they will be sorted behind those entities, that support that attribute. 
 As the server will most likely return different request types (if supported), not all attributes may be applicable to all request types returned. In this case, the server will do its best to sort and will ignore attributes that do not apply to some request types, for the respective requests. The ignored attributes will be applied to the requests that support them. If an attribute does not apply to any request returned, it will be ignored. 
 As best practices, put the common attributes first in the array and the ones that may not apply to all request types towards the end. 
 It is mandatory for all implementations that support this query, to be able to sort on all common simple attributes from {@link javax.oss.om.bi.BusinessInteractionValue} and {@link javax.oss.om.bi.RequestValue} as well as all simple attributes from the respective request types, i.e. {@link javax.oss.om.CustomerOrderValue}. 
 By simple we mean attributes that are simple types (String, ManagedEntityKey etc). An implementation may choose not to support sorting by attributes that are an array, such as {@link javax.oss.om.ProductOrderValue#PRODUCT_ORDER_ITEMS} for example.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "co-v1-5:NamedQueryValue" >    
                <sequence>
                    <element name="sortingAttributes" type="co-v1-5:ArrayOfString" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="sortAscending" type="co-v1-5:ArrayOfBoolean" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="AllOrdersQueryResponse">
        <annotation>
            <documentation>
                The AllOrdersQueryResponse collects the result 
                of query using AllOrdersQueryRequest.
            </documentation>
        </annotation>   
        <complexContent>
            <extension base = "co-v1-5:NamedQueryResponse" >
               <sequence>

                  <element name="sequence" type="int" minOccurs="0"/>
                  <element name="endOfReply" type="boolean" minOccurs="0"/>
                  <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"/>
               </sequence>
            </extension>
        </complexContent>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AllOrdersQuery -->
    <!-- Tigerstripe : End of Query definitions for AllOrdersQuery  -->

    
    
    
    <!-- Tigerstripe : Query definitions for ChildrenFromBulkParentQuery  -->
    <complexType name="ChildrenFromBulkParentQueryValue">
        <annotation>
            <documentation>

This query returns all children of a request that are the parent of a {@link BulkBusinessInteractionRelationshipValue BulkBusinessInteractionRelationship}.  
If a request is participating in a {@link BulkBusinessInteractionRelationshipValue BulkBusinessInteractionRelationship}, and {@link javax.oss.om.bi.RequestValue#getBulk()} returns either {@link javax.oss.om.bi.BusinessInteractionBulk#BULK_PARENT} or {@link javax.oss.om.bi.BusinessInteractionBulk#BULK_ANCESTOR}, then this query returns all child requests that are identified as follows:  
- For all relationships as returned by the parents {@link javax.oss.cbe.bi.BusinessInteractionValue#getReferences()} method. 
- For all returned references, that are of type {@link BulkBusinessInteractionRelationshipValue BulkBusinessInteractionRelationship} 
- All Request as identified by the javax.oss.cbe.AssociationValue#Z_END_KEY}. 

            </documentation>
        </annotation>
        <complexContent>
            <extension base = "co-v1-5:NamedQueryValue" >    
                <sequence>

                    <element name="parent" type="om-v1-0:RequestKey" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ChildrenFromBulkParentQueryResponse">
        <annotation>
            <documentation>
                The ChildrenFromBulkParentQueryResponse collects the result 
                of query using ChildrenFromBulkParentQueryRequest.
            </documentation>

        </annotation>   
        <complexContent>
            <extension base = "co-v1-5:NamedQueryResponse" >
               <sequence>
                  <element name="sequence" type="int" minOccurs="0"/>
                  <element name="endOfReply" type="boolean" minOccurs="0"/>
                  <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"/>
               </sequence>
            </extension>

        </complexContent>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ChildrenFromBulkParentQuery -->
    <!-- Tigerstripe : End of Query definitions for ChildrenFromBulkParentQuery  -->
    
    
    


    <!-- Tigerstripe : Event definitions for AwaitingClientInputEvent  -->
    <element name="AwaitingClientInputEvent">
        <annotation>
            <documentation>

Event that is sent if a request has changed its interactionStatus to {@link javax.oss.om.bi.RequestState#AWAITING_INPUT AWAITING_INPUT}.  
The client is now requested to provide more input for the request, before it can be processed. The specific information that has to be provided by the client is contained in the attribute requiredInformation. This is an Array of Strings that contains the attribute names as they are returned by AttributeAccess.getAttributeNames(). Note that for a request, which extends from {@link javax.oss.cbe.CBEManagedEntityValue}, also the {@link javax.oss.cbe.CharacteristicSpecification} is returned by that method and can be part of the required information.  
There are three possible scenarios:  
- Scenario 1: Client provides all data  
- The client provides all the information by calling {@link JVTOrderManagementSession#setRequestByValue(javax.oss.om.bi.RequestValue, boolean) setRequestByValue} or {@link JVTOrderManagementSession#trySetRequestsByKeys(javax.oss.om.bi.RequestKey[], javax.oss.om.bi.RequestValue) trySetRequestsByKeys}. 
- The client resumes the request after all requested data has been set by calling {@link JVTOrderManagementSession#resumeRequestByKey(javax.oss.om.bi.RequestKey) resumeRequestByKey}, {@link JVTOrderManagementSession#resumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) resumeRequestByKeys} or {@link JVTOrderManagementSession#tryResumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) tryResumeRequestsByKeys}. 
- Since all requested information has been provided, resuming the request succeeds.  
- Scenario 2:  
- The client provides some of the requested information by calling {@link JVTOrderManagementSession#setRequestByValue(javax.oss.om.bi.RequestValue, boolean) setRequestByValue} or {@link JVTOrderManagementSession#trySetRequestsByKeys(javax.oss.om.bi.RequestKey[], javax.oss.om.bi.RequestValue) trySetRequestsByKeys}. 
- The client resumes the request after all requested data has been set by calling {@link JVTOrderManagementSession#resumeRequestByKey(javax.oss.om.bi.RequestKey) resumeRequestByKey}, {@link JVTOrderManagementSession#resumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) resumeRequestByKeys} or {@link JVTOrderManagementSession#tryResumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) tryResumeRequestsByKeys}. 
- Not all requested information has been provided by the client. On resuming the request, the server will throw an {@link IncompleteClientInputException}, which will also contain information about what data is still missing. The client can then try again: provide that data, and resume the request again.  
- Scenario 3:  
- The request is not resumed successfully, before the {@link #getRequestedResponseDate() requestedResponseDate} This will result in an abort of the request by the server, which may occur at any time defined by the server.  

            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="om-v1-0:AwaitingClientInputEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="AwaitingClientInputEventType">

        <complexContent>
            <extension base = "om-v1-0:RequestStateChangeEventType" >    
                <sequence>
                    <element name="requiredInformation" type="co-v1-5:ArrayOfString" minOccurs="0" />
                    <element name="requestedResponseDate" type="dateTime" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AwaitingClientInputEvent -->
    <!-- Tigerstripe : End of Event definitions for AwaitingClientInputEvent  -->
    <!-- Tigerstripe : Event definitions for AwaitingClientValidationEvent  -->
    <element name="AwaitingClientValidationEvent">
        <annotation>
            <documentation>

Event that is sent if a request has changed its interactionStatus to {@link javax.oss.om.bi.RequestState#AWAITING_VALIDATION AWAITING_VALIDATION}.  The client is now requested to validate the state of the Request. There are three possible scenarios:  
- The client validates and approves the state of the Request. The Request is resumed by calling {@link JVTOrderManagementSession#resumeRequestByKey(javax.oss.om.bi.RequestKey) resumeRequestByKey}, {@link JVTOrderManagementSession#resumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) resumeRequestByKeys} or {@link JVTOrderManagementSession#tryResumeRequestsByKeys(javax.oss.om.bi.RequestKey[]) tryResumeRequestsByKeys}. 
- The client validates and does not approve the state of the Request. The Request is aborted by calling {@link JVTOrderManagementSession#abortRequestByKey(javax.oss.om.bi.RequestKey) abortRequestByKey}, {@link JVTOrderManagementSession#abortRequestsByKeys(javax.oss.om.bi.RequestKey[]) abortRequestByKeys} or {@link JVTOrderManagementSession#tryAbortRequestsByKeys(javax.oss.om.bi.RequestKey[]) tryAbortRequestsByKeys}. 
- The client does not react in a timely manner, i.e. date and time provided to the client in the attribute requestedValidationDate has passed, and the client neither resumed nor aborted the method. This will result in an abort of the request by the server. 
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="event" type="om-v1-0:AwaitingClientValidationEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="AwaitingClientValidationEventType">
        <complexContent>
            <extension base = "om-v1-0:RequestStateChangeEventType" >    
                <sequence>

                    <element name="requestedValidationDate" type="dateTime" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of AwaitingClientValidationEvent -->
    <!-- Tigerstripe : End of Event definitions for AwaitingClientValidationEvent  -->
    <!-- Tigerstripe : Event definitions for RequestAttributeValueChangeEvent  -->

    <element name="RequestAttributeValueChangeEvent">
        <annotation>
            <documentation>

Event that is sent if any attribute of a request has changed. 
 A message is sent when:  
- setRequestByValue has caused changes to attribute values of a request. 
- some values of a request have been changed during the execution of a request by the implementation. In this case, the implementation must publish a message, latest before the request completes. 
 This message is not published if the interactionStatus of a request has changed, see {@link RequestStateChangeEvent} instead. 
 The following attributes are populated:  
- The changed attributes. The attribute value contains the new value. The change can be initiated by either the client or the implementation. 
- The request key. The attribute value is used by the client to identify which request has changed. 
- The above rules have to be applied recursivly to contained attributes, that inherit from ManagedEntityValue. If an attribute is an array of ManagedEntityValue, or an interface derived from ManagedEntityValue, the keys for all elements of the array have to be set. 
 
 For additional specifications of the values of this event and of properties of the related JMS message, see  
- {@link JVTOrderManagementSession#setRequestByValue(javax.oss.om.bi.RequestValue, boolean) setRequestByValue} 
- {@link JVTOrderManagementSession#setRequestsByValues(javax.oss.om.bi.RequestValue[], boolean) setRequestsByValues} 
- {@link JVTOrderManagementSession#trySetRequestsByValues(javax.oss.om.bi.RequestValue[], boolean) trySetRequestsByValues} 
- {@link JVTOrderManagementSession#setRequestsByKeys(javax.oss.om.bi.RequestKey[], javax.oss.om.bi.RequestValue) setRequestsByKeys} 
- {@link JVTOrderManagementSession#trySetRequestsByKeys(javax.oss.om.bi.RequestKey[], javax.oss.om.bi.RequestValue) trySetRequestsByKeys} 

            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="om-v1-0:RequestAttributeValueChangeEventType"/>

            </sequence>
        </complexType>
    </element>
    <complexType name="RequestAttributeValueChangeEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="newRequestValue" type="om-v1-0:RequestValue" minOccurs="0" />
                </sequence>

            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestAttributeValueChangeEvent -->
    <!-- Tigerstripe : End of Event definitions for RequestAttributeValueChangeEvent  -->
    <!-- Tigerstripe : Event definitions for RequestCreateEvent  -->
    <element name="RequestCreateEvent">
        <annotation>

            <documentation>

 Event that is sent if a new request has been created. 
 For a precise specification of the values of this event and of properties of the related JMS message, see:  
- {@link JVTOrderManagementSession#createRequestByValue(javax.oss.om.bi.RequestValue) createRequestByValue} 
- {@link JVTOrderManagementSession#createRequestByValue(javax.oss.om.bi.RequestValue) createRequestByValue} 
- {@link JVTOrderManagementSession#tryCreateRequestsByValues(javax.oss.om.bi.RequestValue[]) tryCreateRequestByValues} 
- {@link JVTOrderManagementSession#createAndStartRequestByValue(javax.oss.om.bi.RequestValue) createAndStartRequestByValue} 
- {@link JVTOrderManagementSession#createAndStartRequestByValue(javax.oss.om.bi.RequestValue) createAndStartRequestByValue} 
- {@link JVTOrderManagementSession#tryCreateAndStartRequestsByValues(javax.oss.om.bi.RequestValue[]) tryCreateAndStartRequestByValues} 

            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="om-v1-0:RequestCreateEventType"/>
            </sequence>
        </complexType>

    </element>
    <complexType name="RequestCreateEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="requestValue" type="om-v1-0:RequestValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestCreateEvent -->
    <!-- Tigerstripe : End of Event definitions for RequestCreateEvent  -->
    <!-- Tigerstripe : Event definitions for RequestRemoveEvent  -->
    <element name="RequestRemoveEvent">
        <annotation>
            <documentation>
Event that is sent if a request has been removed. 
 For a specifications of the values of this event and of properties of the related JMS message, see {@link JVTOrderManagementSession} for all flavors of the remove methods.  The contained RequestValue is fully populated.  
- Especially it contains all keys for all contained and associated objects that derive from {@link javax.oss.ManagedEntityValue}. Note that this is the last chance for a client to read the keys for contained items and/or values, specifications and offerings, before the request is removed; this is especially important to know when the key is assigned to the entity very late in the lifecycle of a request. 
- All attributes are set to the latest value. 

            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="event" type="om-v1-0:RequestRemoveEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="RequestRemoveEventType">
        <complexContent>

            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>
                    <element name="requestValue" type="om-v1-0:RequestValue" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestRemoveEvent -->
    <!-- Tigerstripe : End of Event definitions for RequestRemoveEvent  -->

    <!-- Tigerstripe : Event definitions for RequestStateChangeEvent  -->
    <element name="RequestStateChangeEvent">
        <annotation>
            <documentation>

Event that is sent if a request has changed its interactionStatus. 
 A message is sent when:  
-  while executing the request, the interactionStatus of the request has been changed by the implementation. If the interactionStatus change is also a change in one of the super states defined in {@link javax.oss.om.bi.RequestState}, an implementation must publish an event. If the change is only a change in sub states (i.e. there is no change for the states defined in {@link javax.oss.om.bi.RequestState}), an implementation may publish an event. 
Note: For some special cases a subtype of the RequestStateChangeEvent has to be sent. See {@link AwaitingClientValidationEvent} and {@link AwaitingClientInputEvent} for details. 
- One of the methods {@link javax.oss.om.JVTOrderManagementSession#request_lifetime that manage the lifetime of a Request} has been called. 
 
  For additional specifications of the values of this event and of properties of the related JMS message, see section &quot;Managing the lifetime of requests&quot; in {@link JVTOrderManagementSession}.
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="event" type="om-v1-0:RequestStateChangeEventType"/>
            </sequence>
        </complexType>
    </element>
    <complexType name="RequestStateChangeEventType">
        <complexContent>
            <extension base = "co-v1-5:BaseEventType" >    
                <sequence>

                    <element name="reason" type="string" minOccurs="0" />
                    <element ref="om-v1-0:baseCurrentState_RequestStateChangeEvent" minOccurs="0"/>
                    <element name="requestKey" type="om-v1-0:RequestKey" minOccurs="0" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of RequestStateChangeEvent -->

    <element name="baseCurrentState_RequestStateChangeEvent" type="string" abstract="true"/>
     
    <element name="currentState_RequestStateChangeEvent" 
        type="om-v1-0:RequestState" 
        substitutionGroup="om-v1-0:baseCurrentState_RequestStateChangeEvent" /> 

    <element name="currentState_RequestStateChangeEventBusinessInteractionState" 
        type="cbebi-v1-5:BusinessInteractionState" 
        substitutionGroup="om-v1-0:baseCurrentState_RequestStateChangeEvent"/>

    <element name="currentState_RequestStateChangeEventState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="om-v1-0:baseCurrentState_RequestStateChangeEvent"/>

    <!-- Tigerstripe : End of Event definitions for RequestStateChangeEvent  -->

    <!-- Tigerstripe : Session entity Operations -->
    <!-- Tigerstripe : Managed Entity Operations for RequestSpecification ( NB These may be overridden in session)  -->
    <element name="getRequestSpecificationByKeyRequest">

        <annotation>
            <documentation>
                This is the Request for the getRequestSpecificationByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestSpecificationKey" type="om-v1-0:RequestSpecificationKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />

            </sequence>
        </complexType>
    </element>
    <element name="getRequestSpecificationByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestSpecificationByKey Operation
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="requestSpecificationValue" type="om-v1-0:RequestSpecificationValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getRequestSpecificationByKeyException">
        <annotation>
            <documentation>

                This is the Exception for the getRequestSpecificationByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
            </choice>

        </complexType>
    </element>
    <element name="getRequestSpecificationsByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the getRequestSpecificationsByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <complexContent>
                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="templates" type="om-v1-0:ArrayOfRequestSpecificationValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>
        </complexType>

    </element>
    <element name="getRequestSpecificationsByTemplatesResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestSpecificationsByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>

                <extension base="co-v1-5:IteratorResponse">
                <sequence>
                    <element name="results" type="om-v1-0:ArrayOfRequestSpecificationValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getRequestSpecificationsByTemplatesException">

        <annotation>
            <documentation>
                This is the Exception for the getRequestSpecificationsByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>

                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <!-- Tigerstripe : End of Managed Entity Operations for RequestSpecification  -->
    <!-- Tigerstripe : Managed Entity Operations for Request ( NB These may be overridden in session)  -->
    <element name="createRequestByValueRequest">
        <annotation>

            <documentation>
                This is the Request for the createRequestByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValue" type="om-v1-0:RequestValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="createRequestByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createRequestByValue Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createRequestByValueException">
        <annotation>
            <documentation>
                This is the Exception for the createRequestByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
            </choice>
        </complexType>

    </element>
    <element name="createRequestsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createRequestsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createRequestsByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createRequestsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the createRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryCreateRequestsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the tryCreateRequestsByValues Operation
                This operation is MANDATORY
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateRequestsByValuesResponse">
        <annotation>

            <documentation>
                This is the Response for the tryCreateRequestsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>
        </complexType>

    </element>
    <element name="tryCreateRequestsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the tryCreateRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
    <element name="getRequestByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the getRequestByKey Operation
                This operation is MANDATORY
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getRequestByKeyResponse">

        <annotation>
            <documentation>
                This is the Response for the getRequestByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValue" type="om-v1-0:RequestValue"  />
            </sequence>

        </complexType>
    </element>
    <element name="getRequestByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the getRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
            </choice>
        </complexType>
    </element>
    <element name="getRequestsByKeysRequest">
        <annotation>

            <documentation>
                This is the Request for the getRequestsByKeys Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>

        </complexType>
    </element>
    <element name="getRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the getRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
            </choice>
        </complexType>
    </element>

    <element name="getRequestsByTemplatesRequest">
        <annotation>
            <documentation>
                This is the Request for the getRequestsByTemplates Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorRequest">

                <sequence>
                    <element name="templates" type="om-v1-0:ArrayOfRequestValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getRequestsByTemplatesResponse">

        <annotation>
            <documentation>
                This is the Response for the getRequestsByTemplates Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">
                <sequence>

                    <element name="results" type="om-v1-0:ArrayOfRequestValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getRequestsByTemplatesException">
        <annotation>
            <documentation>

                This is the Exception for the getRequestsByTemplates Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="setRequestByValueRequest">
        <annotation>
            <documentation>
                This is the Request for the setRequestByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="requestValue" type="om-v1-0:RequestValue"  />
                <element name="resyncRequired" type="boolean"/>
            </sequence>
        </complexType>
    </element>
    <element name="setRequestByValueResponse">
        <annotation>

            <documentation>
                This is the Response for the setRequestByValue Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="setRequestByValueException">
        <annotation>
            <documentation>
                This is the Exception for the setRequestByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
            </choice>
        </complexType>
    </element>
    <element name="setRequestsByValuesRequest">

        <annotation>
            <documentation>
                This is the Request for the setRequestsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
                <element name="resyncRequired" type="boolean"/>

            </sequence>
        </complexType>
    </element>
    <element name="setRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the setRequestsByValues Operation
            </documentation>
        </annotation>

        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="setRequestsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the setRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossResyncRequiredException" type="co-v1-5:OssResyncRequiredException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="setRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the setRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />           
                <element name="requestValue" type="om-v1-0:RequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="setRequestsByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the setRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>

    </element>
    <element name="setRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the setRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossSetException" type="co-v1-5:OssSetException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="trySetRequestsByValuesRequest">

        <annotation>
            <documentation>
                This is the Request for the trySetRequestsByValues Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
                <element name="resyncRequired" type="boolean"/>

            </sequence>
        </complexType>
    </element>
    <element name="trySetRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the trySetRequestsByValues Operation
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetRequestsByValuesException">
        <annotation>
            <documentation>

                This is the Exception for the trySetRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>

    </element>
    <element name="trySetRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the trySetRequestsByKeys Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />           
                <element name="requestValue" type="om-v1-0:RequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the trySetRequestsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="trySetRequestsByKeysException">
        <annotation>

            <documentation>
                This is the Exception for the trySetRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>

        </complexType>
    </element>
    <element name="removeRequestByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the removeRequestByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeRequestByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the removeRequestByKey Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeRequestByKeyException">
        <annotation>
            <documentation>

                This is the Exception for the removeRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>

                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the removeRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeRequestsByKeysResponse">
        <annotation>

            <documentation>
                This is the Response for the removeRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="removeRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the removeRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryRemoveRequestsByKeysRequest">

        <annotation>
            <documentation>
                This is the Request for the tryRemoveRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>

        </complexType>
    </element>
    <element name="tryRemoveRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the tryRemoveRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="startRequestByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the startRequestByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="startRequestByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the startRequestByKey Operation
            </documentation>
        </annotation>

        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="startRequestByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the startRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
            </choice>
        </complexType>

    </element>
    <element name="startRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the startRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="startRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the startRequestsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="startRequestsByKeysException">
        <annotation>
            <documentation>

                This is the Exception for the startRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryStartRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryStartRequestsByKeys Operation
                This operation is MANDATORY
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="tryStartRequestsByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the tryStartRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                    <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>

        </complexType>
    </element>
    <element name="tryStartRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryStartRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>

    <element name="createAndStartRequestByValueRequest">
        <annotation>

            <documentation>
                This is the Request for the createAndStartRequestByValue Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValue" type="om-v1-0:RequestValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="createAndStartRequestByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createAndStartRequestByValue Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAndStartRequestByValueException">
        <annotation>
            <documentation>
                This is the Exception for the createAndStartRequestByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
            </choice>

        </complexType>
    </element>
    <element name="createAndStartRequestsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createAndStartRequestsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAndStartRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createAndStartRequestsByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createAndStartRequestsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the createAndStartRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryCreateAndStartRequestsByValuesRequest">
        <annotation>
            <documentation>

                This is the Request for the tryCreateAndStartRequestsByValues Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestValues" type="om-v1-0:ArrayOfRequestValue"  />
            </sequence>
        </complexType>
    </element>

    <element name="tryCreateAndStartRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the tryCreateAndStartRequestsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                    <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />

            </sequence>
        </complexType>
    </element>
    <element name="tryCreateAndStartRequestsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the tryCreateAndStartRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>

    <element name="abortRequestByKeyRequest">

        <annotation>
            <documentation>
                This is the Request for the abortRequestByKey Operation
                This operation is MANDATORY
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>

        </complexType>
    </element>
    <element name="abortRequestByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the abortRequestByKey Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="abortRequestByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the abortRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
            </choice>
        </complexType>

    </element>
    <element name="abortRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the abortRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="abortRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the abortRequestsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="abortRequestsByKeysException">
        <annotation>
            <documentation>

                This is the Exception for the abortRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryAbortRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryAbortRequestsByKeys Operation
                This operation is MANDATORY
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="tryAbortRequestsByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the tryAbortRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                    <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>

        </complexType>
    </element>
    <element name="tryAbortRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryAbortRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>

    <element name="suspendRequestByKeyRequest">
        <annotation>

            <documentation>
                This is the Request for the suspendRequestByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>
        </complexType>

    </element>
    <element name="suspendRequestByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the suspendRequestByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>

            </sequence>
        </complexType>
    </element>
    <element name="suspendRequestByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the suspendRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="suspendRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the suspendRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="suspendRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the suspendRequestsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="suspendRequestsByKeysException">
        <annotation>
            <documentation>

                This is the Exception for the suspendRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="trySuspendRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the trySuspendRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="trySuspendRequestsByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the trySuspendRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                    <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>

        </complexType>
    </element>
    <element name="trySuspendRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the trySuspendRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <element name="resumeRequestByKeyRequest">

        <annotation>
            <documentation>
                This is the Request for the resumeRequestByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="requestKey" type="om-v1-0:RequestKey"  />
            </sequence>

        </complexType>
    </element>
    <element name="resumeRequestByKeyResponse">
        <annotation>
            <documentation>
                This is the Response for the resumeRequestByKey Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="resumeRequestByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the resumeRequestByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>

        </complexType>
    </element>
    <element name="resumeRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the resumeRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="resumeRequestsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the resumeRequestsByKeys Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="resumeRequestsByKeysException">
        <annotation>
            <documentation>

                This is the Exception for the resumeRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="tryResumeRequestsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryResumeRequestsByKeys Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="requestKeys" type="om-v1-0:ArrayOfRequestKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="tryResumeRequestsByKeysResponse">

        <annotation>
            <documentation>
                This is the Response for the tryResumeRequestsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                    <element name="requestKeyResults" type="om-v1-0:ArrayOfRequestKeyResult"  />
            </sequence>

        </complexType>
    </element>
    <element name="tryResumeRequestsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryResumeRequestsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>

            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

    <!-- Tigerstripe : End of Managed Entity Operations for Request  -->

    <!-- Tigerstripe : Managed Entity Operations for BusinessInteractionRelationship ( NB These may be overridden in session)  -->
    <element name="createBusinessInteractionRelationshipByValueRequest">
        <annotation>
            <documentation>
                This is the Request for the createBusinessInteractionRelationshipByValue Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>

                <element name="businessInteractionRelationshipValue" type="cbebi-v1-5:BusinessInteractionRelationshipValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="createBusinessInteractionRelationshipByValueResponse">
        <annotation>
            <documentation>
                This is the Response for the createBusinessInteractionRelationshipByValue Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKey" type="cbebi-v1-5:BusinessInteractionRelationshipKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="createBusinessInteractionRelationshipByValueException">
        <annotation>

            <documentation>
                This is the Exception for the createBusinessInteractionRelationshipByValue Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>

                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="createBusinessInteractionRelationshipsByValuesRequest">
        <annotation>
            <documentation>

                This is the Request for the createBusinessInteractionRelationshipsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipValues" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"  />
            </sequence>
        </complexType>
    </element>

    <element name="createBusinessInteractionRelationshipsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createBusinessInteractionRelationshipsByValues Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeys" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKey"  />

            </sequence>
        </complexType>
    </element>
    <element name="createBusinessInteractionRelationshipsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the createBusinessInteractionRelationshipsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>

        </complexType>
    </element>
    <element name="tryCreateBusinessInteractionRelationshipsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the tryCreateBusinessInteractionRelationshipsByValues Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="businessInteractionRelationshipValues" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateBusinessInteractionRelationshipsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the tryCreateBusinessInteractionRelationshipsByValues Operation
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKeyResult"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryCreateBusinessInteractionRelationshipsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the tryCreateBusinessInteractionRelationshipsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the getBusinessInteractionRelationshipByKey Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKey" type="cbebi-v1-5:BusinessInteractionRelationshipKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipByKeyResponse">
        <annotation>

            <documentation>
                This is the Response for the getBusinessInteractionRelationshipByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipValue" type="cbebi-v1-5:BusinessInteractionRelationshipValue"  />
            </sequence>
        </complexType>

    </element>
    <element name="getBusinessInteractionRelationshipByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the getBusinessInteractionRelationshipByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>

                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipsByKeysRequest">
        <annotation>

            <documentation>
                This is the Request for the getBusinessInteractionRelationshipsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeys" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKey"  />
                <element name="attrNames" type="co-v1-5:ArrayOfString" />
            </sequence>

        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the getBusinessInteractionRelationshipsByKeys Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
                <element name="businessInteractionRelationshipValues" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"  />
            </sequence>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the getBusinessInteractionRelationshipsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
    <element name="getBusinessInteractionRelationshipsByTemplateRequest">
        <annotation>
            <documentation>
                This is the Request for the getBusinessInteractionRelationshipsByTemplate Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <complexContent>

                <extension base="co-v1-5:IteratorRequest">
                <sequence>
                    <element name="template" type="cbebi-v1-5:BusinessInteractionRelationshipValue"  />
                    <element name="attrNames" type="co-v1-5:ArrayOfString" />
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="getBusinessInteractionRelationshipsByTemplateResponse">
        <annotation>
            <documentation>
                This is the Response for the getBusinessInteractionRelationshipsByTemplate Operation
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="co-v1-5:IteratorResponse">

                <sequence>
                    <element name="results" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"/>
                </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipsByTemplateException">
        <annotation>

            <documentation>
                This is the Exception for the getBusinessInteractionRelationshipsByTemplate Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>

                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeBusinessInteractionRelationshipByKeyRequest">
        <annotation>
            <documentation>
                This is the Request for the removeBusinessInteractionRelationshipByKey Operation
                This operation is OPTIONAL
            </documentation>

        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKey" type="cbebi-v1-5:BusinessInteractionRelationshipKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="removeBusinessInteractionRelationshipByKeyResponse">
        <annotation>

            <documentation>
                This is the Response for the removeBusinessInteractionRelationshipByKey Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="removeBusinessInteractionRelationshipByKeyException">
        <annotation>
            <documentation>
                This is the Exception for the removeBusinessInteractionRelationshipByKey Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="objectNotFoundException" type="co-v1-5:ObjectNotFoundException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <element name="removeBusinessInteractionRelationshipsByKeysRequest">

        <annotation>
            <documentation>
                This is the Request for the removeBusinessInteractionRelationshipsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeys" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKey"  />
            </sequence>

        </complexType>
    </element>
    <element name="removeBusinessInteractionRelationshipsByKeysResponse">
        <annotation>
            <documentation>
                This is the Response for the removeBusinessInteractionRelationshipsByKeys Operation
            </documentation>
        </annotation>
        <complexType>

            <sequence>
            </sequence>
        </complexType>
    </element>
    <element name="removeBusinessInteractionRelationshipsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the removeBusinessInteractionRelationshipsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="removeException" type="co-v1-5:RemoveException"/>
                <element name="ossIllegalStateException" type="co-v1-5:OssIllegalStateException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>

            </choice>
        </complexType>
    </element>
    <element name="tryRemoveBusinessInteractionRelationshipsByKeysRequest">
        <annotation>
            <documentation>
                This is the Request for the tryRemoveBusinessInteractionRelationshipsByKeys Operation
                This operation is OPTIONAL
            </documentation>
        </annotation>

        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeys" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKey"  />
            </sequence>
        </complexType>
    </element>
    <element name="tryRemoveBusinessInteractionRelationshipsByKeysResponse">
        <annotation>
            <documentation>

                This is the Response for the tryRemoveBusinessInteractionRelationshipsByKeys Operation
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="businessInteractionRelationshipKeyResults" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipKeyResult"  />
            </sequence>
        </complexType>
    </element>

    <element name="tryRemoveBusinessInteractionRelationshipsByKeysException">
        <annotation>
            <documentation>
                This is the Exception for the tryRemoveBusinessInteractionRelationshipsByKeys Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
    <!-- Tigerstripe : End of Managed Entity Operations for BusinessInteractionRelationship  -->
    <!-- Tigerstripe : Operations on the interface JVTOrderManagementSession =================================== -->
   <element name="getRequestSpecificationsRequest">
        <annotation>

            <documentation>
                This is the Request for the getRequestSpecifications Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getRequestSpecificationsResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestSpecifications Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="requestSpecificationValues" type="om-v1-0:ArrayOfRequestSpecificationValue"  />

                </sequence>
        </complexType>
    </element>
    <element name="getRequestSpecificationsException">
        <annotation>
            <documentation>
                This is the Exception for the getRequestSpecifications Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
  
   <element name="getSupportedEntitySpecificationValuesRequest">
        <annotation>
            <documentation>

                This is the Request for the getSupportedEntitySpecificationValues Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="specificationKey" type="om-v1-0:RequestSpecificationKey"  />
                </sequence>
        </complexType>
    </element>

    <element name="getSupportedEntitySpecificationValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the getSupportedEntitySpecificationValues Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="entitySpecificationValues" type="cbecore-v1-5:ArrayOfEntitySpecificationValue"  />

                </sequence>
        </complexType>
    </element>
    <element name="getSupportedEntitySpecificationValuesException">
        <annotation>
            <documentation>
                This is the Exception for the getSupportedEntitySpecificationValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="finderException" type="co-v1-5:FinderException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>

  
   <element name="createRelatedRequestsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createRelatedRequestsByValues Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="parent" type="om-v1-0:RequestValue"  />

                    <element name="children" type="om-v1-0:ArrayOfRequestValue"  />
                    <element name="relationshipType" type="string"  />
                </sequence>
        </complexType>
    </element>
    <element name="createRelatedRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createRelatedRequestsByValues Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="businessInteractionRelationshipValues" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"  />
                </sequence>
        </complexType>
    </element>
    <element name="createRelatedRequestsByValuesException">
        <annotation>

            <documentation>
                This is the Exception for the createRelatedRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>

                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="createAndStartRelatedRequestsByValuesRequest">
        <annotation>
            <documentation>
                This is the Request for the createAndStartRelatedRequestsByValues Operation
            </documentation>

        </annotation>
        <complexType>
                <sequence>
                    <element name="parent" type="om-v1-0:RequestValue"  />
                    <element name="children" type="om-v1-0:ArrayOfRequestValue"  />
                    <element name="relationshipType" type="string"  />
                </sequence>
        </complexType>
    </element>

    <element name="createAndStartRelatedRequestsByValuesResponse">
        <annotation>
            <documentation>
                This is the Response for the createAndStartRelatedRequestsByValues Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="businessInteractionRelationshipValues" type="cbebi-v1-5:ArrayOfBusinessInteractionRelationshipValue"  />

                </sequence>
        </complexType>
    </element>
    <element name="createAndStartRelatedRequestsByValuesException">
        <annotation>
            <documentation>
                This is the Exception for the createAndStartRelatedRequestsByValues Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="duplicateKeyException" type="co-v1-5:DuplicateKeyException"/>
                <element name="createException" type="co-v1-5:CreateException"/>
                <element name="ossIllegalArgumentException" type="co-v1-5:OssIllegalArgumentException"/>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>

    </element>
  
   <element name="getRequestTypesRequest">
        <annotation>
            <documentation>
                This is the Request for the getRequestTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>

                </sequence>
        </complexType>
    </element>
    <element name="getRequestTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestTypes Operation
            </documentation>
        </annotation>

        <complexType>
                <sequence>
                    <element name="requestTypes" type="co-v1-5:ArrayOfString"  />
                </sequence>
        </complexType>
    </element>
    <element name="getRequestTypesException">
        <annotation>
            <documentation>

                This is the Exception for the getRequestTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>

  
   <element name="getRequestSpecificationTypesRequest">
        <annotation>
            <documentation>
                This is the Request for the getRequestSpecificationTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>

        </complexType>
    </element>
    <element name="getRequestSpecificationTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getRequestSpecificationTypes Operation
            </documentation>
        </annotation>
        <complexType>

                <sequence>
                    <element name="requestSpecificationTypes" type="co-v1-5:ArrayOfString"  />
                </sequence>
        </complexType>
    </element>
    <element name="getRequestSpecificationTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getRequestSpecificationTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>

        </annotation>
        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
            </choice>
        </complexType>
    </element>
  
   <element name="getBusinessInteractionRelationshipTypesRequest">
        <annotation>

            <documentation>
                This is the Request for the getBusinessInteractionRelationshipTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                </sequence>
        </complexType>
    </element>

    <element name="getBusinessInteractionRelationshipTypesResponse">
        <annotation>
            <documentation>
                This is the Response for the getBusinessInteractionRelationshipTypes Operation
            </documentation>
        </annotation>
        <complexType>
                <sequence>
                    <element name="businessInteractionRelationshipTypes" type="co-v1-5:ArrayOfString"  />

                </sequence>
        </complexType>
    </element>
    <element name="getBusinessInteractionRelationshipTypesException">
        <annotation>
            <documentation>
                This is the Exception for the getBusinessInteractionRelationshipTypes Operation
                The following exceptions are returned if an error occurs.
            </documentation>
        </annotation>

        <complexType>
            <choice>
                <element name="remoteException" type="co-v1-5:RemoteException"/>
                <element name="ossUnsupportedOperationException" type="co-v1-5:OssUnsupportedOperationException"/>
            </choice>
        </complexType>
    </element>
  
    <!-- Tigerstripe : End of Operations on the interface JVTOrderManagementSession =================================== -->

</schema>
