# ===================================================================
# Spring Boot configuration for the "prod" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    com.nokia.wing.wdh.sdmadapter.service: DEBUG

apm:
  server:
    url: http://************:31333

eureka:
  instance:
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://admin:${jhipster.registry.password}@localhost:8761/eureka/

management:
  metrics:
    export:
      prometheus:
        enabled: false

spring:
  application:
    name: sdm-adapter
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  cloud:
    config:
      retry:
        initial-interval: 1000
        max-interval: 2000
        max-attempts: 100
      uri: http://admin:${jhipster.registry.password}@localhost:8761/config
      # name of the config server's property source (file.yml) that we want to use
      name: SdmAdapterService
      profile: prod
      label: main # toggle to switch to a different version of the configuration as stored in git
      # it can be set to any label, branch or commit of the configuration source Git repository
  thymeleaf:
    cache: true
  sleuth:
    sampler:
      probability: 1 # report 100% of traces
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    base-url: http://localhost:9411
    enabled: false
    locator:
      discovery:
        enabled: true

# ===================================================================
# To enable TLS in production, generate a certificate using:
# keytool -genkey -alias sdmadapterservice -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# https://maximilian-boehm.com/hp2121/Create-a-Java-Keystore-JKS-from-Let-s-Encrypt-Certificates.htm
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#   port: 443
#   ssl:
#     key-store: classpath:config/tls/keystore.p12
#     key-store-password: password
#     key-store-type: PKCS12
#     key-alias: selfsigned
#     # The ciphers suite enforce the security by deactivating some old and deprecated SSL cipher, this list was tested against SSL Labs (https://www.ssllabs.com/ssltest/)
#     ciphers: TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 ,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
# ===================================================================
server:
  port: 9191
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  registry:
    password: admin
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        # As this is the PRODUCTION configuration, you MUST change the default key, and store it securely:
        # - In the JHipster Registry (which includes a Spring Cloud Config server)
        # - In a separate `application-prod.yml` file, in the same folder as your executable JAR file
        # - In the `JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET` environment variable
        base64-secret: YjEzMjkwZTY3ZjJlMTg0MjM4ZTY1YzY5MTc0NjQyNTMxMDUzZjk5ZTQwN2YyMTg4MWMyOWJkZGJjNjU1OTA1Nzk1YTgxMDE0YTU1YzkyNDE4Yzc5OTQyNTE4NmM4ZTJjZGNkMTgwNDI4NDBjMDhhNDdmNWNhNTA5MzRiYjgxMDY=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
  clientType: NTT
  conductor:
    url: http://************:7070/api/
    workerThreads: 20
    workers: 4
    mnoPrefix:
  ratTypes: UTRAN,GERAN,,GAN,I-HSPA-Evolution,E-UTRAN,NB-IoT,LTE-M,NR-As-Secondary-RAT,NR-In-5GS

onends:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: https://localhost/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
    socketTimeout: 30000
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000
  subscriberITFVersion: NW_SUBSCRIBER_v10
  allowedNetwork: NON_5G #ONLY_5G, ALL_WITH_5G

ntt:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: https://localhost/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000

flowone:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: http://localhost:9191/ProvisioningGateway/services/SPMLNWSubscriber10Service
  netType: USCC
  orderNo: CRM_ORDER_NUMBER
  ReqUser: WDH_USER
  technicalProduct: CreateIOTService
  productVersion: 1.0
  ilReqGroup: CreateIOTService
  reqHeaderUsername: bss
  reqHeaderPassword: client
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000
  tops:
    url: http://************:31596/castlemock/mock/soap/project/3LRjye/ResellerManagement_v1_0_Port
    billMgmtUrl: http://localhost:8080/enterprise/billing/billmanagement/v1_1
    paymentUrl: http://localhost:9080/enterprise/ar/payment/v2_1
    maxRetry: 4
    delay: 0
    keyStore: classpath:config/keys/tops_client.keystore
    keyStorePassword: nokiawdh
    trustStore: classpath:config/keys/tops_server.jks
    trustStorePassword: password
    authId: test
    authValue: test
    clientId: test
    uscSecurityToken: test
    soapAction:
      billPdfAction: http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillPdf
      billListAction: http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillList
      trxHistoryAction: http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_1#Payment_v2_0_Binding.getPaymentTransactionHistory

  kafka: wdh.sdm.adapter.audit.log.events
    #auditLogTopic: wdh.nokia.sdmadapter.topic

cai3g:
  location: ita
  trustStore: classpath:config/keys/windtre_eda.jks
  tspassword: windtre
  user: wing
  password: Wing001!
  url: https://localhost:8443/CAI3G1.2/services/CAI3G1.2
  hlrUrl: https://localhost:8443/WING/Dispatcher
  hssUrl: https://localhost:8443/WING/Dispatcher
  sessionPoolSize: 5
  #hlrMoType: WINGSubscription@http://schemas.ericsson.com/ma/CA/WINGSubscription/
  #hssMoType: WINGEPSMultiSC@http://schemas.ericsson.com/ma/CA/WINGEPSMultiSC/
  hlrMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/
  hssMoType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/
  aucMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/

  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  camel:
    tdps:
      -
        serviceName: 222886-orig-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 0
        detectionPoint: 2
        gsmScfAddress: 393205800606
        serviceKey: 52447
        defaultErrorHandling: 1
        cch: 1
        i: true
        dialnum:
      -
        serviceName: 222886-term-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 1
        detectionPoint: 12
        gsmScfAddress: 393205800606
        serviceKey: 53447
        defaultErrorHandling: 1
        cch: 2
        i: true
        dialnum:
