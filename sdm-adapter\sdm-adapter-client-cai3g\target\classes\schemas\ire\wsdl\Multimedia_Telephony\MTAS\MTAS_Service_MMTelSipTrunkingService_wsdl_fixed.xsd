<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
           xmlns:st-serv="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSipTrunkingService/"
           xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSipTrunkingService/"
           targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSipTrunkingService/"
           elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
           jaxb:version="2.0">
	<xs:annotation>
		<xs:documentation xml:lang="en">
		MTAS 15A         	since MA15.0 CP1
			Added:
				SIP Trunking Service

		vMTAS 1.20 			since EDA1
			Added:
				st-terminating-identity-presentation
				st-terminating-identity-presentation-restriction
		</xs:documentation>
	</xs:annotation>
    <xs:element name="publicId" type="publicIdentityType"/>
    <!-- MO Attributes for top level messages -->
    <xs:element name="createService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to create MMTel group service data</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">The default public user identity for the group. This identity
                            must already be configured on the HSS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="sip-trunking-control" type="sip-trunking-control-type"/>
				<xs:element name="st-call-admission-control" type="st-call-admission-control-type" minOccurs="0"/>
				<xs:element name="st-carrier-pre-select-rn" type="st-carrier-pre-select-rn-type" minOccurs="0"/>
				<xs:element name="st-carrier-select-rn" type="st-carrier-select-rn-type" minOccurs="0"/>						
				<xs:element name="st-common-data" type="st-common-data-type" minOccurs="0"/>
				<xs:element name="st-communication-diversion" type="communication-diversion-type" minOccurs="0"/>
				<xs:element name="st-incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0"/>
				<xs:element name="st-malicious-communication-identification" type="st-malicious-communication-identification-type" minOccurs="0"/>
				<xs:element name="st-operator-controlled-outgoing-barring-programs" type="st-operator-controlled-outgoing-barring-programs-type" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" minOccurs="0"/>
				<xs:element name="st-outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyCreate">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
        <xs:keyref name="publicIdKeyRef" refer="publicIdKeyCreate">
            <xs:selector xpath="."/>
            <xs:field xpath="publicId"/>
        </xs:keyref>
    </xs:element>
    <xs:element name="setService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to modify MMTel group service data</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an optional element to control concurrent updates. If
                            present then the set
                            request will be accepted only if the service data version is still at the value given in
                            this element i.e. no other
                            updates have been performed. It is of type integer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="sip-trunking-control" type="sip-trunking-control-type" minOccurs="0" />
				<xs:element name="st-call-admission-control" type="st-call-admission-control-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-carrier-pre-select-rn" type="st-carrier-pre-select-rn-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-carrier-select-rn" type="st-carrier-select-rn-type" nillable="true" minOccurs="0"/>					
				<xs:element name="st-common-data" type="st-common-data-type" minOccurs="0"/>
				<xs:element name="st-communication-diversion" type="communication-diversion-type"  nillable="true" minOccurs="0"/>
				<xs:element name="st-incoming-communication-barring" type="incoming-communication-barring-type" nillable="true" minOccurs="0"/>					
				<xs:element name="st-malicious-communication-identification" type="st-malicious-communication-identification-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-operator-controlled-outgoing-barring-programs" type="st-operator-controlled-outgoing-barring-programs-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation" type="originating-identity-presentation-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-outgoing-communication-barring" type="outgoing-communication-barring-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation" type="terminating-identity-presentation-type" nillable="true" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" nillable="true" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeySet">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
    </xs:element>
    <xs:element name="getResponseService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Contains the currently configured MMTel group service data
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">The default public user identity for the group
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="concurrency-control" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an integer value indicating the current version of the
                            MMTel group service data.
                            This value can be used in a subsequent setMMTelGroup request to make sure that no changes
                            have been made to
                            the service data since the version that was read
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="sip-trunking-control" type="sip-trunking-control-type" minOccurs="0"/>
				<xs:element name="st-call-admission-control" type="st-call-admission-control-type" minOccurs="0"/>
				<xs:element name="st-carrier-pre-select-rn" type="st-carrier-pre-select-rn-type" minOccurs="0"/>
				<xs:element name="st-carrier-select-rn" type="st-carrier-select-rn-type" minOccurs="0"/>
				<xs:element name="st-common-data" type="st-common-data-type" minOccurs="0"/>
				<xs:element name="st-communication-diversion" type="communication-diversion-type" minOccurs="0"/>
				<xs:element name="st-incoming-communication-barring" type="incoming-communication-barring-type" minOccurs="0"/>
				<xs:element name="st-malicious-communication-identification" type="st-malicious-communication-identification-type" minOccurs="0"/>
				<xs:element name="st-operator-controlled-outgoing-barring-programs" type="st-operator-controlled-outgoing-barring-programs-type" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation" type="originating-identity-presentation-type" minOccurs="0"/>
				<xs:element name="st-originating-identity-presentation-restriction" type="originating-identity-presentation-restriction-type" minOccurs="0"/>
				<xs:element name="st-outgoing-communication-barring" type="outgoing-communication-barring-type" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation" type="terminating-identity-presentation-type" minOccurs="0"/>
				<xs:element name="st-terminating-identity-presentation-restriction" type="terminating-identity-presentation-restriction-type" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyGetResp">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
    </xs:element>
    <xs:complexType name="sip-trunking-control-type">
        <xs:sequence>
            <xs:element name="operator-configuration" type="st-control-operator-configuration-type">
                <xs:annotation>
                    <xs:documentation>
                        The configuration parameters for the SIP Trunking service that are available to the operator.
                        This must be present on the creation of the SIP Trunking service.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
	<xs:complexType name="st-control-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>Operator Part of SIP Trunking.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the SIP Trunking
						service.
						This must be present on the creation of the service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="disable-identity-validation" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The disabled-identity-validation element disables the confirmation of the identity of the PBX.
						Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="auxiliary-identity" minOccurs="0" type="sipURI" nillable="true">
				<xs:annotation>
					<xs:documentation>The auxiliary PBX identity is one of the identities used in static mode PBX connect to validate
						the calling user identity received in the P-Asserted-Identity headers. If a valid P-Asserted-Identity header is
						missing, the auxiliary identity is used to populate the P-Asserted-Identity header field. This parameter is a sip
						URI with or without the user part.
						Examples:sip:+<EMAIL>;user=phone or sip:st.operator.com.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="static-route" type="static-route-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						The static route. Use xsi:nil="true" to withdraw this element. This must be present on the creation of the service.
					</xs:documentation>
				</xs:annotation>
				<xs:key name="routeIdKey">
					<xs:selector xpath="." />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
			<xs:element name="dynamic-route" type="dynamic-route-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						The dynamic route. Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
				<xs:key name="dynamicrouteIdKey">
					<xs:selector xpath="." />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="static-route-type">
		<xs:annotation>
			<xs:documentation>Static route for SIP Trunking.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="route-type">
				<xs:sequence>
					<xs:element name="routes" type="routes-type" nillable="true" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
								The routes element can have 0 or any number of route elements. Use xsi:nil="true" to withdraw this routes.
							</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="dynamic-route-type">
		<xs:annotation>
			<xs:documentation>Dynamic route for SIP Trunking.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="dyn-route-type">
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="routes-type">
		<xs:annotation>
			<xs:documentation>
				Routes inside a static route
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="route" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:annotation>
						<xs:documentation>
							Route
						</xs:documentation>
					</xs:annotation>
					<xs:sequence>
						<xs:element name="id" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="elementId"/>
								</xs:appinfo>
								<xs:documentation>
									The id of the route
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="uri" type="sipURI">
							<xs:annotation>
								<xs:documentation>
									The route URI.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="id" type="xs:string" use="required" />
				</xs:complexType>
				<xs:key name="routesIdKey">
					<xs:selector xpath="." />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="route-type">
		<xs:annotation>
			<xs:documentation>Route part of SIP Trunking.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="id" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
						The id of the static route
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="disabled" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The disabled element is set to disable the route. Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="standby-route" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The standby-route element is set if standby route. Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required" />
	</xs:complexType>
	<xs:complexType name="dyn-route-type">
		<xs:annotation>
			<xs:documentation>Route part of SIP Trunking.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="id" type="publicIdentityType" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
						The id of the dynamic route
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="disabled" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The disabled element is set to disable the route. Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="standby-route" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The standby-route element is set if standby route. Use xsi:nil="true" to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:string" use="required" />
	</xs:complexType>
    <xs:simpleType name="publicIdentityType">
        <xs:restriction base="xs:anyURI">
            <xs:pattern value="sip:.*"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="activated-enum-type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="true"/>
            <xs:enumeration value="false"/>
            <xs:enumeration value="profile"/>
        </xs:restriction>
    </xs:simpleType>
	<xs:simpleType name="sipURI">
  		<xs:restriction base="xs:string">
			<xs:pattern value="sip:(.+@)?(([\w\-\.:]*)|(\[[0-9a-fA-F:\.]*\]))([;&amp;].*)?"/>
		</xs:restriction>  	
	</xs:simpleType>
    <xs:complexType name="st-call-admission-control-type">
		<xs:sequence>
			<xs:element name="operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST Call Admission Control service that are available to the operator.
						This must be present on the creation of the ST Call Admission Control service.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the ST Call
									Admission
									Control service. If set to "false" this will withdraw the service from the PBX. This must be present on the creation
									of the
									ST Call Admission Control service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="orig-all-limit" type="xs:nonNegativeInteger" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									Defines the limit of all originating sessions (i.e. the sum of active and inactive originating sessions) for this
									PBX.
									This must be present on the creation of the ST Call Admission Control service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="term-all-limit" type="xs:nonNegativeInteger" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									Defines the limit of all terminating sessions (i.e. the sum of active and inactive terminating sessions) for this
									PBX.
									This must be present on the creation of the ST Call Admission Control service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="total-all-limit" type="xs:nonNegativeInteger" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">
									Defines the limit of all sessions (i.e. the sum of all originating and terminating sessions) for this PBX.
									This must be present on the creation of the ST Call Admission Control service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
    </xs:complexType>
	<xs:complexType name="st-carrier-pre-select-rn-type">
		<xs:annotation>
			<xs:documentation>
			The ST carrier pre-select rn service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST carrier pre-select rn service that are available to the operator. This must be present
					on the creation of the ST carrier pre-select-rn service.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The &lt;activated&gt; element has values "true" or "false". When set to "true" the PBX is provisioned with the ST carrier pre-select rn service.
								If set to "false" this will withdraw the service from the PBX. This must be present on the creation of the ST carrier pre-select rn service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="call-type-carrier-rn" type="call-type-carrier-rn-type" nillable="true" maxOccurs="2">
							<xs:annotation>
								<xs:documentation>
								The &lt;call-type-carrier-rn&gt; element specifies a mapping between a call type and the global carrier id to be pre-selected for calls of that type. 
								The &lt;call-type-carrier-rn&gt; element is a sub-MO allowing either one or two instances with "call-type" as the unique key.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="call-type-carrier-rn-key">
					<xs:selector xpath="./st-serv:call-type-carrier-rn"/>
					<xs:field xpath="@call-type"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="call-type-carrier-rn-type">
		<xs:sequence>
			<xs:element name="call-type" type="call-type-rn-type" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementCall-type"/>
					</xs:appinfo>
					<xs:documentation>
					The type of call either “LOCAL” or “REMOTE”. The value “LOCAL” corresponds to calls to numbers with the same country code and area code as the user.  
					The value “REMOTE” corresponds to all other calls. This must be present on the creation of a &lt;call-type-carrier-rn&gt;.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="global-carrier-code" type="carrier-code-rn-id-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The global carrier code to be use for a call of the given type. This is a string of between 3 and 8 digits. 
					This must be present on the creation of a &lt;call-type-carrier-rn&gt;.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="call-type" type="call-type-rn-type" use="required"/>
	</xs:complexType>
	<xs:simpleType name="call-type-rn-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LOCAL"/>
			<xs:enumeration value="REMOTE"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="carrier-code-rn-id-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{3,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="st-carrier-select-rn-type">
		<xs:annotation>
			<xs:documentation>
			The ST carrier select rn service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST carrier select rn service that are available to the operator. This must be present
					on the creation of the ST carrier select rn service.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The &lt;activated&gt; element has values "true" or "false". When set to "true" the PBX is provisioned with the ST carrier select 
								rn service. If set to "false" this will withdraw the service from the PBX. This must be present on the creation of the 
								ST carrier select rn service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
    <xs:complexType name="st-common-data-type">
		<xs:sequence>
			<!-- new common data elements should be optional and added in alphabetical position within this sequence -->
			<xs:element name="area-code" type="area-code-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Area code 0-6 digits. Leave empty for numbering plans to which it does not apply.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="country-code" type="country-code-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Country code 1-4 digits.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-global-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The maximum number of allowed rules in the PBX service document. Not specified or zero limit means no limit
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
    </xs:complexType>
	<xs:simpleType name="area-code-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{0,6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="country-code-type">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="rule-limit-type">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="100"/>
			<!-- 0 means no limit -->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="communication-diversion-type">
		<xs:annotation>
			<xs:documentation>
			The ST communication diversion service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="cdiv-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST communication diversion service that is available to the operator. 
					This must be present on the creation of the ST communication diversion service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="cdiv-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST communication diversion service can be set on the PBX's behalf by the operator. 
					This shall only be present if the service is provisioned i.e. operator-configuration is present and activated is "true".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>Operator Part of Communication Diversion</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the ST communication diversion service.
					If set to "false" this will withdraw the service and the user-configuration element must be preserved.
					This must be present on the creation of the ST communication diversion service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The cdiv-op-conditions element is a grouping element for fine-grain provisioning options that control which conditions
					the PBX is permitted to use in communication diversion rules
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="not-registered-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The not-registered-condition element has values "activated" or "deactivated". When set to "activated" it
								allows the PBX to use the cdiv-call-state condition with the value of "not-registered" in communication
								diversion rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="not-reachable-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The not-reachable-condition element has values "activated" or "deactivated". When set to "activated" it allows
								the PBX to use the cdiv-call-state condition with the value of "not-reachable" in communication diversion rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="cdiv-op-actions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The cdiv-op-actions element is a grouping element for fine-grain provisioning options to control which actions
					the user is permitted to use for communication diversion rules.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="notify-caller-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The notify-caller-action element has values "activated" or "deactivated". When set to "activated" it allows
								the PBX to use the notify-caller action in communication diversion rules to control whether the caller
								is notified that the call is being forwarded
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="reveal-identity-to-caller-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The reveal-identity-to-caller-action has values "activated" or "deactivated". When set to "activated" it
								allows the PBX to use the reveal-identity-to-caller action in communication diversion rules to
								control whether the caller being notified that the call is being forwarded receives the target's
								identity information
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="reveal-identity-to-target-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The reveal-identity-to-target-action has values "activated" or "deactivated". When set to "activated"
								it allows the PBX to use the reveal-identity-to-target action in communication diversion rules
								to control whether the diverted-to party receives identity information of the diverting party.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
								The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows
								the PBX to use the play-announcement action in communication diversion rules to control whether the caller
								is presented by specific announcement handled by generic announcement service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
		      The maximum number of allowed CDIV rules in the user document. Not specified or zero limit means no limit
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The active element has values "true" or "false". It controls whether the communication diversion service is
					active or not for this PBX
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- cdiv-service-options is a structured parameter allowing it to be made nilable -->
			<!-- ruleset is a structured parameter that is optional and nillable -->
			<xs:element name="cdiv-ruleset" type="cdiv-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Grouping element for a set of zero or more user rules
					</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="cdiv-rule-key">
					<xs:selector xpath="./st-serv:cdiv-rule"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="cdiv-ruleset-type">
		<xs:sequence>
			<xs:element name="cdiv-rule" type="cdiv-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
					An individual rule controlling communication diversion behaviour. The cdiv-rule element is a sub-MO allowing
					multiple instances with "id" as the unique key.
					</xs:documentation>
				</xs:annotation>
				<!-- sub MOs need a key.  The key for rule is id.  -->
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- rule is a sub-MO -->
	<xs:complexType name="cdiv-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
					A unique identifier for an individual rule. This must be unique within the scope of the complete document. This
					must be present on the creation of a cdiv-rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-conditions" type="cdiv-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The cdiv-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
					for the rule to take effect. If no conditions are present then the rule is always applicable. The conditions that
					are permitted depend on the fine grain provisioning options in cdiv-op-conditions
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cdiv-actions is optional but not nillable.  Every CDIV rule must have actions/forward-to/target to be valid but cai3g:Set -->
			<!-- could just update conditions so actions must be optional -->
			<xs:element name="cdiv-actions" type="cdiv-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The cdiv-actions element is a grouping element for the actions for a rule. This must be present on the
					creation of a cdiv-rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required"/>
	</xs:complexType>
	<xs:complexType name="cdiv-conditions-type">
		<xs:sequence>
			<xs:element ref="rule-deactivated" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect
					of deactivating the individual rule. Set to "false" to remove this condition.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cdiv-call-state" type="cdiv-call-state-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The cdiv-call-state condition controls which state the PBX must be in for the rule to apply. 
					The value "not-registered" applies when none of the configured links has been registered by the PBX (valid only in dynamic mode).
					The value "not-reachable" applies when the PBX is not reachable because either a specific response has been received or the Access Profile Timeout timer expires.
					The value "unconditional" is used to clear the other call state values so that the condition is satisfied regardless of the PBX's call state.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="rule-deactivated" type="xs:boolean"/>
	<xs:simpleType name="cdiv-call-state-type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="unconditional"/>
			<xs:enumeration value="not-registered"/>
			<xs:enumeration value="not-reachable"/>
			<!-- EMA does not support nilling a single value parameter so assigning the value of "unconditional"
				means none of the three conditions apply-->
		</xs:restriction>
	</xs:simpleType>
	<!-- Although it would be possible to skip actions and go straight to forward-to
	      this structure matches that stored on HSS to make the mappings between CAI3G and Sh schemas clear -->
	<xs:complexType name="cdiv-actions-type">
		<xs:sequence>
			<xs:element name="forward-to" type="forward-to-type">
				<xs:annotation>
					<xs:documentation>
					The forward-to element is a grouping element with details of the target to which the communication should be
					diverted and optional control of notifications and which identities are revealed to whom. This must be present
					on the creation of a cdiv-rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The play-announcement element has string values from 0 to 32 characters. 
					When the play-announcement action is set with the string value containing characters with the length between 1 to 32,
					if there is any satisfying corresponding conditions and being diverted, the caller will be presented with the 
					specific announcement handled by generic announcement service.
					When the play-announcement action is set with the string value containing character with the length of 0, 
					any play-announcement action element in the rule will be deleted from the rule. 
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="activatedType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="activated"/>
			<xs:enumeration value="deactivated"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="forward-to-type">
		<xs:sequence>
			<!-- 'target' is optional on CAI3G to allow individual updates of other elements but is mandatory in the updated document-->
			<xs:element name="target" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The target element specifies the identity to which the communication should be diverted. This takes the form of
					a sip: or tel: URI or "voicemail:internal" for forwarding to voice mail. Each tel: URI and sip: URI that was converted
					from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be
					normalized after removing a dynamic ad-hoc presentation SSC and/or a CSC. This must be present on the creation
					of a cdiv-rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- There is no need for these elements to be nillable because they can be set to false to achieve the same meaning -->
			<xs:element name="notify-caller" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The notify-caller element has values "true" or "false". It controls whether the caller is notified that the call is
					being forwarded. If it is not included then the default behaviour is to notify the caller (true).
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reveal-identity-to-caller" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The reveal-identity-to-caller element has values "true" or "false". It controls whether the caller being notified
					that the call is being forwarded receives the target's identity information. If it is not included then the
					default behaviour is to reveal the target's identity to the caller (true).
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="notify-served-user" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The notify-served-user element has values "true" or "false". It controls whether the served user is notified that
					the call is being forwarded. If it is not included then the default behaviour is not to notify the served user (false).
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="notify-served-user-on-outbound-call" type="xs:boolean" default="false" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The notify-served-user-on-outbound-call element has values true or false. It controls whether the served user
					is notified that calls are being forwarded when he makes a call attempt. If it is not included then the default
					behaviour is not to notify the served user on outbound calls (false).
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reveal-identity-to-target" type="xs:boolean" default="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The reveal-identity-to-target element has values "true" and "false". It controls whether the diverted-to party
					receives identity information of the diverting party. If it is not included then the default behaviour is to
					reveal the diverting party's identity to the target (true).
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="play-announcement-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="incoming-communication-barring-type">
		<xs:annotation>
			<xs:documentation>
				The ST Incoming Communication Barring service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="icb-operator-configuration-type" nillable="true"
				minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST Incoming Communication Barring service that are available to the
						operator rather than the user. This must be present on the creation of the incoming-communication-barring
						service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="icb-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST Incoming Communication Barring service that are available for the
						user to set directly. These can also be set on the user's behalf by the operator. This shall only be
						present if the service is provisioned i.e. icb-operator-configuration is present and activated is "true"
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the user is provisioned 	with the
						incoming communication barring service. If set to false this will withdraw the user service, but the
						icb-user-configuration element must be preserved. This must be present on the creation of the
						incoming-communication-barring service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The icb-op-conditions element is a grouping element for fine-grain provisioning options that control which
						condition elements the user is permitted to use in ST Incoming Communication Barring rules.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="anonymous-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The anonymous-condition element has values "activated" or "deactivated". When set to "activated"
									it allows the subscriber to use the anonymous condition in ST Incoming Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="communication-diverted-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The communication-diverted-condition element has values "activated" or "deactivated". When set to "activated"
									 it allows the subscriber to use the communication-diverted condition in incoming communication barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The identity-condition element has values "activated" or "deactivated". When set to "activated" it
									allows the subscriber to use the identity condition in ST Incoming Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The other-identity-condition element has values "activated" or "deactivated". When set to "activated"
									it allows the subscriber to use the other-identity condition in ST Incoming Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="icb-op-actions" type="incoming-call-barring-op-actions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The icb-op-actions element is a grouping element for fine-grain provisioning options to controlwhich action
						elements the user is permitted to use in ST Incoming Communication Barring rules.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The maximum number of allowed ST Incoming Communication Barring rules in the user document. Not
						specified or zero limit means no limit
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The active element has values "true" or "false". It controls whether the ST Incoming Communication Barring
						service is active or not for this subscriber. Note that this controls the user rules but has no effect on the
						operator rules.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-ruleset" type="icb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Grouping element for a set of zero or more user rules.
					</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="icb-user-rule-key">
					<xs:selector xpath="./st-serv:icb-rule" />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="icb-ruleset-type">
		<xs:sequence>
			<xs:element name="icb-rule" type="icb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						An individual rule controlling ST Incoming Communication Barring behaviour. The icb-rule element
						is a sub-MO allowing multiple instances with "id" as the unique key
					</xs:documentation>
				</xs:annotation>
				<!-- sub MOs need a key. The key for rule is id. -->
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- rule is a sub-MO -->
	<xs:complexType name="icb-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
						A unique identifier for an individual rule. This must be unique within the scope of the complete
						document. This must be present on the creation of an icb-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-conditions" type="icb-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The icb-conditions element is a grouping element for conditions for a rule. All conditions must
						be satisfied for the rule to take effect. If no conditions are present then the rule is always applicable.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set -->
			<!-- could just update conditions so actions must be optional -->
			<xs:element name="cb-actions" type="incoming-call-barring-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The cb-actions element is a grouping element for the actions for a rule. For communication barring an
						allow action must be present in each rule. This must be present on the creation of an icb-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required" />
	</xs:complexType>
	<xs:complexType name="icb-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The rule-deactivated element has values "true" or "false". If present with the value "true" this
						has the effect of deactivating the individual rule. Set to "false" to remove this condition.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="icb-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The icb-caller-identity element is a grouping element for conditions which are based on the caller's identity
						(or lack of an identity in the case of anonymous).
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="anonymous" type="empty-element-type">
							<xs:annotation>
								<xs:documentation>
									The anonymous element is an empty element specifying a condition which is satisfied if the caller is
									anonymous. This can be removed by deleting the enclosing icb-caller-identity element or by replacing
									it with an identity or other-identity element. The elements anonymous, identity and other-identity are
									mutually exclusive.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity" type="empty-element-type">
							<xs:annotation>
								<xs:documentation>
									The other-identity element is an empty element which matches any identity that has not been specified by
									any of the other rules in the ruleset. It allows for setting a default policy. This can be removed by
									deleting the enclosing icb-caller-identity element or by replacing it with an anonymous or identity element.
									The elements anonymous, identity and other-identity are mutually exclusive.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on icb-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation>
									The identity element is a grouping element for conditions which are based on the caller's identity.
									The condition is satisfied if any of the included one or many elements within it is matched. This
									can be removed by deleting the enclosing icb-caller-identity element or by replacing it with an
									anonymous or other-identity element. The elements anonymous, identity and other-identity are mutually
									exclusive.
								</xs:documentation>
							</xs:annotation>
							<xs:key name="icb-one-key">
								<xs:selector xpath="./st-serv:one" />
								<xs:field xpath="@id" />
							</xs:key>
							<xs:key name="icb-many-key">
								<xs:selector xpath="./st-serv:many" />
								<xs:field xpath="@domain" />
							</xs:key>
							<xs:key name="icb-except-id-key">
								<xs:selector xpath=".//st-serv:except-id" />
								<xs:field xpath="@id" />
							</xs:key>
							<xs:key name="icb-except-domain-key">
								<xs:selector xpath=".//st-serv:except-domain" />
								<xs:field xpath="@domain" />
							</xs:key>
							<xs:key name="icb-number-match-key">
								<xs:selector xpath="./st-serv:number-match" />
								<xs:field xpath="@starts-with" />
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="communication-diverted" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The communication-diverted element has values "true" or "false". If present with the value "true", this
						condition is satisfied if the incoming communication has been diverted. Set to "false" to remove this
						condition.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="incoming-call-barring-actions-type">
		<xs:sequence>
			<xs:element name="allow" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>
						The allow element has values "true" or "false". If set to "false" then any communications satisfying
						the corresponding conditions will be barred unless overridden by another rule with allow set to "true".
						If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. 
						not barred. This must be present on the creation of a communication barring rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
							The play-announcement element has string values from 0 to 32 characters. When the play-announcement action
							is set with the string value containing characters with the length between 1 to 32, if there is any communications
							satisfying the corresponding conditions and being barred (allow=false), the caller will be presented with the
							announcement associated with the announcement code pointed by the string value. When the play-announcement action
							is set with the string value containing character with the length of 0, any play-announcement action element in
							the rule will be deleted from the rule.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="play-segmented-announcement" minOccurs="0" maxOccurs="1" nillable="true">
					<xs:annotation>
						<xs:documentation>
							If there is any communications satisfying the corresponding conditions, the caller will be presented with the
							segmented announcement associated with the announcement code pointed by the "announcement-name" attribute of
							the element. Before trying to invoke any, the segmented (generic) announcement must be configured in MTAS with
							the same name as given in the "announcement-name" attribute. The segmented announcement may contain contain 
							embedded variables, which can be presented in the "announcement-variable" child element. The configured	segmented
							(generic) announencement shall contain as many standalone voice variable segments as many "announcement-variable" 
							child elements are defined for the "play-segmented-announcement" action. The keyed "play-segmented-announcement" 
							action with the "announcement-name"	attribute can be deleted from the list of actions by setting the "xs:nil" 
							attribute to true.
						</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="play-segmented-announcement-type">
								<xs:attribute name="announcement-name" type="announcement-name-type" use="required" />
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
					<xs:key name="AnnouncementVariableNameKey_IncomingCommunicationBarring">
						<xs:annotation>
							<xs:documentation>
								An announcement variable can be embedded into a segmented announcemet only once. Announcement variables, under
								the scope of a segmented announcement, are made unique by the "variable-name" attribute.
							</xs:documentation>
						</xs:annotation>
						<xs:selector xpath="./st-serv:announcement-variable" />
						<xs:field xpath="@variable-name" />
					</xs:key>
				</xs:element>
			</xs:choice>

		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="incoming-call-barring-op-actions-type">
		<xs:sequence>
			<xs:element name="allow-true-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The allow-true-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
						to use the allow action with the value of "true" in the associated communication barring rules to explicitly allow 
						communications that match the associated conditions. With this absent or set to "deactivated" the subscriber is only
						permitted to use the allow action with the value of "false" to bar communications.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The play-announcement-action element has values "activated" or "deactivated". When set to  "activated" it allows the
						subscriber to use the play-announcement action element by adding or removing the element into or from the rule. With
						this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The play-segmented-announcement-action element has values "activated" or "deactivated". When set to "activated" it
						allows the subscriber to use the play-segmented-announcement action element by adding or removing the element into
						or from the rule. With this absent or set to "deactivated" the subscriber is not permitted to use the
						play-segmented-announcement action.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="empty-element-type"/>
	<xs:complexType name="identity-type">
		<xs:annotation>
			<xs:documentation>
			The identity element is a grouping element for conditions which are based on a user's identity. The condition
			is satisfied if any of the one or many elements within it is matched.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="one" type="one-type" nillable="true">
					<xs:annotation>
						<xs:documentation>
						The one element specifies an individual identity to be matched. The one element is a sub-MO allowing
						multiple instances with "id" as the unique key.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="many" type="many-type" nillable="true">
					<xs:annotation>
						<xs:documentation>
						The many element specifies a match for a set of identities. The many element is a sub-MO allowing
						multiple instances with "domain" as the unique key
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="number-match" type="number-match-type" nillable="true">
					<xs:annotation>
						<xs:documentation>
						The number-match element specifies a match for a set of numerical identities. The number-match element is a
						sub-MO allowing multiple instances with "starts-with" as the unique key.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'id'-->
	<xs:complexType name="one-type">
		<xs:sequence>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
					The individual identity to be matched. For all uses except incoming communication barring user rules, this takes
					the form of a sip: or tel: URI. For use within incoming communication barring user rules, this takes the form of a
					sip: or tel: or hidden: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6
					of RFC 3261 contains a normalized number. This must be present on the creation of a one element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:anyURI" use="required"/>
	</xs:complexType>
	<xs:complexType name="many-type">
		<xs:sequence>
			<xs:element name="domain" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementDomain"/>
					</xs:appinfo>
					<xs:documentation>
					The individual domain to be matched. A many element with an explicit domain value matches all identities
					within that domain. A many element with the special wildcard value "*" matches all identities. This must be
					present on the creation of a many element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice minOccurs="0" maxOccurs="unbounded">
				<xs:element name="except-domain" type="except-domain-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
					An individual domain to be excluded from a many with special value "*" that would otherwise match all identities.
					The except-domain element is a sub-MO allowing multiple instances with "domain" as the unique key.
					</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="except-id" type="except-id-type" nillable="true" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
					An individual identity to be excluded from the identities matching the enclosing many. The except-id element
					is a sub-MO allowing multiple instances with "id" as the unique key.
					</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="domain" type="xs:string" use="required"/>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'id'-->
	<xs:complexType name="except-id-type">
		<xs:sequence>
			<xs:element name="id" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
					The individual identity to be excluded from the match. If this is within a many element with a specific domain
					then the excluded identity must be a sip: URI within that domain. If this is within a many element with the
					special wildcard value of "*", then it can be a sip: or tel: URI. Each tel: URI and sip: URI that was converted
					from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number. This must be present
					on the creation of an except-id element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:anyURI" use="required"/>
	</xs:complexType>
	<!-- This is a sub-MO with key attribute of 'domain'-->
	<xs:complexType name="except-domain-type">
		<xs:sequence>
			<xs:element name="domain" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementDomain"/>
					</xs:appinfo>
					<xs:documentation>
					The individual domain to be excluded from the match. This must be present on the creation of an except-domain
					element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="domain" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="number-match-type">
		<xs:sequence>
			<xs:element name="starts-with" type="starts-with-type" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementStarts-with"/>
					</xs:appinfo>
					<xs:documentation>
					The first few characters of the normalised form of the number to be matched.
					This must be present on the creation of a number-match element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="starts-with" type="starts-with-type" use="required"/>
	</xs:complexType>
	<xs:simpleType name="starts-with-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:pattern value="\+{0,1}\d{0,32}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="play-segmented-announcement-type">
		<xs:sequence>
			<xs:element name="announcement-name" type="announcement-name-type" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementAnnouncement-name"/>
					</xs:appinfo>
					<xs:documentation>
					The name of the announcement to be played. This must be present	on the creation of a
					play-segmented-announcement element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="announcement-variable" type="announcement-variable-type" minOccurs="0" maxOccurs="32" nillable="true">
				<xs:annotation>
					<xs:documentation>
					The announcement variable to be embedded into the announcement. It's use is optional, i.e. a segmented
					announcement may or may not contain any variable segment. Maximum 32 announcement variables can be
					embedded into a segmented announcement.
					A keyed "announcement-variable" element with the "variable-name" attribute can be deleted from the list
					of announcement variables by setting the "xs:nil" attribute to true.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="announcement-name-type">
		<xs:annotation>
			<xs:documentation>
				The allowed characters in an announcement name are restricted to a-z, A-Z, _ and 0-9.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
			<xs:pattern value="[0-9a-zA-Z_]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="announcement-variable-type">
		<xs:complexContent>
			<xs:extension base="announcement-variable-base-type">
            <xs:attribute name="variable-name" type="announcement-variable-name-type" use="required"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="announcement-variable-base-type">
		<xs:sequence>
			<xs:element name="variable-name" type="announcement-variable-name-type" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementVariable-name"/>
					</xs:appinfo>
					<xs:documentation>
					The name of the announcement variable to be embedded. This must be present on the creation of an
					announcement-variable element inside a play-segmented-announcement element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="variable-value" type="announcement-variable-value-type" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>
					The variable value is defined in the variable-value child element of the announcement-variable element.
					According to H.248.9, the allowed characters in place of a variable value are ASCII 0x09, 0x20-0x7E.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="announcement-variable-name-type">
		<xs:annotation>
			<xs:documentation>
				The allowed characters in a variable name are restricted to a-z, A-Z, _ and 0-9.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
			<xs:pattern value="[0-9a-zA-Z_]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="announcement-variable-value-type">
		<xs:annotation>
			<xs:documentation>
				The allowed characters in a variable value are restricted to ASCII 0x09, 0x20-0x7e and ASCII.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:pattern value="[&#x0009;&#x0020;-&#x007e;]+"/>
		</xs:restriction>
	</xs:simpleType>
    <xs:complexType name="st-malicious-communication-identification-type">
		<xs:sequence>
			<xs:element name="operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the st malicious communication identification service that is available to the operator rather than the user. This must be present on the creation of the st malicious communication identification service.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The activated element has values "true", "false". When set to "true" the PBX is provisioned with the st malicious communication identification service. If set to "false" this will withdraw the service from the PBX. This must be present on the creation of the st malicious communication identification service. 
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mcid-extension" nillable="true" minOccurs="0" maxOccurs="50">
							<xs:annotation>
								<xs:documentation> 
									The mcid extension element specifies an extension number in the PBX that has MCID activated. The element is a sub-MO allowing multiple instances with id as the unique key. This is limited to 50 instances.Only calls targeted at these numbers will result in an MCID report being generated
								</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
										<xs:element name="id" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:appinfo>
													<jaxb:property name="elementId"/>
												</xs:appinfo>
												<xs:documentation>
													The id for the mcid extension must be present on the creation of a mcid-extension element.
												</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="uri" type="xs:anyURI" minOccurs="0">
											<xs:annotation>
												<xs:documentation>
													The uri is the identity of the target. It is a sip: or tel: URI. Each tel: URI and sip: URI that was converted from a tel: URI according to section 19.1.6 of RFC 3261 contains a normalized number, or a number that can be This must be present on the creation of a mcid extension element.
												</xs:documentation>
											</xs:annotation>
										</xs:element>
								</xs:sequence>
								<xs:attribute name="id" type="xs:string" use="required"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
				<xs:key name="mcidExtensionIdKey">
					<xs:selector xpath="./st-serv:mcid-extension"/>
					<xs:field xpath="@id"/>
				</xs:key>
			</xs:element>
		</xs:sequence>
    </xs:complexType>
    <xs:complexType name="st-operator-controlled-outgoing-barring-programs-type">
		<xs:sequence>
			<xs:element name="operator-configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST operator controlled outgoing barring programs service that are available to
						the operator rather than the user. This must be present on the creation of the operator-controlled-outgoing-barring-programs
						service.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="activated-enum-type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The activated element has values "true" or "false". When set to "true" the user is provisioned with the ST
									operator controlled outgoing barring programs service. If set to "false" this will withdraw the service from
									the user. This must be present on the creation of the operator-controlled-outgoing-barring-programs service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:choice>
							<xs:element name="operator-barring-program" type="operator-barring-program-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
										The operator-barring-program element is a container for each of the categories of outgoing communications that
										is to be barred by the service. The operator-barring-program and operator-permitted-program are mutually
										exclusive.
									</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="operator-permitted-program" type="operator-permitted-program-type" nillable="true" minOccurs="0">
								<xs:annotation>
									<xs:documentation>
										The operator-permitted-program element is a container for each of the categories of outgoing communications that
										is to be allowed by the service - any identity not matched by one of these categories or the global white list is
										barred. The operator-barring-program and operator-permitted-program are mutually exclusive.
									</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:choice>
						<xs:element name="operator-diversion-barring-program" type="operator-diversion-barring-program-type" nillable="true" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The operator-diversion-barring-program element is a container for each of the categories of outgoing
									communications that should be barred as diversion targets.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
 	<xs:complexType name="operator-barring-program-type">
		<xs:sequence>
			<xs:element name="category-name" type="category-name-type" nillable="true" minOccurs="0" maxOccurs="83">
				<xs:annotation>
					<xs:documentation>
						The category-name element contains the name of a category of calls to be barred. This is a multi-value parameter and
						can appear between 0 and 83 times to cover each category of outgoing communications to be barred. The value of each
						category-name element is a string of up to 32 characters that should match one of the category names defined by the
						mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values "Local", "Non Local" or "Allow Local".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="operator-permitted-program-type">
		<xs:sequence>
			<xs:element name="category-name" type="category-name-type" nillable="true" minOccurs="0" maxOccurs="83">
				<xs:annotation>
					<xs:documentation>
						The category-name element contains the name of a category of calls to be permitted. This is a multi-value parameter
						and can appear between 0 and 83 times to cover each category of outgoing communications to be permitted. The value of
						each category-name element is a string of up to 32 characters that should match one of the category names defined by
						the mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values "Local" or "Non Local".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="operator-diversion-barring-program-type">
		<xs:sequence>
			<xs:element name="category-name" type="category-name-type" minOccurs="0" maxOccurs="83">
				<xs:annotation>
					<xs:documentation>
						The category-name element contains the name of a category of calls to be barred for diverted communications. This is
						a multi-value parameter and can appear between 0 and 83 times to cover each category of outgoing communications to be
						barred. The value of each category-name element is a string of up to 32 characters that should match one of the category
						names defined by the mtasOcbBCatName or mtasOcbOpBCatName attributes or one of the special values "Local", "Non Local" 
						or "Allow Local".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="category-name-type">
		<xs:restriction base="xs:string">
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="originating-identity-presentation-type">
		<xs:annotation>
			<xs:documentation>
			The SIP Trunking originating identity presentation service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="oip-operator-configuration-type">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST originating identity presentation service that are available to the operator.
					This must be present on the creation of the ST originating identity presentation service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="oip-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST originating identity presentation service can be set on the PBX's behalf by the operator. 
					This shall only be present if the service is provisioned i.e. operator-configuration is present and activated is "true".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oip-user-configuration-type">
		<xs:annotation>
			<xs:documentation>User part of ST OIP (ST Originating Identity Presentation)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The active element has values "true" or "false". It controls whether the ST originating identity presentation service is
					active or not for this PBX.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oip-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>
			Operator part of ST OIP (ST Originating Identity Presentation)
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the ST originating
					identity presentation service. If set to "false" this will withdraw the PBX service, but the user-configuration
					element is preserved. 
					This must be present on the creation of the ST originating identity presentation service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The restriction-override element has values "override-active" or "override-not-active". The value "override-active"
					means that the originating identity will be presented even if the calling party has requested for their presentation
					to be restricted.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="originating-identity-presentation-restriction-type">
		<xs:annotation>
			<xs:documentation>
			The SIP Trunking originating identity presentation restriction service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="oir-operator-configuration-type">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the ST originating identity presentation restriction service that are available to the
					operator. 
					This must be present on the creation of the ST originating identity presentation restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="oir-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The configuration parameters for the originating identity presentation restriction service that are available for the user to set
					directly. These can also be set on the user's behalf by the operator. 
					This shall only be present if the service is provisioned i.e.
					oir-operator-configuration is present and activated is "true".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oir-user-configuration-type">
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The active element has values "true" or "false". It controls whether the ST originating identity presentation restriction
					service is active or not for this PBX.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The default-behaviour element has values "presentation-restricted" or "presentation-not-restricted". It selects the default
					behaviour in temporary mode when the PBX does not select explicitly within the call whether to restrict their identity or not.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="oir-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>
			Operator part of ST originating identity presentation restriction (OIR)
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the ST originating
					identity presentation restriction service. If set to "false" this will withdraw the PBX service and the user-configuration
					element must be preserved. 
					This must be present on the creation of the ST originating identity presentation restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mode" type="identityPresentationModeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The mode element has values "permanent" or "temporary". The value "permanent" is used to give the PBX a permanent
					restriction service. In this case there must be no user-configuration element. The value "temporary" gives an ST identity
					presentation restriction service where the PBX can choose a default behaviour and also whether to override this on a
					per-call basis. 
					This must be present on the creation of the ST originating identity presentation restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction" type="identityPresentationRestrictionType" default="all-private-information" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The restriction element has values "only-identity" or "all-private-information" and selects whether just the identity of the
					PBX is restricted or all private information. 
					This must be present on the creation of the ST originating identity presentation restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="from-header-screening" type="enabledType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					The element controls execution of From header screening in requests sent by the originating PBX. It can be set to 
					“enabled” or “disabled”. If not present, the From header screening feature is instead controlled by the 
					mtasStFromHeaderScreening node parameter 
					Use xsi:nil=”true” to withdraw this element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--Common Types are defined below-->
	<xs:simpleType name="identityPresentationRestrictionOverrideType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="override-active"/>
			<xs:enumeration value="override-not-active"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationModeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="permanent"/>
			<xs:enumeration value="temporary"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationRestrictionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="only-identity"/>
			<xs:enumeration value="all-private-information"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<xs:simpleType name="identityPresentationDefaultBehaviourType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="presentation-restricted"/>
			<xs:enumeration value="presentation-not-restricted"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="enabledType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="enabled"/>
			<xs:enumeration value="disabled"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="outgoing-communication-barring-type">
		<xs:annotation>
			<xs:documentation>
				The ST Outgoing Communication Barring service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="ocb-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST Outgoing Communication Barring service that are available to the operator
						rather than the user. This must be present on the creation of the outgoing-communication-barring service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="ocb-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST Outgoing Communication Barring service that are available for the user to set
						directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
						provisioned i.e. ocb-operator-configuration is present and activated is "true"
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-operator-configuration-type">
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the user is provisioned with the outgoing
						communication barring service. If set to false this will withdraw the user service, but the ocb-user-configuration
						element is preserved. This must be present on the creation of the outgoing-communication-barring service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-op-conditions" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The ocb-op-conditions element is a grouping element for fine-grain provisioning options that control which condition
						elements the user is permitted to use in ST Outgoing Communication Barring rules.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The identity-condition element has values "activated" or "deactivated". When set to "activated" it allows the
									subscriber to use the identity condition in ST Outgoing Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="other-identity-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The other-identity-condition element has values "activated" or "deactivated". When set to "activated" it
									allows the subscriber to use the other-identity condition in ST Outgoing Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-condition" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The carrier-condition element has values "activated" or "deactivated". When set to "activated"
									it allows the subscriber to use the carrier condition in ST Outgoing Communication Barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-select-code" type="activatedType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>
									The carrier-select-code-condition element has values "activated" or "deactivated". When set to "activated"
									it allows the subscriber to use the carrier-select-code element of the carrier condition in outgoing
									communication barring rules.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ocb-op-actions" type="outgoing-call-barring-op-actions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The ocb-op-actions element is a grouping element for fine-grain provisioning options to control which action elements
						the user is permitted to use in ST Outgoing Communication Barring rules.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="rule-limit" type="rule-limit-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The maximum number of allowed ST Outgoing Communication Barring rules in the user document. Not specified or zero
						limit means no limit
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-user-configuration-type">
		<xs:annotation>
			<xs:documentation>
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The active element has values "true" or "false". It controls whether the ST Outgoing Communication Barring service is
						active or not for this subscriber. Note that this controls the user rules but has no effect on the operator rules.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-ruleset" type="ocb-ruleset-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Grouping element for a set of zero or more user rules.
					</xs:documentation>
				</xs:annotation>
				<!-- NOTE makes rule id unique within the ruleset -->
				<xs:key name="ocb-user-rule-key">
					<xs:selector xpath="./st-serv:ocb-rule" />
					<xs:field xpath="@id" />
				</xs:key>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-ruleset-type">
		<xs:sequence>
			<xs:element name="ocb-rule" type="ocb-rule-type" nillable="true" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						An individual rule controlling ST Outgoing Communication Barring behaviour. The ocb-rule element is a sub-MO
						allowing multiple instances with "id" as the unique key.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ocb-rule-type">
		<xs:sequence>
			<xs:element name="id" type="xs:NCName" minOccurs="0">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="elementId"/>
					</xs:appinfo>
					<xs:documentation>
						A unique identifier for an individual rule. This must be unique within the scope of the complete document.
						This must be present on the creation of an ocb-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-conditions" type="ocb-conditions-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The ocb-conditions element is a grouping element for conditions for a rule. All conditions must be satisfied
						for the rule to take effect. If no conditions are present then the rule is always applicable.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<!-- NOTE cb-actions is optional but not nillable. Every barring rule must have an allow action to be valid but cai3g:Set 
				could just update conditions so actions must be optional -->
			<xs:element name="cb-actions" type="outgoing-call-barring-actions-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The cb-actions element is a grouping element for the actions for a rule. For ST Outgoing Communication Barring an
						allow action must be present in each rule. This must be present on the creation of an ocb-rule element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="id" type="xs:NCName" use="required" />
	</xs:complexType>
	<xs:complexType name="ocb-conditions-type">
		<xs:sequence>
			<xs:element name="rule-deactivated" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The rule-deactivated element has values "true" or "false". If present with the value "true" this has the effect
						of deactivating the individual rule. Set to "false" to remove this condition.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ocb-caller-identity" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The ocb-caller-identity element is a grouping element for conditions which are based on the called party's identity.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="other-identity" type="empty-element-type">
							<xs:annotation>
								<xs:documentation>
									The other-identity element is an empty element which matches any identity that has not been specified by
									any of the other rules in the ruleset. It allows for setting a default policy. This can be removed by deleting the
									enclosing ocb-caller-identity element or by replacing it with an identity element. The elements identity
									and other-identity are mutually exclusive.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<!-- no nilling at the level of identity - use nilling on ocb-caller-identity to remove -->
						<xs:element name="identity" type="identity-type">
							<xs:annotation>
								<xs:documentation>
									The identity element is a grouping element for conditions which are based on the called party's identity. The
									condition is satisfied if any of the included one or many elements within it is matched. This can be removed
									by deleting the enclosing ocb-caller-identity element or by replacing it with an other-identity element. The
									elements identity and other-identity are mutually exclusive.
								</xs:documentation>
							</xs:annotation>
							<xs:key name="ocb-one-key">
								<xs:selector xpath="./st-serv:one" />
								<xs:field xpath="@id" />
							</xs:key>
							<xs:key name="ocb-many-key">
								<xs:selector xpath="./st-serv:many" />
								<xs:field xpath="@domain" />
							</xs:key>
							<xs:key name="ocb-except-id-key">
								<xs:selector xpath=".//st-serv:except-id" />
								<xs:field xpath="@id" />
							</xs:key>
							<xs:key name="ocb-except-domain-key">
								<xs:selector xpath=".//st-serv:except-domain" />
								<xs:field xpath="@domain" />
							</xs:key>
							<xs:key name="ocb-number-match-key">
								<xs:selector xpath="./st-serv:number-match" />
								<xs:field xpath="@starts-with" />
							</xs:key>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="carrier" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The carrier element is a grouping element for conditions which are based on the carrier selected for the call
						on call-by-call basis. If no sub-element is specified, all carriers are matched. The carriers that match to the
						pre-subscribed carriers for the current call-type are subject to this condition.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="carrier-select-code" type="carrier-select-code-type" nillable="true" minOccurs="0"
							maxOccurs="10">
							<xs:annotation>
								<xs:documentation>
									The carrier-select-code element contains the dialed Carrier Select Code. This is a multi-value 
									parameter so it can appear more than once with several Carrier Select Codes. If any of them is
									matches, the carrier condition is fulfilled.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="carrier-name" type="carrier-name-type" nillable="true" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation>
									The carrier-name element contains an alias name of the carrier selected for the call on call-by-call basis.
									This is a multi-value parameter so it can appear more than once with several carrier names. If any of them
									is matches, the carrier condition is fulfilled.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="outgoing-call-barring-actions-type">
		<xs:sequence>
			<xs:element name="allow" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>
						The allow element has values "true" or "false". If set to "false" then any communications satisfying
						the corresponding conditions will be barred unless overridden by another rule with allow set to "true".
						If set to "true" then any communications satisfying the corresponding conditions will be allowed i.e. not barred.
						This must be present on the creation of a communication barring rule.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:element name="play-announcement" type="play-announcement-type" minOccurs="0">
					<xs:annotation>
						<xs:documentation>
							The play-announcement element has string values from 0 to 32 characters.
							When the play-announcement action is set with the string value containing characters with the length between 1 to 32,
							if there is any communications satisfying the corresponding conditions and being barred (allow=false), the caller
							will be presented with the announcement associated with the announcement code pointed by the string value.
							When the play-announcement action is set with the string value containing character with the length of 0,
							any play-announcement action element in the rule will be deleted from the rule.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="play-segmented-announcement" minOccurs="0" maxOccurs="1" nillable="true">
					<xs:annotation>
						<xs:documentation>
							If there is any communications satisfying the corresponding conditions, the caller will be presented with the
							segmented announcement associated with the announcement code pointed by the "announcement-name" attribute of the
							element. Before trying to invoke any, the segmented (generic) announcement must be configured in MTAS with the
							same name as given in the "announcement-name" attribute. The segmented announcement may contain contain embedded
							variables, which can be presented in the "announcement-variable" child element. The configured segmented (generic)
							announencement shall contain as many standalone voice variable segments	as many "announcement-variable" child elements
							are defined for the "play-segmented-announcement" action. The keyed "play-segmented-announcement" action with the
							"announcement-name" attribute can be deleted from the list of actions by setting the "xs:nil" attribute to true.
						</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="play-segmented-announcement-type">
								<xs:attribute name="announcement-name" type="announcement-name-type" use="required" />
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
					<xs:key name="AnnouncementVariableNameKey_OutgoingCommunicationBarring">
						<xs:annotation>
							<xs:documentation>
								An announcement variable can be embedded into a segmented announcemet only once. Announcement variables, under
								the scope of a segmented announcement, are made unique by the "variable-name" attribute.
							</xs:documentation>
						</xs:annotation>
						<xs:selector xpath="./st-serv:announcement-variable" />
						<xs:field xpath="@variable-name" />
					</xs:key>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="outgoing-call-barring-op-actions-type">
		<xs:sequence>
			<xs:element name="allow-true-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The allow-true-action element has values "activated" or "deactivated". When set to "activated" it allows the subscriber
						to use the allow action with the value of "true" in the associated communication barring rules to explicitly allow
						communications that match the associated conditions. With this absent or set to "deactivated" the subscriber is
						only permitted to use the allow action with the value of "false" to bar communications.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The play-announcement-action element has values "activated" or "deactivated". When set to "activated" it allows the
						subscriber to use the play-announcement action element by adding or removing the element into or from the rule.
						With this absent or set to "deactivated" the subscriber is not permitted to use the play-announcement action.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="play-segmented-announcement-action" type="activatedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The play-segmented-announcement-action element has values "activated" or "deactivated". When set to "activated" it
						allows the subscriber to use the play-segmented-announcement action element by adding or removing the element into or from  
						the rule. With this absent or set to "deactivated" the subscriber is not permitted to use the play-segmented-announcement
						action.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="carrier-select-code-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="8"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="carrier-name-type">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<!-- Terminating Identity Presentation/Restriction -->
	<xs:complexType name="terminating-identity-presentation-type">
		<xs:annotation>
			<xs:documentation>
				The SIP Trunking terminating identity presentation service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="tip-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST terminating identity presentation service that are available to the operator.
						This must be present on the creation of the ST terminatinf identity presentation service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="tip-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the ST terminating identity presentation service can be set on the PBX's behalf by the operator.
						This shall only be present if the service is provisioned i.e. operator-configuration is present and activated is "true".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tip-user-configuration-type">
		<xs:annotation>
			<xs:documentation>User part of ST TIP (ST Terminating Identity Presentation)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The active element has values "true" or "false". It controls whether the ST terminating identity presentation service is
						active or not for this PBX.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tip-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>
				Operator part of ST TIP (ST Terminating Identity Presentation)
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the PBX is provisioned with the ST terminating
						identity presentation service. If set to "false" this will withdraw the PBX service, but the user-configuration
						element is preserved.
						This must be present on the creation of the ST terminating identity presentation service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="restriction-override" type="identityPresentationRestrictionOverrideType" default="override-not-active" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The restriction-override element has values "override-active" or "override-not-active". The value "override-active"
						means that the terminating identity will be presented even if the called party has requested for their presentation
						to be restricted.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="terminating-identity-presentation-restriction-type">
		<xs:annotation>
			<xs:documentation>
				The terminating identity presentation restriction service. Use xsi:nil="true" to withdraw the entire service.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="operator-configuration" type="tir-operator-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the terminating identity presentation restriction service that are available to the operator
						rather than the user. This must be present on the creation of the terminating-identity-presentation-restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="user-configuration" type="tir-user-configuration-type" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The configuration parameters for the terminating identity presentation restriction service that are available for the user to
						set directly. These can also be set on the user's behalf by the operator. This shall only be present if the service is
						provisioned i.e. tir-operator-configuration is present and activated="true".
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tir-operator-configuration-type">
		<xs:annotation>
			<xs:documentation>
				Operator part of terminating identity presentation restriction (TIPR)
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="activated" type="activated-enum-type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The activated element has values "true" or "false". When set to "true" the user is provisioned with the terminating identity
						presentation restriction service. If set to "false" this will withdraw the user service and the tir-user-configuration element
						must be preserved. This must be present on the creation of the terminating-identity-presentation-restriction
						service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mode" type="identityPresentationModeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The mode element has values "permanent" or "temporary". The value "permanent" is used to give the user a permanent
						restriction service. In this case there must be no tir-user-configuration element. The value "temporary" gives an identity
						presentation restriction service where the user can choose a default behaviour and also whether to override this on a
						per-call basis. This must be present on the creation of the terminating-identity-presentation-restriction service.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="connected-identity-support" type="enabledType" nillable="true" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The element controls if signaling of connected identity from the PBX is enabled. It can be set to "enabled" or "disabled".
						If the element is not present, the default setting is “disabled”. When connected identity is not supported,
						the From-change feature is removed from the Supported header of the initial request sent to the PBX.
						Use xsi:nil=”true” to withdraw the element.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<xs:complexType name="tir-user-configuration-type">
		<xs:annotation>
			<xs:documentation>
				User part of terminating identity presentation restriction (TIPR)
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="active" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The active element has values "true" and "false". It controls whether the terminating identity presentation restriction
						service is active or not for this subscriber.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="default-behaviour" type="identityPresentationDefaultBehaviourType" default="presentation-restricted" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						The default-behaviour element has values "presentation-restricted" and "presentation-not-restricted". It selects the
						default behaviour in temporary mode when the user does not select explicitly within the call whether to restrict their
						identity or not
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
</xs:schema>