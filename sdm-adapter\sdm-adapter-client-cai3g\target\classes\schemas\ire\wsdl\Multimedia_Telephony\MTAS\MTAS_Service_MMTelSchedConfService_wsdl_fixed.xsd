<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSchedConfService/" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSchedConfService/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:element name="publicId" type="publicIdentityType"/>
	<xs:element name="createService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Creating MMTel Profile
				Service
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType"/>
				<xs:element name="scheduled-conference" type="scheduled-conference-type">
					<xs:annotation>
						<xs:documentation xml:lang="en">The scheduled
							conference service.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyCreate">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="setService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Setting MMTel Profile
				Service
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
                <!-- added by xolseri -->
                <xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an optional element to control concurrent updates. If present then the set
                            request will be accepted only if the service data version is still at the value given in this element i.e. no other
                            updates have been performed. It is of type integer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
				<xs:element name="scheduled-conference" type="scheduled-conference-type">
					<xs:annotation>
						<xs:documentation xml:lang="en">The scheduled
							conference service.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeySet">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:element name="getResponseService">
		<xs:annotation>
			<xs:documentation xml:lang="en">Getting MMTel Profile
				Service
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="publicId" type="publicIdentityType">
					<xs:annotation>
						<xs:documentation xml:lang="en">The default public
							user identity for the subscriber
						</xs:documentation>
					</xs:annotation>
				</xs:element>
                <!-- added by xolseri -->
                <xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an optional element to control concurrent updates. If present then the set
                            request will be accepted only if the service data version is still at the value given in this element i.e. no other
                            updates have been performed. It is of type integer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
				<xs:element name="scheduled-conference" type="scheduled-conference-type">
					<xs:annotation>
						<xs:documentation xml:lang="en">The scheduled
							conference service.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="publicId" type="publicIdentityType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="publicIdKeyGetResp">
			<xs:selector xpath="."/>
			<xs:field xpath="@publicId"/>
		</xs:key>
	</xs:element>
	<xs:simpleType name="publicIdentityType">
		<xs:restriction base="xs:anyURI">
			<xs:pattern value="sip:.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="scheduled-conference-type">
		<xs:sequence>
			<xs:element name="scheduled-conference-operator-configuration">
				<xs:annotation>
					<xs:documentation xml:lang="en">The configuration
						parameters for the scheduled conference service that are only
						available to the operator
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="activated" type="xs:boolean">
							<xs:annotation>
								<xs:documentation xml:lang="en">The activated
									element has values "true" or "false". When set to "true" the
									scheduled conference is ready for traffic operation. This
									element must be present on the creation of the
									scheduled-conference service.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="service-number" type="xs:anyURI" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The identity of the
									Scheduled Conference Service Number that this user is served
									by. Must be a TEL URI (RFC 3966) The element must be present on
									the creation of the scheduled-conference service. In
									addition,
									the service number must be a valid PSI in HSS.
								</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
