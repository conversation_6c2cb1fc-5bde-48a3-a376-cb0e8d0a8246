<!-- edited with XMLSpy v2012 rel. 2 (http://www.altova.com) by <PERSON> User (U.S. Cellular) --><!-- 
Build_Label: REL000
ClearQuest_MR#: 12121 
Build_Date: 2017-01-26-15:55:27
 --><xsd:schema targetNamespace="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/messages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_0" xmlns:enterprise_billing_billmanagement_messages="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/messages" xmlns:enterprise_billing_billmanagement_xsd="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:enterprise_billing_xsd="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types">
	<xsd:annotation>
		<xsd:documentation>Contains Messages used for billManagement local proxyservice.</xsd:documentation>
	</xsd:annotation>
	
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/billing/billmanagement/v1_0/types" schemaLocation="enterprise_billing_billmanagement_types_v1_1.xsd"/>
	<xsd:element name="getBillPdf_Request" type="enterprise_billing_billmanagement_messages:getBillPdf_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request message for getBillPdf.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getBillPdf_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getBillPdf_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="customerId" type="enterprise_common_xsd:CustomerIdType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Customer Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="barId" type="enterprise_common_xsd:BarType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Bar Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="billId" type="enterprise_common_xsd:billIdType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Bill Id .</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
					</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="getBillPdf_Response" type="enterprise_billing_billmanagement_messages:getBillPdf_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response message for getBill.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getBillPdf_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getBill_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="billId" type="enterprise_common_xsd:billIdType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>bill Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PdfBytes" type="enterprise_common_xsd:pdfByptesType" minOccurs="1">
			<xsd:annotation>
					<xsd:documentation>base 64 binary pdf stream</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>	
	
	
	
	<xsd:element name="getBillList_Request" type="enterprise_billing_billmanagement_messages:getBillList_RequestType">
		<xsd:annotation>
			<xsd:documentation>Request message for getBillPdf.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="getBillList_RequestType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getBillPdf_Request'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
		<xsd:element name="customerId" type="enterprise_common_xsd:CustomerIdType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Customer Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="barId" type="enterprise_common_xsd:BarType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>bar Id.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="fromDate" type="enterprise_common_xsd:fromDateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>StartDate.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="toDate" type="enterprise_common_xsd:toDateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>EndDate.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
					</xsd:sequence>
	</xsd:complexType>
	
	<xsd:element name="getBillList_Response" type="enterprise_billing_billmanagement_messages:getBillList_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Response message for getBillList.</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	
	<xsd:complexType name="getBillList_ResponseType">
		<xsd:annotation>
			<xsd:documentation>Definition of the element 'getBill_Response'.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
		   <xsd:element name="billList" type="enterprise_billing_billmanagement_messages:billListType" minOccurs="0" maxOccurs="unbounded"/>
		   </xsd:sequence>
	</xsd:complexType>	
	
	
		<xsd:complexType name="billListType">
			<xsd:sequence>
				<xsd:element name="startDate" type="enterprise_common_xsd:fromDateType">
				<xsd:annotation>
					<xsd:documentation>Response information.</xsd:documentation>
				</xsd:annotation>
				</xsd:element>
				<xsd:element name="endDate" type="enterprise_common_xsd:toDateType">
				<xsd:annotation>
					<xsd:documentation>Response information.</xsd:documentation>
				</xsd:annotation>
				</xsd:element>
				<xsd:element name="billId" type="enterprise_common_xsd:billIdType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>bill Id.</xsd:documentation>
				</xsd:annotation>
				</xsd:element>
				<xsd:element name="available" type="enterprise_common_xsd:IndicatorBooleanType" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>Bill Availability boolean Indicator.</xsd:documentation>
				</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
		
</xsd:schema>