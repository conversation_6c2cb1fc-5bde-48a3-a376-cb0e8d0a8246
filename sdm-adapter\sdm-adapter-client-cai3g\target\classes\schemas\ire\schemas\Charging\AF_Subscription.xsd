<xs:schema targetNamespace="http://schemas.ericsson.com/ma/CS/AF/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.ericsson.com/ma/CS/AF/" xmlns:af="http://schemas.ericsson.com/ma/CS/AF/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb">
   <xs:element name="msisdn" type="msisdnType"/>
   <xs:element name="imsi" type="imsiType"/>
   <xs:element name="private" type="privateType"/>
   <xs:element name="nai" type="naiType"/>
   <xs:element name="sipuri" type="sipuriType"/>
   <xs:element name="createSubscription">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="msisdn" type="msisdnType"/>
            <xs:element name="ttl" type="ttlType" minOccurs="0"/>
            <xs:element name="sdpHostName" type="sdpHostNameType"/>
            <xs:element name="prerequisite" type="prerequisiteType" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
      <xs:key name="key_msisdn">
         <xs:selector xpath="./af:msisdn"/>
         <xs:field xpath="."/>
      </xs:key>
      <xs:keyref name="keyref_msisdn" refer="key_msisdn">
         <xs:selector xpath="."/>
         <xs:field xpath="@msisdn"/>
      </xs:keyref>
   </xs:element>
   <xs:element name="setSubscription">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="ttl" type="ttlType" minOccurs="0"/>
            <xs:element name="sdpHostName" type="sdpHostNameType"/>
            <xs:element name="prerequisite" type="prerequisiteType" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="msisdn" type="msisdnType" use="required">
            <xs:annotation>
               <xs:appinfo>
                  <jaxb:property name="msisdnAttr"/>
               </xs:appinfo>
            </xs:annotation>
         </xs:attribute>
      </xs:complexType>
   </xs:element>
   <xs:element name="getSubscriptionResponse">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="msisdn" type="msisdnType"/>
            <xs:element name="class" type="classType"/>
            <xs:element name="ttl" type="ttlType"/>
            <xs:element name="sdpHostName" type="sdpHostNameType"/>
            <xs:element name="sdpIP" type="sdpIPType"/>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
   <!--simple type definition-->
   <xs:simpleType name="msisdnType">
      <xs:restriction base="xs:string">
          <xs:pattern value="[0-9]{1,28}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="imsiType">
      <xs:restriction base="xs:string">
         <xs:pattern value="[0-9]{1,15}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="privateType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="naiType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="sipuriType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ttlType">
      <xs:restriction base="xs:integer">
         <xs:minInclusive value="0"/>
         <xs:maxInclusive value="2147483647"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="sdpHostNameType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="prerequisiteType">
      <xs:restriction base="xs:boolean"/>
   </xs:simpleType>
   <xs:simpleType name="classType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="sdpIPType">
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
</xs:schema>