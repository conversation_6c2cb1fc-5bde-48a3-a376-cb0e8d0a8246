<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-party="http://amdocs/core/ossj-Common-CBEParty/dat/3" xmlns:amdocs-customer="http://xmlns.aua.oss.amdocs.com/auai/Common-CBECustomer/3" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:amdocs-resource="http://amdocs/core/ossj-Common-CBEResource/dat/3" xmlns:amdocs-service="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5" xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5" targetNamespace="http://amdocs/core/ossj-Common-CBEResource/dat/3" elementFormDefault="qualified">
	<xs:annotation>
		<xs:documentation>This schema was generated at: 07 April 2011</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://amdocs/core/ossj-Common/dat/3" schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common/v1-5" schemaLocation="OSSJ-Common-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5" schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5" schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common-CBEParty/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBEParty_3p0.xsd"/>
	<xs:element name="ResourceKey" type="amdocs-resource:ResourceKey"/>
	<xs:complexType name="ResourceKey">
		<xs:complexContent>
			<xs:extension base="cberesource-v1-5:ResourceKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfResourceKey" type="amdocs-resource:ArrayOfResourceKey"/>
	<xs:complexType name="ArrayOfResourceKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-resource:ResourceKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ResourceSpecificationKey" type="amdocs-resource:ResourceSpecificationKey"/>
	<xs:complexType name="ResourceSpecificationKey">
		<xs:complexContent>
			<xs:extension base="cberesource-v1-5:ResourceSpecificationKey">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ResourceSpecificationValue" type="amdocs-resource:ResourceSpecificationValue"/>
	<xs:complexType name="ResourceSpecificationValue">
		<xs:complexContent>
			<xs:extension base="cberesource-v1-5:ResourceSpecificationValue">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ResourceValue" type="amdocs-resource:ResourceValue"/>
	<xs:complexType name="ResourceValue">
		<xs:complexContent>
			<xs:extension base="cberesource-v1-5:ResourceValue">
				<xs:sequence>
				<!-- check some of these --> 
					<xs:element name="name" type="xs:string" minOccurs="0"/>
					<xs:element name="make" type="xs:string" minOccurs="0"/>
					<xs:element name="model" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="resourceRole" type="xs:string" minOccurs="0"/>
					<xs:element name="configurationRecords" type="amdocs-co:ConfigurationRecordsValue" minOccurs="0"/>
					<xs:element name="resourceType" type="xs:string" minOccurs="0"/>
					<xs:element name="serialNumber" type="xs:string" minOccurs="0"/>
					<xs:element name="owner" type="amdocs-party:PartyRoleValue" minOccurs="0"/>
					<xs:element name="location" type="xs:string" minOccurs="0"/>
					<xs:element name="state" type="xs:string" minOccurs="0"/>
					<xs:element name="resourceSubType" type="xs:string" minOccurs="0"/>
					<xs:element name="associatedServices" type="cbeservice-v1-5:ArrayOfServiceKey" minOccurs="0"/>
					<xs:element name="associatedResources" type="cberesource-v1-5:ArrayOfResourceKey" minOccurs="0"/>
					<xs:element name="previousResourceKey" type="amdocs-resource:ResourceKey" minOccurs="0"/>
					<xs:element name="previousResourceConfiguration" type="amdocs-resource:ResourceValue" minOccurs="0"/>
					<xs:element name="activationTargets" type="amdocs-co:ArrayOfActivationTargetValue" minOccurs="0"/>
					<xs:element name="operator" type="xs:string" minOccurs="0"/>
					<!-- Used by SM --> 
					<xs:element name="associatedProductKey" type="cbecore-v1-5:EntityKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
</xs:schema>
