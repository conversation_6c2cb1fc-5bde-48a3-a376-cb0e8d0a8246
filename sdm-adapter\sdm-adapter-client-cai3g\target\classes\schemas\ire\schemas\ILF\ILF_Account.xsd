<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/ILF" xmlns:x="http://schemas.ericsson.com/ema/UserProvisioning/ILF" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/ILF" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
  <xs:element name="accountId" type="accountIdType"/>
  <xs:element name="routingEntityType" type="routingEntityTypeType"/>
  <xs:element name="routingEntityValue" type="routingEntityValueType"/>
  <xs:element name="destinationType" type="destinationTypeType"/>
  <xs:element name="destinationValue" type="destinationValueType"/>

  <xs:element name="createAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="slfEntry" type="slfEntryType" maxOccurs="unbounded"/>
        <xs:element name="destinationEntry" type="destinationEntryType" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>

    <xs:key name="slfEntryKeyCreate">
      <xs:selector xpath=".//x:slfEntry"/>
      <xs:field xpath="./x:routingEntityValue"/>
      <xs:field xpath="./x:routingEntityType"/>
    </xs:key>

    <xs:key name="destinationEntryKeyCreate">
      <xs:selector xpath=".//x:destinationEntry"/>
      <xs:field xpath="./x:destinationType"/>
    </xs:key>
  </xs:element>

  <xs:element name="getAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="slfEntry" type="slfEntryType" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>

    <xs:key name="slfEntryKeyGet">
      <xs:selector xpath=".//x:slfEntry"/>
      <xs:field xpath="./x:routingEntityValue"/>
      <xs:field xpath="./x:routingEntityType"/>
    </xs:key>
  </xs:element>

  <xs:element name="getResponseAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="accountId"/>
        <xs:element name="slfEntry" type="slfEntryType" maxOccurs="unbounded"/>
        <xs:element name="destinationEntry" type="destinationEntryType" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="accountId" type="accountIdType" use="required">
        <xs:annotation>
          <xs:appinfo>
            <jaxb:property name="accountIdAttr"/>
          </xs:appinfo>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>

    <xs:key name="key_get_account">
      <xs:selector xpath="./x:accountId"/>
      <xs:field xpath="."/>
    </xs:key>
    <xs:keyref name="keyref_get_account" refer="key_get_account">
      <xs:selector xpath="."/>
      <xs:field xpath="@accountId"/>
    </xs:keyref>
  </xs:element>

  <xs:element name="setAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="slfEntry" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element ref="routingEntityValue"/>
              <xs:element ref="routingEntityType"/>
              <xs:element name="action" type="actionType" minOccurs="0"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="destinationEntry" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element ref="destinationValue"/>
              <xs:element ref="destinationType"/>
              <xs:element name="action" type="actionType" minOccurs="0"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>

    <xs:key name="slfEntryKeySet">
      <xs:selector xpath=".//x:slfEntry"/>
      <xs:field xpath="./x:routingEntityValue"/>
      <xs:field xpath="./x:routingEntityType"/>
    </xs:key>

    <xs:key name="destinationEntryKeySet">
      <xs:selector xpath=".//x:destinationEntry"/>
      <xs:field xpath="./x:destinationType"/>
    </xs:key>
  </xs:element>

  <xs:element name="deleteAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="slfEntry" type="slfEntryType" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>

    <xs:key name="slfEntryKeyDelete">
      <xs:selector xpath=".//x:slfEntry"/>
      <xs:field xpath="./x:routingEntityValue"/>
      <xs:field xpath="./x:routingEntityType"/>
    </xs:key>
  </xs:element>

  <!-- below is type definition -->
  <xs:complexType name="slfEntryType">
    <xs:sequence>
      <xs:element ref="routingEntityValue"/>
      <xs:element ref="routingEntityType"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="destinationEntryType">
    <xs:sequence>
      <xs:element ref="destinationValue"/>
      <xs:element ref="destinationType"/>
    </xs:sequence>
  </xs:complexType>

  <xs:simpleType name="accountIdType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
  <xs:simpleType name="actionType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
  <xs:simpleType name="routingEntityTypeType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
  <xs:simpleType name="routingEntityValueType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
  <xs:simpleType name="destinationTypeType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
  <xs:simpleType name="destinationValueType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>
</xs:schema>
