<!-- edited with XMLSpy v2012 rel. 2 (http://www.altova.com) by <PERSON> User (U.S. Cellular) -->
<!-- 
Build_Label: REL500
ClearQuest_MR#: 00348796 
Build_Date: 2016-12-07-14:56:28
-->
<xsd:schema xmlns:enterprise_orders_xsd="http://services.uscellular.com/schema/enterprise/orders/v1_0/types" xmlns:enterprise_common_xsd="http://services.uscellular.com/schema/enterprise/common/v2_0/types" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:enterprise_payment_xsd="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" targetNamespace="http://services.uscellular.com/schema/enterprise/payment/v1_0/types" elementFormDefault="qualified" attributeFormDefault="unqualified" version="v1_0">
	<xsd:annotation>
		<xsd:documentation>Consists of common data types used for enterprise services related to orders.</xsd:documentation>
	</xsd:annotation>
	<xsd:import namespace="http://services.uscellular.com/schema/enterprise/common/v2_0/types" schemaLocation="enterprise_common_types_v2_0.xsd"/>
	<!-- Types            -->
	<xsd:simpleType name="BankAccountNumberType">
		<xsd:annotation>
			<xsd:documentation>Bank account number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="64"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BankCodeType">
		<xsd:annotation>
			<xsd:documentation>Bank code.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardNumberType">
		<xsd:annotation>
			<xsd:documentation>Card number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardNumberLastFourType">
		<xsd:annotation>
			<xsd:documentation>Last 4 digits of the card number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CreditCardNumberType">
		<xsd:annotation>
			<xsd:documentation>Credit number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardExpirationMonthType">
		<xsd:annotation>
			<xsd:documentation>Expiration month of the credit/debit card.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int">
			<xsd:pattern value="1[0-2]|0?[1-9]"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardExpirationYearType">
		<xsd:annotation>
			<xsd:documentation>Expiration year of the credit/debit card.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int">
			<xsd:totalDigits value="4"/>
			<xsd:pattern value="(2)\d{3}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardHolderNameType">
		<xsd:annotation>
			<xsd:documentation>Full name of the customer on the card.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="80"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CVNumberType">
		<xsd:annotation>
			<xsd:documentation>CV number of the credit/debit card.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:int">
			<xsd:maxInclusive value="9999"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CVVType">
		<xsd:annotation>
			<xsd:documentation>Card verification number of the credit/debit card.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="3"/>
			<xsd:maxLength value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FirstNameType">
		<xsd:annotation>
			<xsd:documentation>First or given name</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LastNameType">
		<xsd:annotation>
			<xsd:documentation>Last name </xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MiddleNameType">
		<xsd:annotation>
			<xsd:documentation>General name field.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="OriginatorType">
		<xsd:annotation>
			<xsd:documentation>Originator data, used by USCC to look up the merchant Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PaymentIdType">
		<xsd:annotation>
			<xsd:documentation>This id is a unique id that is created by service	consumer.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="32"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PayMeansIdType">
		<xsd:annotation>
			<xsd:documentation>Pay Means ID.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="64"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RoutingNumberType">
		<xsd:annotation>
			<xsd:documentation>Routing number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SubscriptionIdType">
		<xsd:annotation>
			<xsd:documentation>Subscription Id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="26"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="OriginatorInfoType">
		<xsd:annotation>
			<xsd:documentation>Originator information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="originator" type="enterprise_payment_xsd:OriginatorType">
				<xsd:annotation>
					<xsd:documentation>Originator name, used by USCC to look up the merchant Id. This identifies the payment source like CIM, IVR, MyAccount etc. For ex. for MyAccount this is MYACCT.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="originatorLocation" type="enterprise_payment_xsd:OriginatorType">
				<xsd:annotation>
					<xsd:documentation>Originator location, used by USCC to look up the merchant Id. For ex. for MyAccount this is MYACCT.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- new added -->
	<xsd:complexType name="PaginationInfoType">
		<xsd:annotation>
			<xsd:documentation>Pagination information. </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="pageSize" type="xsd:int">
				<xsd:annotation>
					<xsd:documentation>the number of records that we want to see in a page.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="pageNumber" type="xsd:int">
				<xsd:annotation>
					<xsd:documentation>The page number of the results requested.
        			</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="maxResultsCount" type="xsd:int">
				<xsd:annotation>
					<xsd:documentation>the maximum records that we want to  fetch from the database.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="PaginationResultsInfoType">
		<xsd:annotation>
			<xsd:documentation>Pagination type for search result.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="moreRows" type="xsd:boolean">
				<xsd:annotation>
					<xsd:documentation>Indicates if there are more pages of data to retrieve.
1 means that there are more records to display (0 means there are no record left to display).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="rowCount" type="xsd:int" minOccurs="1">
				<xsd:annotation>
					<xsd:documentation>the number of records that currently in this output display.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="resultsCount" type="xsd:int" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>the total number that exists, this output field will return only in the first page</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="DateType">
		<xsd:annotation>
			<xsd:documentation>	The date.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:dateTime"/>
	</xsd:simpleType>
	<xsd:simpleType name="PaymentOptionType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>AR Balance.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BILL">
				<xsd:annotation>
					<xsd:documentation>Last Billed amount.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OTHER">
				<xsd:annotation>
					<xsd:documentation>other amount, customers choice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VoucherIdType">
		<xsd:annotation>
			<xsd:documentation>Scheduled Payment ID.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="12"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EntitySequenceNumberType">
		<xsd:annotation>
			<xsd:documentation>sequence number .</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ReferenceNumberType">
		<xsd:annotation>
			<xsd:documentation>reference number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Structures       -->
	<!-- Enumerations     -->
	<xsd:simpleType name="BankAccountTypeType">
		<xsd:annotation>
			<xsd:documentation>Bank account type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Checking">
				<xsd:annotation>
					<xsd:documentation>Account type is Checking.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Savings">
				<xsd:annotation>
					<xsd:documentation>Account type is Savings.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CreditCardTypeType">
		<xsd:annotation>
			<xsd:documentation>Credit card types.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AmericanExpress">
				<xsd:annotation>
					<xsd:documentation>Credit card type is American Expresss.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Discover">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Discover.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MasterCard">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Master Card.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Visa">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Visa.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CardTypeShortNotationType">
		<xsd:annotation>
			<xsd:documentation>Credit/Debit card types defined in the short notation.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Credit card type is American Expresss.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DS">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Discover.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MC">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Master Card.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VS">
				<xsd:annotation>
					<xsd:documentation>Credit card type is Visa.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
