<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/EIR/"
	elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<!-- CreateFixedSim MOId: imsi MOType: CreateFixedSim@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="imsi" type="imsiType" />
	<xs:element name="CreateFixedSim">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType" />
				<xs:element name="LockedImei">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imei" type="imeiType" />
							<xs:element name="svn" type="svnType" default="0F" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imsi">
			<xs:selector xpath="./x:imsi" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imsi" refer="key_imsi">
			<xs:selector xpath="." />
			<xs:field xpath="@imsi" />
		</xs:keyref>
	</xs:element>
	<!-- DeleteFixedSim MOId: imsi MOType: DeleteFixedSim@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetFixedSim MOId: imsi MOType: FixedSim@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetResponseFixedSim MOId: imsi MOType: FixedSim@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="GetResponseFixedSim">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsi" type="imsiType" />
				<xs:element name="LockedImei">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imei" type="imeiType" />
							<xs:element name="svn" type="svnType" minOccurs="0" />
						</xs:sequence>						
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
