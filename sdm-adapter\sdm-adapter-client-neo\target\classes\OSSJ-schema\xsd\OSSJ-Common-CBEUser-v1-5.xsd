<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEUser/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbeuser-v1-5="http://ossj.org/xml/Common-CBEUser/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <!-- Tigerstripe : Entity definitions for User  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="UserValue" >
        <annotation>

            <documentation>
Define a user of a service. 
 WARNING: 
 Note that is entity is under definition from the SID team.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfUserValue">
        <sequence>
            <element name="item" type="cbeuser-v1-5:UserValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="UserKey">
        <annotation>

            <documentation>
                This UserKey encapsulates all the information that is necessary to 
                identify a particular instance of a UserValue. The type of the 
                primary key for this UserKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfUserKey">
        <sequence>
            <element name="item" type="cbeuser-v1-5:UserKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="UserKeyResult">
        <annotation>
            <documentation>
                The UserKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a UserValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="userKey" type="cbeuser-v1-5:UserKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfUserKeyResult">
        <sequence>
            <element name="item" type="cbeuser-v1-5:UserKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of User -->
    <!-- Tigerstripe : End of Entity definition for User -->





</schema>
