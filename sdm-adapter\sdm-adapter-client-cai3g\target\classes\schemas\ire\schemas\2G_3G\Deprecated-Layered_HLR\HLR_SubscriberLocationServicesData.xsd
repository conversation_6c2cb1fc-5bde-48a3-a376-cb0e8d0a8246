<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="univ" type="univType"/>
<xs:element name="crel" type="crelType"/>
<xs:element name="eadd" type="eaddType"/>
<xs:element name="gres" type="gresType"/>
<xs:element name="cunrl" type="cunrlType"/>
<xs:element name="plmno" type="plmnoType"/>
<xs:element name="intid" type="intidType"/>
<xs:element name="allpcl" type="allpclType"/>
<xs:element name="mocl" type="moclType"/>
<xs:element name="servt" type="servtType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateSubscriberLocationServicesData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:choice>
<xs:sequence>
<xs:element name="univ" type="univType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="crel" type="crelType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="cunrl" type="cunrlType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="plmno" type="plmnoType"/>
<xs:element minOccurs="0" name="intid" type="intidType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="mocl" type="moclType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="servt" type="servtType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="eadd" type="eaddType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element minOccurs="0" name="notf" type="notfType"/>
<xs:choice minOccurs="0">
<xs:element name="crel" type="crelType"/>
<xs:element name="cunrl" type="cunrlType"/>
</xs:choice>
</xs:sequence>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="SetSubscriberLocationServicesData">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:sequence>
<xs:element name="crel" type="crelType"/>
<xs:element name="notf" type="notfType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="cunrl" type="cunrlType"/>
<xs:element name="notf" type="notfType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="eadd" type="eaddType"/>
<xs:choice>
<xs:element name="gres" type="gresType"/>
<xs:element name="notf" type="notfType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element name="crel" type="crelType"/>
<xs:element name="cunrl" type="cunrlType"/>
</xs:choice>
</xs:sequence>
<xs:sequence>
<xs:element name="servt" type="servtType"/>
<xs:choice>
<xs:element name="gres" type="gresType"/>
<xs:element name="notf" type="notfType"/>
</xs:choice>
</xs:sequence>
</xs:choice>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="required">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetSubscriberLocationServicesData">
<xs:complexType>
<xs:sequence>
<xs:element name="SubscriberLocationServicesData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="PrivacyLCSClassData">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="UniversalLocationServices" type="univType"/>
<xs:element minOccurs="0" name="CallRelatedLocationServices">
<xs:complexType>
<xs:sequence>
<xs:element name="notf" type="notfType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ExternalAddress">
<xs:complexType>
<xs:sequence>
<xs:element name="eadd" type="eaddType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element name="notf" type="notfType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="CallUnrelatedLocationServices">
<xs:complexType>
<xs:sequence>
<xs:element name="notf" type="notfType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="ExternalAddress">
<xs:complexType>
<xs:sequence>
<xs:element name="eadd" type="eaddType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element name="notf" type="notfType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="PLMNOperatorLocationServices">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="intid" type="intidType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="PrivacyServiceTypeData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" name="PrivacyServiceType">
<xs:complexType>
<xs:sequence>
<xs:element name="servt" type="servtType"/>
<xs:element minOccurs="0" name="gres" type="gresType"/>
<xs:element name="notf" type="notfType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="MobileOriginatingLCSClass">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" name="mocl" type="moclType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DeleteSubscriberLocationServicesData">
<xs:complexType>
<xs:sequence/>
</xs:complexType>
</xs:element>
</xs:schema>
