<!-- BCE Organization Unit 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/bce/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd" />
	<xs:element name="serviceProviderId" type="ServiceProviderId" />
	<xs:element name="companyId" type="CompanyId" />
	<xs:element name="organizationUnitId" type="UpOrganizationIdType" />
	<!-- CreateOrganizationUnit MOId: serviceProviderId, companyId, organizationUnitId 
		MOType: organizationUnit@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createOrganizationUnit">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Organization Unit
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="organizationUnitId" type="UpOrganizationIdType"
					maxOccurs="6" />

				<!-- Organization Unit Attributes -->
				<xs:element name="name" type="NameType" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="type" type="UpOrganizationUnitType"
					minOccurs="0" />
				<xs:element name="attachCode" type="AttachCodeType"
					minOccurs="0" />
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds"
					minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
			<xs:attribute name="organizationUnitId" type="UpOrganizationIdType"
				use="required">
			</xs:attribute>
		</xs:complexType>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@serviceProviderId" />
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="serviceProviderId" />
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@companyId" />
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="companyId" />
		</xs:keyref>
		<xs:key name="organizationUnitIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@organizationUnitId" />
		</xs:key>
		<xs:keyref name="organizationUnitIdKeyRef_Create" refer="organizationUnitIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="organizationUnitId" />
		</xs:keyref>
	</xs:element>
	<!-- SetOrganizationUnit MOId: serviceProviderId, companyId, organizationUnitId 
		MOType: organizationUnit@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setOrganizationUnit">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Organization Unit
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="organizationUnitId" type="UpOrganizationIdType"
					maxOccurs="6" />
				<!-- Company Attributes -->
				<xs:element name="name" type="NameType" minOccurs="0" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="type" type="UpOrganizationUnitType"
					minOccurs="0" />
				<xs:element name="attachCode" type="AttachCodeType"
					minOccurs="0" />
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds"
					minOccurs="0" />
				<xs:element name="newParentOrganizationUnitUniqueId"
					type="UpOrganizationIdType" minOccurs="0" maxOccurs="6" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
			<xs:attribute name="organizationUnitId" type="UpOrganizationIdType"
				use="required">
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- getOrganizationUnit MOId: serviceProviderId, companyId, organizationUnitId 
		MOType: organizationUnit@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="getOrganizationUnit">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Organization Unit
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="organizationUnitId" type="UpOrganizationIdType"
					maxOccurs="6" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!-- getOrganizationUnit -->
	<xs:element name="getOrganizationUnitResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Organization Unit
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
				<xs:element name="organizationUnitId" type="UpOrganizationIdType"
					maxOccurs="6" />
				<!-- Company Attributes -->
				<xs:element name="name" type="NameType" minOccurs="0" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="type" type="UpOrganizationUnitType"
					minOccurs="0" />
				<xs:element name="attachCode" type="AttachCodeType"
					minOccurs="0" />
				<xs:element name="callBarringProfileIds" type="CallBarringProfileIds"
					minOccurs="0" />
				<xs:element name="inheritedAttachCode" type="AttachCodeType"
					minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
			<xs:attribute name="organizationUnitId" type="UpOrganizationIdType"
				use="required">
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- deleteOrganizationUnit MOId: serviceProviderId, companyId, organizationUnitId 
		MOType: organizationUnit@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="deleteOrganizationUnit">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting BCE Organization Unit
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="organizationUnitId" type="UpOrganizationIdType"
					maxOccurs="6" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
