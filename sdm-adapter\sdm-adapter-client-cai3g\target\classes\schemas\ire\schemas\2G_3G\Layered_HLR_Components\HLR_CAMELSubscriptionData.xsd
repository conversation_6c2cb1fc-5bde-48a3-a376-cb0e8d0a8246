<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element default="" name="opt" type="xs:anyType"/>
<xs:element default="" name="ext" type="xs:anyType"/>
<xs:element default="" name="crit" type="xs:anyType"/>
<xs:element name="cch" type="cchType"/>
<xs:element name="mmtdp" type="mmtdpType"/>
<xs:element name="dstdp" type="dstdpType"/>
<xs:element name="tsmstdp" type="tsmstdpType"/>
<xs:element name="osmstdp" type="osmstdpType"/>
<xs:element name="gprstdp" type="gprstdpType"/>
<xs:element name="octdp" type="octdpType"/>
<xs:element name="tctdp" type="tctdpType"/>
<xs:element name="vttdp" type="vttdpType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateCAMELSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
</xs:choice>
<xs:choice>
<xs:sequence>
<xs:element name="mmtdp" type="mmtdpType"/>
<xs:element minOccurs="0" name="sk" type="skType"/>
<xs:element minOccurs="0" name="gsa" type="gsaType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="dstdp" type="dstdpType"/>
<xs:element name="dialnum" type="dialnumType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="tsmstdp" type="tsmstdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="osmstdp" type="osmstdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="gprstdp" type="gprstdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="octdp" type="octdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="tctdp" type="tctdpType"/>
<xs:element minOccurs="0" name="i" type="iType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
<xs:sequence>
<xs:element name="vttdp" type="vttdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element name="deh" type="dehType"/>
</xs:sequence>
</xs:choice>
<xs:element minOccurs="0" name="cch" type="cchType"/>
</xs:sequence>
<xs:attribute name="csp" type="cspType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cspAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="GetCAMELSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="CAMELSubscriptionProfile">
<xs:complexType>
<xs:sequence>
<xs:element name="csp" type="cspType"/>
<xs:element maxOccurs="unbounded" name="CamelTriggeringDetectionPointData">
<xs:complexType>
<xs:sequence>
<xs:element name="tdptype" type="tdptypeType"/>
<xs:element name="tdp" type="tdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element minOccurs="0" name="deh" type="dehType"/>
<xs:element name="cch" type="cchType"/>
<xs:element minOccurs="0" name="i" type="iType"/>
<xs:element minOccurs="0" name="dialnum" type="dialnumType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="CAMELSubscriptionOptions">
<xs:complexType>
<xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gcso" type="gcsoType"/>
<xs:element name="mcso" type="mcsoType"/>
<xs:element name="sslo" type="ssloType"/>
<xs:element name="gc2so" type="gc2soType"/>
<xs:element name="mc2so" type="mc2soType"/>
<xs:element minOccurs="0" name="tif" type="tifType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gprsso" type="gprssoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="osmsso" type="osmssoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gc3so" type="gc3soType"/>
<xs:element name="mc3so" type="mc3soType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gc4so" type="gc4soType"/>
<xs:element name="mc4so" type="mc4soType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="mmso" type="mmsoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="tsmsso" type="tsmssoType"/>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="ExtendedCAMELData">
<xs:complexType>
<xs:sequence>
<xs:element name="eoinci" type="eoinciType"/>
<xs:element name="eoick" type="eoickType"/>
<xs:element name="etinci" type="etinciType"/>
<xs:element name="etick" type="etickType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="CAMELTriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="OCTDP2TriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element name="cch" type="cchType"/>
<xs:element minOccurs="0" name="mty" type="mtyType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dnum" type="dnumType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dlgh" type="dlghType"/>
<xs:element minOccurs="0" name="ftc" type="ftcType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="TCTDP12TriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element name="cch" type="cchType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="CAMELSubscriptionData">
<xs:complexType>
<xs:sequence>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element minOccurs="0" name="csp" type="cspType"/>
<xs:element maxOccurs="unbounded" name="CamelTriggeringDetectionPointData">
<xs:complexType>
<xs:sequence>
<xs:element name="tdptype" type="tdptypeType"/>
<xs:element name="tdp" type="tdpType"/>
<xs:element name="sk" type="skType"/>
<xs:element name="gsa" type="gsaType"/>
<xs:element minOccurs="0" name="deh" type="dehType"/>
<xs:element name="cch" type="cchType"/>
<xs:element minOccurs="0" name="i" type="iType"/>
<xs:element minOccurs="0" name="dialnum" type="dialnumType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="CAMELSubscriptionOptions">
<xs:complexType>
<xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gcso" type="gcsoType"/>
<xs:element name="mcso" type="mcsoType"/>
<xs:element name="sslo" type="ssloType"/>
<xs:element name="gc2so" type="gc2soType"/>
<xs:element name="mc2so" type="mc2soType"/>
<xs:element minOccurs="0" name="tif" type="tifType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gprsso" type="gprssoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="osmsso" type="osmssoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gc3so" type="gc3soType"/>
<xs:element name="mc3so" type="mc3soType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="gc4so" type="gc4soType"/>
<xs:element name="mc4so" type="mc4soType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="mmso" type="mmsoType"/>
</xs:sequence>
<xs:sequence minOccurs="0">
<xs:element name="tsmsso" type="tsmssoType"/>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="ExtendedCAMELData">
<xs:complexType>
<xs:sequence>
<xs:element name="eoinci" type="eoinciType"/>
<xs:element name="eoick" type="eoickType"/>
<xs:element name="etinci" type="etinciType"/>
<xs:element name="etick" type="etickType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="CAMELTriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="OCTDP2TriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element name="cch" type="cchType"/>
<xs:element minOccurs="0" name="mty" type="mtyType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dnum" type="dnumType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="dlgh" type="dlghType"/>
<xs:element minOccurs="0" name="ftc" type="ftcType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element maxOccurs="unbounded" minOccurs="0" name="TCTDP12TriggeringCriteriaData">
<xs:complexType>
<xs:sequence>
<xs:element name="cch" type="cchType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bs" type="bsType"/>
<xs:element maxOccurs="unbounded" minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:choice>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="SetCAMELSubscriptionData">
<xs:complexType>
<xs:choice>
<xs:sequence>
<xs:choice>
<xs:element minOccurs="0" name="csp" type="cspType"/>
<xs:element minOccurs="0" name="msisdn" type="msisdnType"/>
</xs:choice>
<xs:choice>
<xs:element name="mmtdp" type="mmtdpType"/>
<xs:element name="dstdp" type="dstdpType"/>
<xs:element name="tsmstdp" type="tsmstdpType"/>
<xs:element name="osmstdp" type="osmstdpType"/>
<xs:element name="gprstdp" type="gprstdpType"/>
<xs:element name="octdp" type="octdpType"/>
<xs:element name="tctdp" type="tctdpType"/>
<xs:element name="vttdp" type="vttdpType"/>
</xs:choice>
<xs:element minOccurs="0" name="sk" type="skType"/>
<xs:element minOccurs="0" name="gsa" type="gsaType"/>
<xs:element minOccurs="0" name="deh" type="dehType"/>
<xs:element minOccurs="0" name="i" type="iType"/>
<xs:element minOccurs="0" name="cch" type="cchType"/>
</xs:sequence>
</xs:choice>
<xs:attribute name="csp" type="cspType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cspAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
<xs:element name="DeleteCAMELSubscriptionData">
<xs:annotation>
<xs:documentation>
				This command delete CAMEL subscription data for a mobile
				subscriber or for a CAMEL subscription profile.
			</xs:documentation>
</xs:annotation>
<xs:complexType>
<xs:sequence>
<xs:sequence minOccurs="0">
<xs:choice>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
</xs:choice>
<xs:element minOccurs="0" name="cch" type="cchType"/>
<xs:choice minOccurs="0">
<xs:element name="mmtdp" type="mmtdpType"/>
<xs:element name="dstdp" type="dstdpType"/>
<xs:element name="tsmstdp" type="tsmstdpType"/>
<xs:element name="osmstdp" type="osmstdpType"/>
<xs:element name="gprstdp" type="gprstdpType"/>
<xs:sequence>
<xs:element minOccurs="0" name="octdp" type="octdpType"/>
<xs:element minOccurs="0" name="tctdp" type="tctdpType"/>
</xs:sequence>
<xs:element name="vttdp" type="vttdpType"/>
</xs:choice>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
