<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/pg/cudb/1.0/" elementFormDefault="qualified">

	<xs:simpleType name="idTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SHARED" />
			<xs:enumeration value="NOSHARE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="serviceType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HLR" />
			<xs:enumeration value="EPS" />
			<xs:enumeration value="IMS" />
			<xs:enumeration value="WLAN" />
			<xs:enumeration value="AUC" />
			<xs:enumeration value="AVG" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="actionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INSERT" />
			<xs:enumeration value="WITHDRAW" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="mscIdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9a-f]{32}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="assocIdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9a-f]{32}" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:minLength value="5" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:minLength value="6" />
			<xs:maxLength value="15" />
			<xs:pattern value="[0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="eraseType">
		<xs:restriction base="xs:boolean" />
	</xs:simpleType>
</xs:schema>
