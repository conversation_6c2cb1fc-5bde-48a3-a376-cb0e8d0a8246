<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
	xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
	<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
		<!-- disable wrapper style generation -->
		<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
	</jaxws:bindings>
	<types>
		<xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cudb="http://schemas.ericsson.com/pg/cudb/1.0/" xmlns="http://schemas.ericsson.com/cai3g1.2/" elementFormDefault="qualified" attributeFormDefault="unqualified">
			<xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd" />
			<xs:import namespace="http://schemas.ericsson.com/pg/cudb/1.0/" schemaLocation="../../../schemas/Generic/Layered_Repair_and_Recover/Layered_MultiServiceConsumer.xsd" />
			<xs:element name="Delete">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MOType" type="xs:string" fixed="MultiServiceConsumer@http://schemas.ericsson.com/pg/cudb/1.0/" />
						<xs:element name="MOId">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="cudb:mscId" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:schema>
	</types>
	<message name="DeleteRequest">
		<part name="parameters" element="cai3g:Delete" />
	</message>
	<message name="DeleteResponse">
		<part name="parameters" element="cai3g:DeleteResponse" />
	</message>
	<message name="HeadInfo">
		<part name="sessionId" element="cai3g:SessionId" />
	</message>
	<message name="Cai3gFault">
		<part name="parameters" element="cai3g:Cai3gFault" />
	</message>
	<message name="Cai3gHeaderFault">
		<part name="sessionIdFault" element="cai3g:SessionIdFault" />
		<part name="transactionIdFault" element="cai3g:TransactionIdFault" />
		<part name="sequenceIdFault" element="cai3g:SequenceIdFault" />
	</message>
	<portType name="CUDB_MultiServiceConsumer">
		<operation name="Delete">
			<input message="cai3g:DeleteRequest" />
			<output message="cai3g:DeleteResponse" />
			<fault name="Cai3gFault" message="cai3g:Cai3gFault" />
		</operation>
	</portType>
	<binding name="CUDB_MultiServiceConsumer" type="cai3g:CUDB_MultiServiceConsumer">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<operation name="Delete">
			<soap:operation soapAction="CAI3G#Delete" style="document" />
			<input>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal" />
			</input>
			<output>
				<soap:body use="literal" />
				<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
					<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal" />
				</soap:header>
			</output>
			<fault name="Cai3gFault">
				<soap:fault name="Cai3gFault" use="literal" />
			</fault>
		</operation>
	</binding>
	<service name="Provisioning">
		<port name="CUDB_MultiServiceConsumer" binding="cai3g:CUDB_MultiServiceConsumer">
			<soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2" />
		</port>
	</service>
</definitions>