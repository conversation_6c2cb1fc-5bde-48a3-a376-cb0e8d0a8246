<xs:schema xmlns="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:x="http://schemas.ericsson.com/ma/IPWORKS/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ma/IPWORKS/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:element name="name" type="userNameType"/>
	<xs:element name="transactionLogId" type="transactionLogIdType"/>
	<xs:element name="CreateAAANSDUser">
		<xs:annotation>
			<xs:documentation>AAANSDUser Create</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="userNameType"/>
				<xs:element name="password" type="userPasswordType" minOccurs="0"/>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="apn" type="apnType" minOccurs="0" />
				<xs:element name="userStatus" type="createUserStatusType" minOccurs="0"/>
				<xs:element name="certificateId" type="certIdType" minOccurs="0"/>
				<xs:element name="certificateIssuerName" type="certificateIssuerNameType" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="name" type="userNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="userNameAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_identity">
			<xs:selector xpath="."/>
			<xs:field xpath="@name"/>
		</xs:key>
		<xs:keyref name="keyref_create_identity" refer="key_create_identity">
			<xs:selector xpath="./x:name"/>
			<xs:field xpath="."/>
		</xs:keyref>
	</xs:element>
	<xs:element name="SetAAANSDUser">
		<xs:annotation>
			<xs:documentation>AAANSDUser Set</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="password" type="userPasswordType" minOccurs="0"/>
				<xs:element name="apn" type="apnType" minOccurs="0" nillable="true" maxOccurs="unbounded"/> 
				<xs:element name="userStatus" type="userStatusType" minOccurs="0"/>
				<xs:element name="certificateId" type="certIdType" minOccurs="0"/>
				<xs:element name="certificateIssuerName" type="certificateIssuerNameType" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="name" type="userNameType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="userNameAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="GetResponseAAANSDUser">
		<xs:annotation>
			<xs:documentation>AAANSDUser response</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="userNameType"/>
				<xs:element name="imsi" type="imsiType"/>
				<xs:element name="msisdn" type="msisdnType"/>
				<xs:element name="apn" type="apnType" minOccurs="0"/>
				<xs:element name="userStatus" type="userStatusType"/>
				<xs:element name="certificateId" type="certIdType" minOccurs="0"/>
				<xs:element name="certificateIssuerName" type="certificateIssuerNameType" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="userNameType">
		<xs:restriction base="xs:string">
			<xs:pattern value="[^A-Z]{1,256}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userPasswordType">
		<xs:restriction base="xs:string">
			<xs:minLength value="4"/>
			<xs:maxLength value="256"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{6,16}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,16}"/>
		</xs:restriction>
	</xs:simpleType>
    <xs:complexType name="apnType">
        <xs:simpleContent>
            <xs:extension base="apnStringType">
				<xs:attribute name="value" type="apnStringType">
					<xs:annotation>
						<xs:appinfo>
							<jaxb:property name="apnValueAttr"/>
						</xs:appinfo>
					</xs:annotation>
				</xs:attribute>
            </xs:extension>
        </xs:simpleContent>
	</xs:complexType>
    <xs:simpleType name="apnStringType">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
        </xs:restriction>
    </xs:simpleType>	
	<xs:simpleType name="createUserStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="enable"/>
			<xs:enumeration value="disable"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="userStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="enable"/>
			<xs:enumeration value="disable"/>
			<xs:enumeration value="reset"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="transactionLogIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="certIdType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="certificateIssuerNameType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="512"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
