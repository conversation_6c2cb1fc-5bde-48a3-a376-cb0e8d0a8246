<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="cch" type="cchType"/>
<xs:element name="criteriaTDP" type="criteriaTDPType"/>
<xs:element name="dnum" type="dnumType"/>
<xs:element name="dlgh" type="dlghType"/>
<xs:element name="ftc" type="ftcType"/>
<xs:element name="bs" type="bsType"/>
<xs:element name="bsg" type="bsgType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="CreateCAMELTriggeringCriteria">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
</xs:choice>
<xs:choice>
<xs:element name="octdp2">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="mty" type="mtyType"/>
<xs:element minOccurs="0" name="dnum" type="dnumType"/>
<xs:element minOccurs="0" name="dlgh" type="dlghType"/>
<xs:element minOccurs="0" name="ftc" type="ftcType"/>
<xs:element minOccurs="0" name="bs" type="bsType"/>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:sequence>
<xs:element name="tctdp12">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="bs" type="bsType"/>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:choice>
<xs:element minOccurs="0" name="cch" type="cchType"/>
</xs:sequence>
<xs:attribute name="csp" type="cspType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cspAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
<xs:key name="key_csp">
<xs:selector xpath="./x:csp"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_csp" refer="key_csp">
<xs:selector xpath="."/>
<xs:field xpath="@csp"/>
</xs:keyref>
<xs:key name="key_msisdn">
<xs:selector xpath="./x:msisdn"/>
<xs:field xpath="."/>
</xs:key>
<xs:keyref name="keyref_msisdn" refer="key_msisdn">
<xs:selector xpath="."/>
<xs:field xpath="@msisdn"/>
</xs:keyref>
</xs:element>
<xs:element name="DeleteCAMELTriggeringCriteria">
<xs:complexType>
<xs:sequence>
<xs:sequence minOccurs="0">
<xs:choice>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
</xs:choice>
<xs:element minOccurs="0" name="cch" type="cchType"/>
<xs:element minOccurs="0" name="criteriaTDP" type="criteriaTDPType"/>
<xs:sequence>
<xs:element minOccurs="0" name="dnum" type="dnumType"/>
<xs:element minOccurs="0" name="dlgh" type="dlghType"/>
<xs:element minOccurs="0" name="ftc" type="ftcType"/>
<xs:element minOccurs="0" name="bs" type="bsType"/>
<xs:element minOccurs="0" name="bsg" type="bsgType"/>
</xs:sequence>
</xs:sequence>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
