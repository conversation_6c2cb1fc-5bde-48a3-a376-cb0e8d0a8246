<!-- BCE Company 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/bce/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd" />
	<xs:element name="serviceProviderId" type="ServiceProviderId" />
	<xs:element name="companyId" type="CompanyId" />

	<!-- CreateCompany MOId: serviceProviderId, companyId MOType: company@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createCompany">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE Company
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />

				<!-- Company attributes -->
				<xs:element name="name" type="NameType" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="domain" type="NameType" minOccurs="0" />
				<xs:element name="searchCountLimit" type="xs:int"
					minOccurs="0" />
				<xs:element name="companySetting" type="CompanySettingType"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="directory" type="UpDirectoryType"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="companyAnnouncements" type="CompanyAnnouncementType"
					minOccurs="0" maxOccurs="unbounded" />
				<!--xs:element name="activeCallBarringProfileIds" type="CallBarringProfileIdType" 
					minOccurs="0" maxOccurs="unbounded"/ -->
			</xs:sequence>

			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
		</xs:complexType>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@serviceProviderId" />
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="serviceProviderId" />
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@companyId" />
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="companyId" />
		</xs:keyref>
	</xs:element>

	<!-- SetCompany MOId: serviceProviderId, companyId MOType: company@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setCompany">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE Company
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />

				<!-- Company attributes -->
				<xs:element name="name" type="NameType" minOccurs="0" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="domain" type="NameType" minOccurs="0" />
				<xs:element name="searchCountLimit" type="xs:int"
					minOccurs="0" />
				<xs:element name="companySetting" type="CompanySettingType"
					minOccurs="0" maxOccurs="unbounded" />

				<xs:element name="directory" type="UpDirectoryType"
					nillable="true" minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="companyAnnouncements" type="CompanyAnnouncementType"
					nillable="true" minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="activeCallBarringProfileIds" type="CallBarringProfileIdType"
					nillable="true" minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<!-- getCompany MOId: serviceProviderId, companyId MOType: company@http://schemas.ericsson.com/ma/bce/ -->
	<!-- getCompanyResponse -->
	<xs:element name="getCompanyResponse">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE Company
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />

				<!-- Company attributes -->
				<xs:element name="name" type="NameType" minOccurs="0" />
				<xs:element name="description" type="DescriptionType"
					minOccurs="0" />
				<xs:element name="address" type="AddressType" minOccurs="0" />
				<xs:element name="domain" type="NameType" minOccurs="0" />
				<xs:element name="searchCountLimit" type="xs:int"
					minOccurs="0" />
				<xs:element name="directory" type="UpDirectoryType"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="activeCallBarringProfileIds" type="CallBarringProfileIdType"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="companySetting" type="CompanySettingType"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="cdgId" type="xs:string" minOccurs="0"
					maxOccurs="unbounded" />
				<xs:element name="autoAttendantId" type="xs:string"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="audioConferencId" type="xs:string"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="companyAdminId" type="xs:string"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="definedCallBarringProfileId" type="xs:string"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="companyAnnouncement" type="CompanyAnnouncementType"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<!-- deleteCompany MOId: serviceProviderId, companyId MOType: company@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="deleteCompany">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting BCE Company (single or
				recursively)
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="serviceProviderId" type="ServiceProviderId" />
				<xs:element name="companyId" type="CompanyId" />
			</xs:sequence>
			<xs:attribute name="serviceProviderId" type="ServiceProviderId"
				use="required">
			</xs:attribute>
			<xs:attribute name="companyId" type="CompanyId" use="required">
			</xs:attribute>
		</xs:complexType>
	</xs:element>

	<!-- types -->
	<xs:complexType name="CompanyAnnouncementType">
		<xs:sequence>
			<xs:element name="name" type="xs:string" minOccurs="0" />
			<xs:element name="fileId" type="xs:long" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" use="required">
			<xs:annotation>
				<xs:appinfo>
					<jaxb:property name="nameAttr" />
				</xs:appinfo>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>

	<xs:complexType name="CompanySettingType">
		<xs:sequence>
			<xs:element name="name" type="xs:string" minOccurs="0" />
			<xs:element name="value" type="xs:string" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" use="required">
		</xs:attribute>
	</xs:complexType>

	<xs:complexType name="UpDirectoryType">
		<xs:sequence>
			<xs:element name="name" type="NameType" />
			<xs:element name="default" type="xs:boolean" minOccurs="0" />
			<xs:element name="minpartialmatch" type="xs:int"
				minOccurs="0" />
			<xs:element name="connectionProfileId" type="ConnectionProfileId"
				minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="name" type="NameType" use="required">
		</xs:attribute>
	</xs:complexType>

	<xs:complexType name="CallBarringProfileIdType">
		<xs:sequence>
			<xs:element name="id" type="BarringProfileId" />
		</xs:sequence>
		<xs:attribute name="id" type="BarringProfileId" use="required">
		</xs:attribute>
	</xs:complexType>

</xs:schema>
