package com.nokia.wing.wdh.sdmadapter.client.neo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration properties for NEO OSSJ adapter client.
 *
 * This class contains all configuration parameters needed for:
 * - OSSJ service endpoints
 * - OAuth 2.0 authentication with EntraID
 * - SOAP security settings
 * - Connection and WebService management
 */
@Data
@ConfigurationProperties(prefix = "neo", ignoreUnknownFields = false)
@ConditionalOnProperty(value="application.clientType", havingValue="NEO")
public class NeoClientProperties {

    // NEO OSSJ Service Configuration
    private String serviceUrl ;
    private Integer connectionTimeout = 45000;
    private Integer readTimeout = 120000;

    // OAuth 2.0 Configuration for EntraID
    private OAuth oauth = new OAuth();

    // SSL/TLS Security Configuration (Feature Flags)
    private Ssl ssl = new Ssl();

    // SOAP Security Configuration
    private SoapSecurity soapSecurity = new SoapSecurity();

    // Audit and Messaging Configuration
    private Audit audit = new Audit();
    private Messaging messaging = new Messaging();

    // Default Values Configuration
    private Defaults defaults = new Defaults();

    // Mapping Configuration for APN/DNN to PDP Name lookups and future extensibility
    private Mapping mapping = new Mapping();

    public static class OAuth {
        private String tokenEndpoint;
        private String clientId;
        private String clientSecret;
        private String scope;
        private String grantType = "client_credentials";
        private Integer tokenRefreshBuffer = 300; // 5 minutes before expiry

        // Authentication method selection
        private AuthenticationMethod authenticationMethod = AuthenticationMethod.RESTTEMPLATE; // Default for backward compatibility

        // Getters and setters
        public String getTokenEndpoint() {
            return tokenEndpoint;
        }

        public void setTokenEndpoint(String tokenEndpoint) {
            this.tokenEndpoint = tokenEndpoint;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getClientSecret() {
            return clientSecret;
        }

        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }

        public String getScope() {
            return scope;
        }

        public void setScope(String scope) {
            this.scope = scope;
        }

        public String getGrantType() {
            return grantType;
        }

        public void setGrantType(String grantType) {
            this.grantType = grantType;
        }

        public Integer getTokenRefreshBuffer() {
            return tokenRefreshBuffer;
        }

        public void setTokenRefreshBuffer(Integer tokenRefreshBuffer) {
            this.tokenRefreshBuffer = tokenRefreshBuffer;
        }

        public AuthenticationMethod getAuthenticationMethod() {
            return authenticationMethod;
        }

        /**
         * Setter for Spring Boot property binding.
         * This method allows Spring Boot to bind both string and enum values.
         */
        public void setAuthenticationMethod(AuthenticationMethod authenticationMethod) {
            this.authenticationMethod = authenticationMethod;
        }
    }

    /**
     * Enumeration of supported authentication methods.
     */
    public enum AuthenticationMethod {
        /**
         * Use RestTemplate-based OAuth authentication (existing implementation).
         * Requires tokenEndpoint, clientId, and clientSecret to be configured.
         */
        RESTTEMPLATE("resttemplate"),

        /**
         * Use Azure SDK DefaultAzureCredential authentication.
         * Works with various Azure authentication sources:
         * - Environment variables (AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID)
         * - Managed Identity (when running in Azure)
         * - Azure CLI, IntelliJ, VS Code, PowerShell (when available)
         */
        AZURE_SDK("azure-sdk");

        private final String value;

        AuthenticationMethod(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        /**
         * Parse authentication method from string value.
         *
         * @param value String value to parse
         * @return Corresponding AuthenticationMethod
         * @throws IllegalArgumentException if value is not recognized
         */
        public static AuthenticationMethod fromValue(String value) {
            if (value == null) {
                return RESTTEMPLATE; // Default
            }

            for (AuthenticationMethod method : values()) {
                if (method.value.equalsIgnoreCase(value.trim())) {
                    return method;
                }
            }

            throw new IllegalArgumentException("Unknown authentication method: " + value +
                ". Supported values: resttemplate, azure-sdk");
        }
    }

    @Data
    public static class SoapSecurity {
        private Boolean enableWsUsernameToken;
        private String username;
        private String password; // Deprecated: OAuth token will be used instead
        private Boolean enableWsTimestamp = false;
    }

    @Data
    public static class Audit {
        private Boolean enabled;
        private String kafkaTopic ;
        private Boolean includeWebServiceEvents ;
        private Boolean includeErrorEvents;
        private Boolean includeOssjOperations;
        private Boolean includeOauthEvents;
        // Message size optimization settings
        private Integer maxFieldLength ;
        private Integer maxTotalMessageSize ;
        private Boolean enableMessageTruncation ;
        private Boolean includeFullPayloads;
    }

    // Messaging Configuration
    @Data
    public static class Messaging {
        private Boolean enabled; // Disabled by default - feature flag
        private Kafka kafka = new Kafka();
    }

    @Data
    public static class Kafka {
        private String bootstrapServers;
        private Integer maxRetries;
        private Long initialRetryDelayMs;
        private Double backoffMultiplier ;
        private String keySerializer ;
        private String valueSerializer ;
        private String acks ;
        private Integer batchSize ;
        private Integer lingerMs;
        private Long bufferMemory ;
        private Long maxRequestSize ;
        private Long requestTimeoutMs;
        private Long deliveryTimeoutMs ;
        private Long maxBlockMs ;

        // Circuit breaker configuration for graceful Kafka unavailability handling
        private Long circuitBreakerTimeoutMs;
        private Long maxConsecutiveFailures;
        private Long circuitBreakerResetTimeoutMs;
    }

    // SSL/TLS Configuration (Feature Flags)
    @Data
    public static class Ssl {
        private Boolean mtlsEnabled ;
        private Boolean httpsEnabled ;
        // NOTE: Certificate paths are corrected for proper mTLS configuration
        // Client keystore: Contains client's private key + certificate for authentication
        private String keystorePath ;
        private String keystorePassword;
        private String keyPassword ;
        private String keystoreType;
        // Server truststore: Contains server's certificate for verification
        private String truststorePath ;
        private String truststorePassword;
        private String truststoreType;
        private Boolean disableHostnameVerification ;
        private Boolean enableSslDebug;
        private Boolean validateCertificates;
        // TESTING ONLY: Disable SSL certificate validation (bypasses all certificate checks)
        // WARNING: This creates a security vulnerability and should NEVER be used in production
        private Boolean disableCertificateValidation;
    }

    /**
     * Default values configuration for NEO OSSJ adapter.
     *
     * These values are used throughout the NEO adapter for SOAP service interactions
     * and can be customized per environment without code changes.
     */
    @Data
    public static class Defaults {
        private String imsiPrefix;
        private String market ;
        private String subMarket;
        private String sms;
        private String operatorId ;
        private String apnName;
        private String pdpName;
        private String originatingClassOfService ;
        private String callerId ;
        private String emlppActive ;
        private String subscriptionClass ;
        private String clientApplicationId;
        private String carrierId;
        private String equipmentType;
        private String externalOfferId ;
        private String offerLevel ;
        private String videoStreamingQuality;
        private String chargingCharacteristic;
        private String arpPreemptCap;
        private String arpPreemptVuln;
        private String iwkEpsInd;
        private String acwacctsubtype ;
    }

    /**
     * Mapping configuration for various lookup operations.
     *
     * This class provides configurable lookup maps for different mapping scenarios:
     * - APN/DNN to PDP Name mapping for NEO characteristic generation
     * - Future extensibility for additional mapping requirements
     *
     * All lookups are case-insensitive for better usability.
     */
    @Data
    public static class Mapping {

        /**
         * APN/DNN to PDP Name lookup map.
         *
         * Maps Access Point Names (APNs) or Data Network Names (DNNs) to their
         * corresponding PDP (Packet Data Protocol) names for NEO service characteristics.
         *
         * Example configuration in YAML:
         * neo:
         *   mapping:
         *     apn-to-pdp-lookup:
         *       attm2mglobal5g: attm2mglobal5g
         *       d50003.etr: D50003
         *       5gcbroadband: 5GCBROADBAND
         *       ims: IMS
         */
        private Map<String, String> apnToPdpLookup = new HashMap<>();

        /**
         * Get PDP name for the given APN/DNN name with case-insensitive lookup.
         *
         * @param apnName The APN or DNN name to lookup
         * @return The corresponding PDP name, or null if not found
         */
        public String getPdpNameForApn(String apnName) {
            if (apnName == null || apnName.trim().isEmpty()) {
                return null;
            }

            // Case-insensitive lookup by converting to lowercase
            String normalizedApnName = apnName.trim().toLowerCase();

            // Search through the map with case-insensitive comparison
            for (Map.Entry<String, String> entry : apnToPdpLookup.entrySet()) {
                if (entry.getKey().toLowerCase().equals(normalizedApnName)) {
                    return entry.getValue();
                }
            }

            return null; // Not found
        }

        /**
         * Check if an APN/DNN name exists in the lookup map (case-insensitive).
         *
         * @param apnName The APN or DNN name to check
         * @return true if the APN exists in the lookup map, false otherwise
         */
        public boolean hasApnMapping(String apnName) {
            return getPdpNameForApn(apnName) != null;
        }

        // TODO: Future enhancement - Add additional mapping types here...like below
        // private Map<String, String> additionalLookupMap = new HashMap<>();
    }
}
