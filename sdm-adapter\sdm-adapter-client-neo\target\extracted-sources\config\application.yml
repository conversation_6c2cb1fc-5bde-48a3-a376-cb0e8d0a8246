# ===================================================================
# NEO Adapter Default Configuration
#
# This file contains default configuration for the NEO adapter that applies
# to all environments. Environment-specific overrides should be placed in
# the main service's application-{profile}.yml files.
# ===================================================================

neo:
  # NEO OSSJ Service Configuration
  # Note: Protocol (http/https) will be automatically adjusted based on SSL configuration
  serviceUrl: ${NEO_SERVICE_URL:http://localhost:8082/neowsdl/services/AUAI-RequestWS-v2-0}
  connectionTimeout: 45000
  readTimeout: 120000

  # OAuth 2.0 Configuration for EntraID
  oauth:
    # Authentication method selection: 'resttemplate' or 'azure-sdk'
    # - resttemplate: Uses traditional OAuth client credentials flow (default for backward compatibility)
    # - azure-sdk: Uses Azure SDK DefaultAzureCredential (supports multiple auth sources)
    authenticationMethod: ${NEO_AUTH_METHOD:resttemplate}

    # RestTemplate authentication configuration (required when authenticationMethod=resttemplate)
    tokenEndpoint: https://login.microsoftonline.com/<tenant-id>/oauth2/v2.0/token
    clientId: ${NEO_CLIENT_ID}
    clientSecret: ${NEO_CLIENT_SECRET}
    grantType: client_credentials

    # Common configuration (required for both authentication methods)
    scope: "api://823845d6-433e-47f4-b47a-d5667307fbe5/.default"
    tokenRefreshBuffer: 300

  # SSL/TLS Security Configuration (Feature Flags)
  ssl:
    mtlsEnabled: ${NEO_MTLS_ENABLED:false}  # mTLS feature flag - disabled by default
    httpsEnabled: ${NEO_HTTPS_ENABLED:false}  # HTTPS feature flag - disabled by default
    # Certificate paths - support both classpath and file system paths
    truststorePath: ${NEO_TRUSTSTORE_PATH:classpath:certificates/client-truststore.jks}
    truststorePassword: ${NEO_TRUSTSTORE_PASSWORD:changeit}
    truststoreType: ${NEO_TRUSTSTORE_TYPE:JKS}
    keystorePath: ${NEO_KEYSTORE_PATH:classpath:certificates/client1.jks}
    keystorePassword: ${NEO_KEYSTORE_PASSWORD:changeit}
    keyPassword: ${NEO_KEY_PASSWORD:changeit}  # Password for private key in keystore
    keystoreType: ${NEO_KEYSTORE_TYPE:JKS}
    disableHostnameVerification: ${NEO_DISABLE_HOSTNAME_VERIFICATION:true}  # For self-signed certificates
    # Additional SSL debugging and validation options
    enableSslDebug: ${NEO_SSL_DEBUG_ENABLED:false}  # Enable SSL handshake debugging
    validateCertificates: ${NEO_VALIDATE_CERTIFICATES:true}  # Validate certificate loading on startup
    # TESTING ONLY: Disable SSL certificate validation (bypasses all certificate checks)
    # WARNING: This creates a security vulnerability and should NEVER be used in production
    disableCertificateValidation: ${NEO_DISABLE_CERTIFICATE_VALIDATION:false}  # TESTING ONLY - disabled by default

  # SOAP Security Configuration
  soapSecurity:
    enableWsUsernameToken: true  # Use OAuth token as password in UsernameToken
    username: ${NEO_SOAP_USERNAME:OUA_SOAP_USER}
    password: ${NEO_SOAP_PASSWORD:deprecated-oauth-token-used-instead}  # Deprecated: OAuth token used instead
    enableWsTimestamp: false  # Disabled by default to match expected SOAP header format

  # Audit Configuration (Feature Flag - disabled by default)
  audit:
    enabled: ${NEO_AUDIT_ENABLED:false}  # Feature flag - disabled by default
    kafkaTopic: wdh.sdm.adapter.neo.audit.log.events
    includeWebServiceEvents: true
    includeErrorEvents: true
    includeOssjOperations: true
    includeOauthEvents: true
    # Message size optimization settings
    maxFieldLength: ${NEO_AUDIT_MAX_FIELD_LENGTH:5000}  # 5KB max per field
    maxTotalMessageSize: ${NEO_AUDIT_MAX_MESSAGE_SIZE:50000000}  # 50MB max total
    enableMessageTruncation: ${NEO_AUDIT_ENABLE_TRUNCATION:true}
    includeFullPayloads: ${NEO_AUDIT_INCLUDE_FULL_PAYLOADS:false}  # Optimize for size

  # Messaging Configuration (Feature Flag - disabled by default)
  messaging:
    enabled: ${NEO_MESSAGING_ENABLED:false}  # Feature flag - disabled by default
    kafka:
      bootstrapServers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}  # Docker Kafka external port
      maxRetries: 3
      initialRetryDelayMs: 1000
      backoffMultiplier: 2.0
      keySerializer: org.apache.kafka.common.serialization.StringSerializer
      valueSerializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all  # Wait for all replicas (matches Docker setup)
      batchSize: 16384
      lingerMs: 5  # Increased for better batching with Docker
      bufferMemory: 67108864  # 64MB - increased for large audit messages
      # Message size configuration for large audit messages
      maxRequestSize: 10485760  # 10MB max request size
      # Circuit breaker configuration for graceful Kafka unavailability handling
      circuitBreakerTimeoutMs: ${NEO_KAFKA_CIRCUIT_BREAKER_TIMEOUT_MS:60000}  # 1 minute
      maxConsecutiveFailures: ${NEO_KAFKA_MAX_CONSECUTIVE_FAILURES:5}  # Open circuit after 5 failures
      circuitBreakerResetTimeoutMs: ${NEO_KAFKA_CIRCUIT_BREAKER_RESET_TIMEOUT_MS:300000}  # 5 minutes
      # Additional Docker-optimized settings
      requestTimeoutMs: 30000
      deliveryTimeoutMs: 120000
      maxBlockMs: 60000

    # Default Values Configuration
    # These values are used throughout the NEO adapter and can be customized per environment
    defaults:
      imsi-prefix: ${NEO_DEFAULT_IMSI_PREFIX:3101705}  # IMSI prefix for ACWSVCNETWORK calculation
      market: ${NEO_DEFAULT_MARKET:ACM}
      sub-market: ${NEO_DEFAULT_SUB_MARKET:ACM}
      sms: ${NEO_DEFAULT_SMS:ALP}
      operator-id: ${NEO_DEFAULT_OPERATOR_ID:21}
      apn-name: ${NEO_DEFAULT_APN_NAME:d50003.etr}
      pdp-name: ${NEO_DEFAULT_PDP_NAME:D50003}
      originating-class-of-service: ${NEO_DEFAULT_ORIGINATING_CLASS_OF_SERVICE:65536}  # Default for Oct
      caller-id: ${NEO_DEFAULT_CALLER_ID:2}
      emlpp-active: ${NEO_DEFAULT_EMLPP_ACTIVE:0}  # 0 means disabled
      subscription-class: ${NEO_DEFAULT_SUBSCRIPTION_CLASS:WA}
      client-application-id: ${NEO_DEFAULT_CLIENT_APPLICATION_ID:ACM}
      carrier-id: ${NEO_DEFAULT_CARRIER_ID:1}
      equipment-type: ${NEO_DEFAULT_EQUIPMENT_TYPE:G}
      external-offer-id: ${NEO_DEFAULT_EXTERNAL_OFFER_ID:XXX}
      offer-level: ${NEO_DEFAULT_OFFER_LEVEL:S}
      video-streaming-quality: ${NEO_DEFAULT_VIDEO_STREAMING_QUALITY:NOTFD}
      charging-characteristic: ${NEO_DEFAULT_CHARGING_CHARACTERISTIC:0000}
      arp-preempt-cap: ${NEO_DEFAULT_ARP_PREEMPT_CAP:NOT_PREEMPT}
      arp-preempt-vuln: ${NEO_DEFAULT_ARP_PREEMPT_VULN:PREEMPTABLE}
      iwk-eps-ind: ${NEO_DEFAULT_IWK_EPS_IND:False}
      acwacctsubtype: ${NEO_DEFAULT_ACWACCTSUBTYPE:True}  # In phase1, will change later

    # Mapping Configuration for APN/DNN to PDP Name lookups and any other lookup should come here
    mapping:
      # APN/DNN to PDP Name lookup map
      # Maps Access Point Names (APNs) or Data Network Names (DNNs) to their corresponding PDP names
      apn-to-pdp-lookup:
        attm2mglobal5g: attm2mglobal5g
        d50003.etr: D50003
        5gcbroadband: 5GCBROADBAND
        ims: IMS
        # TODO: Add additional APN/DNN mappings as needed

# Spring Kafka Configuration for NEO Audit Events (Docker-optimized)
spring:
  kafka:
    bootstrap-servers: ${neo.messaging.kafka.bootstrapServers:localhost:9092}
    producer:
      key-serializer: ${neo.messaging.kafka.keySerializer:org.apache.kafka.common.serialization.StringSerializer}
      value-serializer: ${neo.messaging.kafka.valueSerializer:org.apache.kafka.common.serialization.StringSerializer}
      acks: ${neo.messaging.kafka.acks:all}
      retries: ${neo.messaging.kafka.maxRetries:3}
      batch-size: ${neo.messaging.kafka.batchSize:16384}
      linger-ms: ${neo.messaging.kafka.lingerMs:5}
      buffer-memory: ${neo.messaging.kafka.bufferMemory:67108864}  # 64MB - increased for large audit messages
      # Docker-optimized timeout settings
      request-timeout-ms: ${neo.messaging.kafka.requestTimeoutMs:30000}
      delivery-timeout-ms: ${neo.messaging.kafka.deliveryTimeoutMs:120000}
      properties:
        max.block.ms: ${neo.messaging.kafka.maxBlockMs:60000}
        max.request.size: ${neo.messaging.kafka.maxRequestSize:52428800}  # 50MB max request size
        # Connection settings for Docker environment
        connections.max.idle.ms: 540000
        reconnect.backoff.ms: 50
        retry.backoff.ms: 100

# Logging Configuration for NEO Adapter
logging:
  level:
    com.nokia.wing.wdh.sdmadapter: INFO
#     com.nokia.wing.wdh.sdmadapter.client.neo.interceptor: INFO
#     com.nokia.wing.wdh.sdmadapter.client.neo.messaging: DEBUG
#     com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs: DEBUG
#     com.nokia.wing.wdh.sdmadapter.client.neo.response: DEBUG
    org.springframework.security.oauth2: INFO
    org.springframework.ws.client.core: INFO
    org.springframework.ws.transport.http: INFO
    org.springframework.kafka: INFO

# ========================================
# Authentication Configuration Examples
# ========================================

# Example 1: RestTemplate Authentication (Default - Backward Compatible)
# This is the existing authentication method using OAuth client credentials flow
# neo:
#   oauth:
#     authenticationMethod: resttemplate
#     tokenEndpoint: https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token
#     clientId: your-client-id
#     clientSecret: your-client-secret
#     scope: "api://your-client-id/.default"
#     grantType: client_credentials
#     tokenRefreshBuffer: 300

# Example 2: Azure SDK Authentication (New - Recommended for Azure environments)
# This method uses Azure SDK DefaultAzureCredential which supports multiple authentication sources
# neo:
#   oauth:
#     authenticationMethod: azure-sdk
#     scope: "api://your-client-id/.default"
#     tokenRefreshBuffer: 300

# Azure SDK Authentication Sources (in order of precedence):
# 1. Environment Variables:
#    - AZURE_CLIENT_ID: Application (client) ID
#    - AZURE_CLIENT_SECRET: Client secret
#    - AZURE_TENANT_ID: Directory (tenant) ID
#
# 2. Managed Identity (when running in Azure):
#    - System-assigned managed identity
#    - User-assigned managed identity
#
# 3. Azure CLI (when available):
#    - Uses credentials from 'az login'
#
# 4. IntelliJ IDEA (when available):
#    - Uses Azure Toolkit for IntelliJ credentials
#
# 5. Visual Studio Code (when available):
#    - Uses Azure Account extension credentials
#
# 6. Azure PowerShell (when available):
#    - Uses Connect-AzAccount credentials

# Environment Variables for Azure SDK Authentication:
# export AZURE_CLIENT_ID="your-client-id"
# export AZURE_CLIENT_SECRET="your-client-secret"
# export AZURE_TENANT_ID="your-tenant-id"

# 1. To migrate from RestTemplate to Azure SDK authentication:
#    - Set NEO_AUTH_METHOD=azure-sdk
#    - Ensure Azure credentials are available (environment variables, managed identity, etc.)
#    - Remove or keep existing clientId/clientSecret for fallback
#
# 2. For backward compatibility:
#    - Keep authenticationMethod=resttemplate (default)
#    - No changes needed to existing configuration
