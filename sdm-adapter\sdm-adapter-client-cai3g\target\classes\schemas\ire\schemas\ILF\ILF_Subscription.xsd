<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns="http://schemas.ericsson.com/ema/UserProvisioning/ILF/" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/ILF/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0">
	<xs:element name="imsi" type="imsiType"/>
	<xs:element name="msisdn" type="msisdnType"/>
	<xs:element name="publicIdentity" type="publicIdValueType"/>
	<xs:element name="privateIdentity" type="privateIdValueType"/>
	<xs:element name="setSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsiEntry" nillable="true" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imsi" type="imsiType" minOccurs="0"/>
							<xs:element name="destination" type="destinationType" minOccurs="0"/>
						</xs:sequence>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetImsiKey" id="CAI3GKeySetImsi">
						<xs:selector xpath="."/>
						<xs:field xpath="@imsi"/>
					</xs:key>
					<xs:keyref name="SetImsiKeyRef" refer="SetImsiKey">
						<xs:selector xpath="."/>
						<xs:field xpath="imsi"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="msisdnEntry" nillable="true" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdn" type="msisdnType" minOccurs="0"/>
							<xs:element name="destination" type="destinationType" minOccurs="0"/>
						</xs:sequence>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>						
					</xs:complexType>
					<xs:key name="SetMsisdnKey" id="CAI3GKeySetMsisdn">
						<xs:selector xpath="."/>
						<xs:field xpath="@msisdn"/>
					</xs:key>
					<xs:keyref name="SetMsisdnKeyRef" refer="SetMsisdnKey">
						<xs:selector xpath="."/>
						<xs:field xpath="msisdn"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="publicIdEntry" nillable="true" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="publicIdentity" type="publicIdValueType" minOccurs="0"/>
							<xs:element name="destination" type="destinationType" minOccurs="0"/>
						</xs:sequence>
						<xs:attribute name="publicIdentity" type="publicIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdentityAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetPublicIdKey" id="CAI3GKeySetPublicId">
						<xs:selector xpath="."/>
						<xs:field xpath="@publicIdentity"/>
					</xs:key>
					<xs:keyref name="SetPublicIdKeyRef" refer="SetPublicIdKey">
						<xs:selector xpath="."/>
						<xs:field xpath="publicIdentity"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="privateIdEntry" nillable="true" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateIdentity" type="privateIdValueType" minOccurs="0"/>
							<xs:element name="destination" type="destinationType" minOccurs="0"/>
						</xs:sequence>
						<xs:attribute name="privateIdentity" type="privateIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdentityAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="SetPrivateIdKey" id="CAI3GKeySetPrivateId">
						<xs:selector xpath="."/>
						<xs:field xpath="@privateIdentity"/>
					</xs:key>
					<xs:keyref name="SetPrivateIdKeyRef" refer="SetPrivateIdKey">
						<xs:selector xpath="."/>
						<xs:field xpath="privateIdentity"/>
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imsiAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="msisdn" type="msisdnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="msisdnAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="publicIdentity" type="publicIdValueType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="publicIdentityAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="privateIdentity" type="privateIdValueType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="privateIdentityAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="getSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsiEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="msisdnEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="publicIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="publicIdentity" type="publicIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="privateIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:attribute name="privateIdentity" type="privateIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="optional"/>
			<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
			<xs:attribute name="publicIdentity" type="publicIdValueType" use="optional"/>
			<xs:attribute name="privateIdentity" type="privateIdValueType" use="optional"/>
		</xs:complexType>
	</xs:element>
	<xs:element name="getResponseSubscription">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imsiEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="imsi" type="imsiType"/>
							<xs:element name="destination" type="destinationType"/>
						</xs:sequence>
						<xs:attribute name="imsi" type="imsiType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="imsiAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetImsiKey" id="CAI3GKeyGetImsi">
						<xs:selector xpath="."/>
						<xs:field xpath="@imsi"/>
					</xs:key>
					<xs:keyref name="GetImsiKeyRef" refer="GetImsiKey">
						<xs:selector xpath="."/>
						<xs:field xpath="imsi"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="msisdnEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="msisdn" type="msisdnType"/>
							<xs:element name="destination" type="destinationType"/>
						</xs:sequence>
						<xs:attribute name="msisdn" type="msisdnType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="msisdnAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetMsisdnKey" id="CAI3GKeyGetMsisdn">
						<xs:selector xpath="."/>
						<xs:field xpath="@msisdn"/>
					</xs:key>
					<xs:keyref name="GetMsisdnKeyRef" refer="GetMsisdnKey">
						<xs:selector xpath="."/>
						<xs:field xpath="msisdn"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="publicIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="publicIdentity" type="publicIdValueType"/>
							<xs:element name="destination" type="destinationType"/>
						</xs:sequence>
						<xs:attribute name="publicIdentity" type="publicIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="publicIdAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetPublicIdKey" id="CAI3GKeyGetPublicId">
						<xs:selector xpath="."/>
						<xs:field xpath="@publicIdentity"/>
					</xs:key>
					<xs:keyref name="GetPublicIdKeyRef" refer="GetPublicIdKey">
						<xs:selector xpath="."/>
						<xs:field xpath="publicIdentity"/>
					</xs:keyref>
				</xs:element>
				<xs:element name="privateIdEntry" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="privateIdentity" type="privateIdValueType"/>
							<xs:element name="destination" type="destinationType"/>
						</xs:sequence>
						<xs:attribute name="privateIdentity" type="privateIdValueType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="privateIdAttr"/>
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="GetPrivateIdKey" id="CAI3GKeyGetPrivateId">
						<xs:selector xpath="."/>
						<xs:field xpath="@privateIdentity"/>
					</xs:key>
					<xs:keyref name="GetPrivateIdKeyRef" refer="GetPrivateIdKey">
						<xs:selector xpath="."/>
						<xs:field xpath="privateIdentity"/>
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="imsi" type="imsiType" use="optional"/>
			<xs:attribute name="msisdn" type="msisdnType" use="optional"/>
			<xs:attribute name="publicIdentity" type="publicIdValueType" use="optional"/>
			<xs:attribute name="privateIdentity" type="privateIdValueType" use="optional"/>
		</xs:complexType>
	</xs:element>
	<!-- below is type definition -->
	<xs:simpleType name="destinationType">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<xs:simpleType name="imsiType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{6,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="msisdnType">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{5,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="publicIdValueType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(sip:.{1,256})|(tel:\+[\-.()0-9]{5,256}(!\.\*!)?)"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="privateIdValueType">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="256"/>
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
