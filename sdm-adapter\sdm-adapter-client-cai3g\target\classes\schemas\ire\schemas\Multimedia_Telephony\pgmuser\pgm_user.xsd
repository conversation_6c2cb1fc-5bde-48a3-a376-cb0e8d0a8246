<!-- BCE User 2014-11-17 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/pgm/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/pgm/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/pgm/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:element name="publicId" type="PublicIdType"/>
	<!-- DeleteUser MOId: publicId  MOType: user@http://schemas.ericsson.com/ma/pgm/ -->
	<xs:element name="deleteUser">
		<xs:annotation>
			<xs:documentation>
				The attributes for deleting a PGM User
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:attribute name="publicId" type="PublicIdType" use="required"/>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="PublicIdType">
		<xs:restriction base="xs:string">
			<xs:pattern value="(sip:.{1,256})|(tel:.{1,256})"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
