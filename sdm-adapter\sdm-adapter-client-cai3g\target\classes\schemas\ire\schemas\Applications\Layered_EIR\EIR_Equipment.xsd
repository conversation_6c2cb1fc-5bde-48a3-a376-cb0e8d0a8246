<!-- edited with XMLSpy v2011 sp1 (http://www.altova.com) by <PERSON> (<PERSON><PERSON>) -->
<!-- E<PERSON>, Equipment - PG - Provisioning Interface for EIR data, EAB/K-10:1883 Uen, PB13 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/EIR/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:x="http://schemas.ericsson.com/ma/EIR/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/EIR/"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="types/eirla_types.xsd" />
	<xs:element name="imei" type="imeiType" />
	<xs:element name="svn" type="svnType" />
	<!-- CreateEquipment MOId: imei, svn (optional) MOType: CreateEquipment@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="CreateEquipment">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imei" type="imeiType" />
				<xs:element name="svn" type="svnType" default="0F" minOccurs="0" />
				<xs:element name="date" type="dateType" minOccurs="0" />
				<xs:element name="time" type="timeType" minOccurs="0" />
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:element name="InsertEquipmentListType">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
							<xs:element name="insertReason" type="insertReasonType" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="clarifyReason" type="clarifyReasonType" minOccurs="0" />
				<xs:element name="sourceOfRequest" type="sourceOfRequestType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_imei">
			<xs:selector xpath="./x:imei" />
			<xs:field xpath="." />
		</xs:key>
		<xs:keyref name="keyref_imei" refer="key_imei">
			<xs:selector xpath="." />
			<xs:field xpath="@imei" />
		</xs:keyref>
	</xs:element>
	<!-- DeleteEquipment MOId: imei svn (optional) MOType: DeleteEquipment@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="DeleteEquipment">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:element name="removeReason" type="removeReasonType" minOccurs="0" />
				<xs:element name="clarifyReason" type="clarifyReasonType" minOccurs="0" />
				<xs:element name="sourceOfRequest" type="sourceOfRequestType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- SetEquipment MOId: imei, svn (optional) MOType: SetEquipment@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="SetEquipment">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="date" type="dateType" minOccurs="0" />
				<xs:element name="time" type="timeType" minOccurs="0" />
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:choice>
					<xs:element name="InsertEquipmentListType">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
								<xs:element name="insertReason" type="insertReasonType" minOccurs="0" />
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="RemoveEquipmentListType">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="equipmentListNumber" type="equipmentListNumberType" />
								<xs:element name="removeReason" type="removeReasonType" minOccurs="0" />
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:choice>
				<xs:element name="clarifyReason" type="clarifyReasonType" minOccurs="0" />
				<xs:element name="sourceOfRequest" type="sourceOfRequestType" minOccurs="0" />
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional" default="0F">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<!-- GetEquipment MOId: imsi, svn (optional) MOType: Equipment@http://schemas.ericsson.com/ma/EIR/ -->
	<!-- GetResponseEquipment MOId: imsi, svn (optional) MOType: Equipment@http://schemas.ericsson.com/ma/EIR/ -->
	<xs:element name="GetResponseEquipment">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="imei" type="imeiType" />
				<xs:element name="svn" type="svnType" minOccurs="0" />
				<xs:element name="date" type="dateType" minOccurs="0" />
				<xs:element name="time" type="timeType" minOccurs="0" />
				<xs:element name="comment" type="commentType" minOccurs="0" />
				<xs:sequence>
					<xs:element name="EquipmentListType">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="equipmentListNumber" type="equipmentListNumberType" minOccurs="0" maxOccurs="10" />
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:sequence>
			<xs:attribute name="imei" type="imeiType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="imeiAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="svn" type="svnType" use="optional">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="svnAttr" />
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
