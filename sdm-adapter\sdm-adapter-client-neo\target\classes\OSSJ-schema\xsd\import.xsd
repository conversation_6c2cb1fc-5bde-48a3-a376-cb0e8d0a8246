<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

    <xs:import namespace="http://ossj.org/xml/OrderManagement/v1-0"
        schemaLocation="OSSJ-OrderManagement-v1-0.xsd"/>

    <xs:import namespace="http://ossj.org/xml/Inventory/v1-2"
        schemaLocation="OSSJ-Inventory-v1-2.xsd"/>

    <xs:import namespace="http://amdocs/core/ossj-Common/dat/3"
        schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>

    <xs:import namespace="http://amdocs/core/ossj-OrderManagement/dat/3"
        schemaLocation="Amdocs-OSSJ-OrderManagement_3p0.xsd"/>

    <xs:import namespace="http://amdocs/core/ossj-Common-CBEProduct/dat/3"
        schemaLocation="Amdocs-OSSJ-Common-CBEProduct_3p0.xsd"/>

    <xs:import namespace="http://amdocs/core/ossj-Common-CBEService/dat/3"
        schemaLocation="Amdocs-OSSJ-Common-CBEService_3p0.xsd"/>

    <xs:import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEBi/v1-5"
        schemaLocation="OSSJ-Common-CBEBi-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5"
        schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>

    <xs:import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5"
        schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEProductOffering/v1-5"
        schemaLocation="OSSJ-Common-CBEProductOffering-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5"
        schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
    <xs:import namespace="http://ossj.org/xml/Common-CBEParty/v1-5"
        schemaLocation="OSSJ-Common-CBEParty-v1-5.xsd"/>

    <xs:import namespace="http://schemas.xmlsoap.org/soap/envelope/"
        schemaLocation="SOAP-Env.xsd"/>

</xs:schema>