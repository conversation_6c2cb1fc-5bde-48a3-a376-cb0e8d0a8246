<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" jaxb:version="2.0" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns="http://schemas.ericsson.com/ma/ECE/" xmlns:ns="http://schemas.ericsson.com/ma/ECE/" targetNamespace="http://schemas.ericsson.com/ma/ECE/" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:simpleType name="msisdnType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{5,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="msisdn" type="msisdnType"></xs:element>
 
    <xs:simpleType name="redirectNumberType">
        <xs:restriction base="xs:string">
            <xs:pattern value="(#1(0|1|2|3|4)|[0-9]){5,20}"/>
        </xs:restriction>
    </xs:simpleType>

		<xs:simpleType name="flagsType">
			<xs:restriction base="xs:integer">
				<xs:minInclusive value="0"/>
				<xs:maxInclusive value="65535"/>
			</xs:restriction>
		</xs:simpleType>


    <xs:element name="SetNumberRedirection">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="redirectNumber" type="redirectNumberType" />
                <xs:element name="flags" type="flagsType" />
            </xs:sequence>
            <xs:attribute name="msisdn" type="msisdnType" use="required" />
        </xs:complexType>
    </xs:element>

    <xs:element name="GetNumberRedirection">
        <xs:complexType>
            <xs:attribute name="msisdn" type="msisdnType" use="required" />
        </xs:complexType>
    </xs:element>

    <xs:element name="DeleteNumberRedirection">
        <xs:complexType>
            <xs:attribute name="msisdn" type="msisdnType" use="required" />
        </xs:complexType>
    </xs:element>

    <xs:element name="GetResponseNumberRedirection">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="msisdn" type="msisdnType" />
                <xs:element name="redirectNumber" type="redirectNumberType" />
                <xs:element name="flags" type="flagsType" />
            </xs:sequence>
            <xs:attribute name="msisdn" type="msisdnType" use="optional">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="msisdnAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

</xs:schema>
