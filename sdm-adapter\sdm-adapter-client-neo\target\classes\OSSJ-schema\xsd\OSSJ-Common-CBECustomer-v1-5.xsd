<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBECustomer/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbecustomer-v1-5="http://ossj.org/xml/Common-CBECustomer/v1-5"
    xmlns:cbeparty-v1-5="http://ossj.org/xml/Common-CBEParty/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEParty/v1-5"
        schemaLocation="OSSJ-Common-CBEParty-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <!-- Tigerstripe : Entity definitions for Customer  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->

    <complexType name="CustomerValue" >
        <annotation>
            <documentation>
A person or organization that buys products and services from the enterprise or receives free offers or services. This is modeled as a Party playing the role of Customer. A Customer is a type of PartyRole. Customers can also be other service providers who resell the enterprises products, other service providers that lease the enterprise's resources for utilization by the other service provider's products and services, and so forth.  See SID model phase VI
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbeparty-v1-5:PartyRoleValue" >    
                <sequence>
                    <element name="customerRank" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element ref="cbecustomer-v1-5:baseCustomerState_Customer" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerValue">
        <sequence>
            <element name="item" type="cbecustomer-v1-5:CustomerValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="CustomerKey">
        <annotation>
            <documentation>
                This CustomerKey encapsulates all the information that is necessary to 
                identify a particular instance of a CustomerValue. The type of the 
                primary key for this CustomerKey definition is: string 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbeparty-v1-5:PartyRoleKey">        
                <sequence/>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerKey">
        <sequence>
            <element name="item" type="cbecustomer-v1-5:CustomerKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="CustomerKeyResult">

        <annotation>
            <documentation>
                The CustomerKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a CustomerValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbeparty-v1-5:PartyRoleKeyResult">
                <sequence>
                     <element name="customerKey" type="cbecustomer-v1-5:CustomerKey" nillable="true" minOccurs="0"/>

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfCustomerKeyResult">
        <sequence>
            <element name="item" type="cbecustomer-v1-5:CustomerKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>

    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of Customer -->
    <element name="baseCustomerState_Customer" type="string" abstract="true"/>
     
    <element name="customerState_Customer" 
        type="cbedatatypes-v1-5:CustomerState" 
        substitutionGroup="cbecustomer-v1-5:baseCustomerState_Customer" /> 

    <element name="customerState_CustomerLifeCycleState" 
        type="cbedatatypes-v1-5:LifeCycleState" 
        substitutionGroup="cbecustomer-v1-5:baseCustomerState_Customer"/>

    <element name="customerState_CustomerState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="cbecustomer-v1-5:baseCustomerState_Customer"/>

    <!-- Tigerstripe : End of Entity definition for Customer -->





</schema>
