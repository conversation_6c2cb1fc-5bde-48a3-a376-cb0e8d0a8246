<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEProduct/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    xmlns:cbelocation-v1-5="http://ossj.org/xml/Common-CBELocation/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBELocation/v1-5"
        schemaLocation="OSSJ-Common-CBELocation-v1-5.xsd"/>
    <!-- Tigerstripe : Entity definitions for ProductSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->

    <complexType name="ProductSpecificationValue" >
        <annotation>
            <documentation>
A Common Business Entity interface defining a Product Specification. The Product Specification is a detailed description of a tangible or intangible object made available externally in the form of a ProductOffering to Customers or other Parties playing a PartyRole. A ProductSpecification may consist of other ProductSpecifications supplied together as a collection. Members of the collection may be offered in their own right.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="description" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="productNumber" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="brand" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="lifeCycleState" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="productBusinessName" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfProductSpecificationValue">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductSpecificationKey">
        <annotation>
            <documentation>

                This ProductSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductSpecificationValue. The type of the 
                primary key for this ProductSpecificationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductSpecificationKey">

        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductSpecificationKeyResult">
        <annotation>
            <documentation>
                The ProductSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKeyResult">
                <sequence>
                     <element name="productSpecificationKey" type="cbeproduct-v1-5:ProductSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductSpecificationKeyResult">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductSpecification -->
    <!-- Tigerstripe : End of Entity definition for ProductSpecification -->
    <!-- Tigerstripe : Entity definitions for ProductAssociation  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductAssociationValue" >

        <annotation>
            <documentation>
A ProductAssociationValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:AssociationValue" >    
                <sequence>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductAssociationValue">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductAssociationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductAssociationKey">
        <annotation>

            <documentation>
                This ProductAssociationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductAssociationValue. The type of the 
                primary key for this ProductAssociationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductAssociationKey">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductAssociationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductAssociationKeyResult">
        <annotation>
            <documentation>
                The ProductAssociationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductAssociationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKeyResult">
                <sequence>
                     <element name="productAssociationKey" type="cbeproduct-v1-5:ProductAssociationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductAssociationKeyResult">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductAssociationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ProductAssociation -->
    <!-- Tigerstripe : End of Entity definition for ProductAssociation -->
    <!-- Tigerstripe : Entity definitions for Product  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ProductValue" >

        <annotation>
            <documentation>
A Common Business Entity interface defining a Product. Products are things (tangible or intangible) which enterprises, such as service providers, market, sell or lease to customers to create profit.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="description" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="productSerialNumber" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element ref="cbeproduct-v1-5:baseProductState_Product" minOccurs="0"/>
                    <element name="refProductKey" type="cbeproduct-v1-5:ProductKey" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="refPlaceKeys" type="cbelocation-v1-5:ArrayOfPlaceKey" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfProductValue">
        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductKey">
        <annotation>
            <documentation>
                This ProductKey encapsulates all the information that is necessary to 
                identify a particular instance of a ProductValue. The type of the 
                primary key for this ProductKey definition is: anyType 
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductKey">
        <sequence>

            <element name="item" type="cbeproduct-v1-5:ProductKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ProductKeyResult">
        <annotation>
            <documentation>
                The ProductKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ProductValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="productKey" type="cbeproduct-v1-5:ProductKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfProductKeyResult">

        <sequence>
            <element name="item" type="cbeproduct-v1-5:ProductKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of Product -->
    <element name="baseProductState_Product" type="string" abstract="true"/>
     
    <element name="productState_Product" 
        type="cbeproduct-v1-5:ProductState" 
        substitutionGroup="cbeproduct-v1-5:baseProductState_Product" /> 

    <element name="productState_ProductLifeCycleState" 
        type="cbedatatypes-v1-5:LifeCycleState" 
        substitutionGroup="cbeproduct-v1-5:baseProductState_Product"/>

    <element name="productState_ProductState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="cbeproduct-v1-5:baseProductState_Product"/>

    <!-- Tigerstripe : End of Entity definition for Product -->
    <!-- Tigerstripe : Enumeration definitions for ProductState  -->
    <simpleType name="ProductState">
        <annotation>
            <documentation>
This interface defines the ProductState enumeration.
            </documentation>
        </annotation>
        <restriction base="string">

            <enumeration value="activated" />
            <enumeration value="designed" />
            <enumeration value="suspended" />
            <enumeration value="deactivated" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ProductState  -->





</schema>
