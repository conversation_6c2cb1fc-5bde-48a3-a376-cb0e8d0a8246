<?xml version="1.0" encoding="UTF-8"?>

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
   xmlns:amdocs-inv="http://amdocs/core/ossj-Inventory/dat/3"
   xmlns:amdocs-om="http://amdocs/core/ossj-OrderManagement/dat/3"
   xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
   xmlns:cbecustomer-v1-5="http://ossj.org/xml/Common-CBECustomer/v1-5"
   xmlns:inventory-v1-2="http://ossj.org/xml/Inventory/v1-2"
   xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
   xmlns:cbeproduct-v1-5="http://ossj.org/xml/Common-CBEProduct/v1-5"
   xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5"
   xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5"
   xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3"
   xmlns:amdocs-customer="http://amdocs/core/ossj-Common-CBECustomer/dat/3"
   targetNamespace="http://amdocs/core/ossj-Inventory/dat/3" elementFormDefault="qualified">

   <xs:annotation>

      <xs:documentation>This APS schema was generated at: 07 March 2008 15:30:21
         GMT</xs:documentation>

   </xs:annotation>

   <xs:import namespace="http://ossj.org/xml/Inventory/v1-2"
      schemaLocation="OSSJ-Inventory-v1-2.xsd"/>

   <xs:import namespace="http://amdocs/core/ossj-Common/dat/3"
      schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>

   <xs:import namespace="http://amdocs/core/ossj-OrderManagement/dat/3"
      schemaLocation="Amdocs-OSSJ-OrderManagement_3p0.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
      schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common/v1-5" schemaLocation="OSSJ-Common-v1-5.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common-CBEProduct/v1-5"
      schemaLocation="OSSJ-Common-CBEProduct-v1-5.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5"
      schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common-CBECustomer/v1-5"
      schemaLocation="OSSJ-Common-CBECustomer-v1-5.xsd"/>

   <xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5"
      schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
      
   <xs:import namespace="http://amdocs/core/ossj-Common-CBECustomer/dat/3"
      schemaLocation="Amdocs-OSSJ-Common-CBECustomer_3p0.xsd"/>
      
      <xs:import namespace="http://amdocs/core/ossj-Common-CBEResource/dat/3"
      schemaLocation="Amdocs-OSSJ-Common-CBEResource_3p0.xsd"/>

   <xs:element name="EntityValueAttributeChangeEvent">

      <xs:complexType>

         <xs:sequence>

            <xs:element name="event" type="amdocs-inv:EntityValueAttributeChangeEventType"/>

         </xs:sequence>

      </xs:complexType>

   </xs:element>

   <xs:complexType name="EntityValueAttributeChangeEventType">

      <xs:complexContent>

         <xs:extension base="co-v1-5:BaseEventType">

            <xs:sequence>

               <xs:element name="entityValue" type="cbecore-v1-5:EntityValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryInventoryRequest">

      <xs:complexType>

         <xs:sequence>

            <xs:element name="operator" type="xs:string" minOccurs="0">
               <xs:annotation>
                  <xs:documentation>Describes the custom topic that event notifications associated
                     with this request will be written to. If no operator is included in the
                     request, or the request operator is not present in configuration, this value is
                     ignored and events are sent to the default topic.</xs:documentation>
               </xs:annotation>
            </xs:element>

            <xs:element name="query" type="inventory-v1-2:InventoryQueryValue" minOccurs="0"
               maxOccurs="unbounded"/>

         </xs:sequence>

      </xs:complexType>

   </xs:element>

   <xs:element name="QueryInventoryResponse">

      <xs:complexType>

         <xs:sequence>

            <xs:element name="queryResponse" type="inventory-v1-2:InventoryQueryResponse"
               minOccurs="0" maxOccurs="unbounded"/>

         </xs:sequence>

      </xs:complexType>

   </xs:element>

   <xs:element name="QueryPPVHistoryRequest" type="amdocs-inv:QueryPPVHistoryRequest"/>

   <xs:complexType name="QueryPPVHistoryRequest">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

               <xs:element name="startDate" type="xs:string" minOccurs="0"/>

               <xs:element name="endDate" type="xs:string" minOccurs="0"/>

               <xs:element name="requesterId" type="xs:string" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryPPVHistoryResponse" type="amdocs-inv:QueryPPVHistoryResponse"/>

   <xs:complexType name="QueryPPVHistoryResponse">

      <xs:sequence>

         <xs:element name="queryResponse" type="inventory-v1-2:InventoryQueryResponse" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="QueryPPVCollectionRequest" type="amdocs-inv:QueryPPVCollectionRequest"/>

   <xs:complexType name="QueryPPVCollectionRequest">

      <xs:complexContent>

         <xs:extension base="co-v1-5:NamedQueryValue">

            <xs:sequence>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

               <xs:element name="requesterId" type="xs:string" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryPPVCollectionResponse" type="amdocs-inv:QueryPPVCollectionResponse"/>

   <xs:complexType name="QueryPPVCollectionResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryUsageRecordsValue" type="amdocs-inv:QueryUsageRecordsValue"/>

   <xs:complexType name="QueryUsageRecordsValue">

      <xs:complexContent>

         <xs:extension base="co-v1-5:NamedQueryValue">

            <xs:sequence>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

               <xs:element name="clientId" type="xs:string" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryUsageRecordsResponse" type="amdocs-inv:QueryUsageRecordsResponse"/>

   <xs:complexType name="QueryUsageRecordsResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="CustomerConfigRecord" type="amdocs-inv:CustomerConfigRecord"/>

   <xs:complexType name="CustomerConfigRecord">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="customerKey" type="cbecustomer-v1-5:CustomerKey" minOccurs="0"/>

               <!-- <xs:element name="requestKey" type="amdocs-om:RequestKey" minOccurs="0"/>-->

               <xs:element name="recordDate" type="xs:string" minOccurs="0"/>

               <xs:element name="requestType" type="xs:string" minOccurs="0"/>

               <xs:element name="queryRecordItemNumber" type="xs:string" minOccurs="0"/>

               <xs:element name="customerConfigDetails"
                  type="amdocs-inv:CustomerProductResourceConfigValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="CollectionRecord" type="amdocs-inv:CollectionRecord"/>

   <xs:complexType name="CollectionRecord">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfCustomerConfigRecord" type="amdocs-inv:ArrayOfCustomerConfigRecord"/>

   <xs:complexType name="ArrayOfCustomerConfigRecord">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="item" type="amdocs-inv:CustomerConfigRecord" minOccurs="0"
                  maxOccurs="unbounded"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ServiceConfigurationRecord" type="amdocs-inv:ServiceConfigurationRecord"/>

   <xs:complexType name="ServiceConfigurationRecord">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="UserHierarchyValue" type="amdocs-inv:UserHierarchyValue"/>

   <xs:complexType name="UserHierarchyValue">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="customer" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

               <xs:element name="subcustomers" type="amdocs-inv:ArrayOfCustomerResourceConfigValue"
                  minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfUserHierarchyValue" type="amdocs-inv:ArrayOfUserHierarchyValue"/>

   <xs:complexType name="ArrayOfUserHierarchyValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:UserHierarchyValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="QueryCustomerAndResourcesByAssociatedCustomerResponse"
      type="amdocs-inv:QueryCustomerAndResourcesByAssociatedCustomerResponse"/>

   <xs:complexType name="QueryCustomerAndResourcesByAssociatedCustomerResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryServiceConfigurationResponse"
      type="amdocs-inv:QueryServiceConfigurationResponse"/>

   <xs:complexType name="QueryServiceConfigurationResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryServicesConfigurationResponse"
      type="amdocs-inv:QueryServicesConfigurationResponse"/>
   
   <xs:complexType name="QueryServicesConfigurationResponse">
      
      <xs:complexContent>
         
         <xs:extension base="inventory-v1-2:InventoryQueryResponse">
            
            <xs:sequence/>
            
         </xs:extension>
         
      </xs:complexContent>
      
   </xs:complexType>

   <xs:element name="QueryResourceConfigurationResponse"
      type="amdocs-inv:QueryResourceConfigurationResponse"/>

   <xs:complexType name="QueryResourceConfigurationResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryResourceDetailsResponse" type="amdocs-inv:QueryResourceDetailsResponse"/>

   <xs:complexType name="QueryResourceDetailsResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="CustomerResourceConfigValue" type="amdocs-inv:CustomerResourceConfigValue"/>

   <xs:complexType name="CustomerResourceConfigValue">

      <xs:annotation>

         <xs:documentation>An entity representing a UserHierarchyValue</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="customer" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

               <xs:element name="subcustomers" type="amdocs-inv:ArrayOfCustomerResourceConfigValue"
                  minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>
   
    <xs:element name="SubscriberConfigurationValue" type="amdocs-inv:SubscriberConfigurationValue"/>

   <xs:complexType name="SubscriberConfigurationValue">

      <xs:annotation>

         <xs:documentation>An entity representing a UserHierarchyValue</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="subscriber" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

            </xs:sequence>

			</xs:extension>
      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfCustomerResourceConfigValue"
      type="amdocs-inv:ArrayOfCustomerResourceConfigValue"/>

   <xs:complexType name="ArrayOfCustomerResourceConfigValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:CustomerResourceConfigValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="ServiceHierarchyValue" type="amdocs-inv:ServiceHierarchyValue"/>

   <xs:complexType name="ServiceHierarchyValue">

      <xs:annotation>

         <xs:documentation>An entity representing a ServiceHierarchyValue</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ServiceHierarchyKey" type="amdocs-inv:ServiceHierarchyKey"/>

   <xs:complexType name="ServiceHierarchyKey">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityKey">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ResourceHierarchyValue" type="amdocs-inv:ResourceHierarchyValue"/>

   <xs:complexType name="ResourceHierarchyValue">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfServiceHierarchyValue" type="amdocs-inv:ArrayOfServiceHierarchyValue"/>

   <xs:complexType name="ArrayOfServiceHierarchyValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:ServiceHierarchyValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="ArrayOfResourceHierarchyValue" type="amdocs-inv:ArrayOfResourceHierarchyValue"/>

   <xs:complexType name="ArrayOfResourceHierarchyValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:ResourceHierarchyValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="ProductHierarchyValue" type="amdocs-inv:ProductHierarchyValue"/>

   <xs:complexType name="ProductHierarchyValue">

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="product" type="cbeproduct-v1-5:ProductValue" minOccurs="0"/>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

               <xs:element name="services" type="amdocs-inv:ServiceHierarchyValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfProductHierarchyValue" type="amdocs-inv:ArrayOfProductHierarchyValue"/>

   <xs:complexType name="ArrayOfProductHierarchyValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:ProductHierarchyValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="CustomerProductResourceConfigValue"
      type="amdocs-inv:CustomerProductResourceConfigValue"/>

   <xs:complexType name="CustomerProductResourceConfigValue">

      <xs:annotation>

         <xs:documentation>An entity representing a
            CustomerProductResourceConfigValue</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="cbecore-v1-5:EntityValue">

            <xs:sequence>

               <xs:element name="customer" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

               <xs:element name="products" type="cbeproduct-v1-5:ArrayOfProductValue" minOccurs="0"/>

               <xs:element name="resources" type="cberesource-v1-5:ArrayOfResourceValue"
                  minOccurs="0"/>

               <xs:element name="subcustomers"
                  type="amdocs-inv:ArrayOfCustomerProductResourceConfigValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfCustomerProductResourceConfigValue"
      type="amdocs-inv:ArrayOfCustomerProductResourceConfigValue"/>

   <xs:complexType name="ArrayOfCustomerProductResourceConfigValue">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:CustomerProductResourceConfigValue" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="QueryCustomerAndResourcesByAssociatedCustomerValue"
      type="amdocs-inv:QueryCustomerAndResourcesByAssociatedCustomerValue"/>

   <xs:complexType name="QueryCustomerAndResourcesByAssociatedCustomerValue">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="customer" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryServiceConfigurationValue"
      type="amdocs-inv:QueryServiceConfigurationValue"/>

   <xs:complexType name="QueryServiceConfigurationValue">

      <xs:complexContent>

         <xs:extension base="co-v1-5:NamedQueryValue">

            <xs:sequence>

               <xs:element name="action" type="xs:string" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Describes the action that is being requested. For example,
                        query or delete.</xs:documentation>
                  </xs:annotation>
               </xs:element>

               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

               <xs:element name="clientId" type="xs:string" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>



   <xs:element name="QueryServicesConfigurationValue"
      type="amdocs-inv:QueryServicesConfigurationValue"/>
   
   <xs:complexType name="QueryServicesConfigurationValue">
      
      <xs:complexContent>
         
         <xs:extension base="co-v1-5:NamedQueryValue">
            
            <xs:sequence>
               
               <xs:element name="action" type="xs:string" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Describes the action that is being requested. For example,
                        query or delete.</xs:documentation>
                  </xs:annotation>
               </xs:element>
               
               <xs:element name="service" type="cbeservice-v1-5:ServiceValue" minOccurs="0"/>
               
               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>
               
               <xs:element name="clientId" type="xs:string" minOccurs="0"/>
               
            </xs:sequence>
            
         </xs:extension>
         
      </xs:complexContent>
      
   </xs:complexType>



   <xs:element name="QueryResourceConfigurationValue"
      type="amdocs-inv:QueryResourceConfigurationValue"/>

   <xs:complexType name="QueryResourceConfigurationValue">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="action" type="xs:string" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Describes the action that is being requested. For example,
                        query or delete.</xs:documentation>
                  </xs:annotation>
               </xs:element>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryResourceDetailsValue" type="amdocs-inv:QueryResourceDetailsValue"/>

   <xs:complexType name="QueryResourceDetailsValue">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryCustomerConfigurationValue"
      type="amdocs-inv:QueryCustomerConfigurationValue"/>

   <xs:complexType name="QueryCustomerConfigurationValue">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="customer" type="cbecustomer-v1-5:CustomerValue" minOccurs="0">
                  <xs:annotation>
                     <xs:documentation>Container for all details relating to a particular
                        customer.</xs:documentation>
                  </xs:annotation>
               </xs:element>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryCustomerConfigurationResponse"
      type="amdocs-inv:QueryCustomerConfigurationResponse"/>

   <xs:complexType name="QueryCustomerConfigurationResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryCustomerConfigurationRecordsResponse"
      type="amdocs-inv:QueryCustomerConfigurationRecordsResponse"/>

   <xs:complexType name="QueryCustomerConfigurationRecordsResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryCustomersByResourceResponse"
      type="amdocs-inv:QueryCustomersByResourceResponse"/>

   <xs:complexType name="QueryCustomersByResourceResponse">

      <xs:annotation>

         <xs:documentation>An entity representing a QueryCustomersByResourceResponse. This is an
            auto-generated file: do not modify!</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence/>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QueryCustomersByResourceValue" type="amdocs-inv:QueryCustomersByResourceValue"/>

   <xs:complexType name="QueryCustomersByResourceValue">

      <xs:annotation>

         <xs:documentation>An entity representing a QueryCustomersByResource. This is an
            auto-generated file: do not modify!</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>

               <xs:element name="resource" type="cberesource-v1-5:ResourceValue" minOccurs="0"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="QuerySubscriberByIdValue" type="amdocs-inv:QuerySubscriberByIdValue"/>

   <xs:complexType name="QuerySubscriberByIdValue">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>
               <xs:element name="subscriberKey" type="amdocs-customer:SubscriberKey" minOccurs="0"/>
               <xs:element name="subscriberType" type="xs:string" minOccurs="0"/>
               <xs:element name="operator" type="xs:string" minOccurs="0"/>
            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>
   
   <xs:element name="QuerySubscriberByIdResponse" type="amdocs-inv:QuerySubscriberByIdResponse"/>

   <xs:complexType name="QuerySubscriberByIdResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence>
               <xs:element name="subscriber" type="amdocs-customer:SubscriberValue" minOccurs="0"/>
            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>
   
   <xs:element name="QuerySuggestedNamesRequest" type="amdocs-inv:QuerySuggestedNamesRequest"/>

   <xs:complexType name="QuerySuggestedNamesRequest">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryValue">

            <xs:sequence>
               <xs:element name="subscriber" type="amdocs-customer:SubscriberValue" minOccurs="0"/>
               <xs:element name="operator" type="xs:string" minOccurs="0"/>
            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>
   
   <xs:element name="QuerySuggestedNamesResponse" type="amdocs-inv:QuerySuggestedNamesResponse"/>

   <xs:complexType name="QuerySuggestedNamesResponse">

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:InventoryQueryResponse">

            <xs:sequence>
               <xs:element name="suggestedNames" type="amdocs-inv:ArrayOfSuggestedNames" minOccurs="0"/>
            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>
   
   
   <xs:element name="ArrayOfSuggestedNames" type="amdocs-inv:ArrayOfSuggestedNames"/>

   <xs:complexType name="ArrayOfSuggestedNames">

      <xs:sequence>

         <xs:element name="item" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>
   

   <xs:element name="ReportScopeException" type="amdocs-inv:ReportScopeException"/>

   <xs:complexType name="ReportScopeException">

      <xs:annotation>

         <xs:documentation>Amdocs implementation of {@link ReportScopeException}.</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:ReportScopeException">

            <xs:sequence>

               <xs:element name="ExceptionDetails" type="amdocs-co:ExceptionDetails" minOccurs="0"
                  maxOccurs="unbounded"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="SemanticRuleViolationException"
      type="amdocs-inv:SemanticRuleViolationException"/>

   <xs:complexType name="SemanticRuleViolationException">

      <xs:annotation>

         <xs:documentation>Amdocs implementation of {@link
            SemanticRuleViolationException}.</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:SemanticRuleViolationException">

            <xs:sequence>

               <xs:element name="ExceptionDetails" type="amdocs-co:ExceptionDetails" minOccurs="0"
                  maxOccurs="unbounded"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="SpecificationViolationException"
      type="amdocs-inv:SpecificationViolationException"/>

   <xs:complexType name="SpecificationViolationException">

      <xs:annotation>

         <xs:documentation>Amdocs implementation of {@link
            SpecificationViolationException}.</xs:documentation>

      </xs:annotation>

      <xs:complexContent>

         <xs:extension base="inventory-v1-2:SpecificationViolationException">

            <xs:sequence>

               <xs:element name="ExceptionDetails" type="amdocs-co:ExceptionDetails" minOccurs="0"
                  maxOccurs="unbounded"/>

            </xs:sequence>

         </xs:extension>

      </xs:complexContent>

   </xs:complexType>

   <xs:element name="ArrayOfReportScopeException" type="amdocs-inv:ArrayOfReportScopeException"/>

   <xs:complexType name="ArrayOfReportScopeException">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:ReportScopeException" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="ArrayOfSemanticRuleViolationException"
      type="amdocs-inv:ArrayOfSemanticRuleViolationException"/>

   <xs:complexType name="ArrayOfSemanticRuleViolationException">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:SemanticRuleViolationException" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

   <xs:element name="ArrayOfSpecificationViolationException"
      type="amdocs-inv:ArrayOfSpecificationViolationException"/>

   <xs:complexType name="ArrayOfSpecificationViolationException">

      <xs:sequence>

         <xs:element name="item" type="amdocs-inv:SpecificationViolationException" minOccurs="0"
            maxOccurs="unbounded"/>

      </xs:sequence>

   </xs:complexType>

</xs:schema>
