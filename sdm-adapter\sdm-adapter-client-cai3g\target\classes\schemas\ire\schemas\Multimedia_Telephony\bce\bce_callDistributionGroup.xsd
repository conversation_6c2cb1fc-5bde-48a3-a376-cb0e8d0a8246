<!-- BCE CDG 2014-11-18 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" targetNamespace="http://schemas.ericsson.com/ma/bce/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd"/>
	<xs:element name="serviceProviderId" type="ServiceProviderId"/>
	<xs:element name="companyId" type="CompanyId"/>
	<xs:element name="cdgId" type="UpCdgIdType"/>
	<xs:complexType name="BceCDGType">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId"/>
			<xs:element name="companyId" type="CompanyId"/>
			<xs:element name="cdgId" type="UpCdgIdType"/>
			<!-- CDG Attributes -->
			<xs:element name="sipAddress" type="SipUriType" minOccurs="0" maxOccurs="10"/>
			<xs:element name="bagNumbers" type="UpBagNumbers" minOccurs="0"/>
			<xs:element name="shortNumber" type="ShortNumberType" minOccurs="0"/>
			<xs:element name="name" type="NameType" minOccurs="0"/>
			<xs:element name="description" type="DescriptionType" minOccurs="0"/>
			<xs:element name="maxConcurrentAttempts" type="UpMaxConcurrentAttemptsType" minOccurs="0"/>
			<xs:element name="concurrentAttempts" type="xs:int" minOccurs="0"/>
			<xs:element name="clericalTimeOut" type="UpClericalTimeOutType" minOccurs="0"/>
			<xs:element name="noAnswerTimeOut" type="UpNoAnswerTimeOutType" minOccurs="0"/>
			<xs:element name="maxQueueSize" type="UpMaxQueueSizeType" minOccurs="0"/>
			<xs:element name="maxTimeInQueue" type="UpMaxTimeInQueueType" minOccurs="0"/>
			<xs:element name="cdgOpen" type="UpOpenEnum" minOccurs="0"/>
			<xs:element name="playWelcomeAnnouncement" type="xs:boolean" minOccurs="0"/>
			<xs:element name="playQueueAnnouncement" type="xs:boolean" minOccurs="0"/>
			<xs:element name="overflowWhenBusy" type="UpOverflowSituationType" minOccurs="0"/>
			<xs:element name="overflowWhenNoAgents" type="UpOverflowSituationType" minOccurs="0"/>
			<xs:element name="overflowWhenClosed" type="UpOverflowSituationType" minOccurs="0"/>
			<xs:element name="voiceMailAddress" type="GenericAddressType" minOccurs="0"/>
			<xs:element name="incomingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
			<xs:element name="outgoingNumberPresentation" type="UpNumberPresentationEnum" minOccurs="0"/>
			<xs:element name="agents" type="UpAgentType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="announcements" type="UpAnnouncementType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="anonymousCallRejection" type="UpFeatureState" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="serviceProviderId" type="ServiceProviderId" use="required"/>
		<xs:attribute name="companyId" type="CompanyId" use="required"/>
		<xs:attribute name="cdgId" type="UpCdgIdType" use="required"/>
	</xs:complexType>
	<!-- Create CDG MOId: serviceProviderId, companyId, cdgId  MOType: CallDistributionGroup@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createCDG" type="BceCDGType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE CDG
			</xs:documentation>
		</xs:annotation>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@serviceProviderId"/>
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="serviceProviderId"/>
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@companyId"/>
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="companyId"/>
		</xs:keyref>
		<xs:key name="cdgIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="@cdgId"/>
		</xs:key>
		<xs:keyref name="cdgIdKeyRef_Create" refer="cdgIdKey_Create">
			<xs:selector xpath="."/>
			<xs:field xpath="cdgId"/>
		</xs:keyref>
	</xs:element>
	<!-- Set CDG MOId: serviceProviderId, companyId, cdgId  MOType: CallDistributionGroup@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setCDG" type="BceCDGType">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE CDG
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- get CDG MOId: serviceProviderId, companyId, cdgId  MOType: CallDistributionGroup@http://schemas.ericsson.com/ma/bce/ -->
	<!-- get CDG response -->
	<xs:element name="getCDGResponse" type="BceCDGType">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE CDG
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- delete CDG MOId: serviceProviderId, companyId, cdgId  MOType: CallDistributionGroup@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
