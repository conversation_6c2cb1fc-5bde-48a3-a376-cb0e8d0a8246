<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
<jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
<jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
</jaxws:bindings>
<types>
<xs:schema xmlns="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
<xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd"/>
<xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/HLR_SubscriberData.xsd"/>
<xs:element name="Get">
<xs:complexType>
<xs:sequence>
<xs:element fixed="SubscriberData@http://schemas.ericsson.com/pg/hlr/13.5/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:choice>
<xs:element ref="hlr:msisdn"/>
<xs:element ref="hlr:imsi"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="extension">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" ref="hlr:PrimaryHLRId"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="GetResponse">
<xs:complexType>
<xs:sequence>
<xs:element maxOccurs="unbounded" minOccurs="0" name="MOId" type="AnyMOIdType"/>
<xs:element minOccurs="0" name="MOAttributes">
<xs:complexType>
<xs:sequence>
<xs:element ref="hlr:GetSubscriberData"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="Set">
<xs:complexType>
<xs:sequence>
<xs:element fixed="SubscriberData@http://schemas.ericsson.com/pg/hlr/13.5/" name="MOType" type="xs:string"/>
<xs:element name="MOId">
<xs:complexType>
<xs:sequence>
<xs:element ref="hlr:msisdn"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="MOAttributes">
<xs:complexType>
<xs:sequence>
<xs:element ref="hlr:SetSubscriberData"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element minOccurs="0" name="extension">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" ref="hlr:PrimaryHLRId"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
</types>
<message name="GetRequest">
<part element="cai3g:Get" name="parameters"/>
</message>
<message name="GetResponse">
<part element="cai3g:GetResponse" name="parameters"/>
</message>
<message name="SetRequest">
<part element="cai3g:Set" name="parameters"/>
</message>
<message name="SetResponse">
<part element="cai3g:SetResponse" name="parameters"/>
</message>
<message name="HeadInfo">
<part element="cai3g:SessionId" name="sessionId"/>
</message>
<message name="Cai3gFault">
<part element="cai3g:Cai3gFault" name="parameters"/>
</message>
<message name="Cai3gHeaderFault">
<part element="cai3g:SessionIdFault" name="sessionIdFault"/>
<part element="cai3g:TransactionIdFault" name="transactionIdFault"/>
<part element="cai3g:SequenceIdFault" name="sequenceIdFault"/>
</message>
<portType name="SubscriberData">
<operation name="Get">
<input message="cai3g:GetRequest"/>
<output message="cai3g:GetResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
<operation name="Set">
<input message="cai3g:SetRequest"/>
<output message="cai3g:SetResponse"/>
<fault message="cai3g:Cai3gFault" name="Cai3gFault"/>
</operation>
</portType>
<binding name="SubscriberData" type="cai3g:SubscriberData">
<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
<operation name="Get">
<soap:operation soapAction="CAI3G#Get" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
<operation name="Set">
<soap:operation soapAction="CAI3G#Set" style="document"/>
<input>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal"/>
</input>
<output>
<soap:body use="literal"/>
<soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
<soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal"/>
</soap:header>
</output>
<fault name="Cai3gFault">
<soap:fault name="Cai3gFault" use="literal"/>
</fault>
</operation>
</binding>
<service name="Provisioning">
<port binding="cai3g:SubscriberData" name="SubscriberData">
<soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2"/>
</port>
</service>
</definitions>
