<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
  xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlrOld="http://schemas.ericsson.com/pg/hlr/13.5/" targetNamespace="http://schemas.ericsson.com/cai3g1.2/">
  <jaxws:bindings xmlns:jaxws="http://java.sun.com/xml/ns/jaxws">
    <!-- disable wrapper style generation -->
    <jaxws:enableWrapperStyle>false</jaxws:enableWrapperStyle>
  </jaxws:bindings>
  <types>
    <xs:schema targetNamespace="http://schemas.ericsson.com/cai3g1.2/" xmlns:hlr="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns="http://schemas.ericsson.com/cai3g1.2/"
      elementFormDefault="qualified" attributeFormDefault="unqualified">
      <xs:include schemaLocation="../../../schemas/Generic/cai3g1.2_provisioning.xsd" />
      <xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/HLR_MSISDNChangeover.xsd" />
      <xs:import namespace="http://schemas.ericsson.com/pg/hlr/13.5/" schemaLocation="../../../schemas/2G_3G/Deprecated-Layered_HLR/types/hlrla_types.xsd" />
      <xs:element name="Create">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="MOType" type="xs:string" fixed="MSISDNChangeover@http://schemas.ericsson.com/pg/hlr/13.5/" />
            <xs:element name="MOId">
              <xs:complexType>
                <xs:sequence>
                  <xs:choice>
                    <xs:element ref="hlr:msisdn" />
                    <xs:element ref="hlr:imsi" />
                  </xs:choice>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="MOAttributes">
              <xs:complexType>
                <xs:sequence>
                  <xs:element ref="hlr:CreateMSISDNChangeover" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="extension" minOccurs="0">
              <xs:complexType>
                <xs:sequence>
                  <xs:element ref="hlr:PrimaryHLRId" minOccurs="0" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Cai3gFaultMSISDNCHO">
           	<xs:complexType>
           		<xs:sequence>
           			<xs:element name="faultcode" type="xs:integer" />
           			<xs:element name="faultreason">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="reasonText" type="xs:string" maxOccurs="unbounded" />
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="faultrole" type="xs:string" />
					<xs:element name="details" minOccurs="0">
						<xs:complexType>
							<xs:sequence>
								<xs:element ref="hlrOld:PGFault"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:complexType>
		</xs:element>
    </xs:schema>
  </types>
  <message name="CreateRequest">
    <part name="parameters" element="cai3g:Create" />
  </message>
  <message name="CreateResponse">
    <part name="parameters" element="cai3g:CreateResponse" />
  </message>
  <message name="HeadInfo">
    <part name="sessionId" element="cai3g:SessionId" />
  </message>
  <message name="Cai3gFaultMSISDNCHO">
    <part name="parameters" element="cai3g:Cai3gFaultMSISDNCHO" />
  </message>
  <message name="Cai3gHeaderFault">
    <part name="sessionIdFault" element="cai3g:SessionIdFault" />
    <part name="transactionIdFault" element="cai3g:TransactionIdFault" />
    <part name="sequenceIdFault" element="cai3g:SequenceIdFault" />
  </message>
  <portType name="MSISDNChangeover">
    <operation name="Create">
      <input message="cai3g:CreateRequest" />
      <output message="cai3g:CreateResponse" />
      <fault name="Cai3gFaultMSISDNCHO" message="cai3g:Cai3gFaultMSISDNCHO" />
    </operation>
  </portType>
  <binding name="MSISDNChangeover" type="cai3g:MSISDNChangeover">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
    <operation name="Create">
      <soap:operation soapAction="CAI3G#Create" style="document" />
      <input>
        <soap:body use="literal" />
        <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal" />
      </input>
      <output>
        <soap:body use="literal" />
        <soap:header message="cai3g:HeadInfo" part="sessionId" use="literal">
          <soap:headerfault message="cai3g:Cai3gHeaderFault" part="sessionIdFault" use="literal" />
        </soap:header>
      </output>
      <fault name="Cai3gFaultMSISDNCHO">
        <soap:fault name="Cai3gFaultMSISDNCHO" use="literal" />
      </fault>
    </operation>
  </binding>
  <service name="Provisioning">
    <port name="MSISDNChangeover" binding="cai3g:MSISDNChangeover">
      <soap:address location="http://anypg.anyprovisioningprovider.com/CAI3G1.2/services/CAI3G1.2" />
    </port>
  </service>
</definitions>