<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:amdocs-co="http://amdocs/core/ossj-Common/dat/3" xmlns:amdocs-service="http://amdocs/core/ossj-Common-CBEService/dat/3" xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5" xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5" xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5" xmlns:amdocs-party="http://amdocs/core/ossj-Common-CBEParty/dat/3" targetNamespace="http://amdocs/core/ossj-Common-CBEService/dat/3" elementFormDefault="qualified">
	<xs:import namespace="http://ossj.org/xml/Common-CBEService/v1-5" schemaLocation="OSSJ-Common-CBEService-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBECore/v1-5" schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
	<xs:import namespace="http://ossj.org/xml/Common-CBEResource/v1-5" schemaLocation="OSSJ-Common-CBEResource-v1-5.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common-CBEParty/dat/3" schemaLocation="Amdocs-OSSJ-Common-CBEParty_3p0.xsd"/>
	<xs:import namespace="http://amdocs/core/ossj-Common/dat/3" schemaLocation="Amdocs-OSSJ-Common_3p0.xsd"/>
	<xs:element name="state_Service" type="xs:string" substitutionGroup="cbeservice-v1-5:baseState_Service"/>
	<xs:element name="ServiceKey" type="amdocs-service:ServiceKey">
		<xs:annotation>
			<xs:documentation/>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="ServiceKey">
		<xs:complexContent>
			<xs:extension base="cbeservice-v1-5:ServiceKey">
				<xs:sequence>
					<xs:element name="servicePrimaryKey" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The unique identifier for the service.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceKey" type="amdocs-service:ArrayOfServiceKey"/>
	<xs:complexType name="ArrayOfServiceKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-service:ServiceKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ServiceSpecificationKey" type="amdocs-service:ServiceSpecificationKey"/>
	<xs:complexType name="ServiceSpecificationKey">
		<xs:complexContent>
			<xs:extension base="cbeservice-v1-5:ServiceSpecificationKey">
				<xs:sequence>
					<xs:element name="serviceSpecificationPrimaryKey" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceSpecificationKey" type="amdocs-service:ArrayOfServiceSpecificationKey"/>
	<xs:complexType name="ArrayOfServiceSpecificationKey">
		<xs:sequence>
			<xs:element name="item" type="amdocs-service:ServiceSpecificationKey" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ServiceSpecificationValue" type="amdocs-service:ServiceSpecificationValue"/>
	<xs:complexType name="ServiceSpecificationValue">
		<xs:complexContent>
			<xs:extension base="cbeservice-v1-5:ServiceSpecificationValue">
				<xs:sequence>
					<xs:element name="serviceSpecificationKey" type="amdocs-service:ServiceSpecificationKey" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceSpecificationValue" type="amdocs-service:ArrayOfServiceSpecificationValue"/>
	<xs:complexType name="ArrayOfServiceSpecificationValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-service:ServiceSpecificationValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ServiceValue" type="amdocs-service:ServiceValue"/>
	<xs:complexType name="ServiceValue">
		<xs:complexContent>
			<xs:extension base="cbeservice-v1-5:ServiceValue">
				<xs:sequence>
					<xs:element name="name" type="xs:string" minOccurs="0"/>
					<xs:element name="serviceType" type="xs:string" minOccurs="0"/>
					<xs:element name="currentState" type="xs:string" minOccurs="0"/>
					<xs:element name="serviceSubType" type="xs:string" minOccurs="0"/>
				
					<xs:element name="requiredServices" type="cbeservice-v1-5:ArrayOfServiceKey" minOccurs="0"/>
					<xs:element name="dependantServices" type="cbeservice-v1-5:ArrayOfServiceKey" minOccurs="0"/>
					<xs:element name="associatedResources" type="cberesource-v1-5:ArrayOfResourceKey" minOccurs="0"/>
					<xs:element name="previousServiceConfiguration" type="amdocs-service:ServiceValue" minOccurs="0"/>
					<xs:element name="activationTargets" type="amdocs-co:ArrayOfActivationTargetValue" minOccurs="0"/>
					<xs:element name="associatedProductKey" type="cbecore-v1-5:EntityKey" minOccurs="0"/>
					<xs:element name="operator" type="xs:string" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArrayOfServiceValue" type="amdocs-service:ArrayOfServiceValue"/>
	<xs:complexType name="ArrayOfServiceValue">
		<xs:sequence>
			<xs:element name="item" type="amdocs-service:ServiceValue" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>