<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEService/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cbeservice-v1-5="http://ossj.org/xml/Common-CBEService/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
        
    <!-- Tigerstripe : Entity definitions for ServiceAssociation  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceAssociationValue" >

        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.service.ServiceAssociationValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:AssociationValue" >    
                <sequence>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceAssociationValue">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceAssociationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceAssociationKey">
        <annotation>

            <documentation>
                This ServiceAssociationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceAssociationValue. The type of the 
                primary key for this ServiceAssociationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfServiceAssociationKey">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceAssociationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceAssociationKeyResult">
        <annotation>
            <documentation>
                The ServiceAssociationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceAssociationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKeyResult">
                <sequence>
                     <element name="serviceAssociationKey" type="cbeservice-v1-5:ServiceAssociationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfServiceAssociationKeyResult">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceAssociationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ServiceAssociation -->
    <!-- Tigerstripe : End of Entity definition for ServiceAssociation -->
    <!-- Tigerstripe : Entity definitions for ServiceSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceSpecificationValue" >

        <annotation>
            <documentation>
A Common Business Entity interface defining a ServiceSpecification
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationValue" >    
                <sequence>
                    <element name="name" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="description" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />

                    <element name="validFor" type="cbedatatypes-v1-5:TimePeriod" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="serviceBusinessName" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceSpecificationValue">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>

        </sequence>
    </complexType>
    <complexType name="ServiceSpecificationKey">
        <annotation>
            <documentation>
                This ServiceSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceSpecificationValue. The type of the 
                primary key for this ServiceSpecificationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>

            <extension base = "cbecore-v1-5:EntitySpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceSpecificationKey">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>

    </complexType>
    <complexType name="ServiceSpecificationKeyResult">
        <annotation>
            <documentation>
                The ServiceSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKeyResult">

                <sequence>
                     <element name="serviceSpecificationKey" type="cbeservice-v1-5:ServiceSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceSpecificationKeyResult">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>

    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ServiceSpecification -->
    <!-- Tigerstripe : End of Entity definition for ServiceSpecification -->
    <!-- Tigerstripe : Entity definitions for Service  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ServiceValue" >
        <annotation>
            <documentation>
A Common Business Entity interface defining a Service. Extends from javax.oss.service.ServiceValue
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="mandatory" type="boolean" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element ref="cbeservice-v1-5:baseStartMode_Service" minOccurs="0"/>
                    <element ref="cbeservice-v1-5:baseState_Service" minOccurs="0"/>
                    <element name="subscriberId" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfServiceValue">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceKey">
        <annotation>

            <documentation>
                This ServiceKey encapsulates all the information that is necessary to 
                identify a particular instance of a ServiceValue. The type of the 
                primary key for this ServiceKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfServiceKey">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ServiceKeyResult">
        <annotation>
            <documentation>
                The ServiceKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ServiceValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="serviceKey" type="cbeservice-v1-5:ServiceKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfServiceKeyResult">
        <sequence>
            <element name="item" type="cbeservice-v1-5:ServiceKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of Service -->
    <element name="baseStartMode_Service" type="int" abstract="true"/>
     
    <element name="startMode_Service" 
        type="cbeservice-v1-5:StartMode" 
        substitutionGroup="cbeservice-v1-5:baseStartMode_Service" /> 

    <element name="baseState_Service" type="string" abstract="true"/>
     
    <element name="state_Service" 
        type="cbeservice-v1-5:ServiceState" 
        substitutionGroup="cbeservice-v1-5:baseState_Service" /> 

    <element name="state_ServiceLifeCycleState" 
        type="cbedatatypes-v1-5:LifeCycleState" 
        substitutionGroup="cbeservice-v1-5:baseState_Service"/>

    <element name="state_ServiceState" 
        type="cbedatatypes-v1-5:State" 
        substitutionGroup="cbeservice-v1-5:baseState_Service"/>

    <!-- Tigerstripe : End of Entity definition for Service -->
    <!-- Tigerstripe : Enumeration definitions for ServiceState  -->
    <simpleType name="ServiceState">
        <annotation>
            <documentation>
This interface defines the ServiceState enumeration.
            </documentation>
        </annotation>

        <restriction base="string">
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ServiceState  -->
    <!-- Tigerstripe : Enumeration definitions for StartMode  -->
    <simpleType name="StartMode">
        <annotation>
            <documentation>
This interface defines the StartMode enumeration.
            </documentation>

        </annotation>
        <restriction base="int">
            <!-- name = UNKNOWN -->
            <enumeration value="0" />
            <!-- name = AUTO_BY_ENV -->
            <enumeration value="1" />
            <!-- name = AUTO_BY_DEVICE -->
            <enumeration value="2" />
            <!-- name = MANUAL_BY_PROVIDER -->

            <enumeration value="3" />
            <!-- name = MANUAL_BY_CUSTOMER -->
            <enumeration value="4" />
            <!-- name = ANY -->
            <enumeration value="5" />
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for StartMode  -->





</schema>
