<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bindings xmlns="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
      
    <!--

This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2022.08.25 at 06:31:07 PM IST 

  -->
      
    <bindings xmlns:tns="http://schemas.ericsson.com/cai3g1.2/" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="tns:Notify">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Notify"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Cai3gFaultType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Cai3GFaultType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:CreateResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.CreateResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AnyMOIdType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.AnyMOIdType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ResponseMOAttributesType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.ResponseMOAttributesType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AnySequenceType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.AnySequenceType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:SetResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SetResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:DeleteResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.DeleteResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Search">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Search"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchFiltersType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SearchFiltersType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:SearchResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SearchResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Login">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Login"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:LoginResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.LoginResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Logout">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Logout"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:LogoutResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.LogoutResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Subscribe">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Subscribe"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NotificationFiltersType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.NotificationFiltersType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:SubscribeResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SubscribeResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Unsubscribe">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.Unsubscribe"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:UnsubscribeResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.UnsubscribeResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NotificationHeaderType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.NotificationHeaderType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:NotifyResponse">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.NotifyResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:SessionIdFault">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SessionIdFault"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:HeaderFaultType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.HeaderFaultType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:SequenceIdFault">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SequenceIdFault"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:TransactionIdFault">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.TransactionIdFault"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:ContextFault">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.ContextFault"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SearchFilterType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.SearchFilterType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NotificationFilterType">
                  
            <class ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.NotificationFilterType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:NotificationOperationType">
                  
            <typesafeEnumClass ref="com.nokia.wing.wdh.sdmadapter.client.cai3g.soap.ire.session.NotificationOperationType"/>
                
        </bindings>
          
    </bindings>
    
</bindings>
