<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>
<xs:schema xmlns="http://schemas.ericsson.com/pg/hlr/13.5/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" jaxb:extensionBindingPrefixes="xjc" jaxb:version="2.0" targetNamespace="http://schemas.ericsson.com/pg/hlr/13.5/">
<xs:include schemaLocation="types/hlrla_types.xsd"/>
<xs:element name="csp" type="cspType"/>
<xs:element name="msisdn" type="msisdnType"/>
<xs:element name="PrimaryHLRId" type="primaryhlridType"/>
<xs:element name="SetCAMELExtendedInformation">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="sslo" type="ssloType"/>
<xs:element minOccurs="0" name="gcso" type="gcsoType"/>
<xs:choice minOccurs="0">
<xs:element name="mcso" type="mcsoType"/>
<xs:element name="mc2so" type="mc2soType"/>
<xs:element name="mc3so" type="mc3soType"/>
<xs:element name="mc4so" type="mc4soType"/>
</xs:choice>
<xs:element minOccurs="0" name="gc2so" type="gc2soType"/>
<xs:element minOccurs="0" name="gc3so" type="gc3soType"/>
<xs:element minOccurs="0" name="gc4so" type="gc4soType"/>
<xs:element minOccurs="0" name="tif" type="tifType"/>
<xs:element minOccurs="0" name="gprsso" type="gprssoType"/>
<xs:element minOccurs="0" name="osmsso" type="osmssoType"/>
<xs:element minOccurs="0" name="tsmsso" type="tsmssoType"/>
<xs:element minOccurs="0" name="mmso" type="mmsoType"/>
<xs:choice minOccurs="0">
<xs:element name="etinci" type="etinciType"/>
<xs:element name="etick" type="etickType"/>
</xs:choice>
<xs:choice minOccurs="0">
<xs:element name="eoinci" type="eoinciType"/>
<xs:element name="eoick" type="eoickType"/>
</xs:choice>
</xs:sequence>
<xs:attribute name="csp" type="cspType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="cspAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
<xs:attribute name="msisdn" type="msisdnType" use="optional">
<xs:annotation>
<xs:appinfo>
<jaxb:property name="msisdnAttr"/>
</xs:appinfo>
</xs:annotation>
</xs:attribute>
</xs:complexType>
</xs:element>
</xs:schema>
