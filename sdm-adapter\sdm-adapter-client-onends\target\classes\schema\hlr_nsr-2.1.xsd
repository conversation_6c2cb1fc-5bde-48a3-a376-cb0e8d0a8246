<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="urn:siemens:names:prov:gw:HLR_NSR:2:1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0" xmlns:nsr="urn:siemens:names:prov:gw:HLR_NSR:2:1" elementFormDefault="unqualified" attributeFormDefault="unqualified" version="2.1">
    <xsd:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
    <!-- ******************************************************************** -->
    <!--                    Root Elements                                     -->
    <!-- ******************************************************************** -->
    <xsd:element name="bearerCapabilitySet" type="nsr:BearerCapabilitySet"/>
    <xsd:element name="dCsi" type="nsr:DCsi"/>
    <xsd:element name="gprsCsi" type="nsr:GprsCsi"/>
    <xsd:element name="locationServicesProfile" type="nsr:LocationServicesProfile"/>
    <xsd:element name="mCsi" type="nsr:MCsi"/>
    <xsd:element name="mgCsi" type="nsr:MgCsi"/>
    <xsd:element name="mtSmsCsi" type="nsr:MtSmsCsi"/>
    <xsd:element name="multipleSubscriberProfiles" type="nsr:MultipleSubscriberProfiles"/>
    <xsd:element name="oCsi" type="nsr:OCsi"/>
    <xsd:element name="prohibitedForwardedToNumbers" type="nsr:ProhibitedForwardedToNumbers"/>
    <xsd:element name="qualityOfServiceProfile" type="nsr:QualityOfServiceProfile"/>
    <xsd:element name="roamingArea" type="nsr:RoamingArea"/>
    <xsd:element name="shortCodeTranslation" type="nsr:ShortCodeTranslation"/>
    <xsd:element name="smsCsi" type="nsr:SmsCsi"/>
    <xsd:element name="ssCsi" type="nsr:SsCsi"/>
    <xsd:element name="subscriberRelatedRoutingData" type="nsr:SubscriberRelatedRoutingData"/>
    <xsd:element name="tCsi" type="nsr:TCsi"/>
    <xsd:element name="uCsi" type="nsr:UCsi"/>
    <xsd:element name="ugCsi" type="nsr:UgCsi"/>
    <xsd:element name="voiceMailSystem" type="nsr:VoiceMailSystem"/>
    <xsd:element name="vtCsi" type="nsr:VtCsi"/>
    <!-- HLR 4.5: OICK,  TICK  and  EOICK, EOINCI, ETICK, ETINCI -->
    <xsd:element name="oick" type="nsr:OICK"/>
    <xsd:element name="tick" type="nsr:TICK"/>
    <xsd:element name="extOick" type="nsr:ExtOICK"/>
    <xsd:element name="extTick" type="nsr:ExtTICK"/>
    <xsd:element name="extOinci" type="nsr:ExtOINCI"/>
    <xsd:element name="extTinci" type="nsr:ExtTINCI"/>
    <!-- HLR 4.5.2: Advertisements for USSD features VLRID, OwnMsisdn, ActualTime, LCN, UgCsi-Ext -->
    <xsd:element name="ussdAdvt" type="nsr:UssdAdvt"/>
    <xsd:element name="ugCsiExt" type="nsr:UgCsiExt"/>
    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes begin  -->
    <xsd:element name="roamSubscriptionInfo" type="nsr:RoamSubscriptionInfo"/>
    <xsd:element name="roamPlan" type="nsr:RoamPlan"/>
    <xsd:element name="bsPlan" type="nsr:BSPlan"/>
    <xsd:element name="ssPlan" type="nsr:SSPlan"/>
    <xsd:element name="odbPlan" type="nsr:ODBPlan"/>
    <xsd:element name="csiPlan" type="nsr:CSIPlan"/>
    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes end   -->
    <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
    <xsd:element name="gmlc" type="nsr:GMLC"/>
    <!-- HLR 5.0 - LF: FC122_003888 - Proprietary Nokia IN Short Message Service -->
    <xsd:element name="inSms" type="nsr:INSMS"/>
    <!-- HLR 5.0 SP1: FC122_002113_SMSImprovements -->
    <xsd:element name="addressList" type="nsr:HLRAddressList"/>
    <xsd:element name="smsPriorityList" type="nsr:HLRSmsPriorityList"/>
    <!-- FC122_004031: CAMEL Core INAP Interworking -->
    <xsd:element name="sset" type="nsr:HLRSSetProfile"/>
    <xsd:element name="emoick" type="nsr:HLREMOICKProfile"/>
    <!-- HLR 5.0: FC122_003912: FTNO -->
    <xsd:element name="rule" type="nsr:HLRNbrRule"/>
    <xsd:element name="ruleSet" type="nsr:HLRNbrRuleSet"/>
    <xsd:element name="nbrTranslation" type="nsr:HLRNbrTranslation"/>
    <!-- NTHLR6SP1: RDSP enhancements -Start-->
    <xsd:element name="csiTreatment" type="nsr:HLRCSITreatment"/>
    <xsd:element name="categoryTreatment" type="nsr:HLRCategoryTreatment"/>
    <xsd:element name="psTreatment" type="nsr:HLRPSTreatment"/>
    <xsd:element name="servicePlan" type="nsr:HLRServicePlan"/>
    <xsd:element name="pdpContext" type="nsr:HLRPDPContext"/>
    <!-- NTHLR6SP1: RDSP enhancements -End-->
    <xsd:element name="hlrFraudProfile" type="nsr:HLRFraudProfile"/>
    <!-- NTHLR7SP1: FC122_005795 - Subscriber fraud observation - phase 2 (CFR) -->
    <xsd:element name="hlrFraudObservation" type="nsr:HLRFraudObservation" />
    <!-- FC123_106922_CF_Notification_Override - Start -->
    <xsd:element name="hlrFtnoProfile" type="nsr:HLRFTNOProfile" />
    <xsd:element name="hlrFtnoData" type="nsr:HLRFTNOData" />
    <!-- FC123_106922_CF_Notification_Override - End -->
    <!-- ******************************************************************** -->
    <!--                                 Shared Types                         -->
    <!-- ******************************************************************** -->
    <!-- Basic types -->
    <xsd:simpleType name="NumericString">
        <xsd:annotation>
            <xsd:documentation>Only digits allowed</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="\d+"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: *GsmScfAddr, gmlcAddr -->
    <xsd:simpleType name="TelephoneNumber">
        <xsd:annotation>
            <xsd:documentation>Telephone Number
            Note: E.164 defines the number within numbering plan for global title routing/address
                  Only Country Code (1-3 digits) is mandatory
            Length Range: 1..15
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:NumericString"/>
    </xsd:simpleType>
    <!-- Original attributes: *ReplHndInterr
           Original values:
               repByBAIC == 3        REPLACE BY BAIC
               noReplaceReq == 2  NO REPLACE HANDLING
               ORNA == 7               NO OPTIMAL ROUTING -->
    <xsd:simpleType name="ReplacementHandlingForInterrogation">
        <xsd:annotation>
            <xsd:documentation>
            Replacement handling for Interrogation.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="replaceByBAIC"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="noOptimalRouting"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: ?ServAvail
          Original values:
               noWhere = 0    NOT AVAILABLE
               HPLMN = 1       HOME PLMN
               VPLMN = 2      VISITED PLMN
               ALLPLMN = 3   ALL GSM-PLMNS -->
    <xsd:simpleType name="ServiceAvailability">
        <xsd:annotation>
            <xsd:documentation>
            CAMEL service availability defines the PLMN in which use of the service is allowed.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="noWhere"/>
            <xsd:enumeration value="homePlmn"/>
            <xsd:enumeration value="visitedPlmn"/>
            <xsd:enumeration value="allPlmn"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: *SuppCapPh
          Original values: 1, 2, 4, 8! -->
    <xsd:simpleType name="SupportedCamelPhase">
        <xsd:annotation>
            <xsd:documentation>
               This attribute specifies the phase for which this CAMEL service is capable.
               Values:
                 PHASE1 = 1
                 PHASE2 = 2
                 PHASE3 = 3
                 PHASE4 = 4
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
            <xsd:enumeration value="1"/>
            <xsd:enumeration value="2"/>
            <xsd:enumeration value="3"/>
            <xsd:enumeration value="4"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: dSuppCapPh
           Original values: 4, 8 -->
    <xsd:simpleType name="SupportedCamelPhase3and4">
        <xsd:annotation>
            <xsd:documentation>
               This attribute specifies the phase for which this CAMEL service is capable.
               Restricted to version 3 a 4.
               Values:
                 PHASE3 = 3
                 PHASE4 = 4
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:SupportedCamelPhase">
            <xsd:enumeration value="3"/>
            <xsd:enumeration value="4"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: gprsSuppCapPh
           Original value: 4 -->
    <xsd:simpleType name="SupportedCamelPhase3">
        <xsd:annotation>
            <xsd:documentation>
               This attribute specifies the phase for which this CAMEL service is capable.
               Restricted to version 3.
               Values:
                 PHASE3 = 3
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:SupportedCamelPhase">
            <xsd:enumeration value="3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attributes: mtSmsSuppCapPh
        Original value: 8 -->
    <xsd:simpleType name="SupportedCamelPhase4">
        <xsd:annotation>
            <xsd:documentation>
                This attribute specifies the phase for which this CAMEL service is capable.
                Restricted to version 4.
                Values:
                PHASE4 = 4
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:SupportedCamelPhase">
            <xsd:enumeration value="4"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: *ServKey -->
    <xsd:simpleType name="ServiceKey">
        <xsd:annotation>
            <xsd:documentation>
            Service key as part of CSI, which is used to identify the service in the service control function.
            This attribute must be sent to IN node (for MSC).
            Value range:0..2147483647
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attributes: *SendingOpt
          Original values:
            ntf = 1   Sending notification of subscriber data change to the according gsmSCF.
            opt1 = 16, opt2 = 32  ... opt10 = 8192  are reserved -->
    <xsd:simpleType name="SendingOptionIsNotification">
        <xsd:annotation>
            <xsd:documentation>
             Sending options determine which part of the IN-CAMEL related subscriber data is to be sent to the VLR/MSC
             This type allows only notification of subscriber data change to the GSM Service Control Function.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notificationOfSubscriberDataChange"/>
            <xsd:enumeration value="reservedOption1"/>
            <xsd:enumeration value="reservedOption2"/>
            <xsd:enumeration value="reservedOption3"/>
            <xsd:enumeration value="reservedOption4"/>
            <xsd:enumeration value="reservedOption5"/>
            <xsd:enumeration value="reservedOption6"/>
            <xsd:enumeration value="reservedOption7"/>
            <xsd:enumeration value="reservedOption8"/>
            <xsd:enumeration value="reservedOption9"/>
            <xsd:enumeration value="reservedOption10"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: *Shortcode -->
    <xsd:simpleType name="ServiceShortCode">
        <xsd:annotation>
            <xsd:documentation>Service Short Code. Value Range: 0..999</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attribute: srrServInd, sctServInd -->
    <xsd:simpleType name="ServiceIndicator">
        <xsd:annotation>
            <xsd:documentation>
        Service Indicator specifies the type of service.
        Value Range: 0..9
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- ******************************************************************** -->
    <!--                                Trigger Detection Point               -->
    <!-- ******************************************************************** -->
    <!-- TDP1       TRIGGER DETECTION POINT 1
               GPRS-CSI: - TDP1: DP ATTACH

              TDP2      TRIGGER DETECTION POINT 2
              O-CSI: - TDP2: DP COLLECTED INFO
              GPRS-CSI: - TDP2: DP ATTACH CHANGE POSITION
              MT-SMS-CSI: - TDP2: DP DELIVERY REQUEST
              SMS-CSI: - TDP2: DP COLLECTED INFO

              TDP4      TRIGGER DETECTION POINT 4
              O-CSI   : - TDP4: DP ROUTE SELECTED FAILURE

              TDP11     TRIGGER DETECTION POINT 11
              GPRS-CSI: - TDP11:PDP CONTEXT ESTABLISHMENT

              TDP12     TRIGGER DETECTION POINT 12
              T-CSI   : - TDP12:DP TERMINATING ATTEMPT AUTHORIZ.
              VT-CSI  : - TDP12:DP TERMINATING ATTEMPT AUTHORIZ.
              GPRS-CSI: - TDP12:PDP CONTEXT ESTABLISHMENT ACKN.

              TDP13     TRIGGER DETECTION POINT 13
              T-CSI   : - TDP13:DP T BUSY
             VT-CSI  : - TDP13:DP T BUSY

             TDP14      TRIGGER DETECTION POINT 14
             T-CSI   : - TDP14:DP T NO ANSWER
             VT-CSI  : - TDP14:DP T NO ANSWER
             GPRS-CSI: - TDP14:PDP CONTEXT CHANGE OF POSITION -->
    <!-- Original attribute: TDP3, TDPDATA-GPRS-CSI, TDPDATA-SMS-CSI, base for TDPDATA-* -->
    <xsd:complexType name="TriggerDetectionPoint">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="gsmServiceControlFunction" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="serviceKey" type="nsr:ServiceKey" minOccurs="0"/>
                    <xsd:element name="releaseCall" type="xsd:boolean" default="false" minOccurs="0"/>
                    <!-- Address of the Camel Service Environment
                     to which the SGSN should contact in case of subscriber activities.  -->
                    <!-- (Original attribute: tdp(3)GsmScfAd)  -->
                    <!-- Service key as part of CSI, which is used to identify the service in the service control function.  -->
                    <!-- (Original attribute: tdp(3)ServKey)  -->
                    <!--  This attribute specifies the type of default call handling as part of CSI.
                             It is evaluated in case of establishing an IN dialog to the service control function fails.
                             If set to release, the call is released otherwise the call is continued -->
                    <!-- (Original attribute: tdp(3)ContCall: CONTINUE = 0 / RELEASE = 1)  -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: TDPDATA-O-CSI -->
    <xsd:complexType name="TriggerDetectionPointData">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:TriggerDetectionPoint">
                <xsd:sequence>
                    <xsd:element name="basicServiceCriteria" type="nsr:BasicServiceCriteria" minOccurs="0" maxOccurs="5"/>
                    <xsd:element name="releaseCause" type="nsr:ReleaseCause" minOccurs="0" maxOccurs="5"/>
                    <xsd:element name="destinationNumber" type="nsr:DestinationNumber" minOccurs="0" maxOccurs="10"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: TDPDATA-T-CSI, TDPDATA-VT-CSI -->
    <xsd:complexType name="TriggerDetectionPointDataTerminating">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:TriggerDetectionPoint">
                <xsd:sequence>
                    <xsd:element name="basicServiceCriteria" type="nsr:BasicServiceCriteria" minOccurs="0" maxOccurs="5"/>
                    <xsd:element name="releaseCause" type="nsr:ReleaseCause" minOccurs="0" maxOccurs="5"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="BasicServiceCriteria">
        <xsd:annotation>
            <xsd:documentation>
      Basic Service Criteria enumerates Basic Services and Basic Service Groups
      Values:
           TS10-telephony      is telephony service Group
           TS20-shortMessage   is SMS service group
           TS60-fax            is fax service group
           BS20-dataAsync      is asynchronous data service group
           BS30-dataSync       is synchronous data service group
           BS40-padAccess      is PAD access service group

           TS11 is a particular subclass of a service class with dedicated parameter
           TS21 is a Short Message service mobile terminating
           TS22 is a Short Message service mobile originating
           TS61 is an alternate speech and facsimile group 3 service
           TS62 is an automatic facsimile group 3 service
           VGCS Voice Group Call Service
           VBS Voice Broadcast Service
           BS20 GENR is a general circuit switched data asynchronous service
           BS21 datal circuit duplex asynchronous service (300 b/s)
           BS22 datal circuit duplex asynchronous service (1200 b/s)
           BS23 datal circuit duplex asynchronous service (1200 b/s and 75 b/s)
           BS24 datal circuit duplex asynchronous service (2400 b/s)
           BS25 datal circuit duplex asynchronous service (4800 b/s)
           BS26 datal circuit duplex asynchronous service (9600 b/s)
           BS30 GENR is a general circuit switched data synchronous service
           BS31 datal circuit duplex synchronous service (1200 b/s)
           BS32 datal circuit duplex ssynchronous service (2400 b/s)
           BS33 datal circuit duplex synchronous service (4800 b/s)
           BS34 datal circuit duplex synchronous service (9600 b/s)
           BS41 PAD access circuit asynchronous service (300 b/s)
       BS42 PAD access circuit asynchronous service (1200 b/s)
       BS44 PAD access circuit asynchronous service (4800 b/s)
       BS45 PAD access circuit asynchronous service (9600 b/s)
       BS46 PAD access circuit asynchronous service (9600 b/s)
           BS40GENR general PAD access circuit asynchronous service
           BS61A Alternate Speech / Unrestricted Data
           BS81 Speech followed by Data
           GPRS This is a Nokia special service for GPRS
           TS21GPRS TSMS MT over GPRS
           TS22GPRS SMS MO over GPRS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TS10-telephony"/>
            <xsd:enumeration value="TS20-shortMessage"/>
            <xsd:enumeration value="TS60-fax"/>
            <xsd:enumeration value="BS20-dataAsync"/>
            <xsd:enumeration value="BS30-dataSync"/>
            <xsd:enumeration value="BS40-padAccess"/>
            <xsd:enumeration value="TS11"/>
            <xsd:enumeration value="TS12"/>
            <xsd:enumeration value="TS21"/>
            <xsd:enumeration value="TS22"/>
            <xsd:enumeration value="TS61"/>
            <xsd:enumeration value="TS62"/>
            <xsd:enumeration value="VGCS"/>
            <xsd:enumeration value="VBS"/>
            <xsd:enumeration value="BS20GENR"/>
            <xsd:enumeration value="BS21"/>
            <xsd:enumeration value="BS22"/>
            <xsd:enumeration value="BS23"/>
            <xsd:enumeration value="BS24"/>
            <xsd:enumeration value="BS25"/>
            <xsd:enumeration value="BS26"/>
            <xsd:enumeration value="BS30GENR"/>
            <xsd:enumeration value="BS31"/>
            <xsd:enumeration value="BS32"/>
            <xsd:enumeration value="BS33"/>
            <xsd:enumeration value="BS34"/>
            <xsd:enumeration value="BS40GENR"/>
            <xsd:enumeration value="BS41"/>
            <xsd:enumeration value="BS42"/>
            <xsd:enumeration value="BS44"/>
            <xsd:enumeration value="BS45"/>
            <xsd:enumeration value="BS46"/>
            <xsd:enumeration value="BS61A"/>
            <xsd:enumeration value="BS81A"/>
            <xsd:enumeration value="AllTeleservices"/>
            <xsd:enumeration value="AllBearerServices"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute:    tdp(3)DestNum
              This attribute allows a single value (string) as combination of type and number seperated by _. a-b
              !! in example b-a: tdp3DestNum=129_0049171252
                   a:  DESTINATION NUMBER DIGITS=1...15 digit hexadecimal number
                        where B is interchangeable with * and C is interchangeable with #
                   b:  NUMBER TYPE
                        INTNO = 145        INTERNATIONAL NUMBER
                        UNKNOWN = 129      UNKNOWN
               The destination number in intno format consists of digits in the range 0..9.
               The destination number in unknown format consists of.digits in the range 0..9 and A..E. -->
    <!-- Original attribute: tdp(3)DestNum -->
    <xsd:complexType name="DestinationNumber">
        <xsd:annotation>
            <xsd:documentation>
            Trigger Detection Point Destination number in international or unknown format
            Destination number as part of the ANALYSED INFO CRITERIUM for the CAMEL service.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="format" type="nsr:TelephoneNumberFormat" minOccurs="0"/>
                    <xsd:element name="value" type="nsr:DestinationNumberValue" minOccurs="0"/>
                    <!-- For international format only 0-9 characters are allowed! -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: tdp(3)DestNum a. part in unknown format -->
    <xsd:simpleType name="DestinationNumberValue">
        <xsd:annotation>
            <xsd:documentation>Destination Number in Unknown Format
         Value: The destination number in unknown format consists of.digits in the range 0..9 and A..E.
             where B is interchangeable with * and C is interchangeable with #
             (Note: The destination number in international format consists of digits in the range 0..9.)
             Length Range: 1..15
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string"/>
    </xsd:simpleType>
    <!-- Original attribute: tdp(3)DestNum b. part
           Original values:   international == 145 / unknown == 129 -->
    <xsd:simpleType name="TelephoneNumberFormat">
        <xsd:annotation>
            <xsd:documentation>Destination Number  Format
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="unknown"/>
            <xsd:enumeration value="international"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: tdpRelCause
           Original values:
             NOROTN == 2   NORODES == 3   SPECTON == 4   MISDTRX == 5   CHLUNAC == 6
             CALLAWR == 7   PREEM == 8   PREEMCR == 9   CSVL10..15 == 10..15
             NRMCLCLR == 16   USRBUS == 17   NOUSRRSP == 18   NOUSRAW == 19   SUBABS == 20
             CALLREJ == 21   NOCHNG == 22   RDCNEWD == 23   CSVL24 == 24   EXCHRER == 25
             NSELUSCL == 26   DESTOUT == 27   INVNOFO == 28   FACREJ == 29   RESENQ == 30
             NORUNSP == 31   CSVL32 == 32 NOCRCH == 34   CSVL35..37 == 35..37
             NETOUT == 38   PERFRA == 39   PERFRACO == 40   TEMPFAIL == 41   SWEQUP == 42
             ACSDISC == 43   RNOCRCH == 44   CSVL45 == 45   PRECBL == 46   RESUNSP == 47
             CSVL48 == 48   QOSPNAV == 49   REQFACS == 50   CSVL51,52 == 51,52   baocCUG == 53
             CSVL54 == 54   BAICCOG == 55   CSVL56 == 56   BCNOATH == 57   BCNOAV == 58
             CSVL59..61 == 59..61   INCONS == 62   SERVOP == 63   CSVL64 == 64   BCNOIMP == 65
             CHNOIMP == 66   CSVL67 == 67   CSVL68 == 68   REQFACIM == 69   BCDIG == 70
             CSVL71..78 == 71..78   SERVOVIM == 79   CSVL80 == 80   CREFIN == 81   CHIDNO == 82
             SUSPCL == 83   CLIDUSE == 84   NOSUSPCL == 85   CLIDCL == 86   USRCUG == 87
             DESINCP == 88   CSVL89 == 89   NOEXCUG == 90   TNETIN == 91   CSVL92..94 == 92..94
             MSGIN == 95   MINFMIS == 96   MSGTYP == 97   MSGCOMPM == 98   INFONOX == 99
             INFELID == 100   MSGCOMP == 101   RECOVEX == 102   PARNOEX == 103
            CSVL104..109 == 104..109   MSGDISC == 110   PROTERR == 111   CSVL112 == 112
            CSVL113..126 == 113..126   INTUNSP == 127 -->
    <xsd:simpleType name="ReleaseCause">
        <xsd:annotation>
            <xsd:documentation>
             Release Cause Code
             indicates the cause specific to the armed Basic call state mode DP event.
             The cause may be used by the SCF to decide about the further handling of the call.
             This attribute is applicable to DP Route Select Failure and DP T Busy.
              Values:
               unAllocatedNumber    (UNLLNO)
                     The called party cannot be reached because, although the called party number is in a valid format,
                     it is not currently allocated (assigned).
               noRouteToSpecificTransitNetwork   (NOROSTN)
                     Received a request to route the call through a particular transit network
                     which it does not recognize.
                     Reason: Transit network does not exist or does not serve the equipment which is sending this cause.
                     Notes: This cause is supported on a network-dependent basis.
               noRouteToDestination   (NORODES)
                    The called party cannot be reached because the routing network does not serve the destination.
                    Notes: This cause is supported on a network-dependent basis.
               sendSpecialInfoTone   (SPECTON)
                    The called party cannot be reached for reasons that are of a long term nature
                    and the special information tone should be returned to the calling party.
               misDialedTrunkPrefix   (MISDTRX)
                   The erroneous inclusion of a trunk prefix in the called party number.
               channelUnAcceptable   (CHLUNAC)
                    The channel most recently identified is not acceptable to the sending entity for use in this call.
               callAwardedBeingDelivered   (CALLAWR)
                    The user has been awarded the incoming call, and that the incoming call is being connected
                    to a channel already established to that user for similar calls.
               preemption   (PREEM)
                    The call is being pre-empted.
               preemptionReservedForReuse   (PREEMCR)
                    The call is being pre-empted and the circuit is reserved for reuse by the pre-empting exchange.
               normalCallClearing   (NRMCLCLR)
                    The call is being cleared because one of the users involved in the call
                    has requested that the call be cleared.
                    Notes: Under normal situations, the source of this cause is not the network.
               userBusy   (USRBUS)
                    The called party is unable to accept another call because the user busy condition has been encountered.
                     This cause value may be generated by the called user or by the network.
               noUserResponding   (NOUSRRSP)
                     The called party does not respond to a call establishment message
                     with either an alerting or connect indication within the prescribed period of time allocated.
               noAnswerFromUser   (NOUSRAW)
                     The called party has been alerted but does not respond with a connect indication
                     within a prescribed period of time.
                     Notes: This cause is not necessarily generated by Q.931 procedures but also by internal network timers.
               subscriberAbsent   (SUBABS)
                    The mobile station has logged off, radio contact is not obtained with a mobile station or
                    a personal telecommunication user is temporarily not addressable at any user-network interface.
               callRejected   (CALLREJ)
                     The equipment sending this cause does not wish to accept this call,
                     although it could have accepted the call
                     because the equipment sending this cause is neither busy nor incompatible.
                     Notes: This cause may also be generated by the network,
                     indicating that the call was cleared due to a supplementary service constraint.
                     The diagnostic field may contain additional information about the supplementary service
                     and reason for rejection.
               numberChanged   (NOCHNG)
                     The called party number indicated by the calling party is no longer assigned.
                     The new called party number may optionally be included in the diagnostic field.
                     Notes: If a network does not support this cause value,
                     cause UNLLNO, Unallocated (unassigned) number, shall be used.
               redirectionToNewDestination   (RDCNEWD)
                    Used by a general ISUP protocol mechanism that can be invoked by an exchange
                    that decides that the call should be set-up to a different called number.
                    Such an exchange can invoke a redirection mechanism, by use of this cause value,
                    to request a preceding exchange involved in the call to route the call to the new number.
                exchangeRoutingError  (EXCHRER)
                    The destination indicated by the user cannot be reached, because an intermediate exchange
                    has released the call due to reaching a limit in executing the hop counter procedure.
                    Notes: Generated by an intermediate node, which when decrementing the hop counter value,
                    gives the result 0.
                nonSelectedUserClearing   (NSELUSCL)
                    The user has not been awarded the incoming call.
                destinationOutOfOrder   (DESTOUT)
                    The destination indicated by the user cannot be reached
                    because the interface to the destination is not functioning correctly.
                    Notes: A signalling message was unable to be delivered to the remote party
                    e.g. a physical layer or data link layer failure at the remote party, or user equipment off-line.
                invalidNumberFormat   (INVNOFO)
                    The called party cannot be reached
                    because the called party number is not in a valid format or is not complete.
                    Notes: This condition may be determined:
                               immediately after reception of an end of pulsing (ST) signal
                               on time-out after the last received digit.
                facilityRejected (FACREJ)
                    Supplementary service requested by the user cannot be provided by the network.
                noResponseToStatusEnquiry   (RESENQ)
                    This cause is included in the STATUS message when the reason for generating the STATUS message
                    was the prior receipt of a STATUS ENQUIRY  message.
                normalUnspecified   (NORUNSP)
                     This cause is used to report a normal event only when no other cause in the normal class applies.
                noCurcuitChannelAvailable   (NOCRCH)
                     There is no appropriate circuit/channel presently available to handle the call.
                networkOutOfOrder   (NETOUT)
                     The network is not functioning correctly
                     and this condition is likely to last a relatively long period of time.
                connectionOutOfService   (PERFRA)
                     Permanently established frame mode connection is out of service.
                connectionOperational   (PERFRACO)
                     Permanently established frame mode connection is operational
                     and capable of carrying user information.
                temporaryFailure   (TEMPFAIL)
                      The network is not functioning correctly and this condition is not likely to last a long period of time.
                switchingEquipmentCongestion   (SWEQUP)
                      The switching equipment generating this cause is experiencing a period of high traffic.
                accessInfoDiscarded   (ACSDISC)
                      The network could not deliver access information to the remote user as requested,
                      i.e. user-to-user information, low layer compatibility, high layer compatibility, or subaddress,
                      as indicated in the diagnostic.
                      Notes: The particular type of access information discarded is optionally included in the diagnostic.
                requestedCurcuitChannelNotAvailable   (RNOCRCH)
                      The circuit or channel indicated by the requesting entity cannot be provided by the other side of the interface.
                precedenceCallBlocked   (PRECBL)
                      There are no preemptable circuits or the called user is busy with a call of equal or higher preemptable level.
                resourceUnAvailable   (RESUNSP)
                       This cause is used to report a resource unavailable event
                       only when no other cause in the resource unavailable class applies.
                qualityOfServiceNotAvailable   (QOSPNA)
                      The requested Quality of Service, as defined in Recommendation X.213, cannot be provided.
                requestedFacilityNotAvailable   (REQFACS)
                      The user is not authorized to use a supplementary service (although it is implemented by the equipment)
                barringOutgoingCallWithinCug    (baocCUG)
                      Outgoing calls are not allowed for this member of the CUG.
                barringIncomingCallWithinCug    (BAICCUG)
                      Incoming calls are not allowed for this member of the CUG.
                bearerCapabilityNotAuthorized   (BCNOATH)
                     The user is not  authorized to use the requested bearer capability.
                bearerCapabilityNotAvailable   (BCNOAV)
                     The requested bearer capability is not available at this time.
                inconsistency   (INCONS)
                     There is an inconsistency in the designated outgoing access information and subscriber class.
                serviceOrOptionNotAvailable   (SERVOP)
                     This cause is used to report a service or option not available event
                     only when no other cause in the service or option not available class applies.
                bearerCapabilityNotImplemented   (BCNOIMP)
                     The equipment sending this cause does not support the bearer capability requested.
                channelTypeNotImplemented   (CHNOIMP)
                     The equipment sending this cause does not support the channel type requested.
                requestedFaclityNotImplemented   (REQFACIM)
                     The equipment sending this cause does not support the requested supplementary service.
                onlyRestrictedInfoAvailable   (BCDIG)
                     The calling party has requested an unrestricted bearer service
                     but the equipment sending this cause only supports the restricted version of the requested bearer capability.
                serviceOrOptionNotImplemted   (SERVOVIM)
                    This cause is used to report a service or option not implemented event
                    only when no other cause in the service or option not implemented class applies.
                invalidCallReferenceValue   (CREFIN)
                    Received a message with a call reference
                    which is not currently in use on the user-network interface.
                channelIdDoesNotExist   (CHIDNO)
                    Received a request to use a channel not activated on the interface for a call.
                suspendedCallExists    (SUSPCL)
                    Call resume has been attempted with a call identity
                    which differs from that in use for any presently suspended call(s).
                callIdentityInUse   (CLIDUSE)
                    Received a call suspended request containing a call identity (including the null one)
                    which is already in use for a suspended call within the interfaces over which the call might be resumed.
                noCallSuspended   (NOSUSPCL)
                    Received a call resume request containing a call identity information element
                    which presently does not indicate any suspended call within the interfaces over which calls may be resumed.
                requestedCallIdCleared   (CLIDCL)
                    Received a call resume request containing a call identity information element
                    indicating a suspended call that has in the meantime been cleared while suspended
                    (either by network timeout or by the remote user).
                userIsNotMemberOfCug   (USRCUG)
                    The called user for the incoming CUG call is not a member of the specified CUG or
                    the calling user is an ordinary subscriber calling a CUG subscriber.
                incompatibleDestination   (DESINCP)
                    Received a request to establish a call
                    which has low layer compatibility, high layer compatibility,
                    or other compatibility attributes (e.g. data rate) which cannot be accommodated.
                nonExistentClosedUserGroup   (NOEXCUG)
                    The specified CUG does not exist.
                invalidTransitNetworkSelection   (TNETIN)
                     Transit network identification was received which is of an incorrect format as defined in Annex C/Q.931.
                invalidMessage   (MSGIN)
                    This cause is used to report an invalid message event
                    only when no other cause in the invalid message class applies.
                mandatoryInfoElementMissing   (MINFMIS)
                    The equipment sending this cause has received a message,
                    which is missing mandatory information element.
                messageTypeNonExistent   (MSGTYP)
                    Received a message with a message type is not recognized
                    either because this is a message not defined or defined but not imple-mented
                    by the equipment sending this cause.
                messageNotCompatible    (MSGCOMPM)
                   Received a message such that the procedures do not indicate
                   that this is a permissible message to receive while in the call state,
                   or a STATUS message was received indicating an incompatible call state.
                infoElementParameterNonExistent   (INFONOX)
                    Received a message which includes information element(s)/attribute(s) not recognized
                    because the information element identifier(s)/attribute name(s) are not defined
                    or not implemented by the equipment sending the cause.
                    This cause indicates that the information element(s)/attribute(s) were discarded.
                    However, the information element is not required to be present in the message
                    in order for the equipment sending the cause to process the message.
                invalidInfoElementContent   (INFELID)
                    Received an information element which is implemented
                    however, one or more fields in the information element are coded in such a way
                    which has not been implemented by the equipment sending this cause.
                messageNotCompatibleWithCallState   (MSGCOMP)
                    Received which is incompatible with the call state.
                recoveryOnTimeExpiry   (RECOVEX)
                    A procedure has been initiated by the expiry of a timer in association with error handling procedures.
                parameterNonExistentPassedOn   (PARNOEX)
                    Received a message which includes attributes not recognized
                    because the attributes are not defined or are defined but not implemented.
                    The cause indicates that the parame-ter(s) were ignored.
                    In addition, if the equipment sending this cause is an intermediate point,
                    then this cause indicates that the attribute(s) were passed on unchanged.
                messageDiscarded   (MSGDISC)
                    Discarded a received message which includes a attribute that is not recognized.
                protocolErrorUnSpecified   (PROTERR)
                    This cause is used to report a protocol error event
                    only when no other cause in the protocol error class applies.
                interworkingUnSpecified   (INTUNSP)
                    There has been interworking with a network which does not provide causes for actions it takes.
                    Thus, the precise cause for a message which is being sent cannot be ascertained.
        reservedCause[No]   (CSVL[No])
            Reserved cause
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="unAllocatedNumber"/>
            <xsd:enumeration value="noRouteToSpecificTransitNetwork"/>
            <xsd:enumeration value="noRouteToDestination"/>
            <xsd:enumeration value="sendSpecialInfoTone"/>
            <xsd:enumeration value="misDialedTrunkPrefix"/>
            <xsd:enumeration value="channelUnAcceptable"/>
            <xsd:enumeration value="callAwardedBeingDelivered"/>
            <xsd:enumeration value="preemption"/>
            <xsd:enumeration value="preemptionReservedForReuse"/>
            <xsd:enumeration value="normalCallClearing"/>
            <xsd:enumeration value="userBusy"/>
            <xsd:enumeration value="noUserResponding"/>
            <xsd:enumeration value="noAnswerFromUser"/>
            <xsd:enumeration value="subscriberAbsent"/>
            <xsd:enumeration value="callRejected"/>
            <xsd:enumeration value="numberChanged"/>
            <xsd:enumeration value="redirectionToNewDestination"/>
            <xsd:enumeration value="exchangeRoutingError"/>
            <xsd:enumeration value="nonSelectedUserClearing"/>
            <xsd:enumeration value="destinationOutOfOrder"/>
            <xsd:enumeration value="invalidNumberFormat"/>
            <xsd:enumeration value="facilityRejected"/>
            <xsd:enumeration value="noResponseToStatusEnquiry"/>
            <xsd:enumeration value="normalUnspecified"/>
            <xsd:enumeration value="noCurcuitChannelAvailable"/>
            <xsd:enumeration value="networkOutOfOrder"/>
            <xsd:enumeration value="connectionOutOfService"/>
            <xsd:enumeration value="connectionOperational"/>
            <xsd:enumeration value="temporaryFailure"/>
            <xsd:enumeration value="switchingEquipmentCongestion"/>
            <xsd:enumeration value="accessInfoDiscarded"/>
            <xsd:enumeration value="requestedCurcuitChannelNotAvailable"/>
            <xsd:enumeration value="precedenceCallBlocked"/>
            <xsd:enumeration value="resourceUnAvailable"/>
            <xsd:enumeration value="qualityOfServiceNotAvailable"/>
            <xsd:enumeration value="requestedFacilityNotAvailable"/>
            <xsd:enumeration value="barringOutgoingCallWithinCug"/>
            <xsd:enumeration value="barringIncomingCallWithinCug"/>
            <xsd:enumeration value="bearerCapabilityNotAuthorized"/>
            <xsd:enumeration value="bearerCapabilityNotAvailable"/>
            <xsd:enumeration value="inconsistency"/>
            <xsd:enumeration value="serviceOrOptionNotAvailable"/>
            <xsd:enumeration value="bearerCapabilityNotImplemented"/>
            <xsd:enumeration value="channelTypeNotImplemented"/>
            <xsd:enumeration value="requestedFaclityNotImplemented"/>
            <xsd:enumeration value="onlyRestrictedInfoAvailable"/>
            <xsd:enumeration value="serviceOrOptionNotImplemted"/>
            <xsd:enumeration value="invalidCallReferenceValue"/>
            <xsd:enumeration value="channelIdDoesNotExist"/>
            <xsd:enumeration value="suspendedCallExists"/>
            <xsd:enumeration value="callIdentityInUse"/>
            <xsd:enumeration value="noCallSuspended"/>
            <xsd:enumeration value="requestedCallIdCleared"/>
            <xsd:enumeration value="userIsNotMemberOfCug"/>
            <xsd:enumeration value="incompatibleDestination"/>
            <xsd:enumeration value="nonExistentClosedUserGroup"/>
            <xsd:enumeration value="invalidTransitNetworkSelection"/>
            <xsd:enumeration value="invalidMessage"/>
            <xsd:enumeration value="mandatoryInfoElementMissing"/>
            <xsd:enumeration value="messageTypeNonExistent"/>
            <xsd:enumeration value="messageNotCompatible"/>
            <xsd:enumeration value="infoElementParameterNonExistent"/>
            <xsd:enumeration value="invalidInfoElementContent"/>
            <xsd:enumeration value="messageNotCompatibleWithCallState"/>
            <xsd:enumeration value="recoveryOnTimeExpiry"/>
            <xsd:enumeration value="parameterNonExistentPassedOn"/>
            <xsd:enumeration value="messageDiscarded"/>
            <xsd:enumeration value="protocolErrorUnSpecified"/>
            <xsd:enumeration value="interworkingUnSpecified"/>
            <xsd:enumeration value="reservedCause10"/>
            <xsd:enumeration value="reservedCause11"/>
            <xsd:enumeration value="reservedCause12"/>
            <xsd:enumeration value="reservedCause13"/>
            <xsd:enumeration value="reservedCause14"/>
            <xsd:enumeration value="reservedCause15"/>
            <xsd:enumeration value="reservedCause24"/>
            <xsd:enumeration value="reservedCause32"/>
            <xsd:enumeration value="reservedCause33"/>
            <xsd:enumeration value="reservedCause35"/>
            <xsd:enumeration value="reservedCause36"/>
            <xsd:enumeration value="reservedCause37"/>
            <xsd:enumeration value="reservedCause45"/>
            <xsd:enumeration value="reservedCause48"/>
            <xsd:enumeration value="reservedCause51"/>
            <xsd:enumeration value="reservedCause52"/>
            <xsd:enumeration value="reservedCause54"/>
            <xsd:enumeration value="reservedCause56"/>
            <xsd:enumeration value="reservedCause59"/>
            <xsd:enumeration value="reservedCause60"/>
            <xsd:enumeration value="reservedCause61"/>
            <xsd:enumeration value="reservedCause64"/>
            <xsd:enumeration value="reservedCause67"/>
            <xsd:enumeration value="reservedCause68"/>
            <xsd:enumeration value="reservedCause71"/>
            <xsd:enumeration value="reservedCause72"/>
            <xsd:enumeration value="reservedCause73"/>
            <xsd:enumeration value="reservedCause74"/>
            <xsd:enumeration value="reservedCause75"/>
            <xsd:enumeration value="reservedCause76"/>
            <xsd:enumeration value="reservedCause77"/>
            <xsd:enumeration value="reservedCause78"/>
            <xsd:enumeration value="reservedCause80"/>
            <xsd:enumeration value="reservedCause89"/>
            <xsd:enumeration value="reservedCause92"/>
            <xsd:enumeration value="reservedCause93"/>
            <xsd:enumeration value="reservedCause94"/>
            <xsd:enumeration value="reservedCause104"/>
            <xsd:enumeration value="reservedCause105"/>
            <xsd:enumeration value="reservedCause106"/>
            <xsd:enumeration value="reservedCause107"/>
            <xsd:enumeration value="reservedCause108"/>
            <xsd:enumeration value="reservedCause109"/>
            <xsd:enumeration value="reservedCause112"/>
            <xsd:enumeration value="reservedCause113"/>
            <xsd:enumeration value="reservedCause114"/>
            <xsd:enumeration value="reservedCause115"/>
            <xsd:enumeration value="reservedCause116"/>
            <xsd:enumeration value="reservedCause117"/>
            <xsd:enumeration value="reservedCause118"/>
            <xsd:enumeration value="reservedCause119"/>
            <xsd:enumeration value="reservedCause120"/>
            <xsd:enumeration value="reservedCause121"/>
            <xsd:enumeration value="reservedCause122"/>
            <xsd:enumeration value="reservedCause123"/>
            <xsd:enumeration value="reservedCause124"/>
            <xsd:enumeration value="reservedCause125"/>
            <xsd:enumeration value="reservedCause126"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- ******************************************************************** -->
    <!--     FCOs (Profiles, Services, ...) and their children                -->
    <!-- ******************************************************************** -->
    <!-- *************************************************************** -->
    <!--                               Bearer Capability Set             -->
    <!-- *************************************************************** -->
    <!-- Original attribute: BCASETINNSS  -->
    <xsd:complexType name="BearerCapabilitySet">
        <xsd:annotation>
            <xsd:documentation>Bearer Capability information elements</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="radioChannelRequirements" type="nsr:RadioChannelRequirements" minOccurs="0"/>
                    <xsd:element name="transferMode" type="nsr:TransferMode" minOccurs="0"/>
                    <xsd:element name="informationTransferCapability" type="nsr:InformationTransferCapability" minOccurs="0"/>
                    <xsd:element name="structure" type="nsr:Structure" minOccurs="0"/>
                    <xsd:element name="rateAdaption" type="nsr:RateAdaption" minOccurs="0"/>
                    <xsd:element name="signalingAccessProtocol" type="nsr:SignalingAccessProtocol" minOccurs="0"/>
                    <xsd:element name="userInfoLayer2Protocol" type="nsr:UserInfoLayer2Protocol" minOccurs="0"/>
                    <xsd:element name="synchronousMode" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="numberOfDataBits" type="nsr:NumberOfDataBits" minOccurs="0"/>
                    <xsd:element name="numberOfStopBits" type="nsr:NumberOfStopBits" minOccurs="0"/>
                    <xsd:element name="userRate" type="nsr:UserRate" minOccurs="0"/>
                    <xsd:element name="intermediateRate" type="nsr:IntermediateRate" minOccurs="0"/>
                    <xsd:element name="acceptsNetworkIndependentClock" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="requiresNetworkIndependentClock" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="parityInfo" type="nsr:ParityInfo" minOccurs="0"/>
                    <xsd:element name="connectionElement" type="nsr:ConnectionElement" minOccurs="0"/>
                    <xsd:element name="modemType" type="nsr:ModemType" minOccurs="0"/>
                    <xsd:element name="fixNetworkUserRate" type="nsr:FixNetworkUserRate" minOccurs="0"/>
                    <!-- (Original attribute: syncAsync: SYNCHRONOUS = 0 / ASYNCHRONOUS = 1)  -->
                    <!-- (Original attribute: netClkOnR NETWORK CLOCK ON RECEIPT:
                             NOTACCDATA = 0   cannnot accept data with nic (i.e sender does not support this optional procedure)
                                             ACCDATA = 1          can accept data with nic)  -->
                    <!-- (Original attribute: netClkOnT    NETWORK INDEPENDENT CLOCK ON TRANSMIT
                                               NOTREQTOSEND = 0    does not require to send data with nic
                                               REQTOSEND = 1           requires to send data with nic)  -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: radioChanReq
           Original values:
                    HRCH = 0       half rate channel
                    FRCH = 1       full rate channel
                    DHRCH = 2    dual, full, rate preferred
                    DFRCH = 3    dual, half, rate preferred -->
    <xsd:simpleType name="RadioChannelRequirements">
        <xsd:annotation>
            <xsd:documentation>
            RADIO CHANNEL REQUIREMENTS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="halfRate"/>
            <xsd:enumeration value="fullRate"/>
            <xsd:enumeration value="dualHalfRate"/>
            <xsd:enumeration value="dualFullRate"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: transferMode
          Original values:
                     CM = 0  circuit mode
                     PM = 1  packet mode -->
    <xsd:simpleType name="TransferMode">
        <xsd:annotation>
            <xsd:documentation>
            RADIO CHANNEL REQUIREMENTS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="circuitMode"/>
            <xsd:enumeration value="packetMode"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: infTransfCap
           Original values:
                    SPEECH = 0
                    AUDIO = 2      3.1 KHZ AUDIO EX PLMN
                    UDI = 1           unrestricted digital
                    FAX3 = 3        facsimile group 3
                   ASFAX3 = 7   alternate speech/facsimilie group 3 starting with speech -->
    <xsd:simpleType name="InformationTransferCapability">
        <xsd:annotation>
            <xsd:documentation>
                       RADIO CHANNEL REQUIREMENTS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="speech"/>
            <xsd:enumeration value="audio"/>
            <xsd:enumeration value="unrestrictedDigital"/>
            <xsd:enumeration value="fax3"/>
            <xsd:enumeration value="alternateSpeechFax3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: structure
           Original values:
                 SDUI = 0       SDU integrity
                 UNSTR = 3    unstructed -->
    <xsd:simpleType name="Structure">
        <xsd:annotation>
            <xsd:documentation>
            Structure
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="sduIntegrity"/>
            <xsd:enumeration value="unstructured"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: rateAdaption
         Original Values:
                 NRA = 0          NO RATE ADAPTION
                 V110 = 1        V.110/X.30 RATE ADAPTION
                 X31 = 2         CCITT X.31 FLAG STUFFING
                 H223H245 =7     RATE ADAPTION H.223 & H.245  -->
    <xsd:simpleType name="RateAdaption">
        <xsd:annotation>
            <xsd:documentation>Rate Adaption</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="no"/>
            <xsd:enumeration value="v100X30"/>
            <xsd:enumeration value="x31FlagStuffing"/>
            <xsd:enumeration value="h223H245"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: signAccProt
         Original Values:
            I440 = 1        I.440/450
                   X21 = 2         X.21
                   X28DPI = 3   X.28 DEDICATED PAD INDIV. NUI
                   X28DPU = 4  X.28 DEDICATED PAD UNIV. NUI
                   X28NDP = 5  X.28 NON DEDICATED PAD
                   X32 = 6         X.32 -->
    <xsd:simpleType name="SignalingAccessProtocol">
        <xsd:annotation>
            <xsd:documentation>
             Signaling Access Protocol
             If the value "Other Rate Adaption" is not signalled in the field "Rate adaption"
             then the contents of this field shall be ignored.
             In UMTS, PIAFS shall be considered. In GSM, call shall be rejected if PIAFS requested.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="i440"/>
            <xsd:enumeration value="x21"/>
            <xsd:enumeration value="x28DedicatedPadIndividual"/>
            <xsd:enumeration value="x28DedicatedPadUniversal"/>
            <xsd:enumeration value="x28NonDedicatedPad"/>
            <xsd:enumeration value="x32"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: userInfoL2
         Original Values:
             NONE          USER INFO LAYER 2 PROTOCOL NOT PRESENT
             X25 = 6        RECOMMENDATION X.25 LINK LEVEL
             IA5 = 8         IA5
             X75 = 9        X.75 LAYER 2 MODIFIED, TELETEX
             VTX1 = 10    VIDEOTEX PROFILE 1
             FAX3 = 12    FACSIMILE GROUP 3
             VTX3 = 13    VIDEOTEX PROFILE 3 -->
    <xsd:simpleType name="UserInfoLayer2Protocol">
        <xsd:annotation>
            <xsd:documentation>User Info Layer2 Protocol</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="x25"/>
            <xsd:enumeration value="ia5"/>
            <xsd:enumeration value="x75"/>
            <xsd:enumeration value="vtx1"/>
            <xsd:enumeration value="fax3"/>
            <xsd:enumeration value="vtx3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: numOfDataBit
         Original Values:
            BIT8 = 1  8 bits
            BIT7 = 0  7 bits -->
    <xsd:simpleType name="NumberOfDataBits">
        <xsd:annotation>
            <xsd:documentation>Number Of Data Bits</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
            <xsd:enumeration value="7"/>
            <xsd:enumeration value="8"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: numOfStopBit
         Original Values:
            BIT1 = 0  1 bit
            BIT2 = 1  2 bits -->
    <xsd:simpleType name="NumberOfStopBits">
        <xsd:annotation>
            <xsd:documentation>Number Of Stop Bits</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
            <xsd:enumeration value="1"/>
            <xsd:enumeration value="2"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: userRate
         Original Values:
             B300 = 1           0.3KBIT/S RECOM. X.1 AND V.110
             B1200 = 2         1.2KBIT/S RECOM. X.1 AND V.110
             B2400 = 3         2.4KBIT/S RECOM. X.1 AND V.110
             B4800 = 4         4.8KBIT/S RECOM. X.1 AND V.110
             B9600 = 5         9.6KBIT/S RECOM. X.1 AND V.110
             B12000 = 6       12KBIT/S
             B1200B75 = 7   1.2KBIT/75BIT/S V.23,X.1,V.110  -->
    <xsd:simpleType name="UserRate">
        <xsd:annotation>
            <xsd:documentation>User Rate [kbit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="0.3"/>
            <xsd:enumeration value="1.2"/>
            <xsd:enumeration value="2.4"/>
            <xsd:enumeration value="4.8"/>
            <xsd:enumeration value="9.6"/>
            <xsd:enumeration value="12"/>
            <xsd:enumeration value="1.2/75bit/s"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: intermedRate
         Original Values:
             NONE NONE (No intermediate rate)
             KBS4 = 1     4 KBIT/S
             KBS8 = 2     8 KBIT/S
             KBS16 = 3   16 KBIT/S -->
    <xsd:simpleType name="IntermediateRate">
        <xsd:annotation>
            <xsd:documentation>Intermediate Rate [kbit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="4"/>
            <xsd:enumeration value="8"/>
            <xsd:enumeration value="16"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: parityInfo
         Original Values:
            ODD = 0    ODD
            EVEN = 2   EVEN
            NONE = 3   NONE
            FT0 = 4       FORCED TO 0
            FT1 = 5       FORCED TO 1 -->
    <xsd:simpleType name="ParityInfo">
        <xsd:annotation>
            <xsd:documentation>Parity Information</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="odd"/>
            <xsd:enumeration value="even"/>
            <xsd:enumeration value="none"/>
            <xsd:enumeration value="forcedTo0"/>
            <xsd:enumeration value="forcedTo1"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: connElmnt
         Original Values:
             TR = 0            TRANSPARENT
             NONTR = 1     NONTRANSPARENT (RLP)
             BTR = 2           BOTH, TRANSPARENT PREFERRED
             BNONTR = 3   BOTH, NONTRANSPARENT PREFERRED -->
    <xsd:simpleType name="ConnectionElement">
        <xsd:annotation>
            <xsd:documentation>Connection Element</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="transparent"/>
            <xsd:enumeration value="nonTransparent"/>
            <xsd:enumeration value="bothTransparentPreffered"/>
            <xsd:enumeration value="bothNonTransparentPreffered"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: modemType
         Original Values:
             NONE = 0    NONE (No modem type.)
             V21 = 1    V.21 MODEM
             V22 = 2    V.22 MODEM
             V22BIS = 3  V.22 BIS MODEM
             V23 = 4    V.23 MODEM
             V26TER = 5  V.26 TER MODEM
             V32 = 6     V.32 MODEM
             UNDEF = 7   UNDEFINED (Modem for undefined interface)
             OUTB1 = 8  AUTOBAUDING TYPE 1
             Autobauding type 1 (used only in conjunction with non transparent connection element). -->
    <xsd:simpleType name="ModemType">
        <xsd:annotation>
            <xsd:documentation>Modem Type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="none"/>
            <xsd:enumeration value="v21"/>
            <xsd:enumeration value="v22"/>
            <xsd:enumeration value="v22Bis"/>
            <xsd:enumeration value="v23"/>
            <xsd:enumeration value="v26Ter"/>
            <xsd:enumeration value="v32"/>
            <xsd:enumeration value="undefined"/>
            <xsd:enumeration value="autobaudingType1"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: fixNetURate
         Original Values:
            NONE = 0      NOT APPLICABLE
            B9600 = 1      9.6kBit/s Recommendation X.1 and V.110
            B14400 = 2     14.4kBit/s Recommendation X.1 and V.110
            B19200 = 3     19.2kBit/s Recommendation X.1 and V.110
            B28800 = 4     28.8kBit/s Recommendation X.1 and V.110
            B38400 = 5     38.4kBit/s Recommendation X.1 and V.110
            B48000 = 6     48kBit/s Recommendation X.1 and V.110 (synch)
            B56000 = 7     56kBit/s Recommendation X.1 and V.110 (synch)/bit transparent
            B64000 = 8     64kBit/s bit transparent -->
    <xsd:simpleType name="FixNetworkUserRate">
        <xsd:annotation>
            <xsd:documentation>Fix Network User Rate [kbit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="none"/>
            <xsd:enumeration value="9.6"/>
            <xsd:enumeration value="14.4"/>
            <xsd:enumeration value="19.2"/>
            <xsd:enumeration value="28.8"/>
            <xsd:enumeration value="38.4"/>
            <xsd:enumeration value="48"/>
            <xsd:enumeration value="56"/>
            <xsd:enumeration value="64"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                               D-CSI service                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: D-CSI -->
    <xsd:complexType name="DCsi">
        <xsd:annotation>
            <xsd:documentation>Dialed services CAMEL Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:DSendingOptions" minOccurs="0" maxOccurs="13"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:DReplacementHandlingForLUP" default="barringOfOutgoingCalls" minOccurs="0"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase3and4" default="3" minOccurs="0" maxOccurs="2"/>
                    <xsd:element name="replacementForInterrogation" type="nsr:ReplacementHandlingForInterrogation" default="replaceByBAIC" minOccurs="0"/>
                    <xsd:element name="optimalRouting" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint3" type="nsr:DTriggerDetectionPoint" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- Optimal routing allowed or not: ALLOWED = true / NOT ALLOWED = false -->
                    <!-- (Original attribute: dOptRouting: ALLOWED = 1 / NOT ALLOWED = 0) -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: dSendingOpt
              Original values:
              baprcoh == 1     Barring of premium rate calls while roaming outside HPLMN.
              ntf == 2              Sending notification of subscriber data change to the according gsmSCF.
              opt1 = 4, opt2 = 8, ... opt10 = 2048 are reserved
              baprcih == 4096 Barring of premium rate calls while roaming inside HPLMN.-->
    <xsd:simpleType name="DSendingOptions">
        <xsd:annotation>
            <xsd:documentation>
             D-CSI Sending Options
             determine which part of the IN-CAMEL related subscriber data is to be sent to the VLR/MSC
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notificationOfSubscriberDataChange"/>
            <xsd:enumeration value="barringOfPremiumRateCallsOutsideHplmn"/>
            <xsd:enumeration value="barringOfPremiumRateCallsInsideHplmn"/>
            <xsd:enumeration value="reservedOption1"/>
            <xsd:enumeration value="reservedOption2"/>
            <xsd:enumeration value="reservedOption3"/>
            <xsd:enumeration value="reservedOption4"/>
            <xsd:enumeration value="reservedOption5"/>
            <xsd:enumeration value="reservedOption6"/>
            <xsd:enumeration value="reservedOption7"/>
            <xsd:enumeration value="reservedOption8"/>
            <xsd:enumeration value="reservedOption9"/>
            <xsd:enumeration value="reservedOption10"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: dReplHndlLup
           Original values:
              noReplaceReq == 2    NO REPLACE HANDLING
              repByBAOC == 3        BARRING OF OUTGOING CALLS
              RRUSF == 1               ROAMING RESTRICTIONS -->
    <xsd:simpleType name="DReplacementHandlingForLUP">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for D-CSI determines if the mobile subscriber must be barred if CAMEL not supported.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="barringOfOutgoingCalls"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- D-CSI Trigger Detection Point (3)  -->
    <!-- Original attribute: TDP3 -->
    <xsd:complexType name="DTriggerDetectionPoint">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:TriggerDetectionPoint">
                <xsd:sequence>
                    <xsd:element name="destinationNumber" type="nsr:DestinationNumber" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                               GPRS-CSI service                  -->
    <!-- *************************************************************** -->
    <!-- Original attribute: GPRS-CSI -->
    <xsd:complexType name="GprsCsi">
        <xsd:annotation>
            <xsd:documentation>General Packet Radio Service CAMEL subscription information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacement" type="nsr:GprsReplacementHandling" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase3" default="3" minOccurs="0"/>
                    <xsd:element name="restrictedAccessPointName" type="nsr:AccessPointName" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint1" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint2" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint11" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint12" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint14" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <!-- Restricted access point name is used for each PDP context during GPRS location update,
                                             when the replacement for GPRS-CSI is rapOnly -->
                    <!-- TDP1: DP ATTACH -->
                    <!-- TDP2: DP ATTACH CHANGE POSITION -->
                    <!-- TDP11:PDP CONTEXT ESTABLISHMENT -->
                    <!-- TDP12:PDP CONTEXT ESTABLISHMENT ACKN. -->
                    <!-- TDP14:PDP CONTEXT CHANGE OF POSITION -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: mRepl
           Original values:
               noReplaceReq =2    NO REPLACE HANDLING
               pdpna = 5                PDP CONTEXT NOT ALLOWED
               rapOnly = 6             RESTRICTED ACCESS POINT ONLY -->
    <xsd:simpleType name="GprsReplacementHandling">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for GPRS-CSI determines,
            if the mobile subscriber must be barred if CAMEL not supported.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="packetDataProtocolNotAllowed"/>
            <xsd:enumeration value="restrictedAccessPointOnly"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: restrAPN -->
    <xsd:simpleType name="AccessPointName">
        <xsd:annotation>
            <xsd:documentation> GPRS Access Point Name. Length Range: 1..62</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string"/>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                  Location Services Client                       -->
    <!-- *************************************************************** -->
    <!-- Original attribute: LCSCLIENT -->
    <xsd:complexType name="LocationServicesClient">
        <xsd:annotation>
            <xsd:documentation>Location Services Client
        contains data relevant to external LCS clients that are allowed to locate a target MS for a MT-LR.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="clientAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="notificationMode" type="nsr:LocationNotificationModeForClient" default="locationAllowed" minOccurs="0"/>
                    <xsd:element name="gatewayRestriction" type="nsr:GatewayRestriction" minOccurs="0"/>
                    <!-- Original attribute: lcsProfName-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: nMsUExtClnt
         Original Values:
               notifyfLocAllow  = 0             NOTIFY LOCATION ALLOWED
               notifyAndVerifyLcAllwNoRsp = 1       NOTIFY AND VERIFY LOCATION ALLOWED NO RESPONSE
               notifyAndVerifyLcNoAllwNoRsp = 2     NOTIFY AND VERIFY LOCATION NOT ALLOW NO RESPONSE
               locAllow  = 3                LOCATION ALLOWED -->
    <xsd:simpleType name="LocationNotificationModeForClient">
        <xsd:annotation>
            <xsd:documentation>
        Location Notification mode to mobile station user
                 notifyLocation                 NOTIFY LOCATION ALLOWED
                 notifyAndVerifyLocationNoResponse  NOTIFY AND VERIFY LOCATION ALLOWED NO RESPONSE
                 notifyAndVerifyLocationWithResponse    NOTIFY AND VERIFY LOCATION NOT ALLOW NO RESPONSE
                 locationAllowed                    LOCATION ALLOWED
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notifyLocation"/>
            <xsd:enumeration value="notifyAndVerifyLocationNoResponse"/>
            <xsd:enumeration value="notifyAndVerifyLocationWithResponse"/>
            <xsd:enumeration value="locationAllowed"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: gmlcRestr
        Original Values:
        IGMLC = 1   -> IDENTIFIED GMLC ONLY
        HGMLC = 2   -> ANY GMLC IN THE HOME COUNTRY
        AGMLC = 3   -> ANY GMLC IN ANY COUNTRY -->
    <xsd:simpleType name="GatewayRestriction">
        <xsd:annotation>
            <xsd:documentation>
            Restriction of GMLC
            Values:
               identifiedGateway   allow identified GMLC only
                           gatewayInHplmn      allow any GMLC in home country
                           anyGateway          allow any GMLC
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="identifiedGateway"/>
            <xsd:enumeration value="gatewayInHplmn"/>
            <xsd:enumeration value="anyGateway"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                  Location Services Profile                      -->
    <!-- *************************************************************** -->
    <!-- Original attribute: LCSPROFILE -->
    <xsd:complexType name="LocationServicesProfile">
        <xsd:annotation>
            <xsd:documentation>Location Services Profile</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="locationNotificationMode" type="nsr:LocationNotificationMode" minOccurs="0"/>
                    <xsd:element name="clientClass" type="nsr:ClientClass" minOccurs="0" maxOccurs="5"/>
                    <xsd:element name="supplementaryService" type="nsr:SupplementaryServiceForLocation" minOccurs="0"/>
                    <xsd:element name="gatewayAddress" type="nsr:TelephoneNumber" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="locationServicesClient" type="nsr:LocationServicesClient" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- Address of Gateway Mobile Location Centre -->
                    <!-- (Original attribute: gmlcAddr)  -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: lcsNotify
         Original Values:
               notifyfLocAllow  = 0     NOTIFY LOCATION ALLOWED
               notifyAndVerifyLcAllwNoRsp = 1       NOTIFY AND VERIFY LOCATION ALLOWED NO RESPONSE
               notifyAndVerifyLcNoAllwNoRsp = 2     NOTIFY AND VERIFY LOCATION NOT ALLOW NO RESPONSE
               locAllow  = 3                LOCATION ALLOWED
               locNotAllow = 4              LOCATION NOT ALLOWED -->
    <xsd:simpleType name="LocationNotificationMode">
        <xsd:annotation>
            <xsd:documentation>
        Location Notification mode (to mobile station user)
        (If no external clients are present for the CUNREL service
        then the [[LCSCLIENT::nMsUExtClnt]] is used for the CUNREL service.)
        Values:
                 notifyLocation                 NOTIFY LOCATION ALLOWED
                 notifyAndVerifyLocationNoResponse  NOTIFY AND VERIFY LOCATION ALLOWED NO RESPONSE
                 notifyAndVerifyLocationWithResponse    NOTIFY AND VERIFY LOCATION NOT ALLOW NO RESPONSE
                 locationAllowed                    LOCATION ALLOWED
                 locationNotAllowed         LOCATION NOT ALLOWED
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notifyLocation"/>
            <xsd:enumeration value="notifyAndVerifyLocationNoResponse"/>
            <xsd:enumeration value="notifyAndVerifyLocationWithResponse"/>
            <xsd:enumeration value="locationAllowed"/>
            <xsd:enumeration value="locationNotAllowed"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: lcsPrivClass
     Original Values:
      callRel = 1
          callUnRel = 2
          plmnOperator = 3 -->
    <xsd:simpleType name="SupplementaryServiceForLocation">
        <xsd:annotation>
            <xsd:documentation>
        Values:
        callRelated       allow location by any value added LCS client to which a call is established from the target MS
                callUnRelated  allow location by designated external value added LCS clients
                plmnOperator   allow location by designated PLMN operator LCS clients
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="callRelated"/>
            <xsd:enumeration value="callUnRelated"/>
            <xsd:enumeration value="plmnOperator"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: plmnOpClass
     Original Values: brdcast == 1    omhplmn == 2    omvplmn == 4
              anonym == 8     tmsubs == 16 -->
    <xsd:simpleType name="ClientClass">
        <xsd:annotation>
            <xsd:documentation>
        List of one or more generic classes of LCS client that are allowed to locate the particular MS.
        Values:
        brdcast     LCS client broadcasting location related information
        omhplmn     LCS client in the HPLMN
        omvplmn     LCS client in the VPLMN
        anonym      LCS client recording anonymous location information
        tmsubs      LCS Client supporting a bearer service, teleservice or supplementary service to the target MS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="broadcastingLocation"/>
            <xsd:enumeration value="inHplmn"/>
            <xsd:enumeration value="inVplmn"/>
            <xsd:enumeration value="recordingAnonymousLocation"/>
            <xsd:enumeration value="supportingServices"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                     Mobility Management CSI service             -->
    <!-- *************************************************************** -->
    <!-- Original attribute: M-CSI -->
    <xsd:complexType name="MCsi">
        <xsd:annotation>
            <xsd:documentation>Mobility Management CAMEL Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacement" type="nsr:MReplacementHandling" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="serviceKey" type="nsr:ServiceKey" minOccurs="0"/>
                    <xsd:element name="mobilityManagementTrigger" type="nsr:MobilityManagementTrigger" minOccurs="0" maxOccurs="7"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: mRepl -->
    <xsd:simpleType name="MReplacementHandling">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for M-CSI determines if the mobile subscriber must be barred if CAMEL not supported.
            This field contains the replacement indicator feature flag (RIFF).
            Values:
              noReplaceReq == 2    NO REPLACE HANDLING
              RRUSF == 1               ROAMING RESTRICTIONS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: mmTrigger
          Original values:
                LUPSVLR =1  LOCATION UPDATE IN SAME VLR
                LUPOVLR= 2  LOC UPDATE TO AN ANOTHER VLR
                IMSIA = 4       IMSI ATTACH
                IMSIDMS = 8 MS INITIATED IMSI DETACH
                IMSIDNW = 16   NETWORK INITIATED IMSI DETACH -->
    <xsd:simpleType name="MobilityManagementTrigger">
        <xsd:annotation>
            <xsd:documentation>
            Mobility Management Trigger
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="locationUpdateInSameVlr"/>
            <xsd:enumeration value="locationUpdateToAnotherVlr"/>
            <xsd:enumeration value="imsiAttach"/>
            <xsd:enumeration value="mobileStationInitiatedImsiDetach"/>
            <xsd:enumeration value="networkInitiatedImsiDetach"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--            Mobility Management GPRS CSI service              -->
    <!-- *************************************************************** -->
    <!-- Original attribute: MG-CSI -->
    <xsd:complexType name="MgCsi">
        <xsd:annotation>
            <xsd:documentation>Mobility Management GPRS CAMEL subscription information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacement" type="nsr:MgReplacementHandling" default="noReplacement" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="serviceKey" type="nsr:ServiceKey" minOccurs="0"/>
                    <xsd:element name="mobilityManagementTrigger" type="nsr:GprsMobilityManagementTrigger" minOccurs="0" maxOccurs="7"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: mgRepl
           Original values:
              pdpna = 5                   PDP not avalable
              noReplaceReq = 2    NO REPLACE HANDLING
              substRRUSF = 1       Roaming restrictions due to unsupported feature -->
    <xsd:simpleType name="MgReplacementHandling">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for MG-CSI determines if the mobile subscriber must be barred if CAMEL not supported.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="packetDataProtocolNotAvailable"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: mgTrigger
           Original values:
                  GPRSA = 1                GPRS ATTACH
                  GPRSDMS = 2           MS INITIATED GPRS DETACH
                  GPRSDNW = 4          NETWORK IN. GPRS DETACH
                  RAUOSGSN = 8        RAU TO AN OTHER SGSN
                  RAUSSGSN = 16       RAU WITHIN SAME SGSN
                  RAUOSGSNDD = 32  RAU TO AN OTHER SGSN DISCONNECT BY DETACH
                  NITMSNRCP = 64      NETWORK IN. TX TO MS NRC FOR PAGING) -->
    <xsd:simpleType name="GprsMobilityManagementTrigger">
        <xsd:annotation>
            <xsd:documentation>
            GPRS Mobility Management Trigger
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="gprsAttach"/>
            <xsd:enumeration value="mobileStationInitiatedGprsDetach"/>
            <xsd:enumeration value="networkInitiatedGprsDetach"/>
            <xsd:enumeration value="RoutingAreaUpdateToAnotherSgsn"/>
            <xsd:enumeration value="RoutingAreaUpdateWithinSameSgsn"/>
            <xsd:enumeration value="RoutingAreaUpdateToAnotherSgsnDisconnectByDetach"/>
            <xsd:enumeration value="networkInTransmissionToMobileStationForPaging"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--           Mobile Terminating SMS-CSI service                    -->
    <!-- *************************************************************** -->
    <!-- Original attribute: MTSMS-CSI -->
    <xsd:complexType name="MtSmsCsi">
        <xsd:annotation>
            <xsd:documentation>Mobile terminating Short Message Service CAMEL subscription information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:MtSmsReplacementHandling" default="noReplacement" minOccurs="0"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase4" default="4" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="replacementForRoutingAreaUpdate" type="nsr:MtSmsReplacementHandling" default="noReplacement" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint2" type="nsr:TriggerDetectionPointDataTerminatingSms" minOccurs="0"/>
                    <!-- REPLACEMENT HANDLING for CAMEL not supported by SGSN -->
                    <!-- TDP2: DP DELIVERY REQUEST -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: mtsmsReplLup, mtsmsReplRau
          Original values:
                RRUSF == 1               ROAMING RESTRICTIONS DUE TO UNSUPPORTED FEATURE
                noReplaceReq == 2    NO REPLACE HANDLING
                TS21NA == 11            Suppress Mobile terminating SMS -->
    <xsd:simpleType name="MtSmsReplacementHandling">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for SMS-CSI determines,
            if the mobile subscriber must be barred if CAMEL not supported (by VLR or by SGSN).
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="suppressMobileTerminatingSms"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: TDPDATA-MTSMS-CSI -->
    <xsd:complexType name="TriggerDetectionPointDataTerminatingSms">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:TriggerDetectionPoint">
                <xsd:sequence>
                    <xsd:element name="smsType" type="nsr:SmsType" minOccurs="0" maxOccurs="3"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: smsType
          Original values:
             SMSDLVR = 1     SHORT MESSAGE SERVICE DELIVER
             SMSSTREP = 2   SMS STATUS REPORT
             SMSSBREP = 4  SMS SUBMIT REPORT -->
    <xsd:simpleType name="SmsType">
        <xsd:annotation>
            <xsd:documentation>
             Short Message Type
             is part of the CAMEL TDP CRITERIA for the MTSMSCSI CAMEL service.
             If no criterion is defined, then triggering takes place regardless of the type of the Short Message.
             Values:
             delivery           SHORT MESSAGE SERVICE DELIVER
             statusReport   SMS STATUS REPORT
             submitReport  SMS SUBMIT REPORT
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="delivery"/>
            <xsd:enumeration value="statusReport"/>
            <xsd:enumeration value="submitReport"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                    Multiple Subscriber Profile                  -->
    <!-- *************************************************************** -->
    <!-- Original attribute: MSP -->
    <xsd:complexType name="MultipleSubscriberProfiles">
        <xsd:annotation>
            <xsd:documentation>
            Multiple Subscriber Profiles
            Multiple profiles can be provisioned for a mobile subscriber.
            This will allow the subscriber to separate telecommunication service needs
            into different identities (e.g. business and home).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="isOperatorBarringAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isCallingLineIDRestrictionAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isConnectedLineIDPresentationAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isCallHoldAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isCallWaitAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isCallBarringAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isMultiPartyAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isCallCompletitionToBusySubscriberAvailable" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isExplicitCallTransferAvailable" type="xsd:boolean" minOccurs="0"/>
                    <!-- MPS operator determined baring service is available or not -->
                    <!-- (Original attribute: mspOdb: false = 0  service not available / true = 1   service available)  -->
                    <!-- MSP CALLING LINE ID RESTRICTION service is available or not -->
                    <!-- (Original attribute: mspClir (false = 0 / true = 1)  -->
                    <!-- MSP CONNECTED LINE IDENTIFICATION PRESENTATION service is available or not -->
                    <!-- (Original attribute: mspColp (false = 0 / true = 1)  -->
                    <!-- MSP CALL HOLD  service is available or not -->
                    <!-- (Original attribute: mspCallHold (false = 0 / true = 1)  -->
                    <!-- MSP CALL WAIT service is available or not -->
                    <!-- (Original attribute: mspCallWait (false = 0 / true = 1)  -->
                    <!-- MSP CALL BARRING service is available or not -->
                    <!-- (Original attribute: mspCb (false = 0 / true = 1)  -->
                    <!-- MSP MULTI PARTY service is available or not -->
                    <!-- (Original attribute: mspMultiPty (false = 0 / true = 1)  -->
                    <!-- MSP CALL COMPLETION TO BUSY SUBSCRIBER service is available or not -->
                    <!-- (Original attribute: mspCcbs (false = 0 / true = 1)  -->
                    <!-- MSP EXPLICIT CALL TRANSFER service is available or not -->
                    <!-- (Original attribute: mspEct(false = 0 / true = 1)  -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                               O-CSI service                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: O-CSI -->
    <xsd:complexType name="OCsi">
        <xsd:annotation>
            <xsd:documentation>Originating CAMEL Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:OReplacementHandlingForLUP" default="barringOfOutgoingCalls" minOccurs="0"/>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase" default="1" minOccurs="0" maxOccurs="4"/>
                    <xsd:element name="enableTdpCriteriaDestinationMatchType" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="tdpDestinationLength" type="nsr:TdpDestinationLength" minOccurs="0" maxOccurs="3"/>
                    <xsd:element name="forwardTdpCriteria" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:OSendingOptions" minOccurs="0" maxOccurs="20"/>
                    <xsd:element name="replacementForInterrogation" type="nsr:ReplacementHandlingForInterrogation" default="replaceByBAIC" minOccurs="0"/>
                    <xsd:element name="optimalRouting" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint2" type="nsr:TriggerDetectionPointData" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint4" type="nsr:TriggerDetectionPointData" minOccurs="0"/>
                    <!-- Match type is part of CAMEL TDP CRITERIA for the INMOC-CAMEL service destination number. -->
                    <!-- Enable = true / Inhibit = false  -->
                    <!-- (Original attribute: TdpcDnMtchTp:     Inhibiting = 0 /  Enabling = 1)  -->
                    <!-- Forwarding triggering criterion is part of the CAMEL TDP CRITERIA for the IN-CAMEL service. -->
                    <!-- FORWARD = true / NOT FORWARD = false  -->
                    <!-- (Original attribute: tdpcCallType: FWRD == 0 / NOTFWRD == 1)  -->
                    <!-- Optimal routing allowed or not: ALLOWED = true / NOT ALLOWED = false -->
                    <!-- (Original attribute: oOptRouting: ALLOWED = 1 / NOT ALLOWED = 0) -->
                    <!-- TDP2: DP COLLECTED INFO -->
                    <!-- TDP4: DP ROUTE SELECTED FAILURE -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: oReplHndlLup
           Original values:
            RRUSF = 1 ROAMING RESTRICTIONS          BAOC = 3 BARRING OF OUTGOING CALLS
               BOCETS22 = 8 BAOC except TS 22               noReplaceReq = 2 NO REPLACE HANDLING -->
    <xsd:simpleType name="OReplacementHandlingForLUP">
        <xsd:annotation>
            <xsd:documentation>
                      Replacement handling for OCSI during Location Update, if CAMEL service is not supported.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="barringOfOutgoingCalls"/>
            <xsd:enumeration value="barringOfOutgoingCallsExceptTS22"/>
            <xsd:enumeration value="noReplacement"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: tdpDestNoLgth -->
    <xsd:simpleType name="TdpDestinationLength">
        <xsd:annotation>
            <xsd:documentation>
                       Destination number length list is part of the CAMEL TDP CRITERIA for the INMOC-CAMEL service.
                       In general it is used to allow / not allow IN-traffic based on a given destination number length.
                       Value range: 1..15
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attribute: oSendingOpt
          Original Values:
              sctcoh = 1          SUPPRESS CTC OUTSIDE HPLMN
              sts22ih = 2         SUPPRESS SMS MO INSIDE HPLMN
              sts22oh = 4        SUPPRESS SMS MO OUTSIDE HPLMN
              opt1 = 16, opt2 = 32 ... opt10 = 8192     are reserved
              sctcih = 16384           SUPPRESS CTC INSIDE HPLMN
              ntf = 32768                NOTIF TO REQUESTED GSMSCF
              baprcoh = 65536       BAPRC OUTSIDE HPLMN
              baprcih = 131072      BAPRC INSIDE HPLMN
              bocets22 = 262144   BAOC EXCEPT TS22
              rcalloh = 524288       RELEASE CALL OUTSIDE HPLMN -->
    <xsd:simpleType name="OSendingOptions">
        <xsd:annotation>
            <xsd:documentation>
                     Sending options is used to determine which part of the IN-CAMEL related subscriber data is to be sent to the VLR/MSC.
                     The HLR stores a list of up to 16 options.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notificationOfSubscriberDataChange"/>
            <xsd:enumeration value="suppressCtcInsideHplmn"/>
            <xsd:enumeration value="suppressCtcOutsideHplmn"/>
            <xsd:enumeration value="suppressSmsInsideHplmn"/>
            <xsd:enumeration value="suppressSmsOutsideHplmn"/>
            <xsd:enumeration value="barringOfPremiumRateCallsInsideHplmn"/>
            <xsd:enumeration value="barringOfPremiumRateCallsOutsideHplmn"/>
            <xsd:enumeration value="barringOfOutgoingCallsExceptTS22"/>
            <xsd:enumeration value="releaseCallOutsideHplmn"/>
            <xsd:enumeration value="reservedOption1"/>
            <xsd:enumeration value="reservedOption2"/>
            <xsd:enumeration value="reservedOption3"/>
            <xsd:enumeration value="reservedOption4"/>
            <xsd:enumeration value="reservedOption5"/>
            <xsd:enumeration value="reservedOption6"/>
            <xsd:enumeration value="reservedOption7"/>
            <xsd:enumeration value="reservedOption8"/>
            <xsd:enumeration value="reservedOption9"/>
            <xsd:enumeration value="reservedOption10"/>
            <!--NTHLR 7 FC122_005115 - OCSI START-->
                        <xsd:enumeration value="suppressOcsiForEcf"/>
                        <!--NTHLR 7 FC122_005115 - OCSI END-->
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--              Prohibited Forwarded To Numbers                    -->
    <!-- *************************************************************** -->
    <!-- Original attribute: PROHIBFTNO -->
    <xsd:complexType name="ProhibitedForwardedToNumbers">
        <xsd:annotation>
            <xsd:documentation>
            Prohibited Forwarded To Numbers
            of the HLR for optimized checking whether a forwarding number is allowed.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="type" type="nsr:TelephoneNumberFormat" minOccurs="0"/>
                    <xsd:element name="to" type="nsr:ForwardedToNumber" minOccurs="0"/>
                    <!-- xsd:element name="number" type="nsr:ProhibitedForwardedToNumber" minOccurs="0" maxOccurs="unbounded"/-->
                    <!-- xsd:element name="range" type="nsr:ProhibitedForwardedToRange" minOccurs="0" maxOccurs="unbounded"/-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="ForwardedToNumber">
        <xsd:annotation>
            <xsd:documentation>
            Telephone Number with F wildcard
            Length Range: 1..15 and Pattern: [0-9F]+
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string"/>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                      Quality Of Service Profile                 -->
    <!-- *************************************************************** -->
    <!-- Original attribute: QOFSERV -->
    <xsd:complexType name="QualityOfServiceProfile">
        <xsd:annotation>
            <xsd:documentation>
            Quality Of Service Profile contains attributes
            which determine the degree of satisfaction of a user of the service.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="allocationRetentionPriority" type="nsr:AllocationRetentionPriority" minOccurs="0"/>
                    <xsd:element name="delayClass" type="nsr:DelayClass" minOccurs="0"/>
                    <xsd:element name="reliabilityClass" type="nsr:ReliabilityClass" minOccurs="0"/>
                    <xsd:element name="peakThroughput" type="nsr:PeakThroughput" minOccurs="0"/>
                    <xsd:element name="precedenceClass" type="nsr:PrecedenceClass" minOccurs="0"/>
                    <xsd:element name="meanThroughput" type="nsr:MeanThroughput" minOccurs="0"/>
                    <xsd:element name="trafficClass" type="nsr:TrafficClass" minOccurs="0"/>
                    <xsd:element name="deliveryOrder" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="deliveryOfErrorneousDataUnit" type="nsr:DeliveryOfErrorneousDataUnit" minOccurs="0"/>
                    <xsd:element name="maximumDataUnitSize" type="nsr:MaximumDataUnitSize" minOccurs="0"/>
                    <xsd:element name="maximumBitRateForUpLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="maximumBitRateForDownLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="extendedMaximumBitRateForDownLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="extendedMaximumBitRateForUpLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="residualBitErrorRate" type="nsr:ResidualBitErrorRate" minOccurs="0"/>
                    <xsd:element name="dataUnitErrorRatio" type="nsr:DataUnitErrorRatio" minOccurs="0"/>
                    <xsd:element name="transferDelay" type="nsr:TransferDelay" minOccurs="0"/>
                    <xsd:element name="trafficHandlingPriority" type="nsr:TrafficHandlingPriority" minOccurs="0"/>
                    <xsd:element name="guaranteedBitRateForUpLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="guaranteedBitRateForDownLink" type="nsr:BitRate" minOccurs="0"/>
                    <xsd:element name="extendedGuaranteedBitRateForDownLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="extendedGuaranteedBitRateForUpLink" type="nsr:ExtendedBitRate" minOccurs="0"/>
                    <xsd:element name="optimisedForSignallingTraffic" type="xsd:boolean" default="false" minOccurs="0"/>
                    <xsd:element name="sourceStatisticsDescriptor" type="nsr:SourceStatisticsDescriptor" default="unknown" minOccurs="0"/>
                    <!-- This attribute specifies whether the Delivery Order should be maintained or not. -->
                    <!-- (Original attribute: deliverOrder: false = 0 / true = 1)  -->
                    <!-- HLR 4.5 extendedMaximumBitRateForUpLink -->
                    <!-- This attribute specifies whether the service is optimised for signalling traffic or not
                                            Signalling Indication, octet 14 (see 3GPP TS 23.107)  for communication between MS and network.
                                             If set to '1' the QoS of the PDP context is optimised for signalling
                                            This value is ignored if the Traffic Class is Conversational Streaming or Background class. -->
                    <!-- (Original attribute: signalingIndication: false = 0 / true = 1)  -->
					<!-- RE123_107755 Evolved ARP in QoS Profile -->
                    <xsd:element name="eARPLevel" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="eARPPreEmptCapability" type="nsr:PreEmption" minOccurs="0"/>
                    <xsd:element name="eARPPreEmptVulnerability" type="nsr:PreEmption" minOccurs="0"/>
                    <!-- RE123_107755 Evolved ARP in QoS Profile -->                    
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: allocRetPrio-->
    <xsd:simpleType name="AllocationRetentionPriority">
        <xsd:annotation>
            <xsd:documentation>
        Allocation Retention Priority
        Value Range: 0..255
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attribute: delayClass
         Original Values:
                  LOW   = 1  Low delay
                  NORMAL = 2      Normal delay
                  HIGH   = 3  High delay
                  BESTEFRT = 4  Best effort -->
    <xsd:simpleType name="DelayClass">
        <xsd:annotation>
            <xsd:documentation>Delay Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="low"/>
            <xsd:enumeration value="normal"/>
            <xsd:enumeration value="high"/>
            <xsd:enumeration value="bestEffort"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: reliabClass
         Original Values:
            RCL1 = 1   Non real-time traffic, error sensitive application that cannot cope with data loss
            RCL2 = 2   Non real-time traffic, error sensitive application that can cope with infrequent data loss
            RCL3 = 3   Non real-time traffic, error sensitive application that can cope with data loss, GMM/SM
            RCL4 = 4   Real-time traffic, error sensitive application that can cope with data loss
            RCL5 = 5   Real-time traffic, error non-sensitive application that can cope with data loss -->
    <xsd:simpleType name="ReliabilityClass">
        <xsd:annotation>
            <xsd:documentation>Reliability Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="nonRealTimeCanNotCopeWithDataLoss"/>
            <xsd:enumeration value="nonRealTimeCanCopeWithInfrequentDataLoss"/>
            <xsd:enumeration value="nonRealTimeCanCopeWithDataLoss"/>
            <xsd:enumeration value="realTimeErrorSensitive"/>
            <xsd:enumeration value="realTimeErrorNonSensitive"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: peakThrput
         Original Values:
                  P8K = 1             8  KBIT/S           P16K = 2          16 KBIT/S
                  P32K = 3           32 KBIT/S          P64K = 4           64 KBIT/S
                  P128K = 5        128 KBIT/S         P256K = 6         256 KBIT/S
                  P512K = 7         512 KBIT/S        P1024K = 8       1024 KBIT/S
                  P2048K = 9       2048 KBIT/S -->
    <xsd:simpleType name="PeakThroughput">
        <xsd:annotation>
            <xsd:documentation>Peak Throughput [kbit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="8"/>
            <xsd:enumeration value="16"/>
            <xsd:enumeration value="32"/>
            <xsd:enumeration value="64"/>
            <xsd:enumeration value="128"/>
            <xsd:enumeration value="256"/>
            <xsd:enumeration value="512"/>
            <xsd:enumeration value="1024"/>
            <xsd:enumeration value="2048"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: precedClass
         Original Values:
              HIGH = 1         HIGH PRECEDENCE
                                     Precedence before services with precedence NORMAL and LOW
              NORMAL = 2   NORMAL PRECEDENCE
                                     Precedence before services with precedence LOW
              LOW = 3         LOW PRECEDENCE -->
    <xsd:simpleType name="PrecedenceClass">
        <xsd:annotation>
            <xsd:documentation>Precedence Class</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="high"/>
            <xsd:enumeration value="normal"/>
            <xsd:enumeration value="low"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: meanThrput
         Original Values:
            BESTEFRT = 31    Best effort
            M02 = 1      0.22 bit/s        M04 = 2       0.44 bit/s
            M1 = 3       1.11 bit/s        M2 = 4            2.2 bit/s
            M4 = 5       4.4 bit/s          M11 = 6      11 bit/s
            M22 = 7      22 bit/s           M44 = 8      44 bit/s
            M111 = 9     111 bit/s         M222 = 10     222 bit/s
            M444 = 11    444 bit/s         M1K = 12      1 kbit/s
            M2K = 13     2 kbit/s           M4K = 14         4 kbit/s
            M11K = 15    11 kbit/s         M22K = 16     22 kbit/s
            M44K = 17    44 kbit/s         M111K = 18   111 kbit/s -->
    <xsd:simpleType name="MeanThroughput">
        <xsd:annotation>
            <xsd:documentation>Mean Throughput Class [bit/s]</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="0.22"/>
            <xsd:enumeration value="0.44"/>
            <xsd:enumeration value="1.11"/>
            <xsd:enumeration value="2.2"/>
            <xsd:enumeration value="4.4"/>
            <xsd:enumeration value="11"/>
            <xsd:enumeration value="22"/>
            <xsd:enumeration value="44"/>
            <xsd:enumeration value="111"/>
            <xsd:enumeration value="222"/>
            <xsd:enumeration value="444"/>
            <xsd:enumeration value="1k"/>
            <xsd:enumeration value="2k"/>
            <xsd:enumeration value="4k"/>
            <xsd:enumeration value="11k"/>
            <xsd:enumeration value="22k"/>
            <xsd:enumeration value="44k"/>
            <xsd:enumeration value="111k"/>
            <xsd:enumeration value="bestEffort"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: trafficClass
         Original Values:
         CONVCL =1  -> CONVERSATIONAL CLASS
         STREAMCL = 2   -> STREAMING CLASS
         INTACTCL = 3   -> INTERACTIVE CLASS
         BACKGRCL = 4   -> BACKGROUND CLASS -->
    <xsd:simpleType name="TrafficClass">
        <xsd:annotation>
            <xsd:documentation>
            Traffic Class
            Values:
              conversational
                     Real time applications defined for UMTS (e.g. telephony speech).
                     With Internet and multimedia a number of new application will use this scheme:
                     e.g. voice over IP, video conferencing tools
               streaming
                    Services like real time video (audio) which are one way transports
                    and very sensible to time variation between information entities (packages).
               interactive
                    Non-real time applications expecting messages (responses) within a certain time.
               background
                    Applications like receiving files, e-mails or SMS in the background,
                    in which the destination is not expecting the data within a certain time.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="conversational"/>
            <xsd:enumeration value="streaming"/>
            <xsd:enumeration value="interactive"/>
            <xsd:enumeration value="background"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: deliverESDU
         Original Values:
              NODETECT = 1   NOT DETECTED
              DLV = 2              DELIVERED
              NODLV = 3          NOT DELIVERED -->
    <xsd:simpleType name="DeliveryOfErrorneousDataUnit">
        <xsd:annotation>
            <xsd:documentation>
            Delivery Of Errorneous service Data Unit
            Values:
               notDetected    Erroneous SDUs are not detected
               delivered         Erroneous SDUs are delivered
               notDelivered   Erroneous SDUs are not delivered
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notDetected"/>
            <xsd:enumeration value="delivered"/>
            <xsd:enumeration value="notDelivered"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: maxSduSize-->
    <xsd:simpleType name="MaximumDataUnitSize">
        <xsd:annotation>
            <xsd:documentation>
            Maximum service Data Unit Size
            Values: Only steps of 10 are allowed. Above 1500 the values 1502, 1510 and 1520 are allowed only.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attributes: maxBrUplnk, maxBrDwnlnk, guarBrDwnlnk, guarBrUplnk -->
    <xsd:simpleType name="BitRate">
        <xsd:annotation>
            <xsd:documentation>
            Bit Rate
            Values: Range of decimal number 1..8640 is allowed.
                        1   .. 63   : steps of 1
                       64  .. 568  : steps of 8
                      576 .. 8640 : steps of 64
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attributes: extMaxBrDwnlnk, extGuarBrDwnlnk -->
    <xsd:simpleType name="ExtendedBitRate">
        <xsd:annotation>
            <xsd:documentation>
            Extended Bit Rate
            Range of decimal number allowed:
            8700 kbps - 16 Mbps,in steps of 100kbps increments
            The follwing ranges are allowed only if feature HSDPAPlusSupportInNTHLR is released (See feature: HSDPAPlusSupportInNTHLRin CONFIGURATION DATA):
            17 Mbps - 128 Mbps, in steps of 1 Mbps increments
            130 Mbps - 256Mbps, in steps of 2 Mbps increments
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attribute: residualBer
         Original Values:
             R5EXP2N = 1    5*10 Exponential -2             R1EXP2N = 2    1*10 Exponential -2
             R5EXP3N = 3    5*10 Exponential -3             R4EXP3N = 4    4*10 Exponential -3
             R1EXP3N = 5    1*10 Exponential -3             R1EXP4N = 6    1*10 Exponential -4
             R1EXP5N = 7    1*10 Exponential -5             R1EXP6N = 8    1*10 Exponential -6
             R6EXP8N= 9     6*10 Exponential -8 -->
    <xsd:simpleType name="ResidualBitErrorRate">
        <xsd:annotation>
            <xsd:documentation>
            Residual Bit Error Rate of service
            Values:
                5Exp-2     5*10 Exponential -2                 5Exp-3    5*10 Exponential -3
                4Exp-3    4*10 Exponential -3                  6Exp-8     6*10 Exponential -8
                1Exp-2     1*10 Exponential -2                 1Exp-3    1*10 Exponential -3
                1Exp-4    1*10 Exponential -4                  1Exp-5    1*10 Exponential -5
                1Exp-6    1*10 Exponential -6
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="5Exp-2"/>
            <xsd:enumeration value="5Exp-3"/>
            <xsd:enumeration value="4Exp-3"/>
            <xsd:enumeration value="6Exp-8"/>
            <xsd:enumeration value="1Exp-2"/>
            <xsd:enumeration value="1Exp-3"/>
            <xsd:enumeration value="1Exp-4"/>
            <xsd:enumeration value="1Exp-5"/>
            <xsd:enumeration value="1Exp-6"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: sduErrRatio
         Original Values:
            R1EXP2N = 1   1*10 Exponential -2            R7EXP3N = 2   7*10 Exponential -3
            R1EXP3N = 3   1*10 Exponential -3            R1EXP4N = 4   1*10 Exponential -4
            R1EXP5N = 5   1*10 Exponential -5            R1EXP6N = 6   1*10 Exponential -6
            R1EXP1N = 7   1*10 Exponential -1 -->
    <xsd:simpleType name="DataUnitErrorRatio">
        <xsd:annotation>
            <xsd:documentation>
             service Data Unit  Error Ratio
            Values:
                 7Exp-3   7*10 Exponential -3
                 1Exp-1   1*10 Exponential -1                 1Exp-2   1*10 Exponential -2
                 1Exp-3   1*10 Exponential -3                 1Exp-4   1*10 Exponential -4
                 1Exp-5   1*10 Exponential -5                 1Exp-6   1*10 Exponential -6
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="7Exp-3"/>
            <xsd:enumeration value="1Exp-1"/>
            <xsd:enumeration value="1Exp-2"/>
            <xsd:enumeration value="1Exp-3"/>
            <xsd:enumeration value="1Exp-4"/>
            <xsd:enumeration value="1Exp-5"/>
            <xsd:enumeration value="1Exp-6"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: transfDelay -->
    <xsd:simpleType name="TransferDelay">
        <xsd:annotation>
            <xsd:documentation>
            Transfer Delay
            Values: Range of decimal number 10..4000 is allowed.
                        10   .. 150  : steps of  10
                      200  .. 950  : steps of  50
                    1000 .. 4000 : steps of  100
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt"/>
    </xsd:simpleType>
    <!-- Original attribute: trfHndlgPrio
         Original Values:
            PRIO1 = 1      Traffic priority 1
            PRIO2 = 2    Traffic priority 2
            PRIO3 = 3    Traffic priority 3 -->
    <xsd:simpleType name="TrafficHandlingPriority">
        <xsd:annotation>
            <xsd:documentation>Traffic Handling Priority</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="priority1"/>
            <xsd:enumeration value="priority2"/>
            <xsd:enumeration value="priority3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: sourceStatDescriptor
         Original Values:
              UNKNOWN == 0
              SPEECH == 1 -->
    <xsd:simpleType name="SourceStatisticsDescriptor">
        <xsd:annotation>
            <xsd:documentation>
            Source Statistics Descriptor (see 3GPP TS 23.107)
             The Source Statistics Descriptor value is ignored if the Traffic Class is Interactive or Background.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="unknown"/>
            <xsd:enumeration value="speech"/>
        </xsd:restriction>
    </xsd:simpleType>
	<xsd:simpleType name="PreEmption">
        <xsd:annotation>
            <xsd:documentation>
            Enumerated type
            values :
                ENABLED
                DISABLED
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="enable"/>
            <xsd:enumeration value="disable"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                                Roaming Area                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: ROAM-AREA -->
    <xsd:complexType name="RoamingArea">
        <xsd:annotation>
            <xsd:documentation>Roaming Area in which a mobile subscriber is allowed to roam or not. It also contains a set of ISDNNumbers.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="isAllowed" type="xsd:boolean" default="false" minOccurs="0"/>
                    <xsd:element name="isdnNumber" type="nsr:NumericString" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- This attribute specifies if the roaming area is allowed or not.
                                       Values:
                                             true   positive list (roaming allowed in specific area)
                                             false  negative list (roaming not allowed in specific area) -->
                    <!-- Original attribute: isAreaPos (false = 0 / true = 1) -->
                    <!-- ISDN number digits of those network entity that belong to the ROAMING AREA. -->
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <xsd:element name="isdnNumberWithPlmnId" type="nsr:HLRISDNNumberWithPlmnId" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <!-- FC123_107003_Separate_Roaming_Restriction_List :: Start-->
                    <xsd:element name="blackIsdnNumber" type="nsr:NumericString" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="whiteIsdnNumber" type="nsr:NumericString" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- FC123_107003_Separate_Roaming_Restriction_List :: End-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                    Short Code Translation                       -->
    <!-- *************************************************************** -->
    <!-- Original attribute: SHORTCODETRANSL  -->
    <xsd:complexType name="ShortCodeTranslation">
        <xsd:annotation>
            <xsd:documentation>Short Code Translation</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="serviceIndicator" type="nsr:ServiceIndicator" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                               SMS-CSI service                   -->
    <!-- *************************************************************** -->
    <!-- Original attribute: SMS-CSI -->
    <xsd:complexType name="SmsCsi">
        <xsd:annotation>
            <xsd:documentation>Short Message Service CAMEL subscription information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:SmsReplacementHandlingForLUP" minOccurs="0"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase3" default="3" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="replacementForRoutingAreaUpdate" type="nsr:SmsReplacementHandlingForRAU" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint" type="nsr:TriggerDetectionPoint" minOccurs="0"/>
                    <!-- REPLACEMENT HANDLING for CAMEL not supported by SGSN -->
                    <!-- TDP2: DP COLLECTED INFO -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: smsReplLup
           Original Values:
                RRUSF == 1             ROAMING RESTRICTIONS DUE TO UNSUPPORTED FEATURE
                noReplaceReq == 2  NO REPLACE HANDLING
                TS22NA == 9            Suppress Mobile Originating SMS
                TS22BEHC == 10     Call Barring of all outgoing international calls for SMSMO, except those to HPLMN country. -->
    <xsd:simpleType name="SmsReplacementHandlingForLUP">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for SMS-CSI determines,
            if the mobile subscriber must be barred if CAMEL not supported by VLR.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="suppressMobileOriginatingSms"/>
            <xsd:enumeration value="barringOfOutgoingInternationalSmsExceptToHome"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: smsReplRau
           Original Values:
                noReplaceReq == 2  NO REPLACE HANDLING
                TS22NA == 9            Suppress Mobile Originating SMS
                TS22BEHC == 10     Call Barring of all outgoing international calls for SMSMO, except those to HPLMN country. -->
    <xsd:simpleType name="SmsReplacementHandlingForRAU">
        <xsd:annotation>
            <xsd:documentation>
            SMS-CSI REPLACEMENT HANDLING, for CAMEL not supported by SGSN,
            determines, if the mobile subscriber must be barred.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="suppressMobileOriginatingSms"/>
            <xsd:enumeration value="barringOfOutgoingInternationalSmsExceptToHome"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                     Suplementary Service-CSI                    -->
    <!-- *************************************************************** -->
    <!-- Original attribute: SS-CSI -->
    <xsd:complexType name="SsCsi">
        <xsd:annotation>
            <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="supplementaryServiceCode" type="nsr:SupplementaryServiceCode" minOccurs="0" maxOccurs="5"/>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacement" type="nsr:SsReplacementHandling" default="multiPartyNotAllowed" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: supServCode
          Original values:
               ccbs = 68          CALL COMPL TO BUSY SUBSCRIBER  (ccbsA/ccbsB/ccbsNat?)
               multiPty = 81 MULTI PARTY
               both =82           CCBS and MULTI PARTY
    HLR 4.5 values: Explicit Call Transfer, Call Deflection -->
    <xsd:simpleType name="SupplementaryServiceCode">
        <xsd:annotation>
            <xsd:documentation>
            Supplementary Service Code
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="callCompletitionToBusySubscriber"/>
            <xsd:enumeration value="multiParty"/>
            <xsd:enumeration value="bothCcbsAndMultiParty"/>
            <xsd:enumeration value="explicitCallTransfer"/>
            <xsd:enumeration value="callDeflection"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- Original attribute: ssRepl
           Original values:
              noReplaceReq = 2    NO REPLACE HANDLING
              multiPtyNotAll = 4     MULTI PARTY NOT ALLOWED
              RRUSF = 1               ROAMING RESTRICTIONS -->
    <xsd:simpleType name="SsReplacementHandling">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for SS-CSI determines if the mobile subscriber must be barred if CAMEL not supported.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="multiPartyNotAllowed"/>
            <xsd:enumeration value="explicitCallTransferNotAllowed"/>
            <xsd:enumeration value="multiPartyAndExplicitCallTransferNotAllowed"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                 Subscriber Related Routing Data                 -->
    <!-- *************************************************************** -->
    <!-- Original attribute: SUBRELRO -->
    <xsd:complexType name="SubscriberRelatedRoutingData">
        <xsd:annotation>
            <xsd:documentation>Subscriber Related Routing Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="serviceIndicator" type="nsr:ServiceIndicator" default="0" minOccurs="0"/>
                    <xsd:element name="addressType" type="nsr:AddressTypeForRouting" default="subcriberDependent" minOccurs="0"/>
                    <xsd:element name="address" type="xsd:string" minOccurs="0"/>
                    <!-- Original attribute: subscrServ-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: addressType
           Original values:
           fixed FORMAT = 1
           SubDep = 2        SUBSCRIBER DEPENDENT
           SCAddress = 3     SERVICE CENTER ADDRESS
           NATFRM = 4        NATIONAL FORMAT -->
    <xsd:simpleType name="AddressTypeForRouting">
        <xsd:annotation>
            <xsd:documentation>
            Address Type that is used for the routing purpose
            Values:
              fixedFormat
                    The ADDRESS is used without modification as routing information for routing to the service center.
              subcriberDependent
                    The ADDRESS is modified before it is used as routing information for routing to the service center.
                     (Entered address: F1-Fn (including CC NDC) / Modified address: F1-Fn SN)
              serviceCenterAddress
                     The ADDRESS is used without modification as global title address for SCCP connection to the service center.
              nationalFormat
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="fixedFormat"/>
            <xsd:enumeration value="subcriberDependent"/>
            <xsd:enumeration value="serviceCenterAddress"/>
            <xsd:enumeration value="nationalFormat"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                               T-CSI service                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: T-CSI -->
    <xsd:complexType name="TCsi">
        <xsd:annotation>
            <xsd:documentation>Terminating CAMEL Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="replacementForInterrogation" type="nsr:ReplacementHandlingForInterrogation" default="replaceByBAIC" minOccurs="0"/>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase" default="1" minOccurs="0" maxOccurs="4"/>
                    <xsd:element name="sendingOptions" type="nsr:TSendingOptions" minOccurs="0" maxOccurs="18"/>
                    <xsd:element name="forceLocationRetrieval" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="requireStatusRetrieval" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="requireLocationRetrieval" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="optimalRouting" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint12" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint13" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint14" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <!-- Force retrieval of current location is allowed or not during MTC setup. -->
                    <!-- ALLOWED = true / NOT ALLOWED = false -->
                    <!-- (Original attribute: frcRtrCurrLc: ALLOWED = 1 / NOT ALLOWED = 0) -->
                    <!-- Subscriber status retrieval is required or not during MTC setup -->
                    <!-- REQUIRED= true / NOT REQUIRED= false -->
                    <!-- (Original attribute: msubStatRtr: REQUIRED= 1 / NOT REQUIRED= 0) -->
                    <!-- Subscriber location retrieval is required or not during MTC setup -->
                    <!-- REQUIRED= true / NOT REQUIRED= false -->
                    <!-- (Original attribute: msubLcInfRtr: REQUIRED= 1 / NOT REQUIRED= 0) -->
                    <!-- Optimal routing allowed or not: ALLOWED = true / NOT ALLOWED = false -->
                    <!-- (Original attribute: tOptRouting: ALLOWED = 1 / NOT ALLOWED = 0) -->
                    <!-- TDP12:DP TERMINATING ATTEMPT AUTHORIZ. -->
                    <!-- TDP13:DP T BUSY -->
                    <!-- TDP14:DP T NO ANSWER -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: tSendingOpt
           Original values:
              ntf == 1               Sending notification of subscriber data change to the according gsmSCF.
              vlrisdih == 2        Send VLRISD while roaming inside HPLMN
              vlrisdoh == 4       Send VLRISD while roaming outside HPLMN
              opt1 = 16, opt2 = 32 ... opt10 = 8192     are reserved
              STCSINR == 16384       Suppress sending T-CSI if T-CSI and O-CSI are present in case of CFU or CFNRC
              STCSICFU == 32768     Suppress sending T-CSI if T-CSI is present in case of CFU or forwarding violation.
              STCSIVTS == 65536     Suppress sending T-CSI for a subscriber if VT-CSI is sent to VLR
              SCFUCHK == 131072    Suppress CFU check in second interrogation step.
              RBARCHK == 262144   Reduce barring check priority with respect to T-CSI -->
    <xsd:simpleType name="TSendingOptions">
        <xsd:annotation>
            <xsd:documentation>
             Sending options determine which part of the IN-CAMEL related subscriber data is to be sent to the VLR/MSC
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="notificationOfSubscriberDataChange"/>
            <xsd:enumeration value="sendVlrisdInsideHplmn"/>
            <xsd:enumeration value="sendVlrisdOutsideHplmn"/>
            <xsd:enumeration value="suppressTcsiForCfuOrCfnrc"/>
            <xsd:enumeration value="suppressTcsiForCfuOrViolation"/>
            <xsd:enumeration value="suppressTcsiForVtcsi"/>
            <xsd:enumeration value="suppressCfuCheck"/>
            <xsd:enumeration value="reduceBarringCheckPriority"/>
            <xsd:enumeration value="reservedOption1"/>
            <xsd:enumeration value="reservedOption2"/>
            <xsd:enumeration value="reservedOption3"/>
            <xsd:enumeration value="reservedOption4"/>
            <xsd:enumeration value="reservedOption5"/>
            <xsd:enumeration value="reservedOption6"/>
            <xsd:enumeration value="reservedOption7"/>
            <xsd:enumeration value="reservedOption8"/>
            <xsd:enumeration value="reservedOption9"/>
            <xsd:enumeration value="reservedOption10"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- *************************************************************** -->
    <!--                               U-CSI service                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: NSR-U-CSI, U-CSI -->
    <xsd:complexType name="UCsi">
        <xsd:annotation>
            <xsd:documentation>Subscriber Specific Unstructured Camel Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="value" type="nsr:UCsiValue" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="valueExt" type="nsr:UCsiValueExt" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="UCsiValue">
        <xsd:annotation>
            <xsd:documentation>One UCSI value</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="shortCode" type="nsr:ServiceShortCode" minOccurs="0"/>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="UCsiValueExt">
        <xsd:annotation>
            <xsd:documentation>One UCSI value</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="shortCode" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <!-- FC122_003407 -->
                    <xsd:element name="origRefIndicator" type="nsr:HLRRefIndicatorType" minOccurs="0"/>
                    <xsd:element name="destRefIndicator" type="nsr:HLRRefIndicatorType" minOccurs="0"/>
                    <xsd:element name="ussdSendingOptions" type="nsr:HLRUssdSendingOptions" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- /FC122_003407 -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                     General Unstructured CSI service            -->
    <!-- *************************************************************** -->
    <!-- Original attribute: UG-CSI -->
    <xsd:complexType name="UgCsi">
        <xsd:annotation>
            <xsd:documentation>General Unstructured Camel Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: UG-CSI-Ext -->
    <xsd:complexType name="UgCsiExt">
        <xsd:annotation>
            <xsd:documentation>General Unstructured Camel Subscription Information</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <!-- FC122_003407 -->
                    <xsd:element name="origRefIndicator" type="nsr:HLRRefIndicatorType" minOccurs="0"/>
                    <xsd:element name="destRefIndicator" type="nsr:HLRRefIndicatorType" minOccurs="0"/>
                    <xsd:element name="ussdSendingOptions" type="nsr:HLRUssdSendingOptions" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- /FC122_003407 -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                            Voice Mail System                    -->
    <!-- *************************************************************** -->
    <!-- Original attribute: VMS -->
    <xsd:complexType name="VoiceMailSystem">
        <xsd:annotation>
            <xsd:documentation>
            Voice Mail System
            This class is allowed for the callback supplementary service and the basic service group speech only.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="directoryNumber" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="operatorName" type="xsd:string" minOccurs="0"/>
                    <!-- Directory number of the serving VMS
                           It is used by the network to route the diverted calls to the subscribers VMS.  -->
                    <!-- Original attribute: dirNumber-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!-- Visited mobile service switching centre Terminating CSI         -->
    <!-- *************************************************************** -->
    <!-- Original attribute: VT-CSI -->
    <xsd:complexType name="VtCsi">
        <xsd:annotation>
            <xsd:documentation>Visited mobile service switching centre Terminating CAMEL Subscription Information
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:VtReplacementHandlingForLUP" minOccurs="0"/>
                    <xsd:element name="sendingOptions" type="nsr:SendingOptionIsNotification" minOccurs="0" maxOccurs="11"/>
                    <xsd:element name="supportedCamelPhase" type="nsr:SupportedCamelPhase3and4" default="3" minOccurs="0" maxOccurs="2"/>
                    <xsd:element name="triggerDetectionPoint12" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint13" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <xsd:element name="triggerDetectionPoint14" type="nsr:TriggerDetectionPointDataTerminating" minOccurs="0"/>
                    <!-- TDP12:DP TERMINATING ATTEMPT AUTHORIZ. -->
                    <!-- TDP13:DP T BUSY -->
                    <!-- TDP14:DP T NO ANSWER -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Original attribute: vtRepl
           Original values:
                RRUSF == 1               ROAMING RESTRICTIONS DUE TO UNSUPPORTED FEATURE
                noReplaceReq == 2    NO REPLACE HANDLING
                repByBAIC == 3          REPLACE BY BAIC -->
    <xsd:simpleType name="VtReplacementHandlingForLUP">
        <xsd:annotation>
            <xsd:documentation>
            REPLACEMENT HANDLING for SMS-CSI determines,
            if the mobile subscriber must be barred if CAMEL not supported by VLR.
            This field contains the replacement indicator feature flag (RIFF).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="replaceByBarringOfIncommingCalls"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!--    HLR 4.5: Data Types for IN Proprietary  Data   -->
    <!-- OICK,  TICK  and  EOICK, EOINCI, ETICK, ETINCI -->
    <!-- *************************************************************** -->
    <!--                     IN Category Key Data                        -->
    <!-- *************************************************************** -->
    <!-- ***************          OICK,  TICK         *************** -->
    <xsd:complexType name="OICK">
        <xsd:annotation>
            <xsd:documentation>IN Category Key Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="key" type="nsr:ICKValue" minOccurs="0"/>
                    <xsd:element name="replacementForInterrogation" type="nsr:ReplacementHandlingForInterrogation" default="noReplacement" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:IckReplacementHandlingForLUP" default="noReplacement" minOccurs="0"/>
                    <!-- mandatory: key -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="TICK">
        <xsd:annotation>
            <xsd:documentation>IN Category Key Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="key" type="nsr:ICKValue" minOccurs="0"/>
                    <xsd:element name="replacementForInterrogation" type="nsr:ReplacementHandlingForInterrogation" default="noReplacement" minOccurs="0"/>
                    <xsd:element name="suppressTerminatingIN" type="xsd:boolean" minOccurs="0"/>
                    <!-- mandatory: key -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="ICKValue">
        <xsd:annotation>
            <xsd:documentation>
                IN Category Key Value Type.
                Value Range: 0..999
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:NumericString">
            <xsd:maxLength value="3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="IckReplacementHandlingForLUP">
        <xsd:annotation>
            <xsd:documentation>
                REPLACEMENT HANDLING for IN CAMEL Service,
                if the mobile subscriber must be barred if CAMEL not supported by VLR.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="roamingRestrictions"/>
            <xsd:enumeration value="noReplacement"/>
            <xsd:enumeration value="replaceByBarringOfOutgoingCalls"/>
            <xsd:enumeration value="replaceByBarringOfOutgoingIntCallsExceptToHome"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- **************           EOICK, ETICK         ************ -->
    <xsd:complexType name="ExtOICK">
        <xsd:annotation>
            <xsd:documentation>Extended CAMEL Originating IN Category Key Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:ExtICK"/>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ExtTICK">
        <xsd:annotation>
            <xsd:documentation>Extended CAMEL Terminating IN Category Key Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:ExtICK">
                <xsd:sequence>
                    <xsd:element name="suppressTerminatingIN" type="xsd:boolean" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ExtICK" abstract="true">
        <xsd:annotation>
            <xsd:documentation>IN Category Key Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="key" type="nsr:ExtICKValue" minOccurs="0"/>
                    <!-- mandatory: key -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="ExtICKValue">
        <xsd:annotation>
            <xsd:documentation>
                Extended CAMEL IN Category Key Value Type.
                Value Range: 1..999.
                Note: The value 0 shall be forbidden by validation function
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:NumericString">
            <xsd:maxLength value="3"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- **************        EOINCI, ETINCI       ************ -->
    <xsd:complexType name="ExtOINCI">
        <xsd:annotation>
            <xsd:documentation>Extended CAMEL Originating IN Capability Indicator Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:ExtINCI"/>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ExtTINCI">
        <xsd:annotation>
            <xsd:documentation>Extended CAMEL Terminating IN Capability Indicator Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:ExtINCI">
                <xsd:sequence>
                    <xsd:element name="suppressTerminatingIN" type="xsd:boolean" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ExtINCI" abstract="true">
        <xsd:annotation>
            <xsd:documentation>IN Capability Indicator Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="indicator" type="nsr:ExtINCIValue" minOccurs="0"/>
                    <!-- mandatory: indicator -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="ExtINCIValue">
        <xsd:annotation>
            <xsd:documentation>
                Extended CAMEL IN Capability Indicator Value Type
                Value Range: 1..255
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:unsignedInt">
            <xsd:minInclusive value="1"/>
            <xsd:maxInclusive value="255"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="UssdAdvt">
        <xsd:annotation>
            <xsd:documentation> Advertisements for USSD Services - VLRID, OwnMsisdn, ActualTime, LCN </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="advtData" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!--- FC122_000454: Roaming Dependent Subscriber Profile   Changes begin -->
    <xsd:complexType name="RoamSubscriptionInfo">
        <xsd:annotation>
            <xsd:documentation>
                 This object class defines the Roam Subscription Info profile attached to the subscriber.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="roamPlanInfo" type="nsr:RoamPlanInfo" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="RoamPlanInfo">
        <xsd:annotation>
            <xsd:documentation>
                            This object class maps a Roaming Plan to a particular entity number and type of message.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="entityNumber" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="refRoamPlanRouting" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="refRoamPlanStandard" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="RoamPlan">
        <xsd:annotation>
            <xsd:documentation>
                               This object class defines the Roaming Plan.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="accessRestr" type="nsr:AccessRestriction" minOccurs="0" maxOccurs="3"/>
                    <xsd:element name="ardTreatment" type="nsr:ArdTreatment" minOccurs="0"/>
                    <xsd:element name="categoryTreatment" type="nsr:CategoryTreatment" minOccurs="0"/>
                    <xsd:element name="category" type="nsr:Category" minOccurs="0"/>
                    <xsd:element name="netwAccMode" type="nsr:NetwAccModeType" minOccurs="0"/>
                    <xsd:element name="netwAccModeTreatment" type="nsr:NetwAccModeTreatmentType" minOccurs="0"/>
                    <xsd:element name="fwdChkSsTreatment" type="nsr:FwdChkSsTreatmentType" minOccurs="0"/>
                    <xsd:element name="picTreatment" type="nsr:PicTreatmentType" minOccurs="0"/>
                    <xsd:element name="regSubsDataTreatment" type="nsr:RegSubsDataTreatment" minOccurs="0"/>
                    <xsd:element name="pdpTreatment" type="nsr:PdpTreatmentType" minOccurs="0"/>
                    <xsd:element name="refqOfServName" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="matchQofServName" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="refbsPlanName" type="xsd:string" minOccurs="0" maxOccurs="15"/>
                    <xsd:element name="refssPlanName" type="xsd:string" minOccurs="0" maxOccurs="47"/>
                    <xsd:element name="refodbPlanName" type="xsd:string" minOccurs="0" maxOccurs="9"/>
                    <xsd:element name="refcsiPlanName" type="xsd:string" minOccurs="0" maxOccurs="21"/>
                   <!-- NTHLR6SP1: RDSP enhancements -Start-->
                    <xsd:element name="refPDPServicePlan" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="refCategoryServicePlan" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6SP1: RDSP enhancements -End-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="BSPlan">
        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the Basic Service related treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="sbasicService" type="nsr:BasicServiceTypes" minOccurs="0"/>
                    <xsd:element name="bsTreatment" type="nsr:BsTreatment" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="SSPlan">
        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the Supplementary Service related treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="ssId" type="nsr:SsIdType" minOccurs="0"/>
                    <xsd:element name="ssTreatment" type="nsr:SsTreatmentType" minOccurs="0"/>
                    <xsd:element name="basicServiceGroups" type="nsr:BasicServiceGroupsType" minOccurs="0" maxOccurs="6"/>
                    <xsd:element name="ssSubTreatment" type="nsr:SsSubTreatmentType" minOccurs="0"/>
                    <!-- 5.0 SP1: FC122_004037:RDSP enhancements-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ODBPlan">
        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the Operator Determined Barring related treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="odbId" type="nsr:OdbIdType" minOccurs="0"/>
                    <xsd:element name="odbTreatment" type="nsr:OdbTreatmentType" minOccurs="0"/>
                    <xsd:element name="odbValue" type="xsd:int" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="CSIPlan">
        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the CAMEL Subscription Information related treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="csiId" type="nsr:CsiIdType" minOccurs="0"/>
                    <xsd:element name="csiTreatment" type="nsr:CsiTreatmentType" minOccurs="0"/>
                    <xsd:element name="refCSIProfileName" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="matchCSIProfileName" type="xsd:string" minOccurs="0"/>
                    <!-- NTHLR6 SP1 -Start-->
                    <xsd:element name="refCSITreatment" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 SP1 -End-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="AccessRestriction">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute specifies allowed Access Restriction.
                                    Possible values:
                                    1 UTRAN Not Allowed
                                    2 GERAN Not Allowed
                                    3 E-UTRAN Not Allowed
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="UTRANNotAllowed"/>
            <xsd:enumeration value="GERANNotAllowed"/>
            <!-- FC122_006337 Access Restriction Data required E-UTRAN not allowed -->
            <xsd:enumeration value="E-UTRANNotAllowed" />
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="ArdTreatment">
        <xsd:annotation>
            <xsd:documentation>
                    This attribute indicates the treatment to be applied on ARD.
                    It can take the following values :
                          35- ns         not sent to vlr/sgsn
                          36- induce     assume the subscriber has the service
                          38- suppress   Suppress the Access Restriction data mentioned in accessRestr (if present in the subscriber profile.)
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="induce"/>
            <xsd:enumeration value="suppress"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="CategoryTreatment">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates the treatment to be applied on Category.
                            It can take one of the following values :
                            35 - ns
                            37 - replace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <!-- NTHLR6 SP1 -Start-->
            <xsd:enumeration value="ns"/>
            <!-- NTHLR6 SP1 -End-->
            <xsd:enumeration value="replace"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="Category">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute specifies the mobile subscriber category if the categoryTreatment is "replace".
                            This attribute only accepts a single value entry.
                            Category can take any value between 1 to 254.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:int">
            <xsd:minInclusive value="1"/>
            <xsd:maxInclusive value="254"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="NetwAccModeType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates the network access mode. This
                            attribute only accepts a single value entry.
                            This attribute indicates the network access method.
                            It can take either of the values :
                            0- None
                            1- GSM
                            2- GPRS
                            3- GSM and GPRS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="None"/>
            <xsd:enumeration value="GSM"/>
            <xsd:enumeration value="GPRS"/>
            <xsd:enumeration value="GSMGPRS"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="NetwAccModeTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates the network access mode treatment.  This attribute only accepts a single value entry.
                            It can take either of the values :
                            35- ns
                            38- suppress
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="suppress"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="FwdChkSsTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute only accepts a single value entry.
                            35 - ns
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="PicTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute only accepts a single value entry.
                            35 - ns
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="RegSubsDataTreatment">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute only accepts a single value entry.
                            23- ns, rna
                            32- s, rna
                            35- ns
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns_rna"/>
            <xsd:enumeration value="s_rna"/>
            <xsd:enumeration value="ns"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="PdpTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute defines the treatment for the GPRS PDP data.
                                    The possible values are :
                                    35- ns
                                    37- replace
                                    39- Conditional Replace
                                    40- Conditional Suppress
                                    46- Add
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="replace"/>
            <xsd:enumeration value="conditionalReplace"/>
            <!-- NTHLR6SP1: RDSP enhancements -Start-->
            <xsd:enumeration value="conditionalSuppress"/>
            <!-- NTHLR6SP1: RDSP enhancements -End-->
            <!-- RE123_107083 RDSP enhancements for PDP context data handling-Step 2 Add-->
            <xsd:enumeration value="add"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="BasicServiceTypes">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates the basic service. sbasicService can take the following values :
                            TS11 TS21 TS22 TS61 TS62 TS91 TS92 CDA CDS ALS PAD TS21GPRS TS22GPRS BS61A BS81A
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TS11"/>
            <xsd:enumeration value="TS21"/>
            <xsd:enumeration value="TS22"/>
            <xsd:enumeration value="TS61"/>
            <xsd:enumeration value="TS62"/>
            <xsd:enumeration value="VGCS"/>
            <xsd:enumeration value="VBS"/>
            <xsd:enumeration value="CDA"/>
            <xsd:enumeration value="CDS"/>
            <xsd:enumeration value="ALS"/>
            <xsd:enumeration value="PAD"/>
            <xsd:enumeration value="TS21GPRS"/>
            <xsd:enumeration value="TS22GPRS"/>
            <xsd:enumeration value="BS61A"/>
            <xsd:enumeration value="BS81A"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="BsTreatment">
        <xsd:annotation>
            <xsd:documentation>Treatment indicates the treatment to be applied on the bsId. It can take the following values :
                    17- cs
                    21- ns, raa
                    23- ns, rna
                    29- s, raa
                    32- s, rna
                    35- ns
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="cs"/>
            <xsd:enumeration value="ns_raa"/>
            <xsd:enumeration value="ns_rna"/>
            <xsd:enumeration value="s_raa"/>
            <xsd:enumeration value="s_rna"/>
            <xsd:enumeration value="ns"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="SsIdType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates the supplementary service. Supplementary Service Id can take one of the possible values:
                            65-CW, 66-CallHold, 177-universal, 178-callRel, 179-callUnRel, 180-plmnOperator,
                            193-basicSelfLc, 194-autoSelfLc, 195-transfToThirdPty, 241-HotBill, 25-CNAP, 17-CLIP, 18-CLIR, 19-COLP,
                            20 COLR, 81 MPTY, 113 AOCI, 114 AOCC, 97 CUG, 161 EMLPP, 257-272 -Natss01-Natss15, 129 UUS1, 246 UUS1, 33 CFU,
                            41 CFB, 42 CFNRY, 43 CFNRC, 49 ECT, 146 BAOC, 154 BAIC, 281  CFD-CFB, 282 CFD-CFNRC, 283 CFD-CFNRY

            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="callWait"/>
            <xsd:enumeration value="callHold"/>
            <xsd:enumeration value="universal"/>
            <xsd:enumeration value="callRel"/>
            <xsd:enumeration value="callUnRel"/>
            <xsd:enumeration value="plmnOperator"/>
            <xsd:enumeration value="basicSelfLocation"/>
            <xsd:enumeration value="autoSelfLocation"/>
            <xsd:enumeration value="transferToThirdParty"/>
            <xsd:enumeration value="natHotbill"/>
            <xsd:enumeration value="cnap"/>
            <xsd:enumeration value="clip"/>
            <xsd:enumeration value="clir"/>
            <xsd:enumeration value="colp"/>
            <xsd:enumeration value="colr"/>
            <xsd:enumeration value="mpty"/>
            <xsd:enumeration value="aoci"/>
            <xsd:enumeration value="aocc"/>
            <xsd:enumeration value="cug"/>
            <xsd:enumeration value="emlpp"/>
            <xsd:enumeration value="cfu"/>
            <xsd:enumeration value="cfb"/>
            <xsd:enumeration value="cfnry"/>
            <xsd:enumeration value="cfnrc"/>
            <xsd:enumeration value="cfdCfb"/>
            <xsd:enumeration value="cfdCfnrc"/>
            <xsd:enumeration value="cfdCfnry"/>
            <xsd:enumeration value="ect"/>
            <xsd:enumeration value="baoc"/>
            <xsd:enumeration value="baic"/>
            <xsd:enumeration value="boic"/>
            <xsd:enumeration value="boicExHC"/>
            <xsd:enumeration value="bicRoam"/>
            <xsd:enumeration value="stdUserSig1"/>
            <xsd:enumeration value="natUserSig1"/>
            <xsd:enumeration value="natss01"/>
            <xsd:enumeration value="natss02"/>
            <xsd:enumeration value="natss03"/>
            <xsd:enumeration value="natss04"/>
            <xsd:enumeration value="natss05"/>
            <xsd:enumeration value="natss06"/>
            <xsd:enumeration value="natss07"/>
            <xsd:enumeration value="natss08"/>
            <xsd:enumeration value="natss09"/>
            <xsd:enumeration value="natss10"/>
            <xsd:enumeration value="natss11"/>
            <xsd:enumeration value="natss12"/>
            <xsd:enumeration value="natss13"/>
            <xsd:enumeration value="natss14"/>
            <xsd:enumeration value="natss15"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="SsTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            ssTreatment indicates the treatment to be applied on the suppServiceId.
                            It can take the following values :
                            15 - cns,
                            16 - csu,
                            23 - ns, rna,
                            32 - s, rna
                            35 - ns,
                            36 - induce
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="cns"/>
            <xsd:enumeration value="csu"/>
            <xsd:enumeration value="ns_rna"/>
            <xsd:enumeration value="s_rna"/>
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="induce"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- 5.0 SP1: FC122_004037:RDSP enhancements:START -->
    <xsd:simpleType name="SsSubTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            ssSubTreatment indicates the treatment to be applied on the suppServiceId.
                            41 - clirOp_permanent (for CLIR only),
                            42 - clirOp_tempDefRestr (for CLIR only),
                            43 - clirOp_tempDefAllow (for CLIR only),
                            44 - cliOverride_enabled (for CLIP / CNAP and COLP only),
                            45 - cliOverride_disabled (for CLIP / CNAP and COLP only).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="clirOp_permanent"/>
            <xsd:enumeration value="clirOp_tempDefRestr"/>
            <xsd:enumeration value="clirOp_tempDefAllow"/>
            <xsd:enumeration value="cliOverride_enabled"/>
            <xsd:enumeration value="cliOverride_disabled"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- 5.0 SP1: FC122_004037:RDSP enhancements:END -->
    <xsd:simpleType name="BasicServiceGroupsType">
        <xsd:annotation>
            <xsd:documentation>
                            This attributes stores basic service group data. It indicates the BSVG on which barring is applicable.
                            It can take possible values:
                            TS10- allSpeechTransmissionServices
                            TS20- allShortMessageSevices
                            TS60- allFacsimileTransmissionServices
                            BS20- allDataCDAServices
                            BS30- allDataCDSServices
                            BS40- allPADAccess
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TS10"/>
            <xsd:enumeration value="TS20"/>
            <xsd:enumeration value="TS60"/>
            <xsd:enumeration value="BS20"/>
            <xsd:enumeration value="BS30"/>
            <xsd:enumeration value="BS40"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="OdbIdType">
        <xsd:annotation>
            <xsd:documentation>
                            This attribute indicates ODB value.
                            It can take one of the following values : BAOC BAIC BAROAM PREMIUM BASSM HPLMN POACCESS ECT
                            GPRS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="BAOC"/>
            <xsd:enumeration value="BAIC"/>
            <xsd:enumeration value="BAROAM"/>
            <xsd:enumeration value="PREMIUM"/>
            <xsd:enumeration value="BASSM"/>
            <xsd:enumeration value="HPLMN"/>
            <xsd:enumeration value="POACCESS"/>
            <xsd:enumeration value="ECT"/>
            <xsd:enumeration value="ODBROAMGPRS"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="OdbTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            Treatment to be applied on ODB.  It can take one of the possible values:
                                23 - ns,  rna
                            25 - s, del
                            31 - s,res
                            32 - s,rna
                            33 - tus, del
                            34 - tus, res
                            35 - ns
                            36 - induce
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns_rna"/>
            <xsd:enumeration value="s_del"/>
            <xsd:enumeration value="s_res"/>
            <xsd:enumeration value="s_rna"/>
            <xsd:enumeration value="tus_del"/>
            <xsd:enumeration value="tus_res"/>
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="induce"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="CsiIdType">
        <xsd:annotation>
            <xsd:documentation>
                            Camel Subscription specifies the Camel of which treatment is applied.
                            It can take the following values :
                            OCSI TCSI SSCSI GPRSCSI VTCSI SMSCSI MCSI DCSI MTSMSCSI MGCSI UCSI OICK TICK TIFCSI SSET EMOICK
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OCSI"/>
            <xsd:enumeration value="TCSI"/>
            <xsd:enumeration value="SSCSI"/>
            <xsd:enumeration value="GPRSCSI"/>
            <xsd:enumeration value="VTCSI"/>
            <xsd:enumeration value="SMSCSI"/>
            <xsd:enumeration value="MCSI"/>
            <xsd:enumeration value="DCSI"/>
            <xsd:enumeration value="MTSMSCSI"/>
            <xsd:enumeration value="MGCSI"/>
            <xsd:enumeration value="UCSI"/>
            <xsd:enumeration value="OICK"/>
            <xsd:enumeration value="TICK"/>
            <xsd:enumeration value="TIFCSI"/>
            <xsd:enumeration value="SSET"/>
            <xsd:enumeration value="EMOICK"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="CsiTreatmentType">
        <xsd:annotation>
            <xsd:documentation>
                            CsiTreatmentType indicates the treatment to be applied.
                            It can take the following values :
                            19 - ns, nsusgr
                            18 - ns, nsusgh
                            20 - ns, nsusms
                            21 - ns, raa
                            22 - ns, raalt
                            23 - ns, rna
                            24 - ns, rnscnc
                            26 - s, nsusgh
                            27 - s, nsusgr
                            28 - s, nsusms
                            30 - s, raalt
                            32 - s, rna
                            35 - ns
                            36 - induce
                            39 - Conditional Replace
                            40 - Conditional suppress
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ns_nsusgr"/>
            <xsd:enumeration value="ns_nsusgh"/>
            <xsd:enumeration value="ns_nsusms"/>
            <xsd:enumeration value="ns_raa"/>
            <xsd:enumeration value="ns_raalt"/>
            <xsd:enumeration value="ns_rna"/>
            <xsd:enumeration value="ns_rnscnc"/>
            <xsd:enumeration value="s_nsusgh"/>
            <xsd:enumeration value="s_nsusgr"/>
            <xsd:enumeration value="s_nsusms"/>
            <xsd:enumeration value="s_raalt"/>
            <xsd:enumeration value="s_rna"/>
            <xsd:enumeration value="ns"/>
            <xsd:enumeration value="induce"/>
            <xsd:enumeration value="conditionalReplace"/>
            <xsd:enumeration value="conditionalSuppress"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!--- FC122_00045FC122_000454: Roaming Dependent Subscriber Profile Changes End -->
    <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
    <xsd:complexType name="GMLC">
        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the GMLC data which serves as NSR data for Spatial Trigger
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="gmlcAddress" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                               PROP-IN-SMS service                     -->
    <!-- *************************************************************** -->
    <!-- Original attribute: PROP-IN-SMS -->
    <xsd:complexType name="INSMS">
        <xsd:annotation>
            <xsd:documentation>Prop. IN Short Message Service</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="availability" type="nsr:ServiceAvailability" default="noWhere" minOccurs="0"/>
                    <xsd:element name="gsmServiceControlFunctionAddress" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="serviceKey" type="nsr:ServiceKey" minOccurs="0"/>
                    <xsd:element name="triggeringAllMultipleMsg" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="detectionPntStatusReport" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="detectionPntMobOriginating" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="detectionPntMobTerminating" type="xsd:boolean" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- FC122_004031: CAMEL Core INAP Interworking -->
    <xsd:complexType name="HLRSSetProfile">
        <xsd:annotation>
            <xsd:documentation>SSET type</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="inProfile" type="nsr:HlrInProfile" minOccurs="0"/>
                    <xsd:element name="ssetTrigger" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:OReplacementHandlingForLUP" minOccurs="0"/>
                    <xsd:element name="replacementForInterrogation" type="nsr:HlrCCInapReplHandlingForInterrogation" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="HlrInProfile">
        <xsd:annotation>
            <xsd:documentation>
            Inprofile values
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="full"/>
            <xsd:enumeration value="limited"/>
            <xsd:enumeration value="locationDependent"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="HlrCCInapReplHandlingForInterrogation">
        <xsd:annotation>
            <xsd:documentation>
            Replacement handling for Interrogation.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="replaceByBAIC"/>
            <xsd:enumeration value="noReplacement"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="HLREMOICKProfile">
        <xsd:annotation>
            <xsd:documentation>Emoick</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="emoick" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="replacementForLocationUpdate" type="nsr:OReplacementHandlingForLUP" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- *************************************************************** -->
    <!--                              FC122_002113_SMSImprovements                   -->
    <!-- *************************************************************** -->
    <!-- Original attribute: FC122_002113_SMSImprovements -->
    <xsd:complexType name="HLRAddressList">
        <xsd:annotation>
            <xsd:documentation>set of addresses for LSG (load sharing groups)</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="isdnAddress" type="nsr:TelephoneNumber" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
                    <xsd:element name="addressWithWeight" type="nsr:HLRAddressWithWeight" minOccurs="0" maxOccurs="32"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRSmsPriorityList">
        <xsd:annotation>
            <xsd:documentation>SMS Priority list for sms framework</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="feature" type="nsr:HLRSmsFeature" maxOccurs="9"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRSmsFeature">
        <xsd:annotation>
            <xsd:documentation>Priority list for sms framework</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="featurePriority" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="featureType" type="nsr:HLRSmsFeatureType" minOccurs="0"/>
                    <xsd:element name="refLsgList" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="HLRSmsFeatureType">
        <xsd:annotation>
            <xsd:documentation>
            List of SMS feature names supported
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="3gpp_router"/>
            <xsd:enumeration value="nsn_router"/>
            <xsd:enumeration value="3gpp_normal_enh1"/>
            <xsd:enumeration value="no_tel_enh2"/>
            <xsd:enumeration value="real_imsi_conf_vmsc_enh3"/>
            <xsd:enumeration value="fix_imsi_fix_vmsc_enh4"/>
            <xsd:enumeration value="ip_sm_gw"/>
            <xsd:enumeration value="mss_multisim_ih"/>
            <xsd:enumeration value="sms_prio_cond_overwrite"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!--  FC122_003912 - Flexible FTNO shot code convertions                      -->
    <!--************************************************************************-->
    <xsd:complexType name="HLRNbrRule">
        <xsd:annotation>
            <xsd:documentation>
                An atomic step of processing. Each rule applies a specific regular expression to a current string and
                then invokes one of two outcomes depending on whether a match was found or not. The outcome can
                perform a number of modifications, extract a number of tokens, and finally invoke another rule,
                another rule set or finish processing.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="matchType" type="nsr:HLRNbrRuleInputType" minOccurs="0"/>
                    <xsd:element name="matchValue" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="nbrMatch" type="nsr:HLRNbrBehaviour" minOccurs="0"/>
                    <xsd:element name="nbrNonMatch" type="nsr:HLRNbrBehaviour" minOccurs="0"/>
                    <xsd:element name="defaultTokenList" type="nsr:HLRNbrDefaultTokenList" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="tokenSetCriteria" type="nsr:HLRNbrTokenSetCriteria" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrRuleAction">
        <xsd:annotation>
            <xsd:documentation>
                        Defines the list of modifications/ actions to be performed if the regular expression matched or
                        did not match the input string.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="modificationTarget" type="nsr:HLRNbrOperationTarget" minOccurs="0"/>
                    <xsd:element name="modificationType" type="nsr:HLRNbrOperationType" minOccurs="0"/>
                    <xsd:element name="charCount" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="textString" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrNextStep">
        <xsd:annotation>
            <xsd:documentation>
                        Defines the next steps to execute either if the regular expression matched / did not match the
                        input string.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="type" type="nsr:HLRNbrNextStepType" minOccurs="0"/>
                    <xsd:element name="refNextRuleSetOrRuleId" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="result" type="nsr:HLRNbrNextStepResult" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrDefaultTokenList">
        <xsd:annotation>
            <xsd:documentation>
                        Holds a list of default tokens with values.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="name" type="nsr:PrintableString40" minOccurs="0"/>
                    <xsd:element name="value" type="nsr:PrintableString40" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrBehaviour">
        <xsd:annotation>
            <xsd:documentation> Defines the behaviour for a rule on match or non-match </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="ruleAction" type="nsr:HLRNbrRuleAction" minOccurs="0"/>
                    <xsd:element name="nextStep" type="nsr:HLRNbrNextStep" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrTokenSetCriteria">
        <xsd:annotation>
            <xsd:documentation>
                        Describes how to set the tokens while parsing a string, when a regular expression is matched.
                        The token is set prior to any modifications being applied.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="tokenCriteriaName" type="nsr:PrintableString40" minOccurs="0"/>
                    <xsd:element name="tokenTarget" type="nsr:HLRNbrOperationTarget" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- Simple Types -->
    <xsd:simpleType name="HLRNbrRuleInputType">
        <xsd:annotation>
            <xsd:documentation>Rule Input Type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="string"/>
            <xsd:enumeration value="token"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="HLRNbrOperationTarget">
        <xsd:annotation>
            <xsd:documentation>Defines the target on which the rule modifcation needs to be applied
       whole - this option can be useful for replacing a shortcode such as 123 with a subscriber-specific number, e.g.
       the basic MSISDN.

       match - the matched string
       prematch -  the substring that precedes the matched string
       postmatch - the substring that follows the matched string
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="whole"/>
            <xsd:enumeration value="match"/>
            <xsd:enumeration value="prematch"/>
            <xsd:enumeration value="postmatch"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="HLRNbrOperationType">
        <xsd:annotation>
            <xsd:documentation>
          Defines the modification to be applied on the ModificationTarget
          delete - delete the modification target
          predelete - deletes a number of characters from the start of the modification target.
          postdelete - deletes a number of characters from the end of the modification target.
          replace - replaces the modification target with a string
          prefix - inserts a string before the modification target.
          postfix - inserts a string after the modification target.
          replacewithtoken - replaces the modification target with a token.
          preaddtoken - inserts a token before the modification target.
          postaddtoken - inserts a token after the modification target.

            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="delete"/>
            <xsd:enumeration value="predelete"/>
            <xsd:enumeration value="postdelete"/>
            <xsd:enumeration value="replace"/>
            <xsd:enumeration value="prefix"/>
            <xsd:enumeration value="postfix"/>
            <xsd:enumeration value="replaceWithToken"/>
            <xsd:enumeration value="preAddToken"/>
            <xsd:enumeration value="postAddToken"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="HLRNbrNextStepType">
        <xsd:annotation>
            <xsd:documentation>This attribute indicates whether to finish the current rule or to go to next rule
            or ruleSet.
            This attribute is one of "finish", "rule" or "ruleset".
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="finish"/>
            <xsd:enumeration value="rule"/>
            <xsd:enumeration value="ruleSet"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="HLRNbrNextStepResult">
        <xsd:annotation>
            <xsd:documentation>
        This attribute indicates the result to return if nextStepType is set to "finish", either "success" or "failure".
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="success"/>
            <xsd:enumeration value="failure"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="HLRNbrRuleSet">
        <xsd:annotation>
            <xsd:documentation>
             Organizes translation rules. It is invoked via HLRNbrTranslation object processing, or as the outcome of
             another ruleSet or rule.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="maxRuleCount" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="firstRuleId" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNbrTranslation">
        <xsd:annotation>
            <xsd:documentation>
             This complex type is used to map one or more HLRNBRTranslationInfo entries to a nbrContextType.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="nbrTranslationInfo" type="nsr:HLRNBRTranslationInfo" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRNBRTranslationInfo">
        <xsd:annotation>
            <xsd:documentation>
                Maps a nbrTransIndex to a particular nbrRuleSetName .
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="nbrTransIndex" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="refRuleSetName" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="PrintableString40">
        <xsd:annotation>
            <xsd:documentation>Printable String of length 1 .. 40</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="nsr:PrintableString">
            <xsd:maxLength value="40"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="PrintableString">
        <xsd:annotation>
            <xsd:documentation>Printable String with '@_;' characters. This type is used for names and labels</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[a-zA-Z\d '()+,-./:=?@_;]+"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- NTHLR6 - Introduced for UGCSI Feature: Start-->
    <xsd:simpleType name="HLRRefIndicatorType">
        <xsd:annotation>
            <xsd:documentation>Enumeration of valid values for Reference Indicator</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="IMSI"/>
            <xsd:enumeration value="MSISDN"/>
            <xsd:enumeration value="HLR-NUM"/>
            <xsd:enumeration value="VLR-NUM"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- FC122_003407 -->
    <xsd:simpleType name="HLRUssdSendingOptions">
        <xsd:annotation>
            <xsd:documentation>Set of options that control the sending of parameters in USSD messages</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Send MSISDN in OEN in BSA"/>
            <xsd:enumeration value="Send HLR NUM in OEN in BSA"/>
            <xsd:enumeration value="Send VLR NUM in OEN in BSA"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- FC122_003407 -->
    <!-- NTHLR6 - Introduced for UGCSI Feature: End-->
    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
    <xsd:complexType name="HLRISDNNumberWithPlmnId">
        <xsd:annotation>
            <xsd:documentation> This complex type holds information about ISDN roaming area and its associated PLMN </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="isdnNumber" type="nsr:NumericString" minOccurs="0"/>
                    <xsd:element name="plmnId" type="xsd:int" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: End-->
    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
    <xsd:complexType name="HLRAddressWithWeight">
        <xsd:annotation>
            <xsd:documentation> Address with weight. </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="address" type="nsr:TelephoneNumber" minOccurs="0"/>
                    <xsd:element name="weight" type="xsd:int" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
<!-- NTHLR6SP1: RDSP enhancements -Start-->

    <xsd:complexType name="HLRTreatment">
        <xsd:annotation>
            <xsd:documentation>This abstract FCO is base class for HLR Treatement FCOs. </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
           <xsd:extension base="spml:FirstClassObject">
                    <xsd:sequence>
                        <xsd:element name="refProfileName" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="matchProfileName" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="HLRCSITreatment">
        <xsd:annotation>
            <xsd:documentation>
                          This object class defines set of extended RDSP CAMEL treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:HLRTreatment">
                <xsd:sequence>

                    <xsd:element name="treatment" type="nsr:CsiTreatmentType" minOccurs="0"/>


                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="HLRCategoryTreatment">
        <xsd:annotation>
            <xsd:documentation>
                          This object class defines set of Category treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>

                    <xsd:element name="treatment" type="nsr:CategoryTreatment" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRPSTreatment">
        <xsd:annotation>
            <xsd:documentation>
                          This object class defines set of RDSP treatments for PDP and QOS data.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="nsr:HLRTreatment">
                <xsd:sequence>

                    <xsd:element name="treatment" type="nsr:PdpTreatmentType" minOccurs="0"/>


                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="HLRServicePlan">

        <xsd:annotation>
            <xsd:documentation>
                           This object class defines the PDP, Category related treatments.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="type" type="nsr:HLRServicePlanIdType" minOccurs="0"/>
                    <xsd:element name="refTreatment" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="HLRServicePlanIdType">
        <xsd:annotation>
            <xsd:documentation>
                            It can take the following values :
                            PDP RC ExtRC

            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PDP"/>

            <xsd:enumeration value="RC"/>
            <xsd:enumeration value="ExtRC"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="HLRPDPContext">
        <xsd:annotation>
            <xsd:documentation> PDPContext </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="type" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="apn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="apnArea" type="nsr:HLRPDPAccessPointNameAreaType" minOccurs="0"/>
                    <xsd:element name="chargingCharacteristicsProfile" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="chargingCharacteristicsBehavior" type="xsd:int" minOccurs="0"/>
                    <xsd:element name="extType" type="xsd:int" minOccurs="0"/>
                    <!-- RE123_107083 RDSP enhancements for PDP context data handling-Step 2 Add-->
                    <xsd:element name="pdpContextId" type="xsd:unsignedInt" minOccurs="0"/>
                    <xsd:element name="refqOfServName" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="HLRPDPAccessPointNameAreaType">
        <xsd:annotation>
            <xsd:documentation>Enumeration of valid values for PDPAccessPointNameArea</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HPLMN"/>
            <xsd:enumeration value="ALLPLMN"/>
        </xsd:restriction>
    </xsd:simpleType>
    <!-- NTHLR6SP1: RDSP enhancements -End-->

    <!-- FC122_004142: Subscriber fraud detection & limitation Start-->
    <!-- *************************************************************** -->
    <!-- HLR Fraud Profile -->
    <!-- *************************************************************** -->
    <!-- Original attribute: Fraud Profile -->


    <xsd:complexType name="HLRFraudProfile">
        <xsd:annotation>
            <xsd:documentation>Fraud Profile</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="moCallProfileInfo" type="nsr:HLRFraudProfileTimerInfo"
                        minOccurs="0" />
                    <xsd:element name="cfProfileInfo" type="nsr:HLRFraudProfileTimerInfo"
                        minOccurs="0" />
                    <xsd:element name="ctProfileInfo" type="nsr:HLRFraudProfileTimerInfo"
                        minOccurs="0" />
                    <xsd:element name="ctInvocationProfileInfo" type="nsr:HLRFraudProfileLimitInfo"
                        minOccurs="0" />
                    <!-- NTHLR7SP1: FC122_005795 - VF CSDB Subscriber fraud observation - phase 2 (CFR) :: start -->
                    <xsd:element name="cfFraudObservationInfo" type="nsr:HLRCFFraudObservationInfo"
                        minOccurs="0" />
                    <!-- NTHLR7SP1: FC122_005795 - VF CSDB Subscriber fraud observation - phase 2 (CFR) :: end -->
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:complexType name="HLRFraudProfileTimerInfo">
        <xsd:annotation>
            <xsd:documentation> This complex type holds information about HLR
                Fraud Profile TimerInfo
   </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="timer" type="xsd:int" minOccurs="0" />
                    <xsd:element name="action" type="nsr:HLRActionType"
                        minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:complexType name="HLRFraudProfileLimitInfo">
        <xsd:annotation>
            <xsd:documentation> This complex type holds information about HLR
                Fraud Profile LimitInfo
        </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="maxAllowed" type="xsd:int"
                        minOccurs="0" />
                    <xsd:element name="action" type="nsr:HLRActionType"
                        minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:simpleType name="HLRActionType">
        <xsd:annotation>
            <xsd:documentation>Enumeration of valid values for HLR Action Type
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Report" />
            <xsd:enumeration value="Reject" />
            <xsd:enumeration value="ReportAndReject" />
        </xsd:restriction>
    </xsd:simpleType>
    <!-- FC122_004142: Subscriber fraud detection & limitation End -->

    <!-- NTHLR7SP1: FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: start -->
    <xsd:complexType name="HLRCFFraudObservationInfo">
        <xsd:annotation>
            <xsd:documentation>
                Attributes to support CF registration parameters.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:SecondClassObject">
                <xsd:sequence>
                    <xsd:element name="cfrMax" type="xsd:int" minOccurs="0" />
                    <xsd:element name="cfrAction" type="nsr:HLRCFActionType" minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="HLRCFActionType">
        <xsd:annotation>
            <xsd:documentation>Enumeration of valid values for HLR CF Action Type
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Report" />
            <xsd:enumeration value="Reject" />
            <xsd:enumeration value="ReportAndReject" />
            <xsd:enumeration value="NESpecificAction" />
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="HLRFraudObservation">
        <xsd:annotation>
            <xsd:documentation>HLR Fraud Observation</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="state" type="nsr:HLRFraudObsState" minOccurs="0" />
                    <xsd:element name="fraudAction" type="nsr:HLRActionType" minOccurs="0" />
                    <xsd:element name="maxFraudEvents" type="xsd:int" minOccurs="0" />
                    <xsd:element name="samplingPeriod" type="xsd:int" minOccurs="0" />
                    <xsd:element name="observationNumber" type="xsd:int" minOccurs="0" />
                    <xsd:element name="observationStartTimestamp" type="xsd:dateTime" minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="HLRFraudObsState">
        <xsd:annotation>
            <xsd:documentation>Enumeration of valid values for HLR fraud observation type
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Start" />
            <xsd:enumeration value="Stop" />
        </xsd:restriction>
    </xsd:simpleType>
    <!-- NTHLR7SP1: FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: end -->
    <!-- FC123_106922_CF_Notification_Override - Start -->
    <xsd:complexType name="HLRFTNOProfile">
    <xsd:annotation>
        <xsd:documentation>HLR FTNO Profile</xsd:documentation>
    </xsd:annotation>
    <xsd:complexContent>
        <xsd:extension base="spml:FirstClassObject">
            <xsd:sequence>
                <xsd:element name="refHlrFtnoDataNames" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:extension>
    </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="HLRFTNOData">
        <xsd:annotation>
            <xsd:documentation>HLR FTNO Data</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="spml:FirstClassObject">
                <xsd:sequence>
                    <xsd:element name="ftno" type="nsr:ForwardedToNumber" minOccurs="0"/>
                    <xsd:element name="profileCfMax" type="xsd:int" minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <!-- FC123_106922_CF_Notification_Override - End -->
</xsd:schema>
