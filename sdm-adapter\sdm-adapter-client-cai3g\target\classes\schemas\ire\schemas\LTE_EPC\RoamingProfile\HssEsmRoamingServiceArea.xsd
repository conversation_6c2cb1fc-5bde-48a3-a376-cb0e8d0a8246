<!-- Customer Adaptation, NumberSeriesAnalysis -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/HSS/"
           xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://schemas.ericsson.com/ma/HSS/"
           elementFormDefault="qualified" attributeFormDefault="unqualified"
           jaxb:version="2.0">
    <xs:include schemaLocation="./types/rp_types.xsd"/>

    <!-- CAI3G MOId type definitions. -->
    <xs:element name="EsmDefinedAreaRaid" type="EsmDefinedAreaRaidType"/>
    <xs:element name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType"/>
    <xs:element name="SRR" type="SRRType"/>
    <xs:element name="erase" type="eraseType"/>
    <xs:element name="EsmNonDefinedArea" type="EsmNonDefinedAreaType"/>
    <xs:element name="frontendid" type="hlrfeidType"/>

    <xs:element name="SetHssEsmRoamingServiceArea">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmDefinedAreaRaid" type="EsmDefinedAreaRaidType"/>
                <xs:element name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType"/>
                <xs:element name="SRR" type="SRRType"/>
                <xs:element name="erase" type="eraseType" minOccurs="0"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="EsmDefinedAreaRaid" type="EsmDefinedAreaRaidType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmDefinedAreaRaid attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmRoamingServiceAreaId attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="SRR" type="SRRType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="SRR attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetHssEsmRoamingServiceArea">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="HssEsmRoamingServiceAreaData">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="HssEsmRoamingServiceArea" type="EsmRoamingServiceAreaIdType"/>
                <xs:element name="HssEsmDefinedAreaList"/>
                <xs:element name="HssEsmNonDefinedArea"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="HssEsmDefinedAreaList">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="HssEsmDefinedArea" minOccurs="0"  type="HssEsmDefinedAreaType"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="HssEsmNonDefinedArea">
        <xs:simpleType>
            <xs:restriction base="xs:integer" />
        </xs:simpleType>
    </xs:element>

    <xs:element name="DeleteHssEsmRoamingServiceArea">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmRoamingServiceAreaId attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateHssEsmRoamingServiceArea">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType"/>
                <xs:element name="EsmNonDefinedArea" type="EsmNonDefinedAreaType"/>
                <xs:element name="frontendid" type="hlrfeidType" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="EsmRoamingServiceAreaId" type="EsmRoamingServiceAreaIdType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmRoamingServiceAreaId attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="EsmNonDefinedArea" type="EsmNonDefinedAreaType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="EsmNonDefinedArea attribute"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

</xs:schema>
