<xs:schema xmlns="http://schemas.ericsson.com/ma/SAPC/" xmlns:x="http://schemas.ericsson.com/ma/SAPC/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://schemas.ericsson.com/ma/SAPC/" elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:version="2.0">
	<xs:include schemaLocation="types/sapcla_types.xsd"/>
	<xs:element name="pcFamilyId" type="pcFamilyIdType"/>
	<xs:element name="CreateSAPCFamily">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcFamilyId" type="pcFamilyIdType"/>
				<xs:element name="pcFamilyDescription" type="pcFamilyDescriptionType" minOccurs="0"/>
				<xs:element name="pcAccumulatedData" type="pcAccumulatedDataType" minOccurs="0"/>
				<xs:element name="pcGroup" minOccurs="1" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcGroupId" type="pcGroupIdType"/>
							<xs:element name="pcGroupPriority" type="pcGroupPriorityType" minOccurs="0"/>
							<xs:element name="pcGroupStartDate" type="pcGroupStartDateType" minOccurs="0"/>
							<xs:element name="pcGroupEndDate" type="pcGroupEndDateType" minOccurs="0"/>
							<xs:element name="pcGroupInstancesContracted" type="pcGroupInstancesContractedType" minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcGroupIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_create_pcGroupId">
						<xs:selector xpath="."/>
						<xs:field xpath="@pcGroupId"/>
					</xs:key>
					<xs:keyref name="keyref_create_pcGroupId" refer="key_create_pcGroupId">
						<xs:selector xpath="./x:pcGroupId"/>
						<xs:field xpath="."/>
					</xs:keyref>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcFamilyId" type="pcFamilyIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcFamilyIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_create_pcFamilyId">
			<xs:selector xpath="."/>
			<xs:field xpath="@pcFamilyId"/>
		</xs:key>
		<xs:keyref name="keyref_create_pcFamilyId" refer="key_create_pcFamilyId">
			<xs:selector xpath="./x:pcFamilyId"/>
			<xs:field xpath="."/>
		</xs:keyref>
	</xs:element>
	<xs:element name="GetResponseSAPCFamily">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcFamilyId" type="pcFamilyIdType"/>
				<xs:element name="pcFamilyDescription" type="pcFamilyDescriptionType" minOccurs="0"/>
				<xs:element name="pcAccumulatedData" type="pcAccumulatedDataType" minOccurs="0"/>
				<xs:element name="pcGroup" minOccurs="1" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcGroupId" type="pcGroupIdType"/>
							<xs:element name="pcGroupPriority" type="pcGroupPriorityType" minOccurs="0"/>
							<xs:element name="pcGroupStartDate" type="pcGroupStartDateType" minOccurs="0"/>
							<xs:element name="pcGroupEndDate" type="pcGroupEndDateType" minOccurs="0"/>
							<xs:element name="pcGroupInstancesContracted" type="pcGroupInstancesContractedType" minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcGroupIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="pcFamilyId" type="pcFamilyIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcFamilyIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
	<xs:element name="SetSAPCFamily">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="pcFamilyDescription" type="pcFamilyDescriptionType" nillable="true" minOccurs="0"/>
				<xs:element name="pcAccumulatedData" type="pcAccumulatedDataType" nillable="true" minOccurs="0"/>
				<xs:element name="pcGroup" nillable="true" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="pcGroupId" type="pcGroupIdType" minOccurs="0"/>
							<xs:element name="pcGroupPriority" type="pcGroupPriorityType" nillable="true" minOccurs="0"/>
							<xs:element name="pcGroupStartDate" type="pcGroupStartDateType" nillable="true" minOccurs="0"/>
							<xs:element name="pcGroupEndDate" type="pcGroupEndDateType" nillable="true" minOccurs="0"/>
							<xs:element name="pcGroupInstancesContracted" type="pcGroupInstancesContractedType" nillable="true" minOccurs="0" />
						</xs:sequence>
						<xs:attribute name="pcGroupId" type="pcGroupIdType" use="required">
							<xs:annotation>
								<xs:appinfo>
									<jaxb:property name="pcGroupIdAttr" />
								</xs:appinfo>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
					<xs:key name="key_set_pcGroupId">
						<xs:selector xpath="."/>
						<xs:field xpath="@pcGroupId"/>
					</xs:key>
					<xs:keyref name="keyref_set_pcGroupId" refer="key_set_pcGroupId">
						<xs:selector xpath="./x:pcGroupId"/>
						<xs:field xpath="."/>
					</xs:keyref>
				</xs:element>
				
			</xs:sequence>
			<xs:attribute name="pcFamilyId" type="pcFamilyIdType" use="required">
				<xs:annotation>
					<xs:appinfo>
						<jaxb:property name="pcFamilyIdAttr"/>
					</xs:appinfo>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
		<xs:key name="key_set_pcFamilyId">
			<xs:selector xpath="."/>
			<xs:field xpath="@pcFamilyId"/>
		</xs:key>
	</xs:element>
</xs:schema>
