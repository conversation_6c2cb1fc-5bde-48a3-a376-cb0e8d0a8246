<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions 
	name="PROV_GW_SPML_Service" targetNamespace="urn:siemens:names:prov:gw:HLR_NSR:2:1:wsdl"
	xmlns="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:soap-env="http://schemas.xmlsoap.org/soap/envelope/"
	xmlns:soap-enc="http://schemas.xmlsoap.org/soap/encoding/"
	xmlns:tns="urn:siemens:names:prov:gw:HLR_NSR:2:1:wsdl"
	xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0"
	xmlns:nsr="urn:siemens:names:prov:gw:HLR_NSR:2:1">

	<!-- TYPE SECTION: BEGIN -->
	<wsdl:types>
		<xsd:schema 
				targetNamespace="urn:siemens:names:prov:gw:HLR_NSR:2:1:wsdl"
  				xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
  				xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"
  				xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  				xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  				xmlns="http://www.w3.org/2001/XMLSchema"
  				elementFormDefault="unqualified"
  				attributeFormDefault="unqualified">
            	<xsd:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
            	<xsd:import namespace="urn:siemens:names:prov:gw:HLR_NSR:2:1" schemaLocation="hlr_nsr-2.1.xsd"/>
        </xsd:schema>        
	</wsdl:types>
	<!-- TYPE SECTION: END -->
	
	<!-- MESSAGE SECTION: BEGIN -->

	<wsdl:message name="SPMLBatchRequestMessage">
		<wsdl:part name="body" element="spml:batchRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLBatchResponseMessage"> 
		<wsdl:part name="body" element="spml:batchResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLAddRequestMessage">
		<wsdl:part name="body" element="spml:addRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLAddResponseMessage">
		<wsdl:part name="body" element="spml:addResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLModifyRequestMessage">
		<wsdl:part name="body" element="spml:modifyRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLModifyResponseMessage">
		<wsdl:part name="body" element="spml:modifyResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLDeleteRequestMessage">
		<wsdl:part name="body" element="spml:deleteRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLDeleteResponseMessage">
		<wsdl:part name="body" element="spml:deleteResponse" />
	</wsdl:message>
	
	<wsdl:message name="SPMLCancelRequestMessage">
		<wsdl:part name="body" element="spml:cancelRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLCancelResponseMessage">
		<wsdl:part name="body" element="spml:cancelResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLSearchRequestMessage">
		<wsdl:part name="body" element="spml:searchRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLSearchResponseMessage">
		<wsdl:part name="body" element="spml:searchResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLExtendedRequestMessage">
		<wsdl:part name="body" element="spml:extendedRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLExtendedResponseMessage">
		<wsdl:part name="body" element="spml:extendedResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLStatusRequestMessage">
		<wsdl:part name="body" element="spml:statusRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLStatusResponseMessage">
		<wsdl:part name="body" element="spml:statusResponse" />
	</wsdl:message>

	<wsdl:message name="SPMLChangeIdRequestMessage">
		<wsdl:part name="body" element="spml:changeIdRequest" />
	</wsdl:message>

	<wsdl:message name="SPMLChangeIdResponseMessage">
		<wsdl:part name="body" element="spml:changeIdResponse" />
	</wsdl:message>

	<!-- MESSAGE SECTION: END -->

	<!-- PORT TYPE SECTION: BEGIN -->
	<wsdl:portType name="SPMLHlrNsr21PortType">
         	<wsdl:operation name="batch">
            		<wsdl:input name="SPMLBatchRequestInput" message="tns:SPMLBatchRequestMessage"/> 
		    	<wsdl:output name="SPMLBatchRequestOutput" message="tns:SPMLBatchResponseMessage"/>
	    	</wsdl:operation>

	        <wsdl:operation name="add">
		    	<wsdl:input name="SPMLAddRequestInput" message="tns:SPMLAddRequestMessage"/>
		    	<wsdl:output name="SPMLAddRequestOutput" message="tns:SPMLAddResponseMessage"/>
        	</wsdl:operation>

		<wsdl:operation name="modify">
			<wsdl:input name="SPMLModifyRequestInput" message="tns:SPMLModifyRequestMessage"/> 
			<wsdl:output name="SPMLModifyRequestOutput" message="tns:SPMLModifyResponseMessage"/>
		</wsdl:operation>

		<wsdl:operation name="delete">
			<wsdl:input name="SPMLDeleteRequestInput" message="tns:SPMLDeleteRequestMessage"/> 
			<wsdl:output name="SPMLDeleteRequestOutput" message="tns:SPMLDeleteResponseMessage"/> 
		</wsdl:operation> 

		<wsdl:operation name="cancel">
			<wsdl:input name="SPMLCancelRequestInput" message="tns:SPMLCancelRequestMessage"/> 
			<wsdl:output name="SPMLCancelRequestOutput" message="tns:SPMLCancelResponseMessage"/> 
		</wsdl:operation> 

		<wsdl:operation name="search"> 
			<wsdl:input name="SPMLSearchRequestInput" message="tns:SPMLSearchRequestMessage"/> 
			<wsdl:output name="SPMLSearchRequestOutput" message="tns:SPMLSearchResponseMessage"/> 
		</wsdl:operation> 

		<wsdl:operation name="extendedRequest"> 
			<wsdl:input name="SPMLExtendedRequestInput" message="tns:SPMLExtendedRequestMessage"/> 
			<wsdl:output name="SPMLExtendedRequestOutput" message="tns:SPMLExtendedResponseMessage"/>
		</wsdl:operation> 

	        <wsdl:operation name="status">
			<wsdl:input name="SPMLStatusRequestInput" message="tns:SPMLStatusRequestMessage"/>
		    	<wsdl:output name="SPMLStatusRequestOutput" message="tns:SPMLStatusResponseMessage"/>
        	</wsdl:operation>

        	<wsdl:operation name="changeId">
			<wsdl:input name="SPMLChangeIdRequestInput" message="tns:SPMLChangeIdRequestMessage"/>
			<wsdl:output name="SPMLChangeIdRequestOutput" message="tns:SPMLChangeIdResponseMessage"/>
        	</wsdl:operation>
	</wsdl:portType> 
	<!-- PORT TYPE SECTION: END -->

	<!-- BINDING SECTION: BEGIN -->
	<wsdl:binding name="SPMLHlrNsr21Binding" type="tns:SPMLHlrNsr21PortType">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/> 
		<wsdl:operation name="batch"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/batchRequest" /> 
			<wsdl:input name="SPMLBatchRequestInput" >
				<soap:body use="literal" />
			</wsdl:input> 
			<wsdl:output name="SPMLBatchRequestOutput" >
				<soap:body use="literal" />
			</wsdl:output> 
		</wsdl:operation>

		<wsdl:operation name="add"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/addRequest" />
			<wsdl:input name="SPMLAddRequestInput" >
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="SPMLAddRequestOutput" > 
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="modify"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/modifyRequest" /> 
			<wsdl:input name="SPMLModifyRequestInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
			<wsdl:output name="SPMLModifyRequestOutput" > 
				<soap:body use="literal" />
			</wsdl:output> 
		</wsdl:operation> 

		<wsdl:operation name="delete"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/deleteRequest" /> 
			<wsdl:input name="SPMLDeleteRequestInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
			<wsdl:output name="SPMLDeleteRequestOutput" >
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation> 

		<wsdl:operation name="cancel"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/cancelRequest" /> 
			<wsdl:input name="SPMLCancelRequestInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
			<wsdl:output name="SPMLCancelRequestOutput" >
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation> 

		<wsdl:operation name="search"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/searchRequest" /> 
			<wsdl:input name="SPMLSearchRequestInput" >
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="SPMLSearchRequestOutput" >
				<soap:body use="literal" /> 
			</wsdl:output>
		</wsdl:operation> 

		<wsdl:operation name="extendedRequest"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/extendedRequest"/>
			<wsdl:input name="SPMLExtendedRequestInput" > 
				<soap:body use="literal" /> 
			</wsdl:input> 
			<wsdl:output name="SPMLExtendedRequestOutput" > 
				<soap:body use="literal" /> 
			</wsdl:output> 
		</wsdl:operation>

		<wsdl:operation name="status"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/statusRequest" /> 
			<wsdl:input name="SPMLStatusRequestInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
			<wsdl:output name="SPMLStatusRequestOutput" >
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation> 

		<wsdl:operation name="changeId"> 
			<soap:operation style="document" soapAction="urn:siemens:names:prov:gw:SPML:2:0/changeIdRequest" /> 
			<wsdl:input name="SPMLChangeIdRequestInput" >
				<soap:body use="literal" /> 
			</wsdl:input>
			<wsdl:output name="SPMLChangeIdRequestOutput" >
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation> 

	</wsdl:binding> 
	<!-- BINDING SECTION: END -->

	<wsdl:service name="SPMLHlrNsr21Service">
	    <port name="SPMLHlrNsr21PortType" binding="tns:SPMLHlrNsr21Binding">
	      <soap:address location="http://localhost:8080/ProvisioningGateway/services/SPMLHlrNsr21Service"/>
	    </port>
	</wsdl:service>

</wsdl:definitions>