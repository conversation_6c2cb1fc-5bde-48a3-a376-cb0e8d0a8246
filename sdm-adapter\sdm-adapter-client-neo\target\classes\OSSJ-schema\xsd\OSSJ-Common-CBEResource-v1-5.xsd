<?xml version="1.0" encoding="UTF-8"?>
<!--
 *
 *
 *  Generated with Tigerstripe(tm) Tigerstripe Workbench Feature 2.2.0.v20070514-1108-IXOqsosF8y087t5
 *
 *  Tigerstripe(tm) is a registered trademark of Tigerstripe, Inc.
 *
 *  Plugin  : Tigerstripe, Inc., OSS through Java(TM) Initiative/XMLSpecificationPlugin(0.6) 
 *
 *  Template templates\packageBased\packageBasedSchema.vm
 *    
 *  DO NOT EDIT THIS CODE WAS AUTOMATICALLY GENERATED
 *
Copyright 2002-2007 The Members of the OSS through Java(TM) Initiative.
All rights reserved. Use is subject to license terms.
 *
-->
<!--
DOCUMENTATION IS PROVIDED "AS IS" AND ALL EXPRESS OR IMPLIED
CONDITIONS, REPRESENTATIONS AND WARRANTIES, INCLUDING ANY IMPLIED
WARRANTY OF MERCHANTABILITY, FITNESS FOR FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT, ARE DISCLAIMED, EXCEPT TO THE EXTENT THAT SUCH
DISCLAIMERS ARE HELD TO BE LEGALLY INVALID.
-->
<schema targetNamespace="http://ossj.org/xml/Common-CBEResource/v1-5"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:co-v1-5="http://ossj.org/xml/Common/v1-5"
    xmlns:cberesource-v1-5="http://ossj.org/xml/Common-CBEResource/v1-5"
    xmlns:cbecore-v1-5="http://ossj.org/xml/Common-CBECore/v1-5"
    xmlns:cbedatatypes-v1-5="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
    version = "v1-5"
    elementFormDefault="qualified" >

    <!-- Imports -->

    <import namespace="http://ossj.org/xml/Common/v1-5"
        schemaLocation="OSSJ-Common-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBECore/v1-5"
        schemaLocation="OSSJ-Common-CBECore-v1-5.xsd"/>
    <import namespace="http://ossj.org/xml/Common-CBEDatatypes/v1-5"
        schemaLocation="OSSJ-Common-CBEDatatypes-v1-5.xsd"/>
    <!-- Tigerstripe : Entity definitions for Resource  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceValue" >

        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.resource.ResourceValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:EntityValue" >    
                <sequence>
                    <element name="resourceBusinessName" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceValue">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceKey">

        <annotation>
            <documentation>
                This ResourceKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceValue. The type of the 
                primary key for this ResourceKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKey">        
                <sequence/>
            </extension>
        </complexContent>

    </complexType>
    <complexType name="ArrayOfResourceKey">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceKeyResult">
        <annotation>
            <documentation>

                The ResourceKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntityKeyResult">
                <sequence>
                     <element name="resourceKey" type="cberesource-v1-5:ResourceKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceKeyResult">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of Resource -->
    <!-- Tigerstripe : End of Entity definition for Resource -->

    <!-- Tigerstripe : Entity definitions for ResourceAssociation  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceAssociationValue" >
        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.resource.ResourceAssociationValue.
            </documentation>
        </annotation>

        <complexContent>
            <extension base = "cbecore-v1-5:AssociationValue" >    
                <sequence>

                </sequence>
            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceAssociationValue">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceAssociationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

    <complexType name="ResourceAssociationKey">
        <annotation>
            <documentation>
                This ResourceAssociationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceAssociationValue. The type of the 
                primary key for this ResourceAssociationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKey">        
                <sequence/>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceAssociationKey">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceAssociationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceAssociationKeyResult">
        <annotation>

            <documentation>
                The ResourceAssociationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceAssociationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:AssociationKeyResult">
                <sequence>
                     <element name="resourceAssociationKey" type="cberesource-v1-5:ResourceAssociationKey" nillable="true" minOccurs="0"/>
                </sequence>

            </extension>
        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceAssociationKeyResult">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceAssociationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceAssociation -->

    <!-- Tigerstripe : End of Entity definition for ResourceAssociation -->
    <!-- Tigerstripe : Entity definitions for ResourceSpecification  (Value, ArrayOfValue, Key, ArrayOfKey, KeyResult, ArrayOfKeyResult) -->
    <complexType name="ResourceSpecificationValue" >
        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.resource.ResourceSpecificationValue.
            </documentation>
        </annotation>

        <complexContent>

            <extension base = "cbecore-v1-5:EntitySpecificationValue" >    
                <sequence>
                    <element name="SKUNumber" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="modelNumber" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="partNumber" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="resourceBusinessName" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                    <element name="vendorName" type="string" nillable= "true" minOccurs="0" maxOccurs="1" />
                </sequence>
            </extension>

        </complexContent>
    </complexType>
    <complexType name="ArrayOfResourceSpecificationValue">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceSpecificationValue" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceSpecificationKey">
        <annotation>

            <documentation>
                This ResourceSpecificationKey encapsulates all the information that is necessary to 
                identify a particular instance of a ResourceSpecificationValue. The type of the 
                primary key for this ResourceSpecificationKey definition is: anyType 
            </documentation>
        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKey">        
                <sequence/>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfResourceSpecificationKey">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceSpecificationKey" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
    <complexType name="ResourceSpecificationKeyResult">
        <annotation>
            <documentation>
                The ResourceSpecificationKeyResult encapsulates all the information necessary to indicate the 
                result of a BEST EFFORT operation on a ResourceSpecificationValue. The managed entity key result 
                is used in operations involving the update of multiple managed entities, in the deletion
                of multiple managed entities or in the creation of multiple managed entities or in 
                bulk business operations.
            </documentation>

        </annotation>
        <complexContent>
            <extension base = "cbecore-v1-5:EntitySpecificationKeyResult">
                <sequence>
                     <element name="resourceSpecificationKey" type="cberesource-v1-5:ResourceSpecificationKey" nillable="true" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ArrayOfResourceSpecificationKeyResult">
        <sequence>
            <element name="item" type="cberesource-v1-5:ResourceSpecificationKeyResult"    nillable="true" minOccurs="0" maxOccurs="unbounded"/>        
        </sequence>
    </complexType>
    <!-- Tigerstripe : Global element definitions for extensible Enum attributes of ResourceSpecification -->
    <!-- Tigerstripe : End of Entity definition for ResourceSpecification -->
    <!-- Tigerstripe : Enumeration definitions for ResourceState  -->
    <simpleType name="ResourceState">

        <annotation>
            <documentation>
Interface definition for the javax.oss.cbe.resource.ResourceState.
            </documentation>
        </annotation>
        <restriction base="string">
        </restriction>
    </simpleType>
    <!-- Tigerstripe : End of Enumeration definitions for ResourceState  -->





</schema>
