{"groups": [{"name": "onends", "type": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.charging", "type": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$ChargingPropetries", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties.ChargingPropetries getCharging() "}, {"name": "onends.eps", "type": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$EpsProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties.EpsProperties getEps() "}, {"name": "onends.http", "type": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$HttpProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties.HttpProperties getHttp() "}], "properties": [{"name": "onends.allowed-network", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.charging.charging-character-allowed", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$ChargingPropetries"}, {"name": "onends.charging.charging-character-behavior", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$ChargingPropetries"}, {"name": "onends.charging.charging-character-profile", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$ChargingPropetries"}, {"name": "onends.csd", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.eps.max-bandwidth-down", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$EpsProperties"}, {"name": "onends.eps.max-bandwidth-up", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$EpsProperties"}, {"name": "onends.hlr-nsr-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.hss-unified-nsr-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.http.connection-time-out", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$HttpProperties"}, {"name": "onends.http.max-connections", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$HttpProperties"}, {"name": "onends.http.max-connections-per-route", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$HttpProperties"}, {"name": "onends.http.socket-timeout", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties$HttpProperties"}, {"name": "onends.include-qos-in-apn", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.key-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.subscriber-i-t-f-version", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.trust-store", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}, {"name": "onends.url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.onends.config.OnendsClientProperties"}], "hints": []}