<!-- BCE User 2014-11-17 -->
<xs:schema xmlns="http://schemas.ericsson.com/ma/bce/"
	xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:x="http://schemas.ericsson.com/ma/bce/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/"
	targetNamespace="http://schemas.ericsson.com/ma/bce/"
	elementFormDefault="qualified" attributeFormDefault="unqualified"
	jaxb:version="2.0">
	<xs:include schemaLocation="bce_commonTypes.xsd" />
	<xs:element name="serviceProviderId" type="ServiceProviderId" />
	<xs:element name="companyId" type="CompanyId" />
	<xs:element name="userId" type="UserId" />
	<xs:complexType name="BceUserType">
		<xs:sequence>
			<xs:element name="serviceProviderId" type="ServiceProviderId" />
			<xs:element name="companyId" type="CompanyId" />
			<xs:element name="userId" type="UserId" />
			<!-- org unit ids -->
			<xs:element name="organizationUnitIds" type="UpOrganizationUnitIds"
				minOccurs="0" />
			<!-- User attributes -->
			<xs:element name="sipAddress" type="SipUriType" minOccurs="0"
				maxOccurs="10" />
			<xs:element name="shortNumber" type="ShortNumberType"
				minOccurs="0" />
			<xs:element name="firstName" type="FirstNameType"
				minOccurs="0" />
			<xs:element name="lastName" type="LastNameType" minOccurs="0" />
			<xs:element name="password" type="PasswordType" minOccurs="0" />
			<xs:element name="expirePassword" type="xs:boolean"
				minOccurs="0" />
			<xs:element name="email" type="GenericAddressType"
				minOccurs="0" />
			<xs:element name="active" type="ActiveType" minOccurs="0" />
			<xs:element name="nonTelephonyUser" type="xs:boolean"
				minOccurs="0" />
			<xs:element name="contactAdministrator" type="AdministratorType"
				minOccurs="0" />
			<xs:element name="administrator" type="AdministratorType"
				minOccurs="0" />
			<xs:element name="systemAdministrator" type="AdministratorType"
				minOccurs="0" />
			<xs:element name="clientEnum" type="ClientTypeEnum"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="userName" type="UserNameType" minOccurs="0" />
			<xs:element name="location" type="LocationType" minOccurs="0" />
			<xs:element name="locationAddress" type="AddressType"
				minOccurs="0" />
			<xs:element name="skills" type="SkillsType" minOccurs="0" />
			<xs:element name="role" type="RoleType" minOccurs="0" />
			<xs:element name="webUrl" type="WebUrlType" minOccurs="0" />
			<xs:element name="fax" type="FaxType" minOccurs="0" />
			<xs:element name="title" type="TitleType" minOccurs="0" />
			<xs:element name="department" type="DepartmentType"
				minOccurs="0" />
			<xs:element name="externalId" type="UpExternalId"
				minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="serviceProviderId" type="ServiceProviderId"
			use="required">
		</xs:attribute>
		<xs:attribute name="companyId" type="CompanyId" use="required">
		</xs:attribute>
		<xs:attribute name="userId" type="UserId" use="required">
		</xs:attribute>
	</xs:complexType>
	<!-- CreateUser MOId: serviceProviderId, companyId, userId MOType: user@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="createUser" type="BceUserType">
		<xs:annotation>
			<xs:documentation>
				The attributes for creating BCE User
			</xs:documentation>
		</xs:annotation>
		<xs:key name="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@serviceProviderId" />
		</xs:key>
		<xs:keyref name="serviceProviderIdKeyRef_Create" refer="serviceProviderIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="serviceProviderId" />
		</xs:keyref>
		<xs:key name="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@companyId" />
		</xs:key>
		<xs:keyref name="companyIdKeyRef_Create" refer="companyIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="companyId" />
		</xs:keyref>
		<xs:key name="userIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="@userId" />
		</xs:key>
		<xs:keyref name="userIdKeyRef_Create" refer="userIdKey_Create">
			<xs:selector xpath="." />
			<xs:field xpath="userId" />
		</xs:keyref>
	</xs:element>
	<!-- SetUser MOId: serviceProviderId, companyId, userId MOType: user@http://schemas.ericsson.com/ma/bce/ -->
	<xs:element name="setUser" type="BceUserType">
		<xs:annotation>
			<xs:documentation>
				The attributes for updating BCE User
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- getUser MOId: serviceProviderId, companyId, userId MOType: user@http://schemas.ericsson.com/ma/bce/ -->
	<!-- getUserResponse -->
	<xs:element name="getUserResponse" type="BceUserType">
		<xs:annotation>
			<xs:documentation>
				The attributes for retrieving BCE User
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- deleteUser MOId: serviceProviderId, companyId, userId MOType: user@http://schemas.ericsson.com/ma/bce/ -->
</xs:schema>
