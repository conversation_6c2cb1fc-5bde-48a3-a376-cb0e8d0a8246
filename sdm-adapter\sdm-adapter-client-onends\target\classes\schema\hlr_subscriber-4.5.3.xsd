<?xml version="1.0" encoding="UTF-8"?>
<!--*************************************************************-->
<!--  Example of Unified Subscriber with HLR application         -->
<!--                                                             -->
<!--*************************************************************-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
	xmlns="http://www.w3.org/2001/XMLSchema" 
	xmlns:subscriber="urn:siemens:names:prov:gw:NW_SUBSCRIBER:1:0" 
	xmlns:spml="urn:siemens:names:prov:gw:SPML:2:0" 
	targetNamespace="urn:siemens:names:prov:gw:NW_SUBSCRIBER:1:0"
	elementFormDefault="unqualified" attributeFormDefault="unqualified" version="2.1">
    <!--  schema imports -->
    <xs:import namespace="urn:siemens:names:prov:gw:SPML:2:0" schemaLocation="prov-gw-spml-2.0.xsd"/>
    <!-- common.v100 START -->
    <!--********************************************************************************************************-->
    <!--                          Common / Subscriber Objects                                                   -->
    <!--********************************************************************************************************-->
    <!--***********************************************************-->
    <!--  FIRST CLASS OBJECTS                                                   -->
    <!--***********************************************************-->
    <!-- The one and only first class object in SUBSCRIBER: Subscriber -->
    <xs:complexType name="Subscriber">
        <xs:annotation>
            <xs:documentation>Definition of class subscriber </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                    <xs:element name="auc" type="subscriber:AUC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="hlr" type="subscriber:HLR" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="profile" type="subscriber:Profile" minOccurs="0"/>
                    <xs:element name="masteredBy" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Note: Having multiple Contracts, IMSIs, AUCs, HLRs under one subscriber -->
    <!-- imsiRef string has to be added in Contracts, AUCs, HLRs to bind them with single IMSI -->
    <!-- xs:element name="imsiRef" type="xs:string" minOccurs="0"/ -->
    <!--************************************************************************-->
    <!--  Common SECOND CLASS OBJECTS      -->
    <!--************************************************************************-->
    <xs:complexType name="AUC">
        <xs:annotation>
            <xs:documentation>Abstract base class for a service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <sequence>
                    <xs:element name="imsi" type="xs:string" minOccurs="0"/>
                    <xs:element name="encKey" type="xs:string" minOccurs="0"/>
                    <xs:element name="iccId" type="xs:string" minOccurs="0"/>
                    <xs:element name="algoId" type="xs:int" minOccurs="0"/>
                    <xs:element name="cv" type="xs:string" minOccurs="0"/>
                    <xs:element name="hmac" type="xs:string" minOccurs="0"/>
                    <xs:element name="kdbId" type="xs:string" minOccurs="0"/>
                    <xs:element name="acsub" type="xs:int" minOccurs="0"/>
                    <xs:element name="amf" type="xs:string" minOccurs="0"/>
                    <xs:element name="sqn" type="xs:string" minOccurs="0"/>
                    <!--  FC122_004658 - OPc based MILENAGE Start  -->
                    <xs:element name="opcEncKey" type="xsd:string" minOccurs="0"/>
                    <!--  FC122_004658 - OPc based MILENAGE End  -->
                </sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--                            Profile SCO                                 -->
    <!--************************************************************************-->
    <xs:complexType name="Profile">
        <xs:annotation>
            <xs:documentation>SubscriberProfile</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="givenName" type="subscriber:PrintableString128" minOccurs="0"/>
                    <xs:element name="surname" type="subscriber:PrintableString128" minOccurs="0"/>
                    <xs:element name="username" type="subscriber:PrintableString128" minOccurs="0"/>
                    <xs:element name="password" type="subscriber:String128" minOccurs="0"/>
                    <xs:element name="loginPassword" type="subscriber:String128" minOccurs="0"/>
                    <xs:element name="prepaid" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="roamingAllowed" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!-- ******* *           Application Service base class             ********-->
    <!--************************************************************************-->
    <xs:complexType name="AS">
        <xs:annotation>
            <xs:documentation>Abstract base class for an application service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject"/>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!-- ********                    Simple Types                       ********-->
    <!--************************************************************************-->
    <xs:simpleType name="String128">
        <xs:annotation>
            <xs:documentation>String of length 1 .. 128</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="128"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PrintableString">
        <xs:annotation>
            <xs:documentation>Printable String with '@_;' characters. This type is used for names and labels</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z\d '()+,-./:=?@_;]+"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PrintableString64">
        <xs:annotation>
            <xs:documentation>String of length 1 .. 64</xs:documentation>
        </xs:annotation>
        <xs:restriction base="subscriber:PrintableString">
            <xs:maxLength value="64"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PrintableString128">
        <xs:annotation>
            <xs:documentation>Printable String of length 1 .. 128</xs:documentation>
        </xs:annotation>
        <xs:restriction base="subscriber:PrintableString">
            <xs:maxLength value="128"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PrintableString254">
        <xs:annotation>
            <xs:documentation>Printable String of length 1 .. 254</xs:documentation>
        </xs:annotation>
        <xs:restriction base="subscriber:PrintableString">
            <xs:maxLength value="254"/>
        </xs:restriction>
    </xs:simpleType>
    	<xs:simpleType name="NumericString15">
		<xs:annotation>
			<xs:documentation>Numeric String of length 1..15</xs:documentation>
		</xs:annotation>
		<xs:restriction base="subscriber:NumericString">
			<xs:maxLength value="15"/>
		</xs:restriction>
	</xs:simpleType>
    
    <!--************************************************************************-->
    <!--                        Template Subscriber                             -->
    <!--************************************************************************-->
    <xs:complexType name="TemplateSubscriber">
        <xs:annotation>
            <xs:documentation>Definition of class TemplateSubscriber.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Subscriber"/>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--             Common Subscriber  aliases                                 -->
    <!--************************************************************************-->
    <xs:simpleType name="SubscriberIdentifierAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="identifier"/>
            <xs:enumeration value="imsi"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SubscriberIdentifier">
        <xs:simpleContent>
            <xs:restriction base="spml:Identifier">
                <xs:attribute name="alias" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="SubscriberIdentifierFileNameType">
        <xs:simpleContent>
            <xs:restriction base="spml:IdentifierFileNameType">
                <xs:attribute name="alias" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
                <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="SubscriberAliasType">
        <xs:complexContent>
            <xs:restriction base="spml:AliasType">
                <xs:attribute name="name" type="subscriber:SubscriberIdentifierAliasType" use="required"/>
                <xs:attribute name="value" type="xs:string" use="required"/>
            </xs:restriction>
        </xs:complexContent>
    </xs:complexType>

    <!--************************************************************************-->
    <!-- IDENTITY SWAP Identifier                                                                        -->
    <!--************************************************************************-->
    <xs:simpleType name="SubscriberSwapAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="imsi"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SubscriberSwapIdentifier">
        <xs:simpleContent>
            <xs:extension base="spml:ID">
                <xs:attribute name="alias" type="subscriber:SubscriberSwapAliasType" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <!-- common.v100 END -->
    <!-- hlr45.v100 START -->
    <!--***********************************************************************************************-->
    <!--                        HLR Application Service                                                -->
    <!--***********************************************************************************************-->
    <!--************************************************************************-->
    <!--                         HLR aliases                                    -->
    <!--************************************************************************-->
    <xs:simpleType name="HLRIdentifierAliasType">
        <xs:restriction base="xs:string">
            <!-- Note: also using Subscriber's common aliases: identifier, imsi -->
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HLRIdentifier">
        <xs:simpleContent>
            <xs:restriction base="spml:Identifier">
                <xs:attribute name="alias" type="subscriber:HLRIdentifierAliasType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRIdentifierFileNameType">
        <xs:simpleContent>
            <xs:restriction base="spml:IdentifierFileNameType">
                <xs:attribute name="alias" type="subscriber:HLRIdentifierAliasType" use="required"/>
                <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRAliasType">
        <xs:complexContent>
            <xs:restriction base="spml:AliasType">
                <xs:attribute name="name" type="subscriber:HLRIdentifierAliasType" use="required"/>
                <xs:attribute name="value" type="xs:string" use="required"/>
            </xs:restriction>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--  HLR Application Service SECOND CLASS OBJECTS      -->
    <!--************************************************************************-->
    <xs:complexType name="HLR">
        <xs:annotation>
            <xs:documentation>HLR Application Service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:AS">
                <xs:sequence>
                    <!-- DO NOT USE imsi. Reserver for future /  -->
                    <xs:element name="imsi" type="xs:string" minOccurs="0"/>
                    <xs:element name="contractId" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ntype" type="subscriber:NType" minOccurs="0"/>
                    <xs:element name="imsiActive" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mobileSubscriberType" type="subscriber:MobileSubscriberType" minOccurs="0"/>
                    <xs:element name="umtsSubscriber" type="subscriber:UmtsSubscriber" minOccurs="0"/>
                    <xs:element name="wllSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="tifSubscriber" type="subscriber:TifSubscriber" minOccurs="0"/>
                    <xs:element name="mscat" type="xs:int" minOccurs="0"/>
                    <xs:element name="odboc" type="xs:int" minOccurs="0"/>
                    <xs:element name="odbic" type="xs:int" minOccurs="0"/>
                    <xs:element name="odbr" type="xs:int" minOccurs="0"/>
                    <xs:element name="odboprc" type="xs:int" minOccurs="0"/>
                    <xs:element name="odbssm" type="xs:int" minOccurs="0"/>
                    <xs:element name="osb1" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="osb2" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="osb3" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="osb4" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="clip" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="clipOverride" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="clir" type="xs:int" minOccurs="0"/>
                    <xs:element name="colp" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="colpOverride" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="colr" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="hold" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mpty" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="aoci" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="aocc" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natccbs" type="xs:boolean" minOccurs="0"/>
                    <!-- Release2: natCT - call transfer -->
                    <xs:element name="natct" type="xs:boolean" minOccurs="0"/>
                    <!-- : natHotbill - Hotbill -->
                    <xs:element name="natHotbill" type="xs:boolean" minOccurs="0"/>
                    <!-- : natFR - Forced Routing -->
                    <xs:element name="natFR" type="xs:boolean" minOccurs="0"/>
                    <!-- : natusersig1 - User to User Signalling Service -->
                    <xs:element name="natusersig1" type="xs:boolean" minOccurs="0"/>
                    <!-- Release2: ccbsa and ccbsb added -->
                    <xs:element name="ccbsa" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ccbsb" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss01" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss02" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss03" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss04" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss05" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss06" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss07" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss08" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss09" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss10" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss11" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss12" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss13" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss14" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="natss15" type="xs:boolean" minOccurs="0"/>
                    <!-- Begin of HI40:Activation, Deactivation & interrogation of the new NATSS for 3G services -->
                    <xs:element name="statusNatss01" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss02" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss03" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss04" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss05" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss06" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss07" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss08" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss09" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss10" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss11" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss12" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss13" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss14" type="xs:int" minOccurs="0"/>
                    <xs:element name="statusNatss15" type="xs:int" minOccurs="0"/>
                    <!-- End of HI40:Activation, Deactivation & interrogation of the new NATSS for 3G services -->
                    <!-- Release2: modeled as SCO array to be able to remove exact value -->
                    <xs:element name="subrelro" type="subscriber:SUBRELRO" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="nwa" type="xs:int" minOccurs="0"/>
                    <!-- xs:element name="gprstosm" type="subscriber:GPRSTOSM" minOccurs="0"  maxOccurs="1"/ -->
                    <xs:element name="odbgprs" type="xs:int" minOccurs="0"/>
                    <xs:element name="rr" type="xs:string" minOccurs="0"/>
                    <xs:element name="sr" type="xs:int" minOccurs="0"/>
                    <!-- Release2: modeled as SCO array to be able to remove exact value -->
                    <xs:element name="vgrpid" type="subscriber:VGRPID" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="msp" type="xs:string" minOccurs="0"/>
                    <!-- Release2: introduction of ODBSCI attribute -->
                    <xs:element name="odbsci" type="xs:int" minOccurs="0"/>
                    <!-- BasicServices assigned to subscriber -->
                    <xs:element name="ts11" type="subscriber:TS11" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts21" type="subscriber:TS21" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts22" type="subscriber:TS22" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts61" type="subscriber:TS61" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts62" type="subscriber:TS62" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="vgcs" type="subscriber:VGCS" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="vbs" type="subscriber:VBS" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs20genr" type="subscriber:BS20GENR" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs21" type="subscriber:BS21" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs22" type="subscriber:BS22" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs23" type="subscriber:BS23" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs24" type="subscriber:BS24" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs25" type="subscriber:BS25" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs26" type="subscriber:BS26" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs30genr" type="subscriber:BS30GENR" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs31" type="subscriber:BS31" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs32" type="subscriber:BS32" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs33" type="subscriber:BS33" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs34" type="subscriber:BS34" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs40genr" type="subscriber:BS40GENR" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs41" type="subscriber:BS41" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs42" type="subscriber:BS42" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs44" type="subscriber:BS44" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs45" type="subscriber:BS45" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs46" type="subscriber:BS46" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs61a" type="subscriber:BS61A" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bs81a" type="subscriber:BS81A" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="gprs" type="subscriber:GPRS" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts21Gprs" type="subscriber:TS21GPRS" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="ts22Gprs" type="subscriber:TS22GPRS" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- Call Forwarding Services -->
                    <!-- CFU, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="cfu" type="subscriber:CFU" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- CFB, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="cfb" type="subscriber:CFB" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- CFNrc, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="cfnrc" type="subscriber:CFNrc" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- CFNry, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="cfnry" type="subscriber:CFNry" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- AllCCF, single-valued -->
                    <xs:element name="allccf" type="subscriber:AllCCF" minOccurs="0"/>
                    <!-- AllCF, single-valued -->
                    <xs:element name="allcf" type="subscriber:AllCF" minOccurs="0"/>
                    <!-- CFD, multi-valued -->
                    <!-- Release2: changed to multivalued -->
                    <xs:element name="cfd" type="subscriber:CFD" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- CAW, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="caw" type="subscriber:CAW" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- CUG, single-valued, not used in Release 1 -->
                    <xs:element name="cug" type="subscriber:CUG" minOccurs="0" maxOccurs="10"/>
                    <!-- CUGBSG, single-valued, not used in Release 1 -->
                    <xs:element name="cugbsg" type="subscriber:CUGBSG" minOccurs="0" maxOccurs="5"/>
                    <!-- Call Barring Services -->
                    <!-- COMCB, single-valued -->
                    <xs:element name="comcb" type="subscriber:COMCB" minOccurs="0"/>
                    <!-- BAOC etc, multi-valued, max number = number of values of attribute Basic Service Group -->
                    <xs:element name="baoc" type="subscriber:BAOC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boic" type="subscriber:BOIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boicexhc" type="subscriber:BOICexHC" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- BORO -->
                    <xs:element name="boro" type="subscriber:BORO" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="baic" type="subscriber:BAIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bicroam" type="subscriber:BICRoam" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- Misc. Services -->
                    <xs:element name="emlpp" type="subscriber:EMLPP" minOccurs="0"/>
                    <!-- IMSI Link -->
                    <xs:element name="imsilink" type="xs:string" minOccurs="0"/>
                    <!-- isActiveIMSI: indicates whether this IMSI is the active IMSI in case of a linked subscriber -->
                    <xs:element name="isActiveIMSI" type="xs:boolean" minOccurs="0"/>
                    <!-- Release2: Multi-device support - JANUS -->
                    <xs:element name="multiDevice" type="subscriber:MultiDeviceType" minOccurs="0"/>
                    <!-- RS multi-valued -->
                    <xs:element name="rs" type="subscriber:RS" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- SRF multi-valued -->
                    <xs:element name="srf" type="subscriber:SRF" minOccurs="0" maxOccurs="16"/>
                    <xs:element name="followMe" type="subscriber:FollowMe" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="followMeAccessMatrix" type="subscriber:FollowMeClassOfRegistration" minOccurs="0" maxOccurs="5"/>
                    <xs:element name="twincardImsi" type="xs:string" minOccurs="0"/>
                    <!-- actIMSIGprs: indicates the activation status for a Twin card IMSI subscriber -->
                    <xs:element name="actIMSIGprs" type="xs:boolean" minOccurs="0"/>
                    <!-- LCS Basic procedures -->
                    <xs:element name="cuss" type="subscriber:CUSS" minOccurs="0"/>
                    <xs:element name="crelss" type="subscriber:CRELSS" minOccurs="0"/>
                    <xs:element name="curelss" type="subscriber:CURELSS" minOccurs="0"/>
                    <xs:element name="plmnoss" type="subscriber:PLMNOSS" minOccurs="0"/>
                    <xs:element name="basicSelfLocation" type="subscriber:BasicSelfLocation" minOccurs="0"/>
                    <xs:element name="autoSelfLocation" type="subscriber:AutoSelfLocation" minOccurs="0"/>
                    <xs:element name="transferToThirdParty" type="subscriber:TransferToThirdParty" minOccurs="0"/>
                    <xs:element name="callback" type="subscriber:CALLBACK" minOccurs="0"/>
                    <xs:element name="obGprs" type="xs:int" minOccurs="0"/>
                    <xs:element name="optimalRouting" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ndcLac" type="xs:string" minOccurs="0"/>
                    <xs:element name="imsiTraceReference" type="xs:int" minOccurs="0"/>
                    <xs:element name="imsiTraceType" type="xs:int" minOccurs="0"/>
                    <!-- Expiry Date: The format value of this attribute is YYYY-MM-DD. -->
                    <xs:element name="expiryDate" type="xs:string" minOccurs="0"/>
                    <xs:element name="preferedInterexchangeCarrier" type="xs:string" minOccurs="0"/>
                    <xs:element name="vbsData" type="subscriber:VbsData" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="generalChargingCharacteristics" type="subscriber:GeneralChargingCharacteristics" minOccurs="0"/>
                    <!-- VLRID single-valued -->
                    <!-- Release2: changed to boolean -->
                    <xs:element name="vlrid" type="xs:boolean" minOccurs="0"/>
                    <!-- PDPContext multi-valued, max number = 50 -->
                    <xs:element name="pdpContext" type="subscriber:PDPContext" minOccurs="0" maxOccurs="50"/>
                    <!-- PDPRef single-valued -->
                    <!-- Release2: removed because it is not supported by HLR and data model -->
                    <!-- xs:element name="pdpRef" type="xs:string" minOccurs="0"/ -->
                    <!-- Camel subscription -->
                    <xs:element name="smscsi" type="subscriber:SMSCSI" minOccurs="0"/>
                    <xs:element name="ocsi" type="subscriber:OCSI" minOccurs="0"/>
                    <xs:element name="tcsi" type="subscriber:TCSI" minOccurs="0"/>
                    <xs:element name="ucsisub" type="subscriber:UCSISUB" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- Release2: changed to flat attribute -->
                    <xs:element name="ucsiserv" type="xs:string" minOccurs="0"/>
                    <xs:element name="gprscsi" type="subscriber:GPRSCSI" minOccurs="0"/>
                    <xs:element name="sscsi" type="subscriber:SSCSI" minOccurs="0"/>
                    <xs:element name="mcsi" type="subscriber:MCSI" minOccurs="0"/>
                    <xs:element name="dcsi" type="subscriber:DCSI" minOccurs="0"/>
                    <xs:element name="vtcsi" type="subscriber:VTCSI" minOccurs="0"/>
                    <xs:element name="mtsmscsi" type="subscriber:MTSMSCSI" minOccurs="0"/>
                    <xs:element name="mgcsi" type="subscriber:MGCSI" minOccurs="0"/>
                    <xs:element name="notifyToCSE" type="subscriber:NotifyToCse" minOccurs="0"/>
                    <!-- VlrMobData: cannot be changed by Provisioning Gateway -->
                    <xs:element name="vlrMobData" type="subscriber:VlrMobData" minOccurs="0"/>
                    <!-- SgsnMobData: cannot be changed by Provisioning Gateway -->
                    <xs:element name="sgsnMobData" type="subscriber:SgsnMobData" minOccurs="0"/>
                    <!-- MsgWaitData: cannot be changed by Provisioning Gateway -->
                    <xs:element name="msgWaitData" type="subscriber:MsgWaitData" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- changeOverIMSI: read only attribute. -->
                    <!-- It displays the new IMSI in case of pending seamless changeover for the subscriber -->
                    <xs:element name="changeOverIMSI" type="xs:string" minOccurs="0"/>
                    <!-- ************    HLR4.0   *****************  -->
                    <!-- HI40:Routing Categories -->
                    <xs:element name="routingCategory" type="xs:int" minOccurs="0"/>
                    <xs:element name="routingCategoryExtension" type="xs:int" minOccurs="0"/>
                    <!-- HI40:Automatic Device Detection -->
                    <xs:element name="imeisv" type="xs:string" minOccurs="0"/>
                    <!-- ************    HLR4.5   *****************  -->
                    <!-- HI45: Calling Name Presentation -->
                    <xs:element name="cnap" type="xs:boolean" minOccurs="0"/>
                    <!-- HI45: Calling Name Presentation Override -->
                    <xs:element name="cnapOverride" type="xs:boolean" minOccurs="0"/>
                    <!-- new service  usable only for msisdn based searches-->
                    <xs:element name="allbs" type="subscriber:ALLBS" minOccurs="0"/>
                    <!-- ************    HLR4.5   *****************  -->
                    <!-- HLR 4.5: Explicit Call Transfer -->
                    <xs:element name="ect" type="xs:boolean" minOccurs="0"/>
                    <!-- HLR 4.5: Enhanced SRI for SMS handling -->
                    <xs:element name="enhsrisms" type="xs:boolean" minOccurs="0"/>
                    <!-- HLR 4.5:  Originating/Terminating IN Category Key (O/T ICK)     -->
                    <xs:element name="oick" type="subscriber:OICK" minOccurs="0"/>
                    <xs:element name="tick" type="subscriber:TICK" minOccurs="0" maxOccurs="2"/>
                    <!-- HLR 4.5:  The Extended CAMEL proprietary IN data:
                IN Category Key (EOICK, ETICK) or IN Capability Indicator (EOINCI, ETINCI).
                Note: Implemented in second phase (planned 03/09) -->
                    <xs:element name="eoick" type="subscriber:EOICK" minOccurs="0"/>
                    <xs:element name="etick" type="subscriber:ETICK" minOccurs="0" maxOccurs="2"/>
                    <xs:element name="eoinci" type="subscriber:EOINCI" minOccurs="0"/>
                    <xs:element name="etinci" type="subscriber:ETINCI" minOccurs="0" maxOccurs="2"/>
                    <!-- HLR 4.5 Call forwarding controls -->
                    <!-- odbftno  and prohibitedFtnoCategory has been made obselete -->
                    <xs:element name="odbftno" type="xs:int" minOccurs="0"/>
                    <xs:element name="prohibitedFtnoCategory" type="xs:int" minOccurs="0"/>
                    <!-- HLR 4.5: Anonymous Caller Reject -->
                    <!-- Note: Implementation planned 06/09 -->
                    <!-- xs:element name="acr" type="xs:boolean" minOccurs="0"/ -->
                    <!-- ************    HLR4.5.1   *****************  -->
                    <xs:element name="odbect" type="subscriber:OdbEctType" minOccurs="0" maxOccurs="6"/>
                    <!-- ************    HLR4.5.2   *****************  -->
                    <xs:element name="clgPtyNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="clgPtyData" type="subscriber:ClgPtyData" minOccurs="0"/>
                    <xs:element name="lastCallTimeStamp" type="xs:dateTime" minOccurs="0"/>
                    <xs:element name="lcnService" type="subscriber:LcnService" minOccurs="0"/>
                    <xs:element name="ownMsisdnService" type="subscriber:OwnMsisdnService" minOccurs="0"/>
                    <xs:element name="vlrIdService" type="subscriber:VlrIdService" minOccurs="0"/>
                    <xs:element name="actualTimeService" type="subscriber:ActualTimeService" minOccurs="0"/>
                    <xs:element name="lastCallSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
                    <xs:element name="multiSim" type="subscriber:MultiSimType" minOccurs="0"/>
                    <xs:element name="ussdClirService" type="subscriber:UssdClirService" minOccurs="0"/>
                    <xs:element name="ucsisubext" type="subscriber:UCSISUBEXT" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes Begin -->
                    <xs:element name="roamSubscription" type="subscriber:ROAMSUBSCRIPTION" minOccurs="0"/>
                    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes End  -->
                    <!-- HLR 4.5.5: FC122_00063: Mss MultiSim1 -->
                    <xs:element name="profileType" type="subscriber:HlrProfileType" minOccurs="0"/>
                    <xs:element name="commonMSISDN" type="xs:string" minOccurs="0"/>
                    <xs:element name="overrideCommonCLI" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mssMultiSim" type="subscriber:HlrMssMultiSimType" minOccurs="0"/>
                    <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
                    <xs:element name="spatialTrigger" type="subscriber:SpatialTrigger" minOccurs="0"/>
                    <!-- HLR 5.0 LF: FC122_003888 - Proprietary Nokia IN Short Message Service -->
                    <xs:element name="insms" type="subscriber:INSMS" minOccurs="0"/>
                    <!-- HLR 5.0 LF: FC122_001926 - Automatic Redirection of Calls -->
                    <xs:element name="arc" type="subscriber:ARC" minOccurs="0"/>
                    <!-- HLR 5.0 SP1:: Changes for FC122_003779::Account Code Handling -->
                    <xs:element name="acc" type="subscriber:HlrAcc" minOccurs="0"/>
                    <!-- HLR 5.0 SP1:FC122_003784: PRN retry  -->
                    <xs:element name="csFallback" type="subscriber:HlrCSFallback" minOccurs="0"/>
                    <!--HLR 5.0 SP1: FC122_002113_SMSImprovements-->
                    <xs:element name="smsSubData" type="subscriber:HlrSmsSubData" minOccurs="0"/>
                    <!-- HLR 5.0 SP1: FC122_003912: FTNO -->
                    <xs:element name="ftnTransIndex" type="xs:int" minOccurs="0"/>
                    <!-- HLR 5.0 SP1: FC122_003520: SRVCC -->
                    <xs:element name="icsIndicator" type="xs:int" minOccurs="0"/>
                    <xs:element name="sset" type="subscriber:HLRSSET" minOccurs="0"/>
                    <xs:element name="emoick" type="subscriber:HLREMOICK" minOccurs="0"/>
                    <!-- HLR 5.0 SP1: FC122_003452 UE-Reachability -->
                    <xs:element name="uEReachabilityReqInfo" type="subscriber:UEReachabilityReqInfo" minOccurs="0"/>
                    <!-- HLR 7.0 base: FC122_004632 CSARP START -->
                    <xs:element name="csarp" type="subscriber:HLRCSARP" minOccurs="0"/>
                    <!-- HLR 7.0 base: FC122_004632 CSARP END -->
                    <!-- FC122_004142: Subscriber fraud detection & limitation Start -->
                     <xs:element name="refFraudProfileName" type="xs:string" minOccurs="0"/>
                    <!-- FC122_004142: Subscriber fraud detection & limitation End  -->
                    <!-- FC122_004634 Multimedia Ring Back Tone (MRBT) Start -->
                    <xs:element name="mrbt" type="xs:boolean" minOccurs="0"/>
                    <!-- FC122_004634 Multimedia Ring Back Tone (MRBT) Start -->
                    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
                    <xs:element name="gsmTrace" type="subscriber:HLRGSMTrace" minOccurs="0"/>
                    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR end -->
                    <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: Start -->
                    <xs:element name="subFraudObsInfo" type="subscriber:HLRFraudObservationInfo" minOccurs="0" maxOccurs="2"/>
                    <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: End -->
                    <!-- FC122_005796_Subscriber_fraud_observation_phase3_LUP :: Start -->
                    <xs:element name="simObsActive" type="xs:boolean" minOccurs="0"/>
                    <!-- FC122_005796_Subscriber_fraud_observation_phase3_LUP :: End -->
                    <!-- FC122_005801_  Charging_based_on_Home_Area :: Start -->
                    <xs:element name="chbha" type="subscriber:HLRChargingBasedOnHomeArea" minOccurs="0" />
                    <!-- FC122_005801_  Charging_based_on_Home_Area :: End -->
                    <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
                    <xs:element name="primaryBasicService" type="subscriber:HLRPBS" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - Start -->
                    <xs:element name="cf" type="subscriber:HLRCallForward" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - End -->
                    <!--FC123_107003_Separate_Roaming_Restriction_List Start -->
                    <xs:element name="rrPs" type="xs:string" minOccurs="0"/>
                    <xs:element name="srPs" type="xs:int" minOccurs="0"/>
                    <!-- FC123_107003_Separate_Roaming_Restriction_List End -->
                    <!-- FC123_107002_Multiple_HPLMN_support - Start -->
                    <xs:element name="refHplmnAreaName" type="xs:string" minOccurs="0" />
                    <!-- FC123_107002_Multiple_HPLMN_support - End -->
                    <!-- FC123_108726_Combine 4G and 3G roaming indication over Sh interface: Start -->
                    <xs:element name="shVplmnId" type="subscriber:PrintableString64" minOccurs="0"/>
                    <!-- FC123_108726_Combine 4G and 3G roaming indication over Sh interface: End -->
					<!--FST123_107470 - Enhanced control of CF types to non-Macau international number-->
					<xs:element name="hlrOdbFtnoStandard" type="subscriber:HLROdbFtnoStandard" minOccurs="0"/>
                    <xs:element name="hlrOdbFtnoProprietary" type="subscriber:HLROdbFtnoProprietary" minOccurs="0"/>
                    <!-- FC123_107981-VDF_Turkey_SPRING_2-Local_Requirements-SS7_Fraud_and_Twin_SIM_Card : Start -->
                    <xs:element name="disableRoamingPlausibility" type="xs:boolean" minOccurs="0"/>
                    <!-- FC123_107981-VDF_Turkey_SPRING_2-Local_Requirements-SS7_Fraud_and_Twin_SIM_Card : End -->
		    <!-- FC123_107926 HLR supports MSISDN-less MTC devices - Start -->
                    <xs:element name="hssMsisdnLessIndicator" type="xs:boolean" minOccurs="0"/>
                    <!-- FC123_107926 HLR supports MSISDN-less MTC devices end-->
                    <!-- FC123_107948_Selective_Cancel_Location :: Start -->
                    <xs:element name="cancelLocInVLR" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="cancelLocInSGSN" type="xs:boolean" minOccurs="0"/>
                    <!-- FC123_107948_Selective_Cancel_Location :: End -->
                    <xs:element name="ccbsTerminatingEntry" type="subscriber:CcbsTerminatingEntry" minOccurs="0" maxOccurs="unbounded"/>				
			        <xs:element name="ccbsOriginatingEntry" type="subscriber:CcbsOriginatingEntry" minOccurs="0" maxOccurs="unbounded"/>
			        <xs:element name="ccbsOQueue" type="subscriber:CcbsOQueue" minOccurs="0" maxOccurs="unbounded"/>				
			        <xs:element name="ccbsTQueue" type="subscriber:CcbsTQueue" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="extendedRoamSubscription" type="subscriber:HssExtendedRoamSubscription" minOccurs="0" maxOccurs="unbounded"/>
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
					<xs:element name="hlrSgsnSupportedFeatures" type="subscriber:HLRSgsnSupportedFeatures" minOccurs="0"/>
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HssExtendedRoamSubscription">
         <xs:annotation>
             <xs:documentation> This attributes indicates a symbolic name identifying the Roaming Subscription Info entry. </xs:documentation>
         </xs:annotation>
         <xs:complexContent>
             <xs:extension base="spml:SecondClassObject">
                 <xs:sequence>
                     <xs:element name="priorityOrder" type="xs:int"/>
                     <xs:element name="roamSubscriptionInfo" type="xs:string"/>
                 </xs:sequence>
             </xs:extension>
         </xs:complexContent>
		</xs:complexType>
        	<xs:complexType name="CcbsTerminatingEntry">
        <xs:annotation>
            <xs:documentation>
		CCBSTERMINATINGENTRY
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
	      <xs:extension base="spml:SecondClassObject">
         <xs:sequence>
		<xsd:element name="ccbsTerminatingIndex" type="xsd:int" minOccurs="0"/>
		<xsd:element name="msisdn" type="subscriber:NumericString15" minOccurs="0"/>
		<xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>		
		<xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
		<xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
		<xsd:element name="ccbsTimerStart" type="xsd:dateTime" minOccurs="0"/>
		<xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
		<xsd:element name="retainSupported" type="xsd:boolean" minOccurs="0"/>
		</xs:sequence>
            </xs:extension>
        </xs:complexContent>
	</xs:complexType>
	<xs:complexType name="CcbsOriginatingEntry">
        <xs:annotation>
            <xs:documentation>
		CCBSORGINATINGENTRY
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
	      <xs:extension base="spml:SecondClassObject">
         <xs:sequence>
		<xsd:element name="ccbsOriginatingIndex" type="xsd:int" minOccurs="0"/>
		<xsd:element name="msisdn" type="subscriber:NumericString15" minOccurs="0"/>
		<xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>		
		<xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
		<xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
		<xsd:element name="ccbsTimerStart" type="xsd:dateTime" minOccurs="0"/>
		<xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
		<xsd:element name="retainSupported" type="xsd:boolean" minOccurs="0"/>

		</xs:sequence>
            </xs:extension>
        </xs:complexContent>
	</xs:complexType>
    <xs:complexType name="CcbsOQueue">
        <xs:annotation>
            <xs:documentation>
		CCBSOQUEUE
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
	      <xs:extension base="spml:SecondClassObject">
         <xs:sequence>
		<xsd:element name="ccbsOMSISDN" type="subscriber:NumericString15" minOccurs="0"/>
		<xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>	
		<xsd:element name="ccbsIndex" type="xsd:int" minOccurs="0"/>	
		<xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
		<xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
		<xsd:element name="ccbsPrio" type="xsd:int" minOccurs="0"/>	
		<xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
		</xs:sequence>
            </xs:extension>
    </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CcbsTQueue">
        <xs:annotation>
            <xs:documentation>
				CCBSTQUEUE
              </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
	      <xs:extension base="spml:SecondClassObject">
         <xs:sequence>
		<xsd:element name="ccbsTMSISDN" type="subscriber:NumericString15" minOccurs="0"/>
		<xsd:element name="basicServiceGroup" type="xsd:string" minOccurs="0"/>	
		<xsd:element name="ccbsIndex" type="xsd:int" minOccurs="0"/>	
		<xsd:element name="ccbsReqStat" type="xsd:int" minOccurs="0"/>
		<xsd:element name="ccbsCtxtTName" type="xsd:string" minOccurs="0"/>
		<xsd:element name="ccbsPrio" type="xsd:int" minOccurs="0"/>	
		<xsd:element name="ccbsFeAddress" type="xsd:string" minOccurs="0"/>
		</xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--  TYPE SECTION: Defining complex types/enumeration for flat attributes  -->
    <!--************************************************************************-->
    <!-- definition of type NType, flat attribute of subscriber -->
    <xs:simpleType name="NType">
        <xs:annotation>
            <xs:documentation>Numbering Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="single"/>
            <xs:enumeration value="multi"/>
        </xs:restriction>
    </xs:simpleType>
    <!--************************************************************************-->
    <!--  SECOND CLASS OBJECTS                                                  -->
    <!--************************************************************************-->
    <xs:complexType name="Service">
        <xs:annotation>
            <xs:documentation>Abstract base class for a service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BasicService">
        <xs:annotation>
            <xs:documentation>Abstract base class for a basic service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
                    <xs:element name="bcieID" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Teleservices -->
    <xs:complexType name="TS11">
        <xs:annotation>
            <xs:documentation>
                TS11 is a particular subclass of a service class with dedicated parameter
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TS21">
        <xs:annotation>
            <xs:documentation>
                TS21 is a Short Message service mobile terminating
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TS22">
        <xs:annotation>
            <xs:documentation>
                TS22 is a Short Message service mobile originating
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TS61">
        <xs:annotation>
            <xs:documentation>
                TS61 is an alternate speech and facsimile group 3 service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TS62">
        <xs:annotation>
            <xs:documentation>
                TS62 is an automatic facsimile group 3 service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="VGCS">
        <xs:annotation>
            <xs:documentation>
                VGCS Voice Group Call Service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="VBS">
        <xs:annotation>
            <xs:documentation>
                VBS Voice Broadcast Service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS20GENR">
        <xs:annotation>
            <xs:documentation>
                BS20 GENR is a general circuit switched data asynchronous service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS21">
        <xs:annotation>
            <xs:documentation>
                BS21 datal circuit duplex asynchronous service (300 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS22">
        <xs:annotation>
            <xs:documentation>
                BS22 datal circuit duplex asynchronous service (1200 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS23">
        <xs:annotation>
            <xs:documentation>
                BS23 datal circuit duplex asynchronous service (1200 b/s and 75 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS24">
        <xs:annotation>
            <xs:documentation>
                BS24 datal circuit duplex asynchronous service (2400 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS25">
        <xs:annotation>
            <xs:documentation>
                BS25 datal circuit duplex asynchronous service (4800 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS26">
        <xs:annotation>
            <xs:documentation>
                BS26 datal circuit duplex asynchronous service (9600 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS30GENR">
        <xs:annotation>
            <xs:documentation>
                BS30 GENR is a general circuit switched data synchronous service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS31">
        <xs:annotation>
            <xs:documentation>
                BS31 datal circuit duplex synchronous service (1200 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS32">
        <xs:annotation>
            <xs:documentation>
                BS32 datal circuit duplex ssynchronous service (2400 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS33">
        <xs:annotation>
            <xs:documentation>
                BS33 datal circuit duplex synchronous service (4800 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS34">
        <xs:annotation>
            <xs:documentation>
                BS34 datal circuit duplex synchronous service (9600 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS41">
        <xs:annotation>
            <xs:documentation>
                BS41 PAD access circuit asynchronous service (300 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS42">
        <xs:annotation>
            <xs:documentation>
                BS42 PAD access circuit asynchronous service (12000 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS44">
        <xs:annotation>
            <xs:documentation>
                BS44 PAD access circuit asynchronous service (2400 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS45">
        <xs:annotation>
            <xs:documentation>
                BS45 PAD access circuit asynchronous service (4800 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS46">
        <xs:annotation>
            <xs:documentation>
                BS46 PAD access circuit asynchronous service (9600 b/s)
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS40GENR">
        <xs:annotation>
            <xs:documentation>
                BS40GENR general PAD access circuit asynchronous service
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS61A">
        <xs:annotation>
            <xs:documentation>
                BS61A Alternate Speech / Unrestricted Data
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService">
                <xs:sequence>
                    <xs:element name="bcieIDData" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BS81A">
        <xs:annotation>
            <xs:documentation>
                BS81 Speech followed by Data
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService">
                <xs:sequence>
                    <xs:element name="bcieIDData" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="GPRS">
        <xs:annotation>
            <xs:documentation>
                GPRS This is a Nokia special service for GPRS
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="TransferOptionType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for transferOption element in TS21GPRS</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="transferViaMSC"/>
            <xs:enumeration value="transferViaSGSN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TS21GPRS">
        <xs:annotation>
            <xs:documentation>
                TS21GPRS TSMS MT over GPRS
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService">
                <xs:sequence>
                    <xs:element name="transferOption" type="subscriber:TransferOptionType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TS22GPRS">
        <xs:annotation>
            <xs:documentation>
                TS22GPRS SMS MO over GPRS
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:BasicService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="CfBasicServiceGroupType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for BasicServiceGroup element in CFU</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TS10-telephony"/>
            <xs:enumeration value="TS60-fax"/>
            <xs:enumeration value="BS20-dataAsync"/>
            <xs:enumeration value="BS30-dataSync"/>
            <xs:enumeration value="BS40-padAccess"/>
        </xs:restriction>
    </xs:simpleType>
    <!--FC122_003787:SMS Forwarding in HLR Start -->
    <xs:simpleType name="HLRCfBasicServiceGroupTypeExt">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for BasicServiceGroup element in CFU</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TS10-telephony"/>
            <xs:enumeration value="TS60-fax"/>
            <xs:enumeration value="BS20-dataAsync"/>
            <xs:enumeration value="BS30-dataSync"/>
            <xs:enumeration value="BS40-padAccess"/>
            <xs:enumeration value="TS20-shortMessage"/>
        </xs:restriction>
    </xs:simpleType>
    <!--FC122_003787:SMS Forwarding in HLR End -->
    <xs:complexType name="CFU">
        <xs:annotation>
            <xs:documentation> CFU Call forwarding unconditional </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:HLRCfBasicServiceGroupTypeExt" minOccurs="0"/>
                    <!-- Release2: making isdnNumber and status optional (applies through whole schema for CF and Call Barring elements -->
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CFB">
        <xs:annotation>
            <xs:documentation> CFB Call forwarding on mobile subscriber busy </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CFNrc">
        <xs:annotation>
            <xs:documentation> CFNrc Call forwarding on mobile subscriber not reachable </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CFNry">
        <xs:annotation>
            <xs:documentation> CFNry Call forwarding on no reply </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="AllCCF">
        <xs:annotation>
            <xs:documentation> AllCCF All conditional call forwarding services </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="AllCF">
        <xs:annotation>
            <xs:documentation> AllCF All call forwarding services </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="notifyCallingSubscriber" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="notifyForwardingSubscriber" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CFD">
        <xs:annotation>
            <xs:documentation> CFD Call Forwarding by Default </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="isdnNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <!-- xs:element name="noReplyConditionTimer" type="xs:int" minOccurs="0"/ -->
                    <xs:element name="replaceCFConditional" type="xs:int" minOccurs="0"/>
                    <xs:element name="serviceAvailable" type="xs:int" minOccurs="0"/>
                    <xs:element name="ftnoType" type="subscriber:FtnoType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CAW">
        <xs:annotation>
            <xs:documentation> CAW Call Waiting </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CUG">
        <xs:annotation>
            <xs:documentation> CUG Closed User Group </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="cugIndex" type="xs:int" minOccurs="0"/>
                    <xs:element name="cugInterlockCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="dataNetworkIdentityCode" type="subscriber:NumericString" minOccurs="0"/>
                    <xs:element name="intraCUGRestriction" type="xs:int" minOccurs="0"/>
                    <xs:sequence>
                        <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType" minOccurs="0" maxOccurs="5"/>
                    </xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CUGBSG">
        <xs:annotation>
            <xs:documentation> CUG Closed User Group - Basic Service Group related Data </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:CfBasicServiceGroupType"/>
                    <xs:element name="preferentialCUGIndex" type="xs:int" minOccurs="0"/>
                    <xs:element name="interCUGAccessType" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="SUBRELRO">
        <xs:annotation>
            <xs:documentation> Subscriber Related Routing</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subscriberRouting" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="VGRPID">
        <xs:annotation>
            <xs:documentation> Voice Call Group ID</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="voiceGroupId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="ComCbControlType">
        <xs:annotation>
            <xs:documentation> Enumeration of valid values for COMCB_Password used in COMCB </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="byOperator"/>
            <xs:enumeration value="bySubscriber"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="COMCB">
        <xs:annotation>
            <xs:documentation> COMCB Common call barring attributes </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="control" type="subscriber:ComCbControlType" minOccurs="0"/>
                    <xs:element name="password" type="xs:string" minOccurs="0"/>
                    <xs:element name="pwBlockedBN" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="BarringBasicServiceGroupType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for BasicServiceGroup element in call forwarding</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TS10-telephony"/>
            <xs:enumeration value="TS20-shortMessage"/>
            <xs:enumeration value="TS60-fax"/>
            <xs:enumeration value="BS20-dataAsync"/>
            <xs:enumeration value="BS30-dataSync"/>
            <xs:enumeration value="BS40-padAccess"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BAOC">
        <xs:annotation>
            <xs:documentation> BAOC Barring of all outgoing calls </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BOIC">
        <xs:annotation>
            <xs:documentation> BOIC Barring of all outgoing international calls </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BOICexHC">
        <xs:annotation>
            <xs:documentation> BOICexHC Barring of all outgoing international calls except those directed to the home plmn country </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BORO">
        <xs:annotation>
            <xs:documentation> BORO    Barring of outgoing calls when roaming outside the home plmn country </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="barrGSM" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BAIC">
        <xs:annotation>
            <xs:documentation> BAIC Barring of all incoming calls </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BICRoam">
        <xs:annotation>
            <xs:documentation> BICRoam Barring of all incoming calls when roaming outside the home plmn country </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="EMLPPPriorityType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for EMLPPPriorityTypeb used in EMLPP</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <!-- WPS extension -->
            <xs:enumeration value="priorityA"/>
            <xs:enumeration value="priorityB"/>
            <!-- WPS extension -->
            <xs:enumeration value="priority0"/>
            <xs:enumeration value="priority1"/>
            <xs:enumeration value="priority2"/>
            <xs:enumeration value="priority3"/>
            <xs:enumeration value="priority4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="EMLPP">
        <xs:annotation>
            <xs:documentation> EMLPP eMLPP </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="maxPriority" type="subscriber:EMLPPPriorityType" minOccurs="0"/>
                    <xs:element name="defaultPriority" type="subscriber:EMLPPPriorityType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Release2: definition of type Multi Device, JANUS feature -->
    <xs:simpleType name="MultiDeviceIndicatorType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid multi-device indicators</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="master"/>
            <xs:enumeration value="slave1"/>
            <xs:enumeration value="slave2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ActivePageDeviceType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid multi-devices</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="master"/>
            <xs:enumeration value="slave1"/>
            <xs:enumeration value="slave2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MultiDeviceImsiType">
        <xs:annotation>
            <xs:documentation>SCO for multi device IMSI description</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="mdIndicator" type="subscriber:MultiDeviceIndicatorType"/>
                    <xs:element name="mdImsi" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MultiDeviceMsisdnType">
        <xs:annotation>
            <xs:documentation>SCO for multi device MSISDN description</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="mdIndicator" type="subscriber:MultiDeviceIndicatorType"/>
                    <xs:element name="mdMsisdn" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MultiDeviceType">
        <xs:annotation>
            <xs:documentation>Support of multi device functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="multiDeviceIndicator" type="subscriber:MultiDeviceIndicatorType" minOccurs="0"/>
                    <xs:element name="activePageDevice" type="subscriber:ActivePageDeviceType" minOccurs="0"/>
                    <xs:element name="multiDeviceImsi" type="subscriber:MultiDeviceImsiType" minOccurs="0" maxOccurs="2"/>
                    <xs:element name="multiDeviceMsisdn" type="subscriber:MultiDeviceMsisdnType" minOccurs="0" maxOccurs="2"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RS">
        <xs:annotation>
            <xs:documentation> RS Regional Subscription </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="countryCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="networkDestinationCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode01" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode02" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode03" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode04" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode05" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode06" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode07" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode08" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode09" type="xs:string" minOccurs="0"/>
                    <xs:element name="zoneCode10" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="SRF">
        <xs:annotation>
            <xs:documentation> SRF Subscriber Related Features </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="featureId" type="xs:int" minOccurs="0"/>
                    <xs:element name="featureNumber" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Release2: changed to boolean -->
    <!-- xs:complexType name="VLRID">
        <xs:annotation>
            <xs:documentation> VLRID VLR Id </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="vlrId" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType -->
    <xs:simpleType name="PDPAccessPointNameAreaType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for PDPAccessPointNameArea</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HPLMN"/>
            <xs:enumeration value="ALLPLMN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="IPAddress">
        <xs:annotation>
            <xs:documentation>Either ipv4 or ipv6 address is allowed</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <!-- IPV4 patter address-->
            <xs:pattern value="([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])\.([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])"/>
            <!-- All possible IPV6 patterns -->
            <xs:pattern value="([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}"/>
            <!-- full format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
            <xs:pattern value="(([0-9A-Fa-f]{1,4}:){6})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <!-- compressed format -->
            <xs:pattern value="::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,6})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,5})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){1})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,4})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,3})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,2})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){0,1})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){5})::(([0-9A-Fa-f]{1,4})?)"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){6})::"/>
            <!-- compressed format where the last 4 bytes of the IPv6 address are written in dot-decimal notation, in the style of IPv4 addresses. -->
            <xs:pattern value="::(([0-9A-Fa-f]{1,4}:){0,5})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4})::(([0-9A-Fa-f]{1,4}:){0,4})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}))::(([0-9A-Fa-f]{1,4}:){0,3})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){2})::(([0-9A-Fa-f]{1,4}:){0,2})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){3})::(([0-9A-Fa-f]{1,4}:){0,1})(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
            <xs:pattern value="([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){4})::(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PDPContext">
        <xs:annotation>
            <xs:documentation> PDPContext </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="id" type="xs:int" minOccurs="0"/>
                    <xs:element name="type" type="xs:int" minOccurs="0"/>
                    <xs:element name="address" type="xs:string" minOccurs="0"/>
                    <xs:element name="qosProfile" type="xs:string" minOccurs="0"/>
                    <xs:element name="apn" type="xs:string" minOccurs="0"/>
                    <xs:element name="apnArea" type="subscriber:PDPAccessPointNameAreaType" minOccurs="0"/>
                    <xs:element name="chargingCharacteristics" type="subscriber:ChargingCharacteristics" minOccurs="0" maxOccurs="4"/>
                    <xs:element name="chargingCharacteristicsProfile" type="xs:int" minOccurs="0"/>
                    <xs:element name="chargingCharacteristicsBehavior" type="xs:int" minOccurs="0"/>
                    <!-- FC122_003531 Support of IPv4v6 Dual Stack in Subscriber Profile -->
                    <xs:element name="extType" type="xs:int" minOccurs="0"/>
                    <xs:element name="extAddress" type="subscriber:IPAddress" minOccurs="0"/>
                        <!-- NTHLR6SP1: RDSP enhancements -Start-->
                    <xs:element name="refPdpContextName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6SP1: RDSP enhancements -End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CSI">
        <xs:annotation>
            <xs:documentation> Camel Subscription Information  base class</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
                    <xs:element name="csiState" type="xs:int" minOccurs="0"/>
                    <xs:element name="csiNotify" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="DCSI">
        <xs:annotation>
            <xs:documentation> DCSI Dialed CAMEL subscription information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MCSI">
        <xs:annotation>
            <xs:documentation> MCSI Mobility management CAMEL subscription information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MGCSI">
        <xs:annotation>
            <xs:documentation> MGCSI CAMEL subscription information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MTSMSCSI">
        <xs:annotation>
            <xs:documentation> MTSMSCSI Mobile terminating SMS CAMEL subscription information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="OCSI">
        <xs:annotation>
            <xs:documentation> OCSI Originating Camel Subscription Information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TCSI">
        <xs:annotation>
            <xs:documentation> TCSI Terminating Camel Subscription Information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="GPRSCSI">
        <xs:annotation>
            <xs:documentation> GPRSCSI GPRSCSIServices </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="SMSCSI">
        <xs:annotation>
            <xs:documentation> SMSCSI SMS Camel Subscription Information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="SSCSI">
        <xs:annotation>
            <xs:documentation> SSCSI SSCSIServices </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="VTCSI">
        <xs:annotation>
            <xs:documentation> VTCSI VMSC terminating CAMEL subscription information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:CSI"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="UCSISUB">
        <xs:annotation>
            <xs:documentation> UCSISUB This data item is used in customer specific projects for Prepay subscribers. It controls access to the account Query etc. This object can be applied multiple times</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="ucsiServCode" type="xs:int" minOccurs="0"/>
                    <xs:element name="gsmscfAddress" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="NotifyToCse">
        <xs:annotation>
            <xs:documentation>Notification flag, which indicates the necessity to notify the Camel Service Environment, gsmSCF(GSM Service control function), of the change of subscriber data.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="callForwardingServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="callBarringServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="operatorDeterminedBarringServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="clipServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="clirServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="callWaitServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="callHoldServices" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="explicitCallTransferServices" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- msubType -->
    <xs:simpleType name="MobileSubscriberType">
        <xs:annotation>
            <xs:documentation>This attribute specifies the type of the mobile subscriber.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="genericSubscriber"/>
            <xs:enumeration value="lmuSubscriber"/>
            <!-- Location Measurement Unit Subscriber -->
            <!-- FC123_107249_SWAP_phase_1 :: Start -->
            <xs:enumeration value="m2mSubscriber"/>
            <!-- FC123_107249_SWAP_phase_1 :: Stop -->
        </xs:restriction>
    </xs:simpleType>
    <!-- TIF subscriber -->
    <xs:complexType name="TifSubscriber">
        <xs:annotation>
            <xs:documentation>
                Translation Information Flag subscriber.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="tifCsiActive" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="tifCsiNotificationToCse" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FollowMe, FollowMeSupervisor -->
    <xs:simpleType name="FollowMeBasicServiceGroupType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for BasicServiceGroup element in FollowMe</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TS10-telephony"/>
            <xs:enumeration value="TS60-fax"/>
            <xs:enumeration value="BS20-dataAsync"/>
            <xs:enumeration value="BS30-dataSync"/>
            <xs:enumeration value="BS40-padAccess"/>
            <!-- Really also this? -->
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FollowMe">
        <xs:annotation>
            <xs:documentation>
                Follow Me - Enables a mobile subscriber A to have control over the Follow Me data
                of a remote party B such that subsequent calls directed to remote party B are forwarded
                to subscriber A.
                Follow Me supervisor - Allows to modify the Follow Me data of the remote party who has
                been registered to another initiating subscriber.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="basicServiceGroup" type="subscriber:FollowMeBasicServiceGroupType" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="followMeSupervisor" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Access Matrix for registration -->
    <xs:simpleType name="FollowMeClassOfRegistration">
        <xs:annotation>
            <xs:documentation>
                This attribute holds the classes of registration for the "follow me" supplementary service.
                The following values are allowed:
                    A  Engine/ Train cab- radio basic functions
                    B  Maintenance service user
                    C  Operation support user
                    D  Customer support user
                    E  Train Controller
                This attribute allows entires of a single value or of multiple logical values. The logical
                values are combined to one physical value in database by OR combination.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="classA"/>
            <xs:enumeration value="classB"/>
            <xs:enumeration value="classC"/>
            <xs:enumeration value="classD"/>
            <xs:enumeration value="classE"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- LCS Basic Procedure -->
    <xs:complexType name="CUSS">
        <xs:annotation>
            <xs:documentation>
                LCS universal status. This attribute specifies the status of Call universal supplementary
                service. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CRELSS">
        <xs:annotation>
            <xs:documentation>
                LCS call related status. This attribute specifies the status of call related supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CURELSS">
        <xs:annotation>
            <xs:documentation>
                LCS call unrelated status. This attribute specifies the status of call unrelated supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="PLMNOSS">
        <xs:annotation>
            <xs:documentation>
                LCS PLMN operator status. This attribute specifies the status of PLMN operator supplementary service.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="lcsProfile" type="xs:string" minOccurs="0"/>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="BasicSelfLocation">
        <xs:annotation>
            <xs:documentation>
                Basic Self Location. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="AutoSelfLocation">
        <xs:annotation>
            <xs:documentation>
                Auto Self Location. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TransferToThirdParty">
        <xs:annotation>
            <xs:documentation>
                Mobile subscriber requests transfer of own location to another LCS client. This attribute can accept a single value entry.
                provided: 1
                active: 2
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Charging characteristics -->
    <xs:complexType name="GeneralChargingCharacteristics">
        <xs:annotation>
            <xs:documentation>
                General Charging characteristics.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="chargingCharacteristics" type="subscriber:ChargingCharacteristics" minOccurs="0" maxOccurs="4"/>
                    <xs:element name="chargingCharacteristicsProfile" type="xs:int" minOccurs="0"/>
                    <xs:element name="chargingCharacteristicsBehavior" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="ChargingCharacteristics">
        <xs:annotation>
            <xs:documentation>
                This attribute specifies allowed charging characteristics. This attribute only accepts a single value
                entry.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="none"/>
            <xs:enumeration value="normal"/>
            <xs:enumeration value="prepaid"/>
            <xs:enumeration value="flatRate"/>
            <xs:enumeration value="hotBilling"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- CallBack -->
    <xs:complexType name="CALLBACK">
        <xs:annotation>
            <xs:documentation>
                Call Back feature is introduced in the GSM mobile system as a national PLMN specific
                supplementary service. Enables a call that cannot be completed to the called subscriber
                to be automatically forwarded to a special number. Normally to a voice mail system (VMS).
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="status" type="xs:int" minOccurs="0"/>
                    <xs:element name="vmsShortCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="cbTimer" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Operator Barring GPRS -->
    <xs:simpleType name="OperatorBarringGPRS">
        <xs:annotation>
            <xs:documentation>
                Operator Determined Barring for packet oriented access (GPRS). This attribute indicates
                which one of the following categories of operator determined barring of GPRS applies to
                the mobile subscriber.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="allPOServices"/>
            <xs:enumeration value="hplmnAccessPoints"/>
            <xs:enumeration value="vplmnAccessPoints"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- UMTS SUbscriber -->
    <xs:complexType name="UmtsSubscriber">
        <xs:annotation>
            <xs:documentation>
                This attribute identifies the type of UMTS Subscription.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="accTypeGSM" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="accTypeGERAN" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="accTypeUTRAN" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- VBS Data-->
    <xs:complexType name="VbsData">
        <xs:annotation>
            <xs:documentation>
                Voice Broadcast Service data.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="broadCastId" type="xs:string" minOccurs="0"/>
                    <xs:element name="initPerm" type="xs:boolean" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FTNO Type -->
    <xs:simpleType name="FtnoType">
        <xs:annotation>
            <xs:documentation>
                Type of FTNO format.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="unknown"/>
            <xs:enumeration value="internat"/>
            <!--xs:enumeration value="longUnknown"/-->
            <!--xs:enumeration value="longInternat"/-->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FeaturesNotSupportedByVLRType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for featuresNotSupportedByVLR in VlrMobData</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="regionalRoaming"/>
            <xs:enumeration value="operatorDetermindBarring"/>
            <xs:enumeration value="closedUserGroup"/>
            <xs:enumeration value="oCSI"/>
            <xs:enumeration value="tCSI"/>
            <xs:enumeration value="sSCSI"/>
            <xs:enumeration value="camelDCSI"/>
            <xs:enumeration value="camelVTCSI"/>
            <xs:enumeration value="camelSMSCSI"/>
            <xs:enumeration value="camelMTSMSCSI"/>
            <xs:enumeration value="camelMCSI"/>
            <xs:enumeration value="sMSMT"/>
            <xs:enumeration value="lateFeature"/>
            <xs:enumeration value="aOCC"/>
            <xs:enumeration value="generalBearerService20"/>
            <xs:enumeration value="generalBearerService30"/>
            <xs:enumeration value="generalBearerService40"/>
            <xs:enumeration value="cCBSA"/>
            <xs:enumeration value="cCBSB"/>
            <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
            <xs:enumeration value="oICK"/>
            <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
            <xs:enumeration value="extCamel"/>
            <xs:enumeration value="superCharger"/>
            <!-- HLR 5.0 LF: FC122_001926: Automatic Redirection of Call -->
            <xs:enumeration value="autoCallRedir"/>
            <xs:enumeration value="sset"/>
            <!--HLR 5.0 SP1 Camel Core INAP -->
            <xs:enumeration value="emoick"/>
            <!--HLR 5.0 SP1 Camel Core INAP -->
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="VlrMobData">
        <xs:annotation>
            <xs:documentation> VlrMobData - Mobility data for VLR (GSM) </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="vlrIdValid" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="isdnNumberOfVLR" type="xs:string" minOccurs="0"/>
                    <xs:element name="mobileTerminatingCallPossible" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="plmnAllowed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="roamingAreaAllowed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mscAreaRestrictedReceived" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="msPurged" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="supportedCAMELPhaseByVLR" type="xs:int" minOccurs="0"/>
                    <xs:element name="supportedMAPVersionForLUP" type="xs:int" minOccurs="0"/>
                    <xs:element name="featuresNotSupportedByVLR" type="subscriber:FeaturesNotSupportedByVLRType" minOccurs="0" maxOccurs="25"/>
                    <xs:element name="prohFtnoUpdInVlrFail" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ts10BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="ts20BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="ts60BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="bs20BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="bs30BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="bs40BarrByCb" type="xs:int" minOccurs="0"/>
                    <xs:element name="vlrSupportsLongFtno" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="supportedLCSCapabilitySetsForVLR" type="xs:int" minOccurs="0"/>
                    <xs:element name="offeredCAMEL4CSIsForVLR" type="xs:int" minOccurs="0"/>
                    <xs:element name="mscNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="previousIsdnNumberOfVLR" type="xs:string" minOccurs="0" maxOccurs="5"/>
                    <xs:element name="emoickSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="ssetSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="locUpdateCSTimestamp" type="xs:dateTime" minOccurs="0"/>
                    <!-- HLR 7.0 base: FC003991_MTRF: Start -->
                    <xs:element name="allowedMTRF" type="xs:boolean" minOccurs="0"/>
                    <!-- HLR 7.0 base: FC003991_MTRF: End -->
                    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
                    <xs:element name="csTraceStatus" type="subscriber:HLRTraceStates" minOccurs="0"/>
                    <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="FeaturesNotSupportedBySGSNType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for featuresNotSupportedBySGSN in SgsnMobData</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="sMS"/>
            <xs:enumeration value="gprsCsi"/>
            <xs:enumeration value="smsCsi"/>
            <xs:enumeration value="mtSmsCsi"/>
            <xs:enumeration value="odb"/>
            <xs:enumeration value="mgCsi"/>
            <!-- HLR 4.5 Ericsson Specific IN Handling (OICK,TICK) -->
            <xs:enumeration value="extCamel"/>
            <xs:enumeration value="superCharger"/>
            <!--HLR 5.0 Base: FC122_002631 Support of Roaming Zone codes from HLR to SGSN -->
            <xs:enumeration value="regionalSubscriptionNotSupSGSN"/>
            <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
            <xs:enumeration value="tadsDataRetrieval"/>
            <!-- HLR 7.0 base: FC122_005378 HLR/HSS interworking MM -->
            <xs:enumeration value="initialAttach"/>
        </xs:restriction>
    </xs:simpleType>
    <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
    <xs:simpleType name="HLRIMSVoiceOverPSSupport">
        <xs:annotation>
            <xs:documentation>ims voice over PS support enum</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
            <xs:enumeration value="imsVoiceOverPSNotSupported"/>
            <xs:enumeration value="imsVoiceOverPSSupported"/>
            <xs:enumeration value="imsVoiceOverPSSupportUnknown"/>
        </xs:restriction>
    </xs:simpleType>
    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
    <xs:simpleType name="HLRTraceStates">
        <xs:annotation>
            <xs:documentation>Gsm Tracing states enum </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
            <xs:enumeration value="ACTIVE"/>
            <xs:enumeration value="ACTIVEPENDING"/>
            <xs:enumeration value="NOTACTIVE"/>
        </xs:restriction>
    </xs:simpleType>
    <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
    <xs:complexType name="SgsnMobData">
        <xs:annotation>
            <xs:documentation> SgsnMobData - Mobility data for SGSN (GPRS) </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="sgsnIdValid" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="isdnNumberOfSGSN" type="xs:string" minOccurs="0"/>
                    <xs:element name="plmnAllowed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="roamingAreaAllowed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="gprsAllowed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="supportedCAMELPhaseBySGSN" type="xs:int" minOccurs="0"/>
                    <xs:element name="supportedMAPVersionForLUP" type="xs:int" minOccurs="0"/>
                    <xs:element name="featuresNotSupportedBySGSN" type="subscriber:FeaturesNotSupportedBySGSNType" minOccurs="0" maxOccurs="11"/>
                    <xs:element name="sgsnCamelNot" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="sgsnExtQos" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="gprsCsiSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="gprsSmsCsiSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="gprsMtSmsCsiSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="mgCsiSubst" type="xs:int" minOccurs="0"/>
                    <xs:element name="supportedLCSCapabilitySetsForSGSN" type="xs:int" minOccurs="0"/>
                    <xs:element name="offeredCAMEL4CSIsForSGSN" type="xs:int" minOccurs="0"/>
                    <xs:element name="sgsnAddress" type="xs:string" minOccurs="0"/>
                    <xs:element name="previousIsdnNumberOfSGSN" type="xs:string" minOccurs="0" maxOccurs="5"/>
                    <xs:element name="msPurged" type="xs:boolean" minOccurs="0"/>
                    <!--HLR 5.0 Base: FC122_002631 Support of Roaming Zone codes from HLR to SGSN -->
                    <xs:element name="sgsnAreaRestRcvd" type="xs:boolean" minOccurs="0"/>
                    <!--HLR 5.0 SP1: FC122_003495-T-ADSEPS&PS -->
                    <xs:element name="imsVoiceOverPS" type="subscriber:HLRIMSVoiceOverPSSupport" minOccurs="0"/>
                    <xs:element name="ueSrvccCapability" type="xs:int" minOccurs="0"/>
                    <!-- FCC122_003520 -->
                    <xs:element name="locUpdatePSTimestamp" type="xs:dateTime" minOccurs="0"/>
                    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
                    <xs:element name="psTraceStatus" type="subscriber:HLRTraceStates" minOccurs="0"/>
                    <!--HLR 7.0 SP1:DX HLR compliant IMSI tracing according 3GPP end-->
					<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
		   <xs:element name="hlrSgsnSupportedFeatures" type="subscriber:BitString40" minOccurs="0"/>
		   <!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Sytax definition Bit String 40 -->
  <!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
  <xs:simpleType name="BitString">
    <xs:annotation>
      <xs:documentation>Only 0|1 is allowed</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xsd:string">
      <xs:pattern value="(0|1)+"/>
    </xs:restriction>
  </xs:simpleType>
	<xs:simpleType name="BitString40">
		<xs:annotation>
			<xs:documentation>Bit string with length 40 characters exactly</xs:documentation>
		</xs:annotation>
		<xs:restriction base="subscriber:BitString">
			<xs:minLength value="40"/>
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - end -->
    <xs:simpleType name="MobileStationNotReachableReasonType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for mobileStationNotReachableReason... in Data</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="msNoPagingRespViaMsc"/>
            <xs:enumeration value="msImsiDetached"/>
            <xs:enumeration value="msRoamingRestriction"/>
            <xs:enumeration value="msDeregInHLRForNonGPRS"/>
            <xs:enumeration value="msPurgedForNonGPRS"/>
            <xs:enumeration value="msNoPagingRespViaSGSN"/>
            <xs:enumeration value="msGPRSDetached"/>
            <xs:enumeration value="msDeregInHLRForGPRS"/>
            <xs:enumeration value="msPurgedForGPRS"/>
            <xs:enumeration value="unidentifiedSubViaMSC"/>
            <xs:enumeration value="unidentifiedSubViaSGSN"/>
            <xs:enumeration value="deregInHlrHssForIMS"/>
            <xs:enumeration value="noResponseViaIP_SM_GW"/>
            <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
            <xs:enumeration value="ueDeregistered"/>
            <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
            <!-- FC123_107926 HLR supports MSISDN-less MTC devices: Start -->
            <xs:enumeration value="msTemporarilyUnavailable"/>
            <!-- FC123_107926 HLR supports MSISDN-less MTC devices: End-->
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MsgWaitData">
        <xs:annotation>
            <xs:documentation> MsgWaitData - SMS message waiting data </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="msisdnUsedForAlertingOfSMSServiceCentre" type="xs:string" minOccurs="0"/>
                    <xs:element name="mobileCapacityExceededFlag" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mobileStationNotReachableFlag" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="addressOfSMSServicesCentres" type="xs:string" minOccurs="0" maxOccurs="32"/>
                    <xs:element name="mobileStationNotReachableFlagForGPRS" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="mobileStationNotReachableReasonForGPRS" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
                    <xs:element name="mobileStationNotReachableReasonForGSM" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
                    <xs:element name="alertMsisdn" type="xs:string" minOccurs="0" maxOccurs="32"/>
                    <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
                    <xs:element name="ueNotReachableForIP" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="ueNotReachableReason" type="subscriber:MobileStationNotReachableReasonType" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="INService">
        <xs:annotation>
            <xs:documentation> IN Service</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="OICK">
        <xs:annotation>
            <xs:documentation> Originating IN Category Key</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:INService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="EOICK">
        <xs:annotation>
            <xs:documentation> Extended CAMEL Originating IN Category Key</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:INService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="EOINCI">
        <xs:annotation>
            <xs:documentation> Extended CAMEL Originating IN Capability Indicator</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:INService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TICK">
        <xs:annotation>
            <xs:documentation> Terminating IN Category Key</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:TerminatingINService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ETICK">
        <xs:annotation>
            <xs:documentation> Extended CAMEL Terminating IN Category Key</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:TerminatingINService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ETINCI">
        <xs:annotation>
            <xs:documentation> Extended CAMEL Terminating IN Capability Indicator</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:TerminatingINService"/>
        </xs:complexContent>
    </xs:complexType>
    <!-- define XML schema for flexible msisdn change -->
    <xs:complexType name="FlexibleMsisdnChangeOperation">
        <xs:complexContent>
            <xs:extension base="spml:AbstractOperation">
                <xs:sequence>
                    <xs:element name="regularExpression" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- new service  usable only for msisdn based searches-->
    <xs:complexType name="ALLBS">
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="Property">
        <attribute name="name" type="xs:string"/>
        <attribute name="value" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="Properties">
        <xs:sequence>
            <xs:element name="property" type="subscriber:Property" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SubMigrationOperation">
        <xs:complexContent>
            <xs:extension base="spml:AbstractMigration">
                <xs:sequence>
                    <xs:element name="sourceDataModel" type="xs:string"/>
                    <xs:element name="targetDataModel" type="xs:string"/>
                    <element name="properties" type="subscriber:Properties"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="TerminatingINService">
        <xs:annotation>
            <xs:documentation>Terminating IN Service</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
                    <xs:element name="roamingType" type="subscriber:RoamingType" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="RoamingType">
        <xs:annotation>
            <xs:documentation>This attribute specifies whether the subscriber is roaming in HPLMN or outside HPLMN</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HPLMN"/>
            <xs:enumeration value="OutsideHPLMN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="OdbEctType">
        <xs:annotation>
            <xs:documentation>This attribute indicates which of the categories of operator determined barring of explicit call transfer calls applies to the mobile subscriber</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="all"/>
            <xs:enumeration value="chargeable"/>
            <xs:enumeration value="international"/>
            <xs:enumeration value="interzonal"/>
            <xs:enumeration value="doublyChargeable"/>
            <xs:enumeration value="multiple"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ClgPtyData">
        <xs:annotation>
            <xs:documentation>
            This attribute contains information concerning the last calling number
            e.g. it indicates whether the presenation of the calling number is allowed or restricted and if the number
            was already sent in an USSD_Response to the subscriber. Multiple logical values are stored as one value
            combined by OR.
            Values:
            PresentationAllowed = 0
            PresentationRestricted = 1
            NumberUnavailable = 4
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="presentationAllowed"/>
            <xs:enumeration value="presentationRestricted"/>
            <xs:enumeration value="numberUnavailable"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="UnsignedInt9">
        <xs:annotation>
            <xs:documentation>Integer in the range 0..9</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:nonNegativeInteger">
            <xs:maxInclusive value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NumericString">
        <xs:annotation>
            <xs:documentation>Only digits allowed</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="\d+"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NumericString6_15">
        <xs:annotation>
            <xs:documentation>Numeric String of length 15</xs:documentation>
        </xs:annotation>
        <xs:restriction base="subscriber:NumericString">
            <xs:minLength value="6"/>
            <xs:maxLength value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MultiSimMemberType">
        <xs:annotation>
            <xs:documentation>Support of multi SIM member</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="multiSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
                    <xs:element name="multiSimImsi" type="subscriber:NumericString6_15" minOccurs="0"/>
                    <xs:element name="basicServiceGroup" type="subscriber:BarringBasicServiceGroupType" minOccurs="0" maxOccurs="6"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="MultiSimType">
        <xs:annotation>
            <xs:documentation>Support of multi SIM functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="subSimId" type="subscriber:UnsignedInt9" minOccurs="0"/>
                    <xs:element name="multiSimMember" type="subscriber:MultiSimMemberType" minOccurs="0" maxOccurs="10"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="USSDService">
        <xs:annotation>
            <xs:documentation> USSD Service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="active" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="refAdvtName" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="LcnService">
        <xs:annotation>
            <xs:documentation> LCN </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:USSDService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="OwnMsisdnService">
        <xs:annotation>
            <xs:documentation> OWN MSISDN </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:USSDService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="VlrIdService">
        <xs:annotation>
            <xs:documentation> VLR ID </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:USSDService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ActualTimeService">
        <xs:annotation>
            <xs:documentation> ACTUAL TIME </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:USSDService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="UssdClirService">
        <xs:annotation>
            <xs:documentation> USSD CLIR Service </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:USSDService"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="UCSISUBEXT">
        <xs:annotation>
            <xs:documentation> UCSISUBEXT It controls access to the account Query etc. This object can be applied multiple times</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="ucsiServCodeExt" type="xs:string" minOccurs="0"/>
                    <xs:element name="gsmscfAddress" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for UGCSI Feature: Start-->
                    <xs:element name="origRefIndicator" type="subscriber:HLRRefIndicatorType" minOccurs="0"/>
                    <xs:element name="destRefIndicator" type="subscriber:HLRRefIndicatorType" minOccurs="0"/>
                    <xs:element name="ussdSendingOptions" type="subscriber:HLRUssdSendingOptions" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for UGCSI Feature: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="HlrProfileType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Subscriber Profile Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="mssMultiSim"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HlrMssMultiSimType">
        <xs:annotation>
            <xs:documentation>Support of MSC-S based MultiSim for subscriber functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="denyDirectSMS" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="denyDirectCall" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="groupMSISDN" type="xs:string" minOccurs="0"/>
                    <xs:element name="primarySMSTerminal" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
                    <xs:element name="allowSyncToGroup" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
                    <!-- FC122_006583 - USSD for MSS based MultiSIM hunting group - START -->
                    <xs:element name="refAdvtName" type="xs:string" minOccurs="0"/>
                    <!-- FC122_006583 - USSD for MSS based MultiSIM hunting group - END -->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- HLR 5.0: FC122_003600: Spatial Trigger -->
    <xs:complexType name="SpatialTrigger">
        <xs:annotation>
            <xs:documentation>Spatial Trigger functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="spatialTriggerEvent" type="xs:int" minOccurs="0"/>
                    <xs:element name="gmlcId" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Definitions of application specific extensions of SPML  schema END-->
    <!-- hlr45.v100 END -->
    <!--************************************************************************-->
    <!-- IDENTITY SWAP Related Identifiers                                      -->
    <!--************************************************************************-->
    <xs:simpleType name="HLRSwapAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="imsi"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HLRSwapIdentifier">
        <xs:simpleContent>
            <xs:extension base="spml:ID">
                <xs:attribute name="alias" type="subscriber:HLRSwapAliasType" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <!-- canmsub.v100 START -->
    <!--************************************************************************-->
    <!--                         CanMsub Operation                              -->
    <!--************************************************************************-->
    <xs:complexType name="canMsubOperation">
        <xs:annotation>
            <xs:documentation> Defines the canMsub extended operation. The vlr.sgsn and mme types of
                cancelation is supported.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:AbstractOperation">
                <xs:sequence>
                    <xs:element name="imsi" type="subscriber:NumericString6_15" minOccurs="0"/>
                    <xs:element name="vlr" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <xs:element name="sgsn" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <xs:element name="mme" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <!-- DU3744 : Changed as per Changes of Common AEP 14.01-->
                    <xs:element name="mmeRealm" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <!-- attributes of S6D interface BEGIN-->
                    <xs:element name="sgsnIdentity" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <xs:element name="sgsnRealm" type="subscriber:CanMsubTarget" minOccurs="0"/>
                    <xs:element name="msubPurgedGprs" type="subscriber:CanMsubTargetBoolean" minOccurs="0"/>
                    <xs:element name="gprsDataSentIndication" type="subscriber:CanMsubTargetBoolean" minOccurs="0"/>
                    <!-- attributes of S6D interface END-->
                    <xs:element name="force" type="xs:boolean" minOccurs="0" default="false"/>

                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

       <xs:complexType name="CanMsubTargetBoolean">
        <xs:annotation>
                        <xs:documentation> Defines optional val attribute (of type boolean) for canMsub targets</xs:documentation>
        </xs:annotation>
                <attribute name="val" type="xsd:boolean"/>
    </xs:complexType>

    <!-- DU3744 : Changed as per Changes of Common AEP 14.01-->
    <xs:complexType name="CanMsubSubscriber">
        <xs:annotation>
            <xs:documentation> Defines the canMsub subscriber firstclass object </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                    <!-- imsi value is stored in the identifier attribute of the FCO -->
                    <xs:element name="vlr" type="xs:string" minOccurs="0"/>
                    <xs:element name="sgsn" type="xs:string" minOccurs="0"/>
                    <xs:element name="mme" type="xs:string" minOccurs="0"/>
                    <xs:element name="mmeRealm" type="xs:string" minOccurs="0"/>
                        <xs:element name="mmeHostId" type="xsd:string" minOccurs="0"/>
                        <xs:element name="sgsnIdentity" type="xsd:string" minOccurs="0"/>
                        <xs:element name="sgsnRealm" type="xsd:string" minOccurs="0"/>
                        <xs:element name="msubPurgedGprs" type="xsd:string" minOccurs="0"/>
                        <xs:element name="gprsDataSentIndication" type="xsd:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

        <xs:complexType name="CanMsubTarget">
            <xs:annotation>
                <xs:documentation> Defines optional address attribute for canMsub targets</xs:documentation>
            </xs:annotation>
            <attribute name="address" type="xs:string"/>
        </xs:complexType>

    <!-- canmsub.v100 END -->
    <!-- mssearch.v100 START -->
    <!--************************************************************************-->
    <!-- MSSearch Operation                                                     -->
    <!--************************************************************************-->
    <xs:simpleType name="IMSI">
        <xs:annotation>
            <xs:documentation>IMSI, International Mobile Subscriber Identity </xs:documentation>
        </xs:annotation>
        <xs:restriction base="subscriber:NumericString">
               <xs:minLength value="6"/>
               <xs:maxLength value="15"/>
            </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="GeographicalInformation">
        <xs:sequence>
            <xs:element name="geographicalShape" type="subscriber:GeographicalShape" minOccurs="0"/>
            <xs:element name="geographicalLatitude" type="int" minOccurs="0"/>
            <xs:element name="geographicalLongitude" type="int" minOccurs="0"/>
            <xs:element name="geographicalUncertaintyCode" type="int" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="GeographicalShape">
        <xs:restriction base="string">
            <xs:enumeration value="Ellips.Point+Uncert.Circle"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="GeodeticInformation">
        <xs:sequence>
            <xs:element name="geodeticPresentation" type="subscriber:GeodeticPresentation" minOccurs="0"/>
            <xs:element name="geodeticScreeningIndicator" type="subscriber:GeodeticScreeningIndicator" minOccurs="0"/>
            <xs:element name="geodeticShape" type="subscriber:GeodeticShape" minOccurs="0"/>
            <xs:element name="geodeticLatitude" type="int" minOccurs="0"/>
            <xs:element name="geodeticLongitude" type="int" minOccurs="0"/>
            <xs:element name="geodeticUncertaintyCode" type="int" minOccurs="0"/>
            <xs:element name="geodeticConfidence" type="int" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="GeodeticPresentation">
        <xs:restriction base="string">
            <xs:enumeration value="presentation allowed"/>
            <xs:enumeration value="presentation restricted"/>
            <xs:enumeration value="location not available"/>
            <xs:enumeration value="spare"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="GeodeticScreeningIndicator">
        <xs:restriction base="string">
            <xs:enumeration value="user provided, not verified"/>
            <xs:enumeration value="user provided, verified and passed"/>
            <xs:enumeration value="user provided, verified and failed"/>
            <xs:enumeration value="network provided"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="GeodeticShape">
        <xs:restriction base="string">
            <xs:enumeration value="Ellipsoid Point"/>
            <xs:enumeration value="Ellips.Point+Uncert.Circle"/>
            <xs:enumeration value="Point with Altit.+Uncert."/>
            <xs:enumeration value="ellipse on the ellipsoid"/>
            <xs:enumeration value="ellipsoid circle sector"/>
            <xs:enumeration value="polygon"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NetDetNotReachable">
        <xs:restriction base="string">
            <xs:enumeration value="msPurged"/>
            <xs:enumeration value="imsiDetached"/>
            <xs:enumeration value="restrictedArea"/>
            <xs:enumeration value="notRegistered"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SubscriberState">
        <xs:choice>
            <xs:element name="assumedIdle" type="boolean"/>
            <xs:element name="camelBusy" type="boolean"/>
            <xs:element name="netDetNotReachable" type="subscriber:NetDetNotReachable"/>
            <xs:element name="notProvidedFromVLR" type="boolean"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CellIdOrLAI">
        <xs:sequence>
            <xs:element name="cellGlobalIdOrServiceAreaIdFixedLength" type="subscriber:CellGlobalIdOrServiceAreaIdFixedLength" minOccurs="0"/>
            <xs:element name="laiFixedLength" type="subscriber:LaiFixedLength" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CellGlobalIdOrServiceAreaIdFixedLength">
        <xs:sequence>
            <xs:element name="mccMnc" type="string" minOccurs="0"/>
            <xs:element name="locationAreaCode" type="int" minOccurs="0"/>
            <xs:element name="cellIdentity" type="int" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LaiFixedLength">
        <xs:sequence>
            <xs:element name="mccMnc" type="string" minOccurs="0"/>
            <xs:element name="locationAreaCode" type="int" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LocationInformation">
        <xs:sequence>
            <xs:element name="ageOfLocationInformation" type="int" minOccurs="0"/>
            <xs:element name="geographicalInformation" type="subscriber:GeographicalInformation" minOccurs="0"/>
            <xs:element name="vlr-number" type="string" minOccurs="0"/>
            <xs:element name="locationNumber" type="string" minOccurs="0"/>
            <xs:element name="cellIdOrLAI" type="subscriber:CellIdOrLAI" minOccurs="0"/>
            <xs:element name="selectedLSA-Id" type="string" minOccurs="0"/>
            <xs:element name="msc-Number" type="string" minOccurs="0"/>
            <xs:element name="geodeticInformation" type="subscriber:GeodeticInformation" minOccurs="0"/>
            <xs:element name="currentLocationRetrieved" type="boolean" minOccurs="0"/>
            <xs:element name="sai-Present" type="boolean" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="msSearchOperation">
        <xs:complexContent>
            <xs:extension base="spml:AbstractOperation">
                <xs:sequence>
                    <xs:element name="imsi" type="subscriber:IMSI"/>
                    <xs:element name="infoText" type="xs:string" minOccurs="0"/>
                    <xs:element name="locationInformation" type="subscriber:LocationInformation" minOccurs="0"/>
                    <xs:element name="subscriberState" type="subscriber:SubscriberState" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- mssearch.v100 END -->
    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes Begin -->
    <xs:complexType name="ROAMSUBSCRIPTION">
        <xs:annotation>
            <xs:documentation> This attributes indicates a symbolic name identifying the Roaming Subscription Info entry. </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="roamSubscriptionInfo" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FC122_000454: Roaming Dependent Subscriber Profile Changes End  -->
    <!--************************************************************************-->
    <!--                               HLR Group aliases                        -->
    <!--************************************************************************-->
    <xs:simpleType name="HLRGroupIdentifierAliasType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="identifier"/>
            <xs:enumeration value="msisdn"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HLRGroupIdentifier">
        <xs:simpleContent>
            <xs:restriction base="spml:Identifier">
                <xs:attribute name="alias" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRGroupIdentifierFileNameType">
        <xs:simpleContent>
            <xs:restriction base="spml:IdentifierFileNameType">
                <xs:attribute name="alias" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
                <xs:attribute name="filterType" type="spml:IdentifierFilterType" use="required"/>
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="HLRGroupAliasType">
        <xs:complexContent>
            <xs:restriction base="spml:AliasType">
                <xs:attribute name="name" type="subscriber:HLRGroupIdentifierAliasType" use="required"/>
                <xs:attribute name="value" type="xs:string" use="required"/>
            </xs:restriction>
        </xs:complexContent>
    </xs:complexType>
    <!--************************************************************************-->
    <!--         GROUP First Class Objects    STARTS                            -->
    <!--************************************************************************-->
    <xs:complexType name="HlrGroup">
        <xs:annotation>
            <xs:documentation>Definition of class group </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:FirstClassObject">
                <xs:sequence>
                    <xs:element name="alertingType" type="subscriber:HlrGroupAlertingType" minOccurs="0"/>
                    <xs:element name="alertingTimeParallel" type="xs:int" minOccurs="0"/>
                    <xs:element name="alertingTimeSequence" type="xs:int" minOccurs="0"/>
                    <xs:element name="groupType" type="subscriber:HlrGroupType" minOccurs="0"/>
                    <xs:element name="toneType" type="subscriber:HlrGroupToneType" minOccurs="0"/>
                    <xs:element name="memberCfSupp" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="primaryLBSTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="primarySMSTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="alertDataCalls" type="subscriber:HlrGroupAlertDataCallsType" minOccurs="0"/>
                    <xs:element name="odbic" type="xs:int" minOccurs="0"/>
                    <xs:element name="groupBasicService" type="subscriber:HlrGroupBasicService" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfu" type="subscriber:CFU" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfb" type="subscriber:CFB" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfnrc" type="subscriber:CFNrc" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfnry" type="subscriber:CFNry" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cfd" type="subscriber:CFD" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="msgWaitData" type="subscriber:MsgWaitData" minOccurs="0"/>
                    <xs:element name="member" type="subscriber:HlrGroupMemberType" minOccurs="0" maxOccurs="5"/>
                    <xs:element name="sset" type="subscriber:HLRSSET" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: Start-->
                    <xs:element name="primaryVoiceTerminal" type="xs:string" minOccurs="0"/>
                    <xs:element name="ocsi" type="subscriber:OCSI" minOccurs="0"/>
                    <xs:element name="tcsi" type="subscriber:TCSI" minOccurs="0"/>
                    <xs:element name="odboc" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <xs:element name="refRestrictedAreaName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
                    <xs:element name="syncEnabled" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="caw" type="subscriber:CAW" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="comcb" type="subscriber:COMCB" minOccurs="0"/>
                    <xs:element name="baoc" type="subscriber:BAOC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boic" type="subscriber:BOIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="boicexhc" type="subscriber:BOICexHC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="baic" type="subscriber:BAIC" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="bicroam" type="subscriber:BICRoam" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
                    <xs:element name="huntingTypeForSms" type="subscriber:HlrGroupSmsHuntingType" minOccurs="0"/>
                    <!-- MSS Multisim enhancements - change - BEGIN -->
                    <!--<xs:element name="refPriorityListName" type="xs:string" minOccurs="0"/>-->
                    <xs:element name="smsSubData" type="subscriber:HlrSmsSubData" minOccurs="0"/>
                    <!-- MSS Multisim enhancements - change - END -->
                    <xs:element name="refAddressListName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
                    <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
                    <xs:element name="primaryBasicService" type="subscriber:HLRGRPPBS" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - Start -->
                    <xs:element name="cf" type="subscriber:HLRCallForward" minOccurs="0"/>
                    <!-- FC123_106922_CF_Notification_Override - End -->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HlrGroupMemberType">
        <xs:annotation>
            <xs:documentation>Support of MSC-S based MultiSim for group functionality</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="msisdn" type="xs:string" minOccurs="0"/>
                    <xs:element name="imsi" type="xs:string" minOccurs="0"/>
                    <xs:element name="huntingOrder" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: Start-->
                    <xs:element name="restrictedCsPlmnId" type="xs:int" minOccurs="0"/>
                    <xs:element name="restrictedPsPlmnId" type="xs:int" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - RoamingRestrictions: End-->
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: Start-->
                    <xs:element name="allowSyncFromGroup" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for MSS Multisim - SS Synchronization: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HlrGroupBasicService">
        <xs:annotation>
            <xs:documentation>List basic services that can be assigned to a Group </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="serviceName" type="subscriber:HlrGroupServiceNameType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="HlrGroupServiceNameType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Profile Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ts11"/>
            <xs:enumeration value="ts21"/>
            <xs:enumeration value="ts22"/>
            <xs:enumeration value="ts61"/>
            <xs:enumeration value="ts62"/>
            <xs:enumeration value="bs20genr"/>
            <xs:enumeration value="bs21"/>
            <xs:enumeration value="bs22"/>
            <xs:enumeration value="bs23"/>
            <xs:enumeration value="bs24"/>
            <xs:enumeration value="bs25"/>
            <xs:enumeration value="bs26"/>
            <xs:enumeration value="bs30genr"/>
            <xs:enumeration value="bs31"/>
            <xs:enumeration value="bs32"/>
            <xs:enumeration value="bs33"/>
            <xs:enumeration value="bs34"/>
            <xs:enumeration value="bs40genr"/>
            <xs:enumeration value="bs41"/>
            <xs:enumeration value="bs42"/>
            <xs:enumeration value="bs44"/>
            <xs:enumeration value="bs45"/>
            <xs:enumeration value="bs46"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Group Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="singleUser"/>
            <xs:enumeration value="multipleUser"/>
            <xs:enumeration value="singleUserSpecial"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupAlertingType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Alerting Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="bothWithParallelPrefered"/>
            <xs:enumeration value="parallel"/>
            <xs:enumeration value="sequential"/>
            <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: Start-->
            <xs:enumeration value="intelligent"/>
            <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for Voice: End-->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupAlertDataCallsType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Alert Data calls Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="notAlerted"/>
            <xs:enumeration value="sequential"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HlrGroupToneType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Tone Type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="simpleTone"/>
            <xs:enumeration value="ringbackTone"/>
        </xs:restriction>
    </xs:simpleType>
    <!--************************************************************************-->
    <!--           GROUP First Class Objects    ENDS                            -->
    <!--************************************************************************-->
    <!-- HLR 5.0 LF: FC122_003888 - Proprietary Nokia IN Short Message Service -->
    <xs:complexType name="INSMS">
        <xs:annotation>
            <xs:documentation> IN Short Message Service</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
                    <xs:element name="state" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- HLR 5.0 LF: FC122_001926 - Automatic Redirection of Calls -->
    <xs:complexType name="ARC">
        <xs:annotation>
            <xs:documentation> Automatic Redirection of Calls</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="active" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="redirInd" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- Changes for FC122_003779::Account Code Handling -->
    <xs:complexType name="HlrAcc">
        <xs:annotation>
            <xs:documentation> Account Code </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="active" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="accountCode" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- HLR 5.0 SP1:FC122_003784: PRN retry  -->
    <xs:complexType name="HlrCSFallback">
        <xs:annotation>
            <xs:documentation>HLR CS Fallback subscription</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="prnRetry" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for CSFallback - MTRR Feature: Start-->
                    <xs:element name="mtrr" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for CSFallback - MTRR Feature: End-->
                    <!-- NTHLR7 - Introduced for CSFallback - MTRF Feature: Start-->
                    <xs:element name="mtrf" type="xs:boolean" minOccurs="0"/>
                    <!-- NTHLR7 - Introduced for CSFallback - MTRF Feature: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--HLR 5.0 SP1: FC122_002113_SMSImprovements-->
    <xs:complexType name="HlrSmsSubData">
        <xs:annotation>
            <xs:documentation>HLR SMSSubData</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="refPriorityListName" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: Start-->
                    <xs:element name="ipSmGwAddress" type="xs:string" minOccurs="0"/>
                    <!-- NTHLR6 - Introduced for SMS Step 2(IP_SM_GW) Feature: End-->
                    <!--NTHLR7 - Introduced for FC122_005383: HLR-HSS interworkingFeature:Start-->
                    <xs:element name="refSCAddress" type="xs:string" minOccurs="0"/>
                    <!--NTHLR7- Introduced for FC122_005383: HLR-HSS interworking Feature: End-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR start -->
    <xs:complexType name="HLRGSMTrace">
        <xs:annotation>
            <xs:documentation>HLR gsmtracing</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="traceReference" type="xs:int" minOccurs="0"/>
                    <xs:element name="omcId" type="xs:string" minOccurs="0"/>
                    <xs:element name="csTraceActive" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="csTraceType" type="xs:int" minOccurs="0"/>
                    <xs:element name="psTraceActive" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="psTraceType" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <!--HLR 7.0 SP1:FC122_005000 IMSI Tracing according DX HLR end -->
    <xs:complexType name="HLRServiceType">
        <xs:annotation>
            <xs:documentation> Service Type</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="operatorServiceName" type="xs:string" minOccurs="0"/>
                    <xs:element name="state" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HLRSSET">
        <xs:annotation>
            <xs:documentation> SSET CAMEL CORE INAP information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:HLRServiceType"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="HLREMOICK">
        <xs:annotation>
            <xs:documentation> EMOICK CAMEL CORE INAP information </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:HLRServiceType"/>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="UEReachabilityReqInfo">
        <xs:annotation>
            <xs:documentation>UE-Reachability request information</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="urppMME" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="urppSGSN" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="asAddressSh" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for UEReachability MAPJ Support: Start-->
                    <xs:element name="asAddressMAPJ" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <!-- NTHLR6 - Introduced for UEReachability MAPJ Support: Start-->
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- NTHLR6 - Introduced for UGCSI Feature: Start-->
    <xs:simpleType name="HLRRefIndicatorType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for Reference Indicator</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IMSI"/>
            <xs:enumeration value="MSISDN"/>
            <xs:enumeration value="HLR-NUM"/>
            <xs:enumeration value="VLR-NUM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HLRUssdSendingOptions">
        <xs:annotation>
            <xs:documentation>Set of options that control the sending of parameters in USSD messages</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Send MSISDN in OEN in BSA"/>
            <xs:enumeration value="Send HLR NUM in OEN in BSA"/>
            <xs:enumeration value="Send VLR NUM in OEN in BSA"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- NTHLR6 - Introduced for UGCSI Feature: End-->
    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: Start-->
    <xs:simpleType name="HlrGroupSmsHuntingType">
        <xs:annotation>
            <xs:documentation>Enumeration of valid values for HuntingType</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="primaryTerminal"/>
            <xs:enumeration value="intelligentHunting"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- NTHLR6 - Introduced for MSS Multisim - InteligentHunting for SMS: End-->
    <!-- HLR 7.0 base: FC122_004632 CSARP START -->
    <xs:complexType name="HLRCSARP">
        <xs:annotation>
            <xs:documentation> CS Allocation / Retention Priority</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="active" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="priority" type="xs:int" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- HLR 7.0 base: FC122_004632 CSARP END -->

    <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: Start -->
    <xs:complexType name="HLRFraudObservationInfo">
        <xs:annotation>
            <xs:documentation>Subscriber fraud observation information
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="observationName" type="xs:string" minOccurs="0" />
                    <xs:element name="observationNumber" type="xs:int" minOccurs="0" />
                    <xs:element name="samplingSlot" type="xs:int" minOccurs="0" />
                    <xs:element name="currentEventCount" type="xs:int" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FC122_005795 - Subscriber fraud observation - phase 2 (CFR) :: End -->
    <!-- FC122_005801_  Charging_based_on_Home_Area :: Start -->
    <xs:complexType name="HLRChargingBasedOnHomeArea">
        <xs:annotation>
            <xs:documentation>Subscriber charging based on home area information
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="subscriber:Service">
                <xs:sequence>
                    <xs:element name="chargingClass" type="subscriber:HLRChargingClassTypes" minOccurs="0" />
                    <xs:element name="chargingArea" type="xs:int" minOccurs="0" maxOccurs="3" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
     <xs:simpleType name="HLRChargingClassTypes">
        <xs:annotation>
        <xs:documentation>Charging Class types enum </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
            <xs:enumeration value="regional"/>
            <xs:enumeration value="national"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- FC122_005801_  Charging_based_on_Home_Area :: End -->
    <!-- FC123_106922_CF_Notification_Override - Start -->
    <xs:complexType name="HLRCallForward">
        <xs:annotation>
            <xs:documentation>Subscriber call forwarding services information
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="refHlrFtnoProfile" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- FC123_106922_CF_Notification_Override - End -->
    <!-- FC122_006191 DX HLRe vs. NT HLR missing parity_parameter PBS (Primary Basic Service) -->
     <xs:simpleType name="HLRPBS">
        <xs:annotation>
            <xs:documentation>This attribute specifies the values for PBS.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
             <xs:enumeration value="notDefined"/>
            <xs:enumeration value="ts11"/>
            <xs:enumeration value="ts21"/>
            <xs:enumeration value="ts22"/>
            <xs:enumeration value="ts61"/>
            <xs:enumeration value="ts62"/>
            <xs:enumeration value="vgcs"/>
            <xs:enumeration value="vbs"/>
            <xs:enumeration value="bs20genr"/>
            <xs:enumeration value="bs21"/>
            <xs:enumeration value="bs22"/>
            <xs:enumeration value="bs23"/>
            <xs:enumeration value="bs24"/>
            <xs:enumeration value="bs25"/>
            <xs:enumeration value="bs26"/>
            <xs:enumeration value="bs30genr"/>
            <xs:enumeration value="bs31"/>
            <xs:enumeration value="bs32"/>
            <xs:enumeration value="bs33"/>
            <xs:enumeration value="bs34"/>
            <xs:enumeration value="bs40genr"/>
            <xs:enumeration value="bs41"/>
            <xs:enumeration value="bs42"/>
            <xs:enumeration value="bs44"/>
            <xs:enumeration value="bs45"/>
            <xs:enumeration value="bs46"/>
            <xs:enumeration value="bs61a"/>
            <xs:enumeration value="bs81a"/>
            <xs:enumeration value="gprs"/>
            <xs:enumeration value="ts21Gprs"/>
            <xs:enumeration value="ts22Gprs"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="HLRGRPPBS">
        <xs:annotation>
            <xs:documentation>This attribute specifies the values for PBS For HLR Group.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="notDefined"/>
            <xs:enumeration value="ts11"/>
            <xs:enumeration value="ts21"/>
            <xs:enumeration value="ts22"/>
            <xs:enumeration value="ts61"/>
            <xs:enumeration value="ts62"/>
            <xs:enumeration value="bs20genr"/>
            <xs:enumeration value="bs21"/>
            <xs:enumeration value="bs22"/>
            <xs:enumeration value="bs23"/>
            <xs:enumeration value="bs24"/>
            <xs:enumeration value="bs25"/>
            <xs:enumeration value="bs26"/>
            <xs:enumeration value="bs30genr"/>
            <xs:enumeration value="bs31"/>
            <xs:enumeration value="bs32"/>
            <xs:enumeration value="bs33"/>
            <xs:enumeration value="bs34"/>
            <xs:enumeration value="bs40genr"/>
            <xs:enumeration value="bs41"/>
            <xs:enumeration value="bs42"/>
            <xs:enumeration value="bs44"/>
            <xs:enumeration value="bs45"/>
            <xs:enumeration value="bs46"/>
        </xs:restriction>
    </xs:simpleType>
    <!--FST123_107470 - Enhanced control of CF types to non-Macau international number-->
    <xs:simpleType name="HLROdbFtnoStandard">
        <xs:annotation>
            <xs:documentation>This attribute specifies the values for OdbFtnoStandard.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">            
            <xs:enumeration value="barIntFtnoExHplmnC"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HLROdbFtnoProprietary">
        <xs:annotation>
            <xs:documentation>This attribute specifies the values for OdbFtnoProprietary.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">           
            <xs:enumeration value="barIntCondCfFtnoExHplmnC"/>
        </xs:restriction>
    </xs:simpleType>
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - Start -->
	 <xs:complexType name="HLRSgsnSupportedFeatures">
        <xs:annotation>
            <xs:documentation>Subscriber Sgsn Supported Features information
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="spml:SecondClassObject">
                <xs:sequence>
                    <xs:element name="feature" type="subscriber:HLRFeatureList" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="HLRFeatureList">
        <xs:annotation>
        <xs:documentation>This is an enumeration of all the supported features 
			of the origin hosts in S6a/S6d interface as defined by 3GPP.
		</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xsd:string">
            <xs:enumeration value="nrAsSecondaryRAT"/>
        </xs:restriction>
    </xs:simpleType>
	<!-- FC123_109029 Extended ARD in HLR FE for 5G Option 3 support - End -->
</xs:schema>
