# NEO Client Content-Length Header Fix

## Problem Description

The NEO client was experiencing consistent `org.apache.http.ProtocolException: Content-Length header already present` errors across all operations (provide, suspend, resume, cease) when using HTTPS.

### Error Details
```
Caused by: org.apache.http.ProtocolException: Content-Length header already present
org.apache.http.client.ClientProtocolException: null
        at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:187)
        at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
        ...
NEO PROVIDE operation failed for IMSI: 310170523410085 - Web service client error: I/O error: null
```

## Root Cause Analysis

After comparing the NEO client configuration with working clients (NTT, CAI3G, ONENDS), the issue was identified:

1. **JAXB Marshaller Configuration**: The NEO client had `JAXB_FORMATTED_OUTPUT = true` property set, which other clients did not have.

2. **Content-Length Calculation Conflict**: 
   - Spring WebServiceTemplate calculates Content-Length based on the original XML
   - JAXB formatting adds indentation and line breaks, changing the actual byte size
   - Apache HttpClient tries to add Content-Length again based on the formatted XML
   - Result: "Content-Length header already present" error

3. **Timing Issue**: The `ContentLengthHeaderRemover` interceptor was working, but Spring was adding the header back after formatting.

## Solution Implemented

### 1. Removed Problematic JAXB Property

**File**: `NeoWebServiceTemplateConfiguration.java`

**Before**:
```java
marshaller.setMarshallerProperties(java.util.Map.of(
    jakarta.xml.bind.Marshaller.JAXB_FORMATTED_OUTPUT, true,
    jakarta.xml.bind.Marshaller.JAXB_ENCODING, "UTF-8"
));
```

**After**:
```java
// Note: Removed JAXB_FORMATTED_OUTPUT property as it causes Content-Length header conflicts
// The formatted output changes the actual byte size of SOAP messages, causing
// "Content-Length header already present" errors with Apache HttpClient
marshaller.setMarshallerProperties(java.util.Map.of(
    jakarta.xml.bind.Marshaller.JAXB_ENCODING, "UTF-8"
));
```

### 2. Enhanced ContentLengthHeaderRemover

**File**: `NeoSslConfigurationUtil.java`

**Improvements**:
- Added debug logging to track header removal
- Enhanced to remove both Content-Length and Transfer-Encoding headers
- Added detailed comments explaining the fix
- Added proper imports for Logger and Header classes

**Enhanced Implementation**:
```java
private static class ContentLengthHeaderRemover implements HttpRequestInterceptor {
    private static final Logger interceptorLog = LoggerFactory.getLogger(ContentLengthHeaderRemover.class);
    
    @Override
    public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
        // Remove all Content-Length headers to prevent conflicts
        Header[] contentLengthHeaders = request.getHeaders(HTTP.CONTENT_LEN);
        if (contentLengthHeaders.length > 0) {
            interceptorLog.debug("NEO SSL Config: Removing {} Content-Length header(s) to prevent conflicts", 
                               contentLengthHeaders.length);
            request.removeHeaders(HTTP.CONTENT_LEN);
        }
        
        // Also remove Transfer-Encoding headers that might conflict
        Header[] transferEncodingHeaders = request.getHeaders("Transfer-Encoding");
        if (transferEncodingHeaders.length > 0) {
            interceptorLog.debug("NEO SSL Config: Removing {} Transfer-Encoding header(s) to prevent conflicts", 
                               transferEncodingHeaders.length);
            request.removeHeaders("Transfer-Encoding");
        }
    }
}
```

### 3. Added Informative Logging

Added log messages to indicate the fix has been applied:
```java
log.info("NEO JAXB Marshaller: JAXB_FORMATTED_OUTPUT disabled to prevent Content-Length header conflicts");
```

## Verification

### Expected Behavior After Fix

1. **No More Content-Length Errors**: The `ProtocolException` should no longer occur
2. **Successful SOAP Operations**: All NEO operations (provide, suspend, resume, cease) should work
3. **Clean Logging**: Debug logs will show header removal when enabled
4. **Consistent with Other Clients**: NEO client now follows the same pattern as working clients

### Testing the Fix

1. **Run NEO Operations**: Test provide, suspend, resume, and cease operations
2. **Check Logs**: Look for the new log message about JAXB_FORMATTED_OUTPUT being disabled
3. **Enable Debug Logging**: Set logging level to DEBUG to see header removal logs
4. **Monitor for Errors**: Ensure no more Content-Length header errors occur

### Debug Logging Configuration

To see the header removal in action, add this to your logging configuration:
```yaml
logging:
  level:
    com.nokia.wing.wdh.sdmadapter.client.neo.security.ssl.NeoSslConfigurationUtil.ContentLengthHeaderRemover: DEBUG
```

## Files Modified

1. `sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/config/NeoWebServiceTemplateConfiguration.java`
   - Removed `JAXB_FORMATTED_OUTPUT = true` property
   - Added explanatory comments and logging

2. `sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/security/ssl/NeoSslConfigurationUtil.java`
   - Enhanced `ContentLengthHeaderRemover` with better logging and header handling
   - Added missing imports for Logger, LoggerFactory, and Header

## Impact

- **Positive**: Fixes the consistent Content-Length header error affecting all NEO operations
- **Minimal**: Only removes XML formatting (indentation), which doesn't affect functionality
- **Consistent**: Aligns NEO client configuration with other working clients
- **Maintainable**: Adds better logging and documentation for future troubleshooting

## Future Considerations

- Monitor for any other header-related issues
- Consider adding unit tests for the ContentLengthHeaderRemover
- Keep this fix in mind when updating JAXB or HTTP client dependencies
