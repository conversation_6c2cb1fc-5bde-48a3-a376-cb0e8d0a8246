# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

sdmadpterclient:
  onends:
    includeQosInApn: false
    trustStore: classpath:config/keys/poc_ntt.jks
    keyStore: classpath:config/keys/ntsysopt2-client.keystore
    password: provgw
    url: https://**********/ProvisioningGateway/services/SPMLNWSubscriber10Service
    hlrNsrUrl: https://**********/ProvisioningGateway/services/SPMLHlrNsr21Service 
    hssUnifiedNsrUrl: https://localhost/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
    http:
      maxConnections: 25
      maxConnectionsPerRoute: 15
